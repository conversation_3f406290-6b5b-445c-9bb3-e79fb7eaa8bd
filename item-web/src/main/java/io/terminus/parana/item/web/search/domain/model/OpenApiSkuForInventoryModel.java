package io.terminus.parana.item.web.search.domain.model;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.parana.item.search.docobject.SkuDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.naming.ldap.PagedResultsControl;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2018-10-31 下午7:45
 */
@Data
public class OpenApiSkuForInventoryModel implements Serializable {

    private static final long serialVersionUID = -2545794022184317676L;

    @ApiModelProperty("SKUID")
    private Long id;
    @ApiModelProperty("物理库存")
    private Long realQuantity;
    @ApiModelProperty("可销库存")
    private Long sellableQuantity;
    @ApiModelProperty("错误原因")
    private String errorMessage;



}
