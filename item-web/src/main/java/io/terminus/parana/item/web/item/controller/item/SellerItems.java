package io.terminus.parana.item.web.item.controller.item;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.item.area.api.bean.request.*;
import io.terminus.parana.item.area.api.bean.response.FullAreaItemDetailInfo;
import io.terminus.parana.item.area.api.facade.AreaItemReadFacade;
import io.terminus.parana.item.area.api.facade.AreaItemWriteFacade;
import io.terminus.parana.item.common.base.IdVersionPair;
import io.terminus.parana.item.common.util.excel.expor.ItemExcelExportHelper;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.cronjob.api.bean.dto.CronJobDTO;
import io.terminus.parana.item.cronjob.api.bean.request.CronJobBatchAbandonRequest;
import io.terminus.parana.item.cronjob.api.bean.request.CronJobBatchCreateRequest;
import io.terminus.parana.item.cronjob.api.bean.request.CronJobQueryByTargetCodeRequest;
import io.terminus.parana.item.cronjob.api.bean.request.CronJobScanPagingRequest;
import io.terminus.parana.item.cronjob.api.bean.request.param.CronJobParam;
import io.terminus.parana.item.cronjob.api.facade.CronJobReadFacade;
import io.terminus.parana.item.cronjob.api.facade.CronJobWriteFacade;
import io.terminus.parana.item.cronjob.enums.CronJobStatus;
import io.terminus.parana.item.cronjob.enums.CronJobType;
import io.terminus.parana.item.delivery.api.facade.DeliveryFeeReadFacade;
import io.terminus.parana.item.dto.ItemBatchUpdateStatusDTO;
import io.terminus.parana.item.item.api.bean.request.item.*;
import io.terminus.parana.item.item.api.bean.request.item.param.ItemDetailParam;
import io.terminus.parana.item.item.api.bean.response.item.FullItemWithDetailInfo;
import io.terminus.parana.item.item.api.bean.response.item.ItemHomeStatisticalBriefInfo;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.facade.ItemReadFacade;
import io.terminus.parana.item.item.api.facade.ItemWriteFacade;
import io.terminus.parana.item.item.enums.ItemStatus;
import io.terminus.parana.item.sap.api.facade.SkuYlGetConstantsFacade;
import io.terminus.parana.item.shop.api.bean.request.ShopSingleQueryByIdRequest;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.shop.api.facade.ShopReadFacade;
import io.terminus.parana.item.shop.enums.ShopStatus;
import io.terminus.parana.item.util.FtpUtil;
import io.terminus.parana.item.web.item.param.CronJobCreateParam;
import io.terminus.parana.item.web.item.param.ItemBatchUpdateImportExcelTranscriptTemplate;
import io.terminus.parana.item.web.item.param.ItemImportExcelTranscriptTemplate;
import io.terminus.parana.item.web.item.query.ItemCreateQueryPlus;
import io.terminus.parana.item.web.item.query.ItemUpdateQuery;
import io.terminus.parana.item.web.item.query.ItemUpdateStatusQuery;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-05-28
 */
@Slf4j
@Api(
        value = "商品_Controller",
        tags = {"商品_Controller"}
)
@RestController
@RequestMapping("/api/item")
@RequiredArgsConstructor
public class SellerItems {

    private final ItemReadFacade itemReadFacade;
    private final ItemWriteFacade itemWriteFacade;
    private final AreaItemWriteFacade areaItemWriteFacade;
    private final CronJobReadFacade cronJobReadFacade;
    private final CronJobWriteFacade cronJobWriteFacade;
    private final SkuYlGetConstantsFacade skuYlGetConstantsFacade;
    private final ShopReadFacade shopReadFacade;
    private final DeliveryFeeReadFacade deliveryFeeReadFacade;
    private final AreaItemReadFacade areaItemReadFacade;
    private final ItemExcelExportHelper itemExcelExportHelper;

    @Value("${ftp.host:1}")
    private String ftpHost;
    @Value("${ftp.port:21}")
    private Integer ftpPort;
    @Value("${ftp.username:admin}")
    private String ftpUserName;
    @Value("${ftp.password:admin}")
    private String ftpPassword;



    private FullItemWithDetailInfo getFullItem(Long id) {
        Integer tenantId = RequestContext.getTenantId();

        FullItemWithDetailQueryRequest request = new FullItemWithDetailQueryRequest();
        request.setItemId(id);
        request.setTenantId(tenantId);

        return Assert.take(itemReadFacade.queryFullItemWithDetail(request));
    }

    @PutMapping("/all")
    @ApiOperation("更新商品完整信息")
    public Boolean update(@RequestBody @ApiParam("商品信息") ItemUpdateQuery query) {
        ItemUpdateRequest request = new ItemUpdateRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setShopId(RequestContext.getShopId());
        request.setItemParam(query.getItemParam());
        request.setSkuParamList(query.getSkuParamList());
        request.setSkuYLParam(query.getSkuYLParam());
        request.setItemDetailParam(query.getItemDetailParam());
        request.setRelationParam(query.getRelationParam());
        request.setUpdateParams(query.getUpdateParams());
        request.setType(query.getType());
        request.setOperatorIdSet(query.getOperatorIdSet());
        request.setUserId(RequestContext.getUserId());
        request.setUserName(RequestContext.getUserName());
        log.info("all.update.request:{}", request);
        return Assert.take(itemWriteFacade.update(request));
    }

    @PutMapping("/status")
    @ApiOperation("更新商品状态")
    public Boolean updateStatus(@RequestBody ItemUpdateStatusQuery dto) {
        IdVersionPair pair = new IdVersionPair(dto.getId(), dto.getVersion());
        ItemUpdateStatusRequest request = new ItemUpdateStatusRequest();
        request.setTargetList(Lists.newArrayList(pair));
        request.setShopId(RequestContext.getShopId());
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(RequestContext.getUserId().toString());
        return Assert.take(itemWriteFacade.sellerUpdateStatus(request));
    }


    @PutMapping("/status/batch")
    @ApiOperation("批量更新商品状态")
    public Boolean batchUpdateStatus(@RequestBody ItemBatchUpdateStatusDTO dto) {
        ItemUpdateStatusRequest request = new ItemUpdateStatusRequest();
        request.setTargetList(dto.getIdVersionPairList());
        request.setTenantId(RequestContext.getTenantId());
        request.setShopId(RequestContext.getShopId());
        request.setStatus(dto.getStatus());
        request.setUpdatedBy(RequestContext.getUserId().toString());

        return Assert.take(itemWriteFacade.sellerUpdateStatus(request));
    }

    @PutMapping("/cancel")
    @ApiOperation("商品作废")
    public Boolean cancel(@RequestBody VendorItemCancelRequest request) {

        log.info("itemId:{} OperatorId:{} VendorId:{} Version:{} ", request.getItemId(), request.getOperatorId(), request.getVendorId(), request.getVersion());

        ItemCancelRequest itemCancelRequest = new ItemCancelRequest();
        itemCancelRequest.setItemId(request.getItemId());
        itemCancelRequest.setOperatorId(request.getOperatorId());
        itemCancelRequest.setVendorId(request.getVendorId());
        itemCancelRequest.setVersion(request.getVersion());

        return Assert.take(itemWriteFacade.cancel(itemCancelRequest));
    }

    @DeleteMapping("/batch")
    @ApiOperation("批量删除商品")
    public Boolean batchDeleteItems(@RequestBody List<IdVersionPair> pairList) {
        ItemDeleteRequest request = new ItemDeleteRequest();
        request.setTargetList(pairList);
        request.setTenantId(RequestContext.getTenantId());
        request.setShopId(RequestContext.getShopId());
        request.setUpdatedBy(RequestContext.getUserId().toString());

        return Assert.take(itemWriteFacade.delete(request));
    }

    @ApiOperation("创建商品")
    @PostMapping("/plus")
    public Long createItemPlus(@RequestBody @ApiParam("商品对象") ItemCreateQueryPlus query) {
        ItemCreateRequest request = new ItemCreateRequest();
        log.info("createItemPlus=========={}", query);
        request.setItemParam(query.getItem());
        request.setSkuParamList(query.getSkus());
        request.setSkuYLParam(query.getSkuYL());
        request.setItemDetailParam(query.getItemDetail());
        request.setShopId(RequestContext.getShopId());
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(RequestContext.getUserId().toString());
        request.setItemTypeAndAttributeParam(query.getItemTypeAndAttributeParam());
        request.setVendorItemChannlRelationCreateRequests(query.getVendorItemChannlRelationCreateRequests());
        request.setType(query.getType());
        request.setOperatorIdSet(query.getOperatorIdSet());
        request.setUserId(RequestContext.getUserId());
        request.setUserName(RequestContext.getUserName());
        return Assert.take(itemWriteFacade.createPlus(request));
    }

    @ApiOperation("更新商品详情")
    @PutMapping("/detail")
    @Deprecated
    public Boolean updateItemDetail(@RequestBody ItemDetailParam itemDetailParam) {
        Integer tenantId = RequestContext.getTenantId();
        ItemDetailUpdateRequest request = new ItemDetailUpdateRequest();
        request.setItemDetailParam(itemDetailParam);
        request.setTenantId(tenantId);
        request.setVendorId(RequestContext.getShopId());
        request.setUpdatedBy(RequestContext.getUserId().toString());
        return Assert.take(itemWriteFacade.updateItemDetail(request));
    }

    @ApiOperation("获取商品定时上下架任务")
    @GetMapping("/cronjob")
    public List<CronJobDTO> findCronJobByItemId(@RequestParam Set<Long> itemIds) {
        CronJobQueryByTargetCodeRequest request = new CronJobQueryByTargetCodeRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setStatus(CronJobStatus.WAITING_FOR_EXECUTION);
        request.setTargetCodeList(AssembleDataUtils.set2list(itemIds, Objects::toString));
        request.setTypeList(Lists.newArrayList(
                CronJobType.ITEM_AUTOMATICALLY_ON_SHELVES,
                CronJobType.ITEM_AUTOMATICALLY_OFF_SHELVES));
        return Assert.take(cronJobReadFacade.queryByTargetCodes(request));
    }

    @PostMapping("/cronjob")
    @ApiOperation("商品上下架定时任务创建")
    public Boolean itemOnShelfJobCreate(@RequestBody CronJobCreateParam param) {
        if (CollectionUtils.isEmpty(param.getItemIds())) {
            throw new RestException("item.ids.is.empty");
        }
        CronJobBatchCreateRequest request = new CronJobBatchCreateRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setCronJobParamList(checkAndConverter(param.getItemIds(), param.getOnShelvesAt(), param.getOffShelvesAt()));
        return Assert.take(cronJobWriteFacade.batchCreate(request));
    }

    @DeleteMapping("/cronjob")
    @ApiOperation("商品上下架定时任务删除")
    public Boolean itemJobDelete(@RequestParam Set<Long> ids) {
        CronJobBatchAbandonRequest request = new CronJobBatchAbandonRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setTargetCodeList(AssembleDataUtils.set2list(ids, Objects::toString));
        Response<Boolean> response = cronJobWriteFacade.batchAbandon(request);
        return Assert.take(response);
    }

    @GetMapping("/cronjob/paging")
    @ApiOperation("分页获取商品上下架定时任务")
    public Paging<CronJobDTO> itemJobPaging(@RequestParam(required = false) Integer type,
                                            @RequestParam(required = false) Integer status,
                                            @RequestParam(required = false) Integer pageNo,
                                            @RequestParam(required = false) Integer pageSize) {
        CronJobScanPagingRequest request = new CronJobScanPagingRequest();
        List<Integer> typeList = Lists.newArrayList();
        typeList.add(type);
        request.setTypeList(typeList);
        request.setStatus(status);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        Response<Paging<CronJobDTO>> paging = cronJobReadFacade.scanJobPaging(request);
        return Assert.take(paging);
    }

    private List<CronJobParam> checkAndConverter(Set<Long> itemIds, Date onShelvesAt, Date offShelvesAt) {

        ItemQueryByIdRequest itemRequest = new ItemQueryByIdRequest();
        itemRequest.setTenantId(RequestContext.getTenantId());
        itemRequest.setIdSet(itemIds);
        Response<List<ItemInfo>> itemResponse = itemReadFacade.queryById(itemRequest);
        if (!itemResponse.isSuccess()) {
            log.error("item info query error, cause:{}", itemResponse.getError());
            throw new RestException("item.info.query.error");
        }

        List<ItemInfo> itemList = itemResponse.getResult();
        Map<Long, Integer> itemStatusMap = itemList.stream().collect(Collectors.toMap(ItemInfo::getId, ItemInfo::getStatus));

        List<CronJobParam> cronJobParamList = Lists.newArrayList();
        itemIds.forEach(itemId -> {
            Integer status = itemStatusMap.get(itemId);
            if (status == null) {
                log.warn("item:{}, 状态信息丢失", itemId);
                return;
            }
            log.info("item:{} status:{}", itemId, status);

            if (status.equals(ItemStatus.ON_SHELF.getValue())) {
                if (onShelvesAt != null && offShelvesAt == null) {
                    log.warn("item:{}, 上架商品不能只设置上架任务", itemId);
                    throw new RestException("上架商品不能只设置上架任务");
                }
                if (onShelvesAt != null && offShelvesAt != null
                        && compareDate(onShelvesAt, offShelvesAt) <= 0) {
                    log.warn("item:{}, 上架商品不能设置先上架后下架任务", itemId);
                    throw new RestException("上架商品不能设置先上架后下架任务");
                }
            }

            if (status.equals(ItemStatus.OFF_SHELF.getValue())) {
                if (onShelvesAt == null && offShelvesAt != null) {
                    log.warn("item:{}, 下架商品不能只设置下架任务", itemId);
                    throw new RestException("下架商品不能只设置下架任务");
                }
                if (onShelvesAt != null && offShelvesAt != null
                        && compareDate(onShelvesAt, offShelvesAt) >= 0) {
                    log.warn("item:{}, 下架商品不能设置先下架后上架任务", itemId);
                    throw new RestException("下架商品不能设置先下架后上架任务");
                }
            }

            if (onShelvesAt != null) {
                CronJobParam cronJobParam = new CronJobParam();
                cronJobParam.setTenantId(RequestContext.getTenantId());
                cronJobParam.setTargetCode(itemId.toString());
                cronJobParam.setType(CronJobType.ITEM_AUTOMATICALLY_ON_SHELVES);
                cronJobParam.setExecuteAt(onShelvesAt);
                cronJobParamList.add(cronJobParam);
            }
            if (offShelvesAt != null) {
                CronJobParam cronJobParam = new CronJobParam();
                cronJobParam.setTenantId(RequestContext.getTenantId());
                cronJobParam.setTargetCode(itemId.toString());
                cronJobParam.setType(CronJobType.ITEM_AUTOMATICALLY_OFF_SHELVES);
                cronJobParam.setExecuteAt(offShelvesAt);
                cronJobParamList.add(cronJobParam);
            }
        });
        return cronJobParamList;
    }

    public Integer compareDate(Date dt1, Date dt2) {
        if (dt1 == null || dt2 == null) {
            return null;
        }

        if (dt1.getTime() > dt2.getTime()) {
            return 1;
        } else if (dt1.getTime() < dt2.getTime()) {
            return -1;
        } else {
            return 0;
        }
    }

    @PostMapping("/item/batch-publish")
    @ApiOperation("批量发布商品")
    public Boolean batchPublishAreaItem(@RequestBody VendorItemPublishRequest request) {
        validateZqVendor(RequestContext.getShopId(), request);
        request.setVendorId(RequestContext.getShopId());
        request.setTenantId(RequestContext.getTenantId());
        log.info("batchPublishAreaItem==RequestContext.getTenantId======{}", RequestContext.getTenantId());
        request.setUserId(RequestContext.getUserId());
        request.setUserName(RequestContext.getUserName());
        log.info("batchPublishAreaItem========{}", request);
        return Assert.take(areaItemWriteFacade.publish(request));
    }

    @PostMapping("/item/batch-publish-items")
    @ApiOperation("多商品批量发布")
    public Boolean batchPublishAreaItemByItems(@RequestBody VendorItemPublishRequest request) {

        //入参校验
        ParamUtil.notEmpty(request.getItemIdSet(), "商品ID列表不能为空");
        request.setVendorId(RequestContext.getShopId());

        validateZqVendor(RequestContext.getShopId(), request);

        return Assert.take(areaItemWriteFacade.publishByItems(request));
    }

    //政企供应商 屏蔽发布商品功能
    private void validateZqVendor(Long vendorId, AbstractRequest request) {

        if (null == vendorId) {
            return;
        }
        ShopSingleQueryByIdRequest shopRequest = new ShopSingleQueryByIdRequest();
        BeanUtils.copyProperties(request, shopRequest);
        shopRequest.setId(vendorId);
        ShopInfo shopInfo = Assert.take(shopReadFacade.querySingleById(shopRequest));

        boolean isZqFlag = (null != shopInfo && StringUtils.isNotBlank(shopInfo.getIsZq()) && StringUtils.equals("Y", shopInfo.getIsZq()));
        Assert.isTrue(!isZqFlag, "政企供应商暂时不允许发布商品信息！");

    }

    @PostMapping("/update-area-channel")
    @ApiOperation("供应商修改销售区域销售渠道")
    public Boolean updateAreaAndChannelByVendor(@RequestBody ItemUpdateAreaAndChannelRequest request) {
        request.setVendorId(RequestContext.getShopId());
        request.setTenantId(RequestContext.getTenantId());
        return Assert.take(itemWriteFacade.updateAreaAndChannelByVendor(request));
    }


    @PostMapping("/check-again-update")
    @ApiOperation("供应商更新商品校验")
    public Boolean checkItemAgainUpdate(@RequestBody ItemAgainUpdateRequest request) {
        request.setVendorId(RequestContext.getShopId());
        request.setTenantId(RequestContext.getTenantId());
        return Assert.take(itemReadFacade.queryItemIsAgainUpdate(request));
    }

    @ApiOperation(value = "批量导入商品")
    @PostMapping(value = "/itemExcelUpload", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "importItemFile", value = "文件流对象,接收数组格式", required = true, dataType = "__File")
    public Response<Boolean> categoryExcelImport(MultipartFile importfile) {
        String errorMsg = "";
        try {
            ShopSingleQueryByIdRequest shopRequest = new ShopSingleQueryByIdRequest();
            shopRequest.setId(RequestContext.getShopId());
            shopRequest.setTenantId(RequestContext.getTenantId());
            ShopInfo shopInfo = Assert.take(shopReadFacade.querySingleById(shopRequest));
            if (ShopStatus.FROZEN.equals(ShopStatus.resolve(shopInfo.getStatus()))) {
                log.error("供应商被冻结!");
                return Response.fail("供应商被冻结!");
            }
            // TODO 校验是否存着正在处理的商品导入单 可以参考药购SellerItemImport.itemExcelImport

            byte[] bytes = importfile.getBytes();
            log.info("文件名：{}, 真实文件：{}", importfile.getName(), importfile.getOriginalFilename());
            return itemWriteFacade.uploadFileToJob(bytes, importfile.getOriginalFilename(), RequestContext.getShopId(), RequestContext.getUserId(),RequestContext.getUserName());
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
            errorMsg = e.getMessage();
        }
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new RuntimeException(errorMsg);
        }
        return Response.ok();
    }

    @ApiOperation("模板导出")
    @GetMapping("/excelTemplate")
    public void exportCategoryTemplate(HttpServletResponse response) {
        List<ItemImportExcelTranscriptTemplate> datas = new ArrayList<>();
        String fileName = "item_import_template.xlsx";
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-disposition", "attachment;charset=utf-8;filename=" + fileName);
        try {
           if (datas.isEmpty()) {
                itemExcelExportHelper.downloadTemplate(ItemImportExcelTranscriptTemplate.class, response.getOutputStream());
            } else {
                itemExcelExportHelper.downloadTemplate(datas, ItemImportExcelTranscriptTemplate.class, response.getOutputStream());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RestException(e.getMessage());
        }
    }

    @ApiOperation("供应商商品(预览)详情")
    @GetMapping("/preview/detail")
    public FullAreaItemDetailInfo previewDetail(FullAreaItemQueryByOperatorIdAndItemIdRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        return Assert.take(areaItemReadFacade.areaItemPreviewDetail(request));
    }


    @PostMapping("/ppt/vendor/offShelfCheck")
    @ApiOperation("下架前校验")
    public Response<String> offShelfCheck(@RequestBody AreaItemOffShelfRequest request) {
        return areaItemWriteFacade.offShelfCheck(request);
    }

    @PostMapping("/vendor/off-shelf")
    @ApiOperation("供应商下架商品")
    public Boolean offShelf(@RequestBody AreaItemOffShelfRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(RequestContext.getUserName());
        return Assert.take(areaItemWriteFacade.vendorOffShelf(request));
    }

    @PostMapping("/vendor/on-shelf")
    @ApiOperation("供应商上架商品")
    public Boolean onShelf(@RequestBody AreaItemOnShelfRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(RequestContext.getUserName());
        return Assert.take(areaItemWriteFacade.operatorOnShelf(request));
    }

    @ApiOperation("数据概况")
    @GetMapping("/data/brief")
    public ItemHomeStatisticalBriefInfo getItemStatisticalBrief(ItemHomeStatisticalBriefRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        request.setVendorId(RequestContext.getShopId());
        Response<ItemHomeStatisticalBriefInfo> itemStatisticalBrief = areaItemReadFacade.getItemStatisticalBrief(request);
        if (!itemStatisticalBrief.isSuccess()) {
            throw new RestException(itemStatisticalBrief.getError());
        }
        return itemStatisticalBrief.getResult();
    }

    @ApiOperation(value = "上传压缩文件到FTP")
    @PostMapping(value = "/uploadZip")
    @SneakyThrows
    public JSONObject uploadZip(@RequestParam MultipartFile file) {
        String host = "127.0.0.1";
        String username = "admin";
        String password = "admin";
        String originalFilename = file.getOriginalFilename();
        // 获取文件后缀
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        String fileName = "ftp_" + IdUtil.fastSimpleUUID() + suffix;
//        boolean isUpLoad = FtpUtil.uploadFile(host, ftpPort, username, password, "/", "", fileName, file.getInputStream());
        boolean isUpLoad = FtpUtil.uploadFile(ftpHost, ftpPort, ftpUserName, ftpPassword, "/", "", fileName, file.getInputStream());
        if(!isUpLoad) {
            throw new RestException("上传失败");
        }
        JSONObject json = new JSONObject();
        json.put("url", fileName);
        return json;
    }

    @ApiOperation(value = "压缩文件导入商品主图和详情图")
    @PostMapping(value = "/importImageZip")
    public Boolean importImageZip(@RequestBody ItemImportImageRequest request) {
        request.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedName(RequestContext.getUserName());
        request.setShopId(RequestContext.getShopId());
        Assert.isFalse(StringUtils.isBlank(request.getFilePath()), "文件路径不能为空");
        return Assert.take(itemWriteFacade.importImageForZip(request));
    }

    @PostMapping("/updateItemForWdt")
    @ApiOperation("供应商更新商品校验")
    public Boolean updateItemForWdt(@RequestBody ItemWdtUpdateRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        return Assert.take(itemWriteFacade.updateItemForWdt(request));
    }


    @ApiOperation("批量导入修改的模板导出")
    @GetMapping("/exportItemBatchUpdateTemp")
    public void exportItemBatchUpdateTemp(HttpServletResponse response) {
        List<ItemBatchUpdateImportExcelTranscriptTemplate> datas = new ArrayList<>();
        String fileName = "item_import_template.xlsx";
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-disposition", "attachment;charset=utf-8;filename=" + fileName);
        try {
            ItemBatchUpdateImportExcelTranscriptTemplate template = new ItemBatchUpdateImportExcelTranscriptTemplate();
            template.setItemId("必填（除此项，其他只需填写要修改的项）");
            template.setItemName("导入时请删除此行");
            template.setUniversalName("开发票的商品名称");
            template.setKeyword("多个用,隔开");
            template.setVatrate("任选其中一个： 0、0.06、0.09、0.11、0.13、0.16、0.17");
            template.setDeliveryFeeTempName("填写后台创建的运费模板名称");
            template.setSupportReturn("填写对应值： 1表示“是”，0表示“否”");
            template.setSkuId("如果需要修改N-P列，则此项必填");
            template.setStatus("填写对应值： -1表示“是”，0表示“否”");
            datas.add(template);
            itemExcelExportHelper.downloadTemplate(datas, ItemBatchUpdateImportExcelTranscriptTemplate.class, response.getOutputStream());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RestException(e.getMessage());
        }
    }

    @ApiOperation(value = "批量导入修改")
    @PostMapping(value = "/importItemBatchUpdate", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "importItemBatchUpdate", value = "文件流对象,接收数组格式", required = true, dataType = "__File")
    public Response<Boolean> importItemBatchUpdate(MultipartFile importfile) {
        String errorMsg = "";
        try {
            byte[] bytes = importfile.getBytes();
            log.info("文件名：{}, 真实文件：{}", importfile.getName(), importfile.getOriginalFilename());
            return itemWriteFacade.importItemBatchUpdate(
                    bytes, importfile.getOriginalFilename(), RequestContext.getShopId(), RequestContext.getUserId(), RequestContext.getUserName(), RequestContext.getTenantId());
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
            errorMsg = e.getMessage();
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(errorMsg)) {
            throw new RuntimeException(errorMsg);
        }
        return Response.ok();
    }
}
