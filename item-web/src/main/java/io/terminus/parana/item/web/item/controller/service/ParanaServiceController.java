package io.terminus.parana.item.web.item.controller.service;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.item.choicelot.api.facade.ChoiceLotLibReadFacade;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.service.api.bean.request.*;
import io.terminus.parana.item.service.api.bean.response.ParanaServiceInfoResponse;
import io.terminus.parana.item.service.api.facade.ParanaServiceReadFacade;
import io.terminus.parana.item.service.api.facade.ParanaServiceWriteFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(
	value = "服务表_Controller",
	tags = {"服务表_Controller"}
)
@RequestMapping("/api/service/web/paranaService")
@RequiredArgsConstructor
public class ParanaServiceController {

    private final ParanaServiceReadFacade paranaServiceReadFacade;
    private final ChoiceLotLibReadFacade choiceLotLibReadFacade;

    @ApiOperation("分页查询服务表列表")
    @GetMapping("/page")
    public Paging<ParanaServiceInfoResponse> page(ParanaServicePageRequest request) {
        request.setPageSize(100);
        request.setServiceType(0);
        Response<Paging<ParanaServiceInfoResponse>> response = paranaServiceReadFacade.page(request);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        List<ParanaServiceInfoResponse> newList = Lists.newArrayList();
        Paging<ParanaServiceInfoResponse> paging = response.getResult();
        if(CollectionUtil.isNotEmpty(paging.getData())){
            //获取类型为选品库的所有选品库id  todo api的数据隔离根据二期需求改动
            Set<Long> set = paging.getData().stream().filter(f -> f.getType() != null && f.getType() != 2).map(ParanaServiceInfoResponse::getProjectId).collect(Collectors.toSet());
            //根据服务绑定的选品库查看选品库绑定的渠道
            Map<Long, List<Long>> map = Assert.take(choiceLotLibReadFacade.listChoiceVisibleRelationship(set));
            if(CollectionUtil.isNotEmpty(map)){
                for (ParanaServiceInfoResponse service : paging.getData()) {
                    List<Long> channels = map.get(service.getProjectId());
                    //根据选品库id获取绑定的渠道集合
                    if(CollectionUtil.isNotEmpty(channels) && Objects.nonNull(RequestContext.getShopId())){
                        if(CollectionUtil.isNotEmpty(channels.stream().filter(f -> Objects.nonNull(f) && f.longValue() == RequestContext.getShopId()).collect(Collectors.toList()))){
                            newList.add(service);
                        }
                    }else{
                        newList.add(service);
                    }
                }
                paging.setData(newList);
            }

        }
        return paging;
    }

    @GetMapping(value = "/view")
    @ApiOperation("查询服务表")
    public ParanaServiceInfoResponse view(ParanaServiceQueryRequest request) {

    	Response<ParanaServiceInfoResponse> response = paranaServiceReadFacade.view(request);
        if (!response.isSuccess()) {
            log.info("view ParanaService failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

    @GetMapping(value = "/list")
    @ApiOperation("查询服务表列表")
    public List<ParanaServiceInfoResponse> list(ParanaServiceQueryRequest request) {
    	Response<List<ParanaServiceInfoResponse>> response = paranaServiceReadFacade.list(request);
        if (!response.isSuccess()) {
            log.info("list ParanaService failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }


}
