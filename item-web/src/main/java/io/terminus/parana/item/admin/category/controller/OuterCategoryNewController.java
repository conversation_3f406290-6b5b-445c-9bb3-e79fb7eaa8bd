package io.terminus.parana.item.admin.category.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.item.admin.category.bean.excel.CategoryExcelImportProcessor;
import io.terminus.parana.item.admin.category.bean.excel.CategoryInportTemplate;
import io.terminus.parana.item.admin.category.bean.excel.OuterCategoryExcelImportBO;
import io.terminus.parana.item.admin.category.bean.vo.OuterCategoryNewSearchListVO;
import io.terminus.parana.item.admin.category.bean.vo.OuterCategoryNewSearchVO;
import io.terminus.parana.item.admin.category.bean.vo.OuterCategoryNewUpdateVO;
import io.terminus.parana.item.admin.category.bean.vo.OuterCategoryNewVO;
import io.terminus.parana.item.category.api.bean.request.*;
import io.terminus.parana.item.category.api.bean.response.OuterCategoryNewInfo;
import io.terminus.parana.item.category.api.bean.response.OuterCategoryNewListInfo;
import io.terminus.parana.item.category.api.facade.OuterCategoryNewReadFacade;
import io.terminus.parana.item.category.api.facade.OuterCategoryNewWriteFacade;
import io.terminus.parana.item.common.util.excel.expor.ItemExcelExportHelper;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.api.bean.request.item.ItemExcelReportCreateRequest;
import io.terminus.parana.item.shop.api.facade.ShopReadFacade;
import io.terminus.parana.item.web.excel.ProcessResult;
import io.terminus.parana.item.zhengqi.api.bean.request.OuterShopQueryRequest;
import io.terminus.parana.item.zhengqi.api.bean.response.OuterCategoryBindingInfoList;
import io.terminus.parana.item.zhengqi.api.bean.response.OuterShopInfoResponse;
import io.terminus.parana.item.zhengqi.api.facade.OuterShopReadFacade;
import io.terminus.parana.item.zhengqi.api.facade.ZhengQiItemWriteFacade;
import io.terminus.parana.trade.common.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@RestController
@Api("外部类目接口")
@RequestMapping("/api/admin/item/OuterCategoryNew")
public class OuterCategoryNewController {

    @Autowired
    private OuterCategoryNewWriteFacade outerCategoryNewWriteFacade;

    @Autowired
    private OuterCategoryNewReadFacade categoryNewReadFacade;

    @Autowired
    private CategoryExcelImportProcessor categoryExcelImportProcessor;

    @Autowired
    private final ItemExcelExportHelper itemExcelExportHelper;
    @Autowired
    private ShopReadFacade shopReadFacade;
    @Autowired
    private ZhengQiItemWriteFacade zhengQiItemWriteFacade;
    @Autowired
    private OuterShopReadFacade outerShopReadFacade;

    public OuterCategoryNewController(ItemExcelExportHelper itemExcelExportHelper) {
        this.itemExcelExportHelper = itemExcelExportHelper;
    }


    @ApiOperation("外部类目创建")
    @RequestMapping(value = "/create", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long create(@RequestBody OuterCategoryNewVO outerCategoryNewVO) {

        OuterCategoryNewCreateRequest outerCategoryNewCreateRequest = OuterCategoryNewCreateRequest.builder()
                .name(outerCategoryNewVO.getName())
                .code(outerCategoryNewVO.getCode())
                .tenantId(RequestContext.getTenantId())
                .operatorId(outerCategoryNewVO.getOperatorId())
                .outId(outerCategoryNewVO.getOutId())
                .outPid(outerCategoryNewVO.getOutPid())
                .outLevel(outerCategoryNewVO.getOutLevel())
                .categoryIds(outerCategoryNewVO.getCategoryIds())
                .createdBy(RequestContext.getUserId().toString())
                .createdByName(RequestContext.getUserName())
                .build();
        if (ObjectUtil.isEmpty(outerCategoryNewCreateRequest.getOperatorId())) {
            outerCategoryNewCreateRequest.setOperatorId(2100260002L);
        }
        return Assert.take(outerCategoryNewWriteFacade.createOuter(outerCategoryNewCreateRequest));

    }

    @ApiOperation("外部类目更新")
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean update(@RequestBody OuterCategoryNewUpdateVO updateVO) {

        OuterCategoryNewUpdateRequest outerCategoryNewUpdateRequest = OuterCategoryNewUpdateRequest.builder()
                .id(updateVO.getId())
                .tenantId(RequestContext.getTenantId())
                .operatorId(updateVO.getOperatorId())
                .name(updateVO.getName())
                .code(updateVO.getCode())
                .updatedAt(updateVO.getUpdatedAt())
                .updatedBy(RequestContext.getUserId().toString())
                .updatedByName(RequestContext.getUserName())
                .outId(updateVO.getOutId())
                .outPid(updateVO.getOutPid())
                .outLevel(updateVO.getOutLevel())
                .categoryIds(updateVO.getCategoryIds())
                .build();
//        outerCategoryNewUpdateRequest.setTenantId(RequestContext.getTenantId());
//        outerCategoryNewUpdateRequest.setUpdatedBy(RequestContext.getUserId().toString());
//        if (ObjectUtil.isEmpty(outerCategoryNewUpdateRequest.getOperatorId())) {
//            outerCategoryNewUpdateRequest.setOperatorId(2100260002L);
//        }
        return Assert.take(outerCategoryNewWriteFacade.updateOuter(outerCategoryNewUpdateRequest));

    }

    @ApiOperation("外部类目删除")
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean delete(@RequestBody OuterCategoryNewDeleteRequest request) {

        request.setTenantId(RequestContext.getTenantId());
        request.setOperatorId(request.getOperatorId());
        request.setUpdatedBy(RequestContext.getUserId().toString());
        request.setUpdatedByName(RequestContext.getUserName());
        if (request.getStatus().equals("RUN")) {
            request.setStatus("DIS");
        } else {
            request.setStatus("RUN");
        }

        return Assert.take(outerCategoryNewWriteFacade.deleteOuter(request));

    }

    @ApiOperation("外部类目模糊查询")
    @RequestMapping(value = "/findByName", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OuterCategoryNewInfo> findByName(@RequestBody OuterCategoryNewSearchVO vo) {

        OuterCategoryNewGetNameRequest outerCategoryNewGetNameRequest = new OuterCategoryNewGetNameRequest();
        outerCategoryNewGetNameRequest.setTenantId(RequestContext.getTenantId());
        outerCategoryNewGetNameRequest.setOperatorId(vo.getOperatorId());
        outerCategoryNewGetNameRequest.setName(vo.getName());
        Response<List<OuterCategoryNewInfo>> OutRequest = categoryNewReadFacade.findByName(outerCategoryNewGetNameRequest);
        if (!OutRequest.isSuccess()) {
            log.error("findByName failed, params:{}, cause:{}",
                    outerCategoryNewGetNameRequest, OutRequest.getError());
            throw new ServiceException(OutRequest.getError());
        }
        return OutRequest.getResult();

    }

    @ApiOperation("外部类目,名字,外部id,编码的条件查询，或者查询全部,的返回列表")
    @RequestMapping(value = "/selectOuterAttribute", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OuterCategoryNewListInfo> selectOuterAttribute(@RequestBody OuterCategoryNewSearchListVO vo) {

        OuterCategoryNewGetListRequest outerCategoryNewGetListRequest = new OuterCategoryNewGetListRequest();
        outerCategoryNewGetListRequest.setTenantId(RequestContext.getTenantId());
        outerCategoryNewGetListRequest.setOperatorId(vo.getOperatorId());
        outerCategoryNewGetListRequest.setName(vo.getName());
        outerCategoryNewGetListRequest.setOutId(vo.getOutId());
        outerCategoryNewGetListRequest.setCode(vo.getCode());
        Response<List<OuterCategoryNewListInfo>> OutRequest = categoryNewReadFacade.selectOuterAttribute(outerCategoryNewGetListRequest);
        return OutRequest.getResult();

    }


    @ApiOperation("分页外部类目")
    @RequestMapping(value = "/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<OuterCategoryNewInfo> paging(OuterCategoryNewSearchVO vo) {

        OuterCategoryNewGetRequest request = OuterCategoryNewGetRequest.builder()
                .tenantId(RequestContext.getTenantId())
                .operatorId(vo.getOperatorId())
                .name(vo.getName())
                .outId(vo.getOutId())
                .pageNo(vo.getPageNo())
                .pageSize(vo.getPageSize())
                .status(vo.getStatus())
                .outLevel(vo.getOutLevel())
                .id(vo.getId())
                .build();
        Response<Paging<OuterCategoryNewInfo>> resp = categoryNewReadFacade.paging(request);

        return resp.getResult();

    }

    @ApiOperation(value = "外部类目导入")
    @PostMapping(value = "/categoryExcelImport", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "importfile", value = "文件流对象,接收数组格式", required = true, dataType = "__File")
    public Boolean categoryExcelImport(MultipartFile importfile) {
        try {
            ProcessResult<OuterCategoryExcelImportBO> result = categoryExcelImportProcessor.process(importfile);
            Assert.isTrue(result.isSuccess(), result.getErrorMessage());
            List<OuterCategoryExcelImportBO> excelData = result.getData();
            List<OuterCategoryExcelImportRequest> requests = new ArrayList<>();
//            if (excelData.size() > 500) {
//                throw new ServiceException("批量导入数量不能超过500条！");
//            }
            log.info("excelData.size()--->" + excelData.size());
            log.info("excelData--->" + excelData);

//            List<Long> operatorIds = excelData.stream().map(OuterCategoryExcelImportBO::getOperatorId).distinct().collect(Collectors.toList());
//            if (operatorIds.size() > 1) {
//                throw new ServiceException("不支持导入多个项目的外部分类信息！");
//            }

            List<String> outPids = Lists.newArrayList();
            for (OuterCategoryExcelImportBO bo : excelData) {
//                if (ObjectUtil.isEmpty(bo.getOperatorId())) {
//                    bo.setOperatorId(2100260002L);
//                } else {
//                    bo.setOperatorId(bo.getOperatorId());
//                }
                if (StringUtils.isBlank(bo.getOutId()) || StringUtils.isBlank(bo.getName())) {
                    throw new ServiceException("分类名称或者外部id数据为空！");
                }
                if (ObjectUtil.isNotEmpty(bo.getOutId())) {
                    if (!NumberUtil.isNumber(bo.getOutId())) {
                        throw new ServiceException("外部id：" + bo.getOutId() + "不是数字！");
                    }
                }
                if (null != bo.getOutPid() && !bo.getOutPid().equals("0") && !bo.getOutPid().equals("-1")) {
                    if (NumberUtil.isNumber(bo.getOutPid())) {
                        outPids.add(bo.getOutPid());
                    } else {
                        throw new ServiceException("外部父id：" + bo.getOutPid() + "不是数字！");
                    }

                }
            }
            Map<Long, List<OuterCategoryNewInfo>> outMap = Maps.newHashMap();
            if (outPids.size() > 0) {
                outPids = outPids.stream().distinct().collect(Collectors.toList());
                List<OuterCategoryNewInfo> list = Assert.take(categoryNewReadFacade.findByOperatorIdAndOuterIds(null, outPids, RequestContext.getTenantId()));
                if (CollectionUtil.isEmpty(list)) {
                    throw new ServiceException("父类id：" + outPids + "未找到！");
                } else {
                    Set<String> ids = list.stream().map(OuterCategoryNewInfo::getOutId).collect(Collectors.toSet());
                    List<String> collect = outPids.stream().filter(f -> !ids.contains(f)).collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(collect)) {
                        throw new ServiceException("父类id：" + collect + "未找到！");
                    }
                    outMap = list.stream().collect(Collectors.groupingBy(OuterCategoryNewInfo::getId));
                }
            }

            for (OuterCategoryExcelImportBO bo : excelData) {
                OuterCategoryExcelImportRequest req = new OuterCategoryExcelImportRequest();
//                req.setId(bo.getId());
                req.setTenantId(RequestContext.getTenantId());
//                req.setOperatorId(2100260002L);
                req.setName(bo.getName());
//                req.setCode(bo.getCode());
                req.setOutId(bo.getOutId());
                req.setCreatedBy(RequestContext.getUserId().toString());
                req.setCreatedByName(RequestContext.getUserName());
                req.setCreatedAt(new Date());
                req.setUpdatedAt(new Date());
                if (null == bo.getOutPid() || bo.getOutPid().equals("0") || bo.getOutPid().equals("-1")) {
                    req.setOutPid("0");
                    req.setOutLevel(1L);
                } else {
                    req.setOutPid(bo.getOutPid());
                    List<OuterCategoryNewInfo> list = outMap.get(Long.parseLong(req.getOutPid()));
                    if (CollectionUtil.isNotEmpty(list)) {
                        req.setOutLevel(list.get(0).getOutLevel() + 1);
                    }
                }
                req.setUpdateById(RequestContext.getUserId().toString());
                req.setCategoryNames(bo.getCategoryNames());
                requests.add(req);
            }

//            ShopSingleQueryByIdRequest query = new ShopSingleQueryByIdRequest();
//            query.setId(operatorIds.get(0));
//            query.setTenantId(RequestContext.getTenantId());
//            ShopInfo shopInfo = Assert.take(shopReadFacade.querySingleById(query));
//            if (shopInfo == null) {
//                throw new ServiceException("未查询到项目信息！");
//            }
            return Assert.take(outerCategoryNewWriteFacade.categoryExcelImport(requests));
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
            if (StringUtils.isNotEmpty(e.getMessage())) {
                throw new RestException(e.getMessage());
            }
            log.error("外部分类导入失败！ message:{} case:{}", e.getMessage(), Throwables.getStackTraceAsString(e));
            throw new RestException("外部分类导入失败");
        }
    }


    @ApiOperation("数据导出")
    @GetMapping("/outerCategoryExport")
    public Boolean exportCategory(OuterCategoryNewGetRequest request) {
        Integer tenantId = RequestContext.getTenantId();
        Long operatorId = request.getOperatorId();
//        if (ObjectUtil.isEmpty(operatorId)) {
//            operatorId = 2100260002L;
//        }
        GetCategoryListRequest req = new GetCategoryListRequest();
        req.setOperatorId(operatorId);
        req.setTenantId(tenantId);
        req.setName(request.getName());
        req.setOutId(request.getOutId());
        req.setId(request.getId());
        req.setStatus(request.getStatus());
        String reqParamJson = JSON.toJSONString(req);
        ItemExcelReportCreateRequest excelRequest = new ItemExcelReportCreateRequest();
        excelRequest.setOperatorId(RequestContext.getShopId());
        excelRequest.setUserId(RequestContext.getUserId());
        excelRequest.setFileType("外部类目导出");
        String fileName = "OuterCategoryNewListExport_" + TimeUtils.format(new Date(), "yyyy-MM-dd_HH-mm-ss");
        excelRequest.setFileName(fileName);
        excelRequest.setReportType("outer_category_new_list_export");
        excelRequest.setCenter("item");
        excelRequest.setRequestJson(reqParamJson);
        Response<Boolean> response = zhengQiItemWriteFacade.excelCreate(excelRequest);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        return Boolean.TRUE;
    }

    @ApiOperation("模板导出")
    @GetMapping("/excelTemplate")
    public void exportCategoryTemplate(HttpServletResponse response) {
        List<CategoryInportTemplate> datas = new ArrayList<>();
        String fileName = "outerCategory_template.xlsx";
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-disposition", "attachment;charset=utf-8;filename=" + fileName);
        try {
            if (datas.isEmpty()) {
                itemExcelExportHelper.downloadTemplate(CategoryInportTemplate.class, response.getOutputStream());
            } else {
                itemExcelExportHelper.downloadTemplate(datas, CategoryInportTemplate.class, response.getOutputStream());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RestException(e.getMessage());
        }
    }


    @RequestMapping(value = "/children", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("获取下一级类目")
    public List<OuterCategoryNewInfo> findChildrenByPid(@RequestParam(value = "pid", required = false, defaultValue = "0") Long pid,
                                                        @RequestParam(value = "operatorId", required = false) Long operatorId
    ) {

        OuterCategoryNewGetRequest request = new OuterCategoryNewGetRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setPid(pid);
        request.setOperatorId(operatorId);

        Response<List<OuterCategoryNewInfo>> response = categoryNewReadFacade.findChildrenByPid(request);

        if (!response.isSuccess()) {
            log.info("find Children By Pid failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }


    @RequestMapping(value = "/getCategoryBinding", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("获取外部类目绑定关系")
    public List<OuterCategoryBindingInfoList> getCategoryBinding(@RequestParam(value = "id", required = true) Long id
    ) {

        OuterCategoryNewGetRequest request = new OuterCategoryNewGetRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setId(id);

        Response<List<OuterCategoryBindingInfoList>> response = categoryNewReadFacade.getCategoryList(request);

        if (!response.isSuccess()) {
            log.info("get Category Binding failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }


    @GetMapping(value = "/outerShop/list")
    @ApiOperation("查询店铺表列表")
    public List<OuterShopInfoResponse> list(OuterShopQueryRequest request) {
        request.setTenantId(io.terminus.parana.item.common.filter.RequestContext.getTenantId());
        Response<List<OuterShopInfoResponse>> response = outerShopReadFacade.list(request);
        if (!response.isSuccess()) {
            log.info("list OuterShop failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

//    @ApiOperation("获取类目路径")
//    @RequestMapping(value = "/ancestors", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
//    public List<OuterCategoryNewInfo> getAncestors(@RequestParam Long outCategoryId) {
//        OuterCategoryNewGetRequest request = OuterCategoryNewGetRequest.builder().id(outCategoryId).build();
//        request.setTenantId(RequestContext.getTenantId());
//        return Assert.take(categoryNewReadFacade.findAncestorsOf(request));
//    }
//
//    @GetMapping("/getCategories")
//    @ApiOperation("获取类目列表")
//    public List<OuterCategoryNewInfo> getCategories(@RequestParam(value = "projectId", required = true) Long projectId,
//                                                    @RequestParam(value = "itemId", required = true) Long itemId) {
//        return Assert.take(categoryNewReadFacade.getCategories(projectId, itemId, RequestContext.getTenantId()));
//    }
//
//    @GetMapping("/getOuterCategoryList")
//    @ApiOperation("根据项目id 和采购类目id  获取采购类目绑定的项目下的外部分类集合")
//    public List<OuterCategoryNewInfo> getOuterCategoryList(@RequestParam(value = "projectId", required = true) Long projectId,
//                                                    @RequestParam(value = "categoryId", required = true) Long categoryId) {
//        return Assert.take(categoryNewReadFacade.getOuterCategoryList(projectId, categoryId, RequestContext.getTenantId()));
//    }
}
