package io.terminus.parana.item.admin.card;

import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.item.card.api.bean.request.*;
import io.terminus.parana.item.card.api.bean.response.ParanaItemCardInfoResponse;
import io.terminus.parana.item.card.api.bean.response.ParanaPinNUrlResponse;
import io.terminus.parana.item.card.api.facade.ParanaItemCardReadFacade;
import io.terminus.parana.item.card.api.facade.ParanaItemCardWriteFacade;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.api.bean.request.item.ItemExcelReportCreateRequest;
import io.terminus.parana.item.item.api.facade.ItemWriteFacade;
import io.terminus.parana.item.web.excel.ParanaItemCardInfoDTO;
import io.terminus.parana.trade.common.util.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(
        value = "_Controller",
        tags = {"_Controller"}
)

@RequestMapping("/api/item/paranaItemCard")
@RequiredArgsConstructor
public class ParanaItemCardController {

    private final ParanaItemCardReadFacade paranaItemCardReadFacade;
    private final ParanaItemCardWriteFacade paranaItemCardWriteFacade;
    private final ItemWriteFacade itemWriteFacade;

    @ApiOperation("分页查询列表")
    @GetMapping({"/page"})
    public Paging<ParanaItemCardInfoResponse> page(ParanaItemCardPageRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        Response<Paging<ParanaItemCardInfoResponse>> response = paranaItemCardReadFacade.page(request);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        return response.getResult();
    }

    @PostMapping(value = "/create")
    @ApiOperation("创建")
    public Boolean create(@RequestBody ParanaItemCardCreateRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        request.setCreatedBy(String.valueOf(RequestContext.getUserId()));
        Response<Boolean> response = paranaItemCardWriteFacade.create(request);
        if (!response.isSuccess()) {
            log.info("create ParanaItemCard failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

    @PostMapping(value = "/update")
    @ApiOperation("修改")
    public Boolean update(@RequestBody ParanaItemCardUpdateRequest request) {

        Response<Boolean> response = paranaItemCardWriteFacade.update(request);
        if (!response.isSuccess()) {
            log.info("update ParanaItemCard failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

    @PostMapping(value = "/delete")
    @ApiOperation("删除")
    public Boolean delete(@RequestBody ParanaItemCardDeleteRequest request) {

        Response<Boolean> response = paranaItemCardWriteFacade.delete(request);
        if (!response.isSuccess()) {
            log.info("delete ParanaItemCard failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

    @GetMapping(value = "/view")
    @ApiOperation("查询")
    public ParanaItemCardInfoResponse view(ParanaItemCardQueryRequest request) {

        Response<ParanaItemCardInfoResponse> response = paranaItemCardReadFacade.view(request);
        if (!response.isSuccess()) {
            log.info("view ParanaItemCard failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

    @GetMapping(value = "/list")
    @ApiOperation("查询列表")
    public List<ParanaItemCardInfoResponse> list(ParanaItemCardQueryRequest request) {

        Response<List<ParanaItemCardInfoResponse>> response = paranaItemCardReadFacade.list(request);
        if (!response.isSuccess()) {
            log.info("list ParanaItemCard failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

    @GetMapping(value = "/findByOrderId")
    @ApiOperation("根据订单号查询卡号卡密")
    public List<ParanaItemCardInfoResponse> findByOrderId(CardActiveBindRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        Response<List<ParanaItemCardInfoResponse>> response = paranaItemCardReadFacade.findByOrderId(request);
        if (!response.isSuccess()) {
            log.info("findByOrderId ParanaItemCard failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

    @PostMapping(value = "/listByOrderId")
    @ApiOperation("根据订单号查询卡号卡密")
    public List<ParanaItemCardInfoResponse> listByOrderId(@RequestBody CardActiveBindRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        Response<List<ParanaItemCardInfoResponse>> response = paranaItemCardReadFacade.listByOrderId(request);
        if (!response.isSuccess()) {
            log.info("findByOrderId ParanaItemCard failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

    @GetMapping(value = "/exportByOrderId")
    @ApiOperation("根据订单号导出卡号卡密")
    public void exportByOrderId(CardActiveBindRequest request,HttpServletResponse response) throws IOException {
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
        Response<List<ParanaItemCardInfoResponse>> result = paranaItemCardReadFacade.findByOrderId(request);
        if (!result.isSuccess()) {
            log.info("listByOrderId ParanaItemCard failed, params:{}, cause:{}",
                    request, result.getError());
            throw new RestException(result.getError(), result.getCode());
        }
        List<ParanaItemCardInfoResponse> data = result.getResult();
        List<ParanaItemCardInfoDTO> collect = new ArrayList<>();
        int index = 1;
        for (ParanaItemCardInfoResponse datum : data) {
            ParanaItemCardInfoDTO paranaItemCardInfoDTO = new ParanaItemCardInfoDTO();
            paranaItemCardInfoDTO.setIndex(String.valueOf(index));
            paranaItemCardInfoDTO.setCardId(datum.getCardId());
            paranaItemCardInfoDTO.setCardPassword(datum.getCardPassword());
            collect.add(paranaItemCardInfoDTO);
            index++;
        }

        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("导出卡号卡密"+System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream(), ParanaItemCardInfoDTO.class)
                .sheet("卡号卡密")
                .doWrite(collect);
    }

    @ApiOperation("商品卡片导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public Boolean export(@RequestBody ParanaItemCardPageRequest request) {
        log.info("Item Configuration Request.export, request = {}", request);
        ItemExcelReportCreateRequest excelRequest = new ItemExcelReportCreateRequest();
        excelRequest.setUserId(RequestContext.getUserId());
        String fileName = "商品卡片列表_" + TimeUtils.format(new Date(), "yyyy-MM-dd_HH-mm-ss");
        excelRequest.setFileType("商品卡片导出");
        request.setTenantId(RequestContext.getTenantId());
        String reqParamJson = JSON.toJSONString(request);
        excelRequest.setFileName(fileName);
        excelRequest.setReportType(ExcelExportType.ITEM_CARD_EXPORT.getReportType());
        excelRequest.setCenter("item");
        excelRequest.setRequestJson(reqParamJson);
        Response<Boolean> response = itemWriteFacade.excelCreate(excelRequest);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        return Boolean.TRUE;
    }


    @GetMapping(value = "/getUrlByUserInfo")
    @ApiOperation("获取核销入口")
    public ParanaPinNUrlResponse getUrlByUserInfo(ParanaPinNUrlQueryRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
        Response<ParanaPinNUrlResponse> response = paranaItemCardReadFacade.getUrlByUserInfo(request);
        if (!response.isSuccess()) {
            log.info("getUrlByUserInfo failed, params:{}, cause:{}",
                    request, response.getError());
            throw new RestException(response.getError(), response.getCode());
        }
        return response.getResult();
    }

    @ApiOperation("商品卡片批量导入")
    @PostMapping("/import")
    public boolean importSalePrice(@RequestParam("file") MultipartFile file) {
        ParanaItemCardPageRequest request = new ParanaItemCardPageRequest();
        ItemExcelReportCreateRequest excelRequest = new ItemExcelReportCreateRequest();
        excelRequest.setOperatorId(RequestContext.getShopId());
        excelRequest.setUserId(RequestContext.getUserId());
        excelRequest.setFileType("商品卡片批量导入");
        excelRequest.setFileName("商品卡片批量导入" + TimeUtils.format(new Date(), "yyyy-MM-dd HH-mm-ss"));
        excelRequest.setReportType("item_card_batch_import");
        excelRequest.setCenter("item");
        //校验文件
        log.info("itemCard batch import check start >>>>>>> ");
        FastByteArrayOutputStream fos = null;
        InputStream in = null;
        try {
            if(file.isEmpty()){
                throw new RestException("文件不存在！");
            }
            in = file.getInputStream();
            //创建Excel工作薄
            String fileName = file.getOriginalFilename();
            Workbook wb = null;
            String fileType = fileName.substring(fileName.lastIndexOf("."));
            if(".xls".equals(fileType)){
                wb = new HSSFWorkbook(in);  //2003-
            }else if(".xlsx".equals(fileType)){
                wb = new XSSFWorkbook(in);  //2007+
            } else {
                throw new RestException("仅支持[excel]xls和xlsx格式导入！");
            }
            if(null == wb){
                throw new RestException("创建Excel工作薄为空！");
            }
            //得到一个工作表
            Sheet sheet = wb.getSheetAt(0);
            //获得表头
            Row rowHead = sheet.getRow(0);
            //判断表头是否正确
            if(rowHead.getPhysicalNumberOfCells() < 5) {
                throw  new RestException("表头不存在！");
            }
            fos = IoUtil.read(file.getInputStream());
            String fileUrl = Assert.take(itemWriteFacade.uploadFileForOss(excelRequest, fos.toByteArray()));
            if (StringUtils.isEmpty(fileUrl)) {
                throw new RestException("文件上传失败，请重新再试！");
            }
            request.setFileUrl(fileUrl);
            request.setCreatedBy(String.valueOf(RequestContext.getUserId()));
            String reqParamJson = JSON.toJSONString(request);
            excelRequest.setRequestJson(reqParamJson);
            log.info("itemCard batch import >>>>>>> excelRequest {}", JSONUtil.toJsonStr(excelRequest));
            Response<Boolean> response = itemWriteFacade.excelCreate(excelRequest);
            if (!response.isSuccess()) {
                throw new RestException(response.getError());
            }
        }catch (Exception e) {
            log.error("itemCard batch import check error >>>>>>> {}", Throwables.getStackTraceAsString(e));
            throw new RestException("商品卡片批量导入，导入失败！");
        }finally {
            IoUtil.close(fos);
        }
        return Boolean.TRUE;
    }
}
