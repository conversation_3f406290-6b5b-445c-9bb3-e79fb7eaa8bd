package io.terminus.parana.item.admin.search.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.inventory.api.bean.response.info.inventory.InventoryEntityResponseInfo;
import io.terminus.parana.item.admin.search.adaptor.AdminAreaItemComponentAdaptor;
import io.terminus.parana.item.choicelot.api.bean.response.ChoiceLotEntireInfo;
import io.terminus.parana.item.choicelot.api.facade.ChoiceLotLibReadFacade;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.api.bean.request.item.ItemExcelReportCreateRequest;
import io.terminus.parana.item.item.api.bean.request.item.ParanaItemGroupQueryRequest;
import io.terminus.parana.item.item.api.bean.response.item.ParanaItemGroupInfoResponse;
import io.terminus.parana.item.item.api.facade.ItemWriteFacade;
import io.terminus.parana.item.item.api.facade.ParanaItemGroupReadFacade;
import io.terminus.parana.item.partnership.api.bean.request.VendorPartnershipQueryByOperatorIdRequest;
import io.terminus.parana.item.partnership.api.facade.VendorPartnershipReadFacade;
import io.terminus.parana.item.search.docobject.AreaItemDO;
import io.terminus.parana.item.search.dto.FrontItemDTO;
import io.terminus.parana.item.search.dto.SearchedItemWithAggs;
import io.terminus.parana.item.search.facade.AreaItemSearchFacade;
import io.terminus.parana.item.search.facade.PrimarySearchFacade;
import io.terminus.parana.item.search.request.AreaItemSearchRequest;
import io.terminus.parana.item.search.request.AreaPrimarySearchRequest;
import io.terminus.parana.item.shop.api.bean.request.ShopPagingRequest;
import io.terminus.parana.item.shop.api.bean.request.ShopTreeQueryRequest;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.shop.api.bean.response.ShopTreeInfo;
import io.terminus.parana.item.shop.api.facade.ShopReadFacade;
import io.terminus.parana.item.web.search.component.SearchJoinedComponent;
import io.terminus.parana.trade.common.util.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2020-08-04 下午6:18
 */
@Slf4j
@RestController
@Api(value = "区域运营商品搜索")
@RequestMapping(value = "/api/area/admin/item")
@RequiredArgsConstructor
public class AdminAreaItemSearchController {
    private final AreaItemSearchFacade itemSearchFacade;
    private final AdminAreaItemComponentAdaptor areaItemComponentAdaptor;
    private final ShopReadFacade shopReadFacade;
//    private final AreaSkuExtendsReadFacade areaSkuExtendsReadFacade;

    private final ItemWriteFacade itemWriteFacade;

    private final PrimarySearchFacade primarySearchFacade;
    private final SearchJoinedComponent searchJoinedComponent;
    private final ChoiceLotLibReadFacade choiceLotLibReadFacade;
    private final VendorPartnershipReadFacade vendorPartnershipReadFacade;
    private final ParanaItemGroupReadFacade paranaItemGroupReadFacade;
    /**
     * 申通渠道id
     */
    @Value("${st.distributorId:34008}")
    private Long distributorId;

    @ApiOperation("区域商品列表")
    @GetMapping(value = "/search")
    public Paging<AreaItemDO> search(AreaItemSearchRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        request.setOperatorId(RequestContext.getShopId());
//        if (ObjectUtil.isNotEmpty(request.getSourceType())) {
//            if (!request.getSourceType().equals("1")) {
//                request.setSourceType("0_2");
//            }
//        }
        //运营商选品库管理查询未绑定商品时  如果选品库是平台  只查询运营自己品牌商的商品
        if(StringUtils.isNotEmpty(request.getNotChoiceLotLibIds()) && request.getOperatorId() != 1){
            ChoiceLotEntireInfo choiceLotLib = Assert.take(choiceLotLibReadFacade.getById(Long.parseLong(request.getNotChoiceLotLibIds()), request.getOperatorId()));
            if(choiceLotLib != null && choiceLotLib.getType() != null && choiceLotLib.getType() == 1){
                VendorPartnershipQueryByOperatorIdRequest param = new VendorPartnershipQueryByOperatorIdRequest();
                param.setOperatorId(request.getOperatorId());
                Set<Long> vendorIds = Assert.take(vendorPartnershipReadFacade.findVendorIdsByOperatorId(param));
                if(CollectionUtil.isEmpty(vendorIds)){
                    vendorIds.add(-1L);
                }
                request.setVendorIds(Joiner.on("_").join(vendorIds));
                request.setSourceType("0");
            }
        }
        log.info("AreaItemSearchRequest.search.request:{}", request);
        Paging<AreaItemDO> resultsPaging = Assert.take(itemSearchFacade.search(request));
        if(resultsPaging.isEmpty()){
            return Paging.empty();
        }
        // 查询库存
        Paging<AreaItemDO> results = fillInventory(resultsPaging);
//        Paging<AreaItemDO> results = areaItemComponentAdaptor.queryItemInvenTory(resultsPaging, request);
        if (StringUtils.isNotBlank(request.getItemIds())) {
            String[] itemIdArray = request.getItemIds().split("_");
            Map<Long, AreaItemDO> resultMapByItemId = results.getData().stream().collect(Collectors.toMap(AreaItemDO::getItemId, Function.identity()));
            List<AreaItemDO> list = Lists.newArrayList();
            for (String itemId : itemIdArray) {
                if (resultMapByItemId.get(Long.valueOf(itemId.trim())) != null) {
                    list.add(resultMapByItemId.get(Long.valueOf(itemId)));
                }
            }
            return new Paging(results.getTotal(), list, request.getPageSize());
        } else {
            return results;
        }
    }


    @ApiOperation("中台-中央商品池-价格修改记录")
    @GetMapping(value = "/adminSearch")
    public Paging<AreaItemDO> adminSearch(AreaItemSearchRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        //限制商品显示
        List<String> relationshipTypes = Lists.newArrayList();
        ShopPagingRequest shopPagingRequest = new ShopPagingRequest();
        List<ShopInfo> shopInfos = Assert.take(shopReadFacade.list(shopPagingRequest));
        for (ShopInfo operator : shopInfos) {
            relationshipTypes.add(operator.getId() + "-" + "0");
        }
        relationshipTypes.add("1-1");
        request.setRelationshipType(String.join("_", relationshipTypes));

        log.info("AreaItemSearchRequest.adminSearch.request:{}", request);
        Paging<AreaItemDO> resultsPaging = Assert.take(itemSearchFacade.search(request));
        if (resultsPaging.isEmpty()) {
            return Paging.empty();
        }
        // 查询库存
        Paging<AreaItemDO> results = fillInventory(resultsPaging);
//        Paging<AreaItemDO> results = areaItemComponentAdaptor.queryItemInvenTory(resultsPaging, request);
        if (StringUtils.isNotBlank(request.getItemIds())) {
            String[] itemIdArray = request.getItemIds().split("_");
            Map<Long, AreaItemDO> resultMapByItemId = results.getData().stream().collect(Collectors.toMap(AreaItemDO::getItemId, Function.identity()));
            List<AreaItemDO> list = Lists.newArrayList();
            for (String itemId : itemIdArray) {
                if (resultMapByItemId.get(Long.valueOf(itemId.trim())) != null) {
                    list.add(resultMapByItemId.get(Long.valueOf(itemId)));
                }
            }
            return new Paging(results.getTotal(), list, request.getPageSize());
        } else {
            return results;
        }
    }


    @ApiOperation("商品明细导出")
    @GetMapping("/export")
    public Boolean shopExport(AreaItemSearchRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        request.setOperatorId(RequestContext.getShopId());
        log.info("area.item.export, request = {}", request);
        String reqParamJson = JSON.toJSONString(request);
        ItemExcelReportCreateRequest excelRequest = new ItemExcelReportCreateRequest();
        excelRequest.setOperatorId(RequestContext.getShopId());
        excelRequest.setUserId(RequestContext.getUserId());
        excelRequest.setFileType("区运营商品列表导出");
        String fileName = "区运营商品列表导出" + TimeUtils.format(new Date(), "yyyy-MM-dd HH-mm-ss");
        excelRequest.setFileName(fileName);
        excelRequest.setReportType("area_item_export");
        excelRequest.setCenter("item");
        excelRequest.setRequestJson(reqParamJson);
        Response<Boolean> response = itemWriteFacade.excelCreate(excelRequest);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        return Boolean.TRUE;
    }

    @ApiOperation("选品库商品列表")
    @GetMapping(value = "/searchChoiceItem")
    public Paging<AreaItemDO> searchChoiceItem(AreaItemSearchRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        request.setOperatorId(RequestContext.getShopId());
        request.setFunnelChoice(1);
        log.info("AreaItemSearchRequest.searchChoiceItem.request:{}", request);
        Paging<AreaItemDO> results = Assert.take(itemSearchFacade.searchChoiceItem(request));
        if (StringUtils.isNotBlank(request.getItemIds())) {
            String[] itemIdArray = request.getItemIds().split("_");
            Map<Long, AreaItemDO> resultMapByItemId = results.getData().stream().collect(Collectors.toMap(AreaItemDO::getItemId, Function.identity()));
            List<AreaItemDO> list = Lists.newArrayList();
            for (String itemId : itemIdArray) {
                if (resultMapByItemId.get(Long.valueOf(itemId.trim())) != null) {
                    list.add(resultMapByItemId.get(Long.valueOf(itemId)));
                }
            }
            return new Paging(results.getTotal(), list, request.getPageSize());
        } else {
            return results;
        }

    }


    @ApiOperation("商品明细导出-带价格历史")
    @PostMapping("/itemPriceHistoryExport")
    public Boolean itemPriceHistoryExport(@RequestBody AreaItemSearchRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        request.setOperatorId(RequestContext.getShopId());
//        if (ObjectUtil.isNotEmpty(request.getSourceType())) {
//            if (!request.getSourceType().equals("1")) {
//                request.setSourceType("0_2");
//            }
//        }
        //运营商选品库管理查询未绑定商品时  如果选品库是平台  只查询运营自己品牌商的商品
        if (StringUtils.isNotEmpty(request.getNotChoiceLotLibIds()) && request.getOperatorId() != 1) {
            ChoiceLotEntireInfo choiceLotLib = Assert.take(choiceLotLibReadFacade.getById(Long.parseLong(request.getNotChoiceLotLibIds()), request.getOperatorId()));
            if (choiceLotLib != null && choiceLotLib.getType() != null && choiceLotLib.getType() == 1) {
                VendorPartnershipQueryByOperatorIdRequest param = new VendorPartnershipQueryByOperatorIdRequest();
                param.setOperatorId(request.getOperatorId());
                Set<Long> vendorIds = Assert.take(vendorPartnershipReadFacade.findVendorIdsByOperatorId(param));
                if (CollectionUtil.isEmpty(vendorIds)) {
                    vendorIds.add(-1L);
                }
                request.setVendorIds(Joiner.on("_").join(vendorIds));
                request.setSourceType("0");
            }
        }
        String reqParamJson = JSON.toJSONString(request);
        ItemExcelReportCreateRequest excelRequest = new ItemExcelReportCreateRequest();
        excelRequest.setOperatorId(RequestContext.getShopId());
        excelRequest.setUserId(RequestContext.getUserId());
        excelRequest.setFileType("区运营商品价格历史导出");
        String fileName = "区运营商品价格历史导出" + TimeUtils.format(new Date(), "yyyy-MM-dd HH-mm-ss");
        excelRequest.setFileName(fileName);
        excelRequest.setReportType("item_price_history_export");
        excelRequest.setCenter("item");
        excelRequest.setRequestJson(reqParamJson);
        Response<Boolean> response = itemWriteFacade.excelCreate(excelRequest);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        return Boolean.TRUE;
    }

    @ApiOperation("区域商品列表")
    @GetMapping(value = "/hq/search")
    public Paging<AreaItemDO> searchOfHq(AreaItemSearchRequest request) {
        //客户类型
        if (!ObjectUtils.isEmpty(request.getCustomerType())) {
            //0-运营商；1-中台
            Integer customerType = request.getCustomerType();
            if (0 == customerType) {
                request.setNoRelationshipType("1-1_1-2");
            }else {
                request.setRelationshipType("1-1_1-2");
            }
        }
        request.setTenantId(RequestContext.getTenantId());
        Paging<AreaItemDO> paging = Assert.take(itemSearchFacade.search(request));
        if (paging.getTotal() == 0L) {
            return Paging.empty();
        }
        Set<Long> allOperatorIds = paging.getData().stream().map(AreaItemDO::getOperatorId).collect(Collectors.toSet());
        allOperatorIds.addAll(paging.getData().stream().map(AreaItemDO::getSourceOperatorId).collect(Collectors.toSet()));
        allOperatorIds.addAll(paging.getData().stream().map(AreaItemDO::getShopId).collect(Collectors.toSet()));
        Map<Long, ShopInfo> shopInfoMap = areaItemComponentAdaptor.getShopInfoMap(allOperatorIds, request.getTenantId());
        Map<String, Integer> chooseNumMap = Maps.newHashMap();
        if (!ObjectUtils.isEmpty(request.getOperatorId()) && 1L == request.getOperatorId()) {
            //如果是平台商品管理 需要查询选品库数量
            List<Long> itemIds = paging.getData().stream().map(AreaItemDO::getItemId).collect(Collectors.toList());
            chooseNumMap = areaItemComponentAdaptor.getChooseNumMap(1L, itemIds);
        }
        Map<Long, ParanaItemGroupInfoResponse> groupMap = Maps.newHashMap();
        Map<Long, ParanaItemGroupInfoResponse> parentGroupMap = Maps.newHashMap();
        List<Long> groupIds = paging.getData().stream().map(AreaItemDO::getItemGroupId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(groupIds)){
            ParanaItemGroupQueryRequest queryRequest = new ParanaItemGroupQueryRequest();
            queryRequest.setTenantId(request.getTenantId());
            queryRequest.setIds(groupIds);
            List<ParanaItemGroupInfoResponse> groupList = Assert.take(paranaItemGroupReadFacade.list(queryRequest));
            if(CollectionUtil.isNotEmpty(groupList)){
                groupMap = groupList.stream().collect(Collectors.toMap(ParanaItemGroupInfoResponse::getId, v -> v));

                //查询父类
                List<Long> parentIds = groupList.stream().map(ParanaItemGroupInfoResponse::getParentId).distinct().collect(Collectors.toList());
                queryRequest.setIds(parentIds);
                List<ParanaItemGroupInfoResponse> parentList = Assert.take(paranaItemGroupReadFacade.list(queryRequest));
                if(CollectionUtil.isNotEmpty(parentList)) {
                    parentGroupMap = parentList.stream().collect(Collectors.toMap(ParanaItemGroupInfoResponse::getId, v -> v));
                }

            }
        }
        for (AreaItemDO areaItemDO : paging.getData()) {
            if (shopInfoMap.containsKey(areaItemDO.getSourceOperatorId())) {
                ShopInfo shopInfo = shopInfoMap.get(areaItemDO.getSourceOperatorId());
                areaItemDO.setSourceOperatorName(shopInfo.getName());
            }
            if (shopInfoMap.containsKey(areaItemDO.getOperatorId())) {
                ShopInfo shopInfo = shopInfoMap.get(areaItemDO.getOperatorId());
                areaItemDO.setOperatorName(shopInfo.getName());
            }
            if (shopInfoMap.containsKey(areaItemDO.getShopId())) {
                ShopInfo shopInfo = shopInfoMap.get(areaItemDO.getShopId());
                areaItemDO.setShopName(shopInfo.getName());
            }
            if (chooseNumMap.containsKey(areaItemDO.getOperatorId() + "-" + areaItemDO.getItemId())) {
                areaItemDO.setChooseNum(chooseNumMap.get(areaItemDO.getOperatorId() + "-" + areaItemDO.getItemId()));
            }
            if(areaItemDO.getItemGroupId() != null && groupMap.containsKey(areaItemDO.getItemGroupId())){
                ParanaItemGroupInfoResponse group = groupMap.get(areaItemDO.getItemGroupId());
                String groupName = "";
                if(parentGroupMap.containsKey(group.getParentId())){
                    areaItemDO.setItemParentGroupId(group.getParentId());
                    groupName = parentGroupMap.get(group.getParentId()).getGroupName() + ">";
                }
                areaItemDO.setItemGroupName(groupName + group.getGroupName());
            }
        }
        return fillInventory(paging);
    }

    @ApiOperation("中台商品管理-商品管理导出")
    @GetMapping("/admin/exportExcel")
    public Boolean adminExportExcel(AreaItemSearchRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        log.info("admin item area item.export, request = {}", request);
        String reqParamJson = JSON.toJSONString(request);
        ItemExcelReportCreateRequest excelRequest = new ItemExcelReportCreateRequest();
        excelRequest.setUserId(RequestContext.getUserId());
        excelRequest.setFileType("中台商品管理-商品管理导出");
        String fileName = "AdminItemAreaItemExport_" + TimeUtils.format(new Date(), "yyyy-MM-dd_HH-mm-ss");
        excelRequest.setFileName(fileName);
        excelRequest.setReportType(ExcelExportType.CENTER_ITEM_POOL_AREA_ITEM_EXPORT.getReportType());
        excelRequest.setCenter("item");
        excelRequest.setRequestJson(reqParamJson);
        Response<Boolean> response = itemWriteFacade.excelCreate(excelRequest);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        return Boolean.TRUE;
    }

    @ApiOperation("中央商品池-运营商品列表导出")
    @GetMapping("/exportExcel")
    public Boolean exportExcel(AreaItemSearchRequest request) {
        //客户类型
        if (!ObjectUtils.isEmpty(request.getCustomerType())) {
            //0-运营商；1-中台
            Integer customerType = request.getCustomerType();
            if (0 == customerType) {
                request.setNoRelationshipType("1-1_1-2");
            }else {
                request.setRelationshipType("1-1_1-2");
            }
        }
        request.setTenantId(RequestContext.getTenantId());
        log.info("center item pool area item.export, request = {}", request);
        String reqParamJson = JSON.toJSONString(request);
        ItemExcelReportCreateRequest excelRequest = new ItemExcelReportCreateRequest();
        excelRequest.setUserId(RequestContext.getUserId());
        excelRequest.setFileType("中央商品池-运营商品列表导出");
        String fileName = "CenterItemPoolAreaItemExport_" + TimeUtils.format(new Date(), "yyyy-MM-dd_HH-mm-ss");
        excelRequest.setFileName(fileName);
        excelRequest.setReportType(ExcelExportType.CENTER_ITEM_POOL_AREA_ITEM_EXPORT.getReportType());
        excelRequest.setCenter("item");
        excelRequest.setRequestJson(reqParamJson);
        Response<Boolean> response = itemWriteFacade.excelCreate(excelRequest);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        return Boolean.TRUE;
    }

    @ApiOperation("通卡直充商品分组-导出")
    @GetMapping("/exportItemGroupExcel")
    public Boolean exportItemGroupExcel(AreaItemSearchRequest request) {
        //客户类型
        if (!ObjectUtils.isEmpty(request.getCustomerType())) {
            //0-运营商；1-中台
            Integer customerType = request.getCustomerType();
            if (0 == customerType) {
                request.setNoRelationshipType("1-1_1-2");
            }else {
                request.setRelationshipType("1-1_1-2");
            }
        }
        request.setTenantId(RequestContext.getTenantId());
        log.info("center item group area item.export, request = {}", request);
        String reqParamJson = JSON.toJSONString(request);
        ItemExcelReportCreateRequest excelRequest = new ItemExcelReportCreateRequest();
        excelRequest.setUserId(RequestContext.getUserId());
        excelRequest.setFileType("通卡直充商品分组-商品列表导出");
        String fileName = "CenterItemGroupAreaItemExport_" + TimeUtils.format(new Date(), "yyyy-MM-dd_HH-mm-ss");
        excelRequest.setFileName(fileName);
        excelRequest.setReportType(ExcelExportType.CENTER_ITEM_GROUP_AREA_ITEM_EXPORT.getReportType());
        excelRequest.setCenter("item");
        excelRequest.setRequestJson(reqParamJson);
        Response<Boolean> response = itemWriteFacade.excelCreate(excelRequest);
        if (!response.isSuccess()) {
            throw new RestException(response.getError());
        }
        return Boolean.TRUE;
    }


    @ApiOperation("区域商品列表")
    @GetMapping(value = "/hq/search/audit")
    public Paging<AreaItemDO> hqSearchAudit(AreaItemSearchRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        ShopTreeQueryRequest shopTreeRequest = new ShopTreeQueryRequest();
        shopTreeRequest.setId(RequestContext.getShopId());
        Response<List<ShopTreeInfo>> shopTreeResponse = shopReadFacade.queryTreeShop(shopTreeRequest);
        if (shopTreeResponse.isSuccess() && !shopTreeResponse.getResult().isEmpty()) {
            List<ShopTreeInfo> list = shopTreeResponse.getResult();
            StringBuilder operatorIds = new StringBuilder();
            for (ShopTreeInfo info : list) {
                operatorIds.append(info.getId()).append("_");
            }
            request.setOperatorIds(operatorIds.toString().substring(0, operatorIds.length() - 1));
        } else {
            request.setOperatorId(RequestContext.getShopId());
        }

        return Assert.take(itemSearchFacade.search(request));
    }


    @ApiOperation("查询申通渠道购买的选品库下商品未绑定类目的数据")
    @GetMapping("/searchWithSellerAgg")
    public SearchedItemWithAggs<FrontItemDTO> searchWithSellerAgg(AreaPrimarySearchRequest param) {
        param.setTenantId(RequestContext.getTenantId());
        param.setOperatorId(RequestContext.getShopId());
        param.setCateBindingType(0);
        param.setDistributorId(distributorId);
        return Assert.take(primarySearchFacade.searchWithSellerAgg(param));
    }

    private Paging<AreaItemDO> fillInventory(Paging<AreaItemDO> areaItemPaging) {
        Map<String, List<InventoryEntityResponseInfo>> inventoryMap = searchJoinedComponent.getBatchOperatorInventories(areaItemPaging.getData());
        for (AreaItemDO areaItemDO : areaItemPaging.getData()) {
            Integer sourceType = areaItemDO.getSourceType();
            if (!ObjectUtils.isEmpty(sourceType)) {
                if (0 == sourceType) {
                    if (inventoryMap.containsKey(areaItemDO.getOperatorId() + "-" + areaItemDO.getItemId())) {
                        List<InventoryEntityResponseInfo> infos = inventoryMap.get(areaItemDO.getOperatorId() + "-" + areaItemDO.getItemId());
                        areaItemDO.setSellableQuantity(infos.stream().filter(e -> !ObjectUtils.isEmpty(e.getSellableQuantity())).mapToLong(InventoryEntityResponseInfo::getSellableQuantity).sum());
                    }
                }else if (1 == sourceType) {
                    if (inventoryMap.containsKey("1" + "-" + areaItemDO.getItemId())) {
                        List<InventoryEntityResponseInfo> infos = inventoryMap.get("1" + "-" + areaItemDO.getItemId());
                        areaItemDO.setSellableQuantity(infos.stream().filter(e -> !ObjectUtils.isEmpty(e.getSellableQuantity())).mapToLong(InventoryEntityResponseInfo::getSellableQuantity).sum());
                    }
                }else if (2 == sourceType) {
                    if (inventoryMap.containsKey(areaItemDO.getSourceOperatorId() + "-" + areaItemDO.getItemId())) {
                        List<InventoryEntityResponseInfo> infos = inventoryMap.get(areaItemDO.getSourceOperatorId() + "-" + areaItemDO.getItemId());
                        areaItemDO.setSellableQuantity(infos.stream().filter(e -> !ObjectUtils.isEmpty(e.getSellableQuantity())).mapToLong(InventoryEntityResponseInfo::getSellableQuantity).sum());
                    }
                }
            }
        }
        return areaItemPaging;
    }

    public static void main(String[] args) {
        String s = HttpUtil.createGet("https://api.it120.cc/common/region/v2/child?pid=440304002000").execute().body();
        log.info(s);
    }
}
