package io.terminus.parana.item.admin.shop.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eascs.user.userauthentication.api.facade.UserAuthenticationWriteFacade;
import com.eascs.user.usersettledvendor.api.facade.UserSettledVendorFacade;
import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.draco.web.autoconfig.context.TenantContext;
import io.terminus.parana.common.lang.util.JsonUtils;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.item.admin.shop.bean.query.ShopQuery;
import io.terminus.parana.item.admin.shop.bean.vo.AreaOperatorSearchVO;
import io.terminus.parana.item.admin.shop.bean.vo.ShopVO;
import io.terminus.parana.item.admin.shop.bean.vo.VendorSearchVO;
import io.terminus.parana.item.admin.shop.converter.ShopAdminConverter;
import io.terminus.parana.item.common.enums.ShopStatusEnum;
import io.terminus.parana.item.common.excel.OperatorExcelTemplate;
import io.terminus.parana.item.common.log.ItemOperationLogComponent;
import io.terminus.parana.item.common.util.AdminSellerExcelTamplate;
import io.terminus.parana.item.common.util.AdminShopExcelTemplate;
import io.terminus.parana.item.common.util.AdminVendorExcelTemplate;
import io.terminus.parana.item.common.util.excel.expor.ItemExcelExportHelper;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.api.facade.ItemWriteFacade;
import io.terminus.parana.item.partnership.api.bean.request.PartnershipVendorListPagingRequest;
import io.terminus.parana.item.partnership.api.bean.request.VendorPartnershipUpdateRequest;
import io.terminus.parana.item.partnership.api.bean.request.param.VendorPartnershipParam;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShipInfo;
import io.terminus.parana.item.partnership.api.facade.VendorPartnershipWriteFacade;
import io.terminus.parana.item.relation.api.bean.request.QueryShopRelationByIdRequest;
import io.terminus.parana.item.relation.api.bean.request.QueryShopRelationRequest;
import io.terminus.parana.item.relation.api.bean.response.ShopRelationInfo;
import io.terminus.parana.item.relation.api.facade.ShopRelationReadFacade;
import io.terminus.parana.item.search.request.ShopFreezeRequest;
import io.terminus.parana.item.search.request.SupplierContractRequest;
import io.terminus.parana.item.shop.api.bean.request.*;
import io.terminus.parana.item.shop.api.bean.request.param.ShopParam;
import io.terminus.parana.item.shop.api.bean.response.*;
import io.terminus.parana.item.shop.api.facade.*;
import io.terminus.parana.item.shop.enums.ShopBusinessType;
import io.terminus.parana.item.shop.enums.ShopType;
import io.terminus.parana.item.shop.enums.SpecialOperatorType;
import io.terminus.parana.user.api.facade.AddressReadFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api("运营店铺接口")
@RequiredArgsConstructor
@RequestMapping("/api/admin/shop")
public class AdminShops {

    @Value("${fixed.sham.operatorId}")
    private Long shamOperatorId;

    private final ItemWriteFacade itemWriteFacade;
    private final ShopWriteFacade shopWriteFacade;
    private final ShopReadFacade shopReadFacade;
    private final ShopRelationReadFacade shopRelationReadFacade;
    private final ShopAdminConverter shopAdminConverter;
    private final SupplierInfoReadFacade supplierInfoReadFacade;
    private final SupplierInfoLogReadFacade supplierInfoLogReadFacade;
    private final SupplierInfoWriteFacade supplierInfoWriteFacade;
    private final ItemOperationLogComponent operationLogComponent;
    private final VendorPartnershipWriteFacade vendorPartnershipWriteFacade;
    private final AddressReadFacade addressReadFacade;
    @Autowired
    private final ItemExcelExportHelper itemExcelExportHelper;
    private final UserAuthenticationWriteFacade userAuthenticationWriteFacade;

    private final UserSettledVendorFacade userSettledVendorFacade;


    @GetMapping(value = "/vendorPaging", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "供应商列表")
    public Paging<ShopVO> vendorPaging(@RequestParam(required = false) Long id,
                                       @RequestParam(required = false) String name,
                                       @RequestParam(required = false) Integer status,
                                       @RequestParam(required = false) String regionalOperationName,
                                       @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                       @RequestParam(required = false, defaultValue = "20") Integer pageSize) {

        return this.paging(id,name,ShopType.VENDOR.getShopTypeByValue(),status,pageNo,pageSize,regionalOperationName);
    }

    @GetMapping(value = "/zqVendorPaging", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "政企供应商列表")
    public Paging<ShopVO> zqVendorPaging(@RequestParam(required = false) Long id,
                                         @RequestParam(required = false) String name,
                                         @RequestParam(required = false) Integer status,
                                         @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                         @RequestParam(required = false, defaultValue = "20") Integer pageSize) {

        ShopPagingRequest req = new ShopPagingRequest();
        req.setTenantId(RequestContext.getTenantId());
        if (id != null) {
            req.setIdSet(Sets.newHashSet(id));
        }
        req.setName(name);
        req.setType(ShopType.VENDOR.getShopTypeByValue());
        req.setStatus(status);
        req.setIsZq("Y");
        req.setPageNo(pageNo);
        req.setPageSize(pageSize);
        return shopAdminConverter.info2vo(Assert.take(shopReadFacade.page(req)));
    }


    @GetMapping(value = "/areaOperatorPaging", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "区域运营列表")
    public Paging<ShopVO> areaOperatorPaging(@RequestParam(required = false) Long id,
                                       @RequestParam(required = false) String name,
                                       @RequestParam(required = false) Integer status,
                                       @RequestParam(required = false) String regionalOperationName,
                                       @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                       @RequestParam(required = false, defaultValue = "20") Integer pageSize) {

        return this.paging(id,name,ShopType.AREA_OPERATOR.getShopTypeByValue(),status,pageNo,pageSize,regionalOperationName);
    }

    @PostMapping(value = "/areaOperatorPaging", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "区域运营列表")
    public Paging<ShopVO> areaOperatorPaging(@RequestBody ShopPagingRequest shopPagingRequest) {
        shopPagingRequest.setType(ShopType.AREA_OPERATOR.getShopTypeByValue());
        return shopAdminConverter.info2vo(Assert.take(shopReadFacade.page(shopPagingRequest)));
    }
    @PostMapping(value = "/updateFlowRatio", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "修改运营商流水比例")
    public Boolean updateFlowRatio(@RequestBody UpdateFlowRatioRequest request) {
        request.setTenantId(RequestContext.getTenantId());
        return Assert.take(shopWriteFacade.updateFlowRatio(request));
    }

    @GetMapping(value = "/allOperatorNoAdmin", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "区域运营列表(不包含平台)")
    public Paging<ShopVO> allOperatorNoAdmin(ShopPagingRequest request) {
        request.setExcludeIds(Collections.singletonList(1L));
        request.setType(ShopType.AREA_OPERATOR.getShopTypeByValue());
        request.setStatus(1);
        return shopAdminConverter.info2vo(Assert.take(shopReadFacade.page(request)));
    }


    @GetMapping(value = "/paging", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "店铺列表")
    public Paging<ShopVO> paging(@RequestParam(required = false) Long id,
                                 @RequestParam(required = false) String name,
                                 @RequestParam(required = false) Integer type,
                                 @RequestParam(required = false) Integer status,
                                 @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                 @RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                 @RequestParam(required = false) String regionalOperationName) {
        ShopPagingRequest req = new ShopPagingRequest();
        req.setTenantId(RequestContext.getTenantId());
        req.setOperationId(RequestContext.getShopId());
        if (id != null) {
            req.setIdSet(Sets.newHashSet(id));
        }
        req.setName(name);
        req.setType(type);
        req.setStatus(status);
        req.setPageNo(pageNo);
        req.setPageSize(pageSize);
        req.setRegionalOperationName(regionalOperationName);
        return shopAdminConverter.info2vo(Assert.take(shopReadFacade.page(req)));
    }


    @ApiOperation("运营商数据导出")
    @GetMapping("/exportOperatorTemplate")
    public void exportOperatorTemplate(HttpServletResponse response, ShopPagingRequest request) {

        request.setTenantId(RequestContext.getTenantId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String fileName = "operator_template.xlsx";
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-disposition", "attachment;charset=utf-8;filename=" + fileName);

        List<ShopInfo> dataList = new ArrayList<>();
        int page = 1;
        int pageSize = 100;
        while (true) {
            request.setPageNo(page);
            request.setPageSize(pageSize);
            request.setType(ShopType.AREA_OPERATOR.getShopTypeByValue());
            request.setOffset(null);
            log.info("exportOperatorTemplate.req::{}", JSONUtil.toJsonStr(request));
            Response<Paging<ShopInfo>> resp = shopReadFacade.page(request);
            if (!resp.isSuccess()) {
                break;
            }
            log.info("运营商数据导出总数：{}", resp.getResult().getTotal());
            if (CollUtil.isNotEmpty(resp.getResult().getData())) {
                dataList.addAll(resp.getResult().getData());
                page++;
            } else {
                break;
            }
        }


        List<OperatorExcelTemplate> dataExcelList = new ArrayList<>();
        for (ShopInfo data : dataList) {
            OperatorExcelTemplate operatorExcelTemplate = new OperatorExcelTemplate();
            operatorExcelTemplate.setId(data.getId());
            operatorExcelTemplate.setShopNames(data.getShopNames()==null?" ":data.getShopNames());
            operatorExcelTemplate.setName(data.getName()==null?" ":data.getName());
            SupplierInfoInfoResponse supplierInfoInfo = data.getSupplierInfoInfoResponse();
            if (ObjectUtil.isNotNull(supplierInfoInfo)) {
                if (supplierInfoInfo.getBillingCycle() != null) {
                    if (supplierInfoInfo.getBillingCycle() == 1) {
                        operatorExcelTemplate.setBillingCycle("周结");
                    } else if (supplierInfoInfo.getBillingCycle() == 2) {
                        operatorExcelTemplate.setBillingCycle("半月结");
                    } else {
                        operatorExcelTemplate.setBillingCycle("月结");
                    }
                }
                operatorExcelTemplate.setApplyName(supplierInfoInfo.getApplyName());
                operatorExcelTemplate.setApplyPhone(supplierInfoInfo.getApplyPhone());
            }
            operatorExcelTemplate.setEnterpriseBusinessAddress(" ");
            if (ObjectUtil.isNotNull(data.getSupplierInfoInfoResponse())) {
                String extraJson = Optional.ofNullable(data.getSupplierInfoInfoResponse().getExtraJson()).orElse("{}");
                Map<String, Object> params = JSONUtil.parseObj(extraJson);
                operatorExcelTemplate.setEnterpriseBusinessAddress(params.getOrDefault("enterpriseBusinessAddress", " ").toString());
            }

            if (data.getCreatedAt()!=null){
                operatorExcelTemplate.setCreatedAt(sdf.format(data.getCreatedAt()));
            }

            if (data.getStatus()!=null){
                operatorExcelTemplate.setStatus(data.getStatus() ==1? "正常":"冻结");
            }
            if (data.getUpdatedAt()!=null){
                operatorExcelTemplate.setUpdatedAt(sdf.format(data.getUpdatedAt()));
            }
            dataExcelList.add(operatorExcelTemplate);
        }
        try {
            if (dataExcelList.isEmpty()) {
                itemExcelExportHelper.downloadTemplate(OperatorExcelTemplate.class, response.getOutputStream());
            } else {
                itemExcelExportHelper.downloadTemplate(dataExcelList, OperatorExcelTemplate.class, response.getOutputStream());
            }
        } catch (Exception e) {
            log.error("error: {}", Throwables.getStackTraceAsString(e));
            log.error(e.getMessage(), e);
            throw new RestException(e.getMessage());
        }
    }

    @ApiOperation(value = "根据id获取店铺")
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ShopInfo findShopById(@PathVariable("id") Long shopId) {
        ShopSingleQueryByIdRequest request = new ShopSingleQueryByIdRequest();
        request.setId(shopId);
        request.setTenantId(RequestContext.getTenantId());
        return Assert.take(shopReadFacade.querySingleById(request));
    }
    @Deprecated
    @ApiOperation(value = "创建店铺,使用至5.5.4.0版本")
    @PostMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Long createShop(@RequestBody ShopQuery shopQuery) {
        ShopParam shopParam = shopAdminConverter.query2param(shopQuery);
        shopParam.setTenantId(RequestContext.getTenantId());

        ShopCreateRequest request = new ShopCreateRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setShopParam(shopParam);
        request.setUpdatedBy(String.valueOf(RequestContext.getUserId()));

        return Assert.take(shopWriteFacade.create(request));
    }

    @Deprecated
    @ApiOperation(value = "更新店铺,使用至5.5.4.0版本")
    @PutMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean updateShop(@RequestBody ShopQuery shopQuery) {
        ShopParam shopParam = shopAdminConverter.query2param(shopQuery);
        shopParam.setTenantId(RequestContext.getTenantId());

        ShopUpdateRequest request = new ShopUpdateRequest();
        request.setShopParam(shopParam);
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(String.valueOf(RequestContext.getUserId()));

        return Assert.take(shopWriteFacade.update(request));
    }


    @ApiOperation(value = "创建店铺")
    @PostMapping(value = "new", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Long createShopWithParam(@RequestBody ShopParam param) {

        param.setTenantId(RequestContext.getTenantId());
        param.setCreatedBy(String.valueOf(RequestContext.getUserId()));
        ShopCreateRequest request = new ShopCreateRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setShopParam(param);
        request.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
        request.setSupplierInfoCreateParam(param.getSupplierInfoCreateParam());

        return Assert.take(shopWriteFacade.create(request));
    }

    @ApiOperation(value = "创建供应商")
    @PostMapping(value = "vendorNew", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Long createVendorWithParam(@RequestBody ShopParam param) {

        param.setType(ShopType.VENDOR.getShopTypeByValue());
        return  this.createShopWithParam(param);
    }

    @ApiOperation(value = "创建区域运营商")
    @PostMapping(value = "areaOperatorNew", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Long createAreaOperatorWithParam(@RequestBody ShopParam param) {

        param.setType(ShopType.AREA_OPERATOR.getShopTypeByValue());
        param.setCreatedBy(String.valueOf(RequestContext.getUserId()));
        param.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
        param.setTenantId(RequestContext.getTenantId());
        param.setBusinessType(1);
        param.setIndustryId(1L);
        param.setStatus(1);
        if (ObjectUtil.isNotNull(param.getSupplierInfoCreateParam())){
            param.getSupplierInfoCreateParam().setCreateUserId(RequestContext.getUserId());
            param.getSupplierInfoCreateParam().setUpdateUserId(RequestContext.getUserId());
            param.getSupplierInfoCreateParam().setTenantId(RequestContext.getTenantId().longValue());
            param.getSupplierInfoCreateParam().setAuditStartTime(new Date());
            param.getSupplierInfoCreateParam().setAuditStatus(2);
        }
        return  this.createShopWithParam(param);
    }

    @ApiOperation(value = "更新店铺")
    @PutMapping(value = "new", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean updateShopWithParam(@RequestBody ShopParam param) {
        param.setTenantId(RequestContext.getTenantId());
        param.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
//        param.setBusinessType(1);
//        param.setIndustryId(1L);
        if (ObjectUtil.isNotNull(param.getSupplierInfoCreateParam())){
            param.getSupplierInfoCreateParam().setUpdateUserId(RequestContext.getUserId());
            param.getSupplierInfoCreateParam().setTenantId(RequestContext.getTenantId().longValue());
        }
        if(StringUtils.isBlank(param.getRegionalOperationName())){
            param.setRegionalOperationName(param.getShopNames());
        }

        ShopUpdateRequest request = new ShopUpdateRequest();
        request.setShopParam(param);
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
        return Assert.take(shopWriteFacade.update(request));
    }


    @ApiOperation(value = "更新供应商")
    @PutMapping(value = "vendorNew", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean updateVendorWithParam(@RequestBody ShopParam param) {

        param.setType(ShopType.VENDOR.getShopTypeByValue());
        return this.updateShopWithParam(param);
    }

    @ApiOperation(value = "更新区域运营商")
    @PutMapping(value = "areaOperatorNew", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean updateAreaOperatorWithParam(@RequestBody ShopParam param) {

        param.setType(ShopType.AREA_OPERATOR.getShopTypeByValue());
        return this.updateShopWithParam(param);
    }


    @ApiOperation(value = "冻结店铺")
    @PutMapping(value = "/frozen/{id}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void frozenSeller(@PathVariable("id") Long shopId) {
        ShopFrozenRequest request = new ShopFrozenRequest();
        request.setIdSet(Sets.newHashSet(shopId));
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(String.valueOf(RequestContext.getUserId()));

        Assert.take(shopWriteFacade.frozen(request));
    }

    @ApiOperation(value = "解冻店铺")
    @PutMapping(value = "/unfrozen/{id}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void unfrozenSeller(@PathVariable("id") Long shopId) {
        ShopUnfrozenRequest request = new ShopUnfrozenRequest();
        request.setIdSet(Sets.newHashSet(shopId));
        request.setTenantId(RequestContext.getTenantId());
        request.setUpdatedBy(String.valueOf(RequestContext.getUserId()));

        Assert.take(shopWriteFacade.unfrozen(request));
    }

    @ApiOperation(value = "根据名字模糊查询店铺信息")
    @GetMapping(value = "/fuzz", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<ShopInfo> findShopByFuzzName(@ApiParam("店铺名称") @RequestParam(required = false) String name,
                                             @ApiParam("店铺类型") @RequestParam Integer shopType,
                                             @ApiParam("查询数量") @RequestParam(required = false, defaultValue = "10") Integer size) {
        FindShopByFuzzNameRequest request = new FindShopByFuzzNameRequest();
        request.setName(name);
        request.setType(shopType);
        request.setSize(size);
        request.setTenantId(RequestContext.getTenantId());
        request.setId(RequestContext.getShopId());
        log.info("fuzz.findShopByFuzzName.request:{}",request);
        return Assert.take(shopReadFacade.findShopByFuzzName(request));
    }

    @ApiOperation(value = "根据供应商名字模糊查询供应商信息")
    @GetMapping(value = "/vendorFuzz", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<ShopInfo> findVendorByFuzzName(@ApiParam("供应商名字") @RequestParam(required = false) String name,
                                             @ApiParam("查询数量") @RequestParam(required = false, defaultValue = "10") Integer size) {

        return this.findShopByFuzzName(name,ShopType.VENDOR.getShopTypeByValue(),size);
    }

    @ApiOperation(value = "根据区域运营名字模糊查询区域运营信息")
    @GetMapping(value = "/areaOperatorFuzz", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<ShopInfo> findAreaOperatorByFuzzName(@ApiParam("区域运营名称") @RequestParam(required = false) String name,
                                               @ApiParam("查询数量") @RequestParam(required = false, defaultValue = "10") Integer size) {

        return this.findShopByFuzzName(name,ShopType.AREA_OPERATOR.getShopTypeByValue(),size);
    }


    @ApiOperation(value = "根据关联关系源店铺id获取关联关系列表")
    @GetMapping(value = "/relation/source", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<ShopRelationInfo> findShopRelationBySource(@ApiParam("源店铺id") @RequestParam Long sourceShopId) {
        QueryShopRelationByIdRequest request = new QueryShopRelationByIdRequest();
        request.setShopId(sourceShopId);
        request.setTenantId(RequestContext.getTenantId());

        return Assert.take(shopRelationReadFacade.queryBySource(request));
    }

    @ApiOperation(value = "根据关联关系目标店铺id获取关联关系列表")
    @GetMapping(value = "/relation/target", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<ShopRelationInfo> findShopRelationByTarget(@ApiParam("目标店铺id") @RequestParam Long targetShopId) {
        QueryShopRelationByIdRequest request = new QueryShopRelationByIdRequest();
        request.setShopId(targetShopId);
        request.setTenantId(RequestContext.getTenantId());

        return Assert.take(shopRelationReadFacade.queryByTarget(request));
    }

    @ApiOperation(value = "根据关联关系源店铺id和目标店铺id获取关联关系")
    @GetMapping(value = "/relation", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ShopRelationInfo findShopRelation(@ApiParam("源店铺id") @RequestParam Long sourceShopId,
                                             @ApiParam("目标店铺id") @RequestParam Long targetShopId) {
        QueryShopRelationRequest request = new QueryShopRelationRequest();

        request.setSourceShopId(sourceShopId);
        request.setTargetShopId(targetShopId);
        request.setTenantId(RequestContext.getTenantId());

        return Assert.take(shopRelationReadFacade.queryBySourceAndTarget(request));
    }

    @ApiOperation(value = "根据店铺类型校验店铺名是否可用")
    @GetMapping(value = "/check/name", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean checkShopNameAvailable(@RequestParam(required = false) Long id,
                                          @RequestParam String name,
                                          @RequestParam Integer type) {
        ShopCheckNameRequest request = new ShopCheckNameRequest();
        request.setId(id);
        request.setName(name);
        request.setType(type);
        request.setTenantId(RequestContext.getTenantId());

        return Assert.take(shopReadFacade.checkNameAvailable(request));
    }

    @GetMapping("/auth-category/{shopId}")
    public List<Long> queryAuthCategory(@PathVariable Long shopId) {
        ShopQueryAuthCategoryRequest request = new ShopQueryAuthCategoryRequest();
        request.setShopId(shopId);
        return Assert.take(shopReadFacade.queryAuthCategory(request));
    }

    @PostMapping("/auth-category")
    public Boolean flushAuthCategory(@RequestBody ShopFlushAuthCategoryRequest request) {
        return Assert.take(shopWriteFacade.flushAuthCategory(request));
    }

    @GetMapping(value = "/vendorList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "根据名称联想查询与区域运营有合作关系的供应商列表（不分页 名称联想）")
    public List<VendorSearchVO> vendorList(@RequestParam(required = false) Long id,
                                           @RequestParam(required = true) String name,
                                           @RequestParam(required = false,defaultValue = "10") Integer limit) {

        FindShopByFuzzNameFilterByIdsRequest request = new FindShopByFuzzNameFilterByIdsRequest();
        request.setName(name);
        request.setType(ShopType.VENDOR.getShopTypeByValue());
        request.setSize(limit);
        request.setId(MoreObjects.firstNonNull(id, RequestContext.getShopId()));
        request.setTenantId(RequestContext.getTenantId());
        return shopAdminConverter.info2VSvo(Assert.take(shopReadFacade.findShopByFuzzNameAllFilterByIds(request)));
    }

    @GetMapping(value = "/shopVendorList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "根据名称联想查询与区域运营有合作关系的供应商列表（不分页 名称联想）")
    public List<VendorSearchVO> shopVendorList(@RequestParam(required = false) Long id,
                                           @RequestParam(required = true) String name,
                                           @RequestParam(required = false,defaultValue = "10") Integer limit) {

        FindShopByFuzzNameFilterByIdsRequest request = new FindShopByFuzzNameFilterByIdsRequest();
        request.setName(name);
        request.setType(ShopType.VENDOR.getShopTypeByValue());
        request.setSize(limit);
        request.setId(MoreObjects.firstNonNull(id, RequestContext.getShopId()));
        request.setTenantId(RequestContext.getTenantId());
        return shopAdminConverter.info2VSvo(Assert.take(shopReadFacade.findShopByShopNameFilterByIds(request)));
    }

    @GetMapping(value = "/areaOperatorList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "有合作关系的区域运营列表（不分页 名称联想）")
    public List<AreaOperatorSearchVO> areaOperatorList(@RequestParam(required = false) Long vendorId,
                                                       @RequestParam(required = true) String name,
                                                       @RequestParam(required = false, defaultValue = "10") Integer size) {

        FindShopByFuzzNameFilterByIdsRequest request = new FindShopByFuzzNameFilterByIdsRequest();
        request.setName(name);
        request.setType(ShopType.AREA_OPERATOR.getShopTypeByValue());
        request.setSize(size);
        request.setId(MoreObjects.firstNonNull(vendorId, RequestContext.getShopId()));
        request.setTenantId(RequestContext.getTenantId());
        return shopAdminConverter.info2opVo(Assert.take(shopReadFacade.findShopByFuzzNameFilterByIds(request)));
    }


    @ApiOperation(value = "查询区域运营树形列表")
    @GetMapping(value = "/areaOperatorTreeList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String findAreaOperatorTreeList(@ApiParam("区域运营ID") @RequestParam(required = false) Long id) {
        ShopTreeQueryRequest request = new ShopTreeQueryRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setId(id);
        List<ShopTreeInfo> shopInfoList = Assert.take(shopReadFacade.queryTreeShop(request));
        if (CollectionUtils.isEmpty(shopInfoList)) {
            shopInfoList= Lists.newArrayList();
        }
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("data",shopInfoList);
        map.put("success",true);
        return JsonUtils.toJson(map);
    }

    @ApiOperation(value = "特殊区域运营商类型")
    @GetMapping(value = "/getSpecialOperatorType", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<SpecialOperatorTypeInfo> getSpecialOperatorType() {
        List<SpecialOperatorTypeInfo> infos = Lists.newArrayList();
        for (SpecialOperatorType specialOperatorType : SpecialOperatorType.values()) {
            SpecialOperatorTypeInfo info = new SpecialOperatorTypeInfo();
            info.setValue(specialOperatorType.getId());
            info.setName(specialOperatorType.getName());
            infos.add(info);
        }
        return infos;
    }

    @ApiOperation(value = "是否政企品牌配送供应商")
    @GetMapping(value = "/vendorListOfZq")
    public Response<List<ShopInfo>> vendorlistByZqAndIsBrandAndIsDeliery(@ApiParam("是否配送供应商") @RequestParam String isDeliery) {
        String isBrand = null;
        String isZq = null;
        if (StringUtils.isBlank(isDeliery)){
            isDeliery = "Y";
        }
        if(StringUtils.isBlank(isZq)){
            isZq = "Y";
        }
        return shopReadFacade.listByZqAndIsBrandAndIsDeliery(isBrand,isDeliery,isZq,RequestContext.getTenantId());
    }

    @ApiOperation("数据导出")
    @GetMapping("/excelTemplate")
    public void exportVendorTemplate(HttpServletResponse response,
                                     @RequestParam(required = false) Long id,
                                     @RequestParam(required = false) String name,
                                     @RequestParam(required = false) Integer status,
                                     @RequestParam(required = false) String regionalOperationName) {
        PartnershipVendorListPagingRequest request = new PartnershipVendorListPagingRequest();
        request.setStatus(status);
        request.setOperatorId(RequestContext.getShopId());
        if (id != null) {
            request.setIdSet(Sets.newHashSet(id));
        }
        request.setName(name);
        request.setPid(RequestContext.getShopId());
        request.setType(ShopType.AREA_OPERATOR.getShopTypeByValue());
        request.setRegionalOperationName(regionalOperationName);
        request.setTenantId(RequestContext.getTenantId());
        List<VendorWithPartnerShipInfo> data = shopReadFacade.getList(request);
        String fileName = "admin_shops_template.xlsx";
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-disposition", "attachment;charset=utf-8;filename=" + fileName);
        List<AdminShopExcelTemplate> datas = new ArrayList<>();
        for (VendorWithPartnerShipInfo datum : data) {
            AdminShopExcelTemplate adminShopExcelTemplate = new AdminShopExcelTemplate();
            adminShopExcelTemplate.setId(datum.getId());
            adminShopExcelTemplate.setAddress(datum.getAddress()==null?"":datum.getAddress());
            adminShopExcelTemplate.setBankAccount(datum.getBankAccount());
            adminShopExcelTemplate.setBankAccountName(datum.getBankAccountName());
            adminShopExcelTemplate.setBankName(datum.getBankName());
            adminShopExcelTemplate.setBankSubBranchName(datum.getBankSubBranchName());
            adminShopExcelTemplate.setContactMobile(datum.getContactMobile());
            adminShopExcelTemplate.setContactName(datum.getContactName());
            adminShopExcelTemplate.setName(datum.getName());
            adminShopExcelTemplate.setStatus(ShopStatusEnum.getValues(datum.getStatus()));
            adminShopExcelTemplate.setRegionalOperationName(datum.getRegionalOperationName() == null?"":datum.getRegionalOperationName());
            adminShopExcelTemplate.setTin(datum.getTin());
            adminShopExcelTemplate.setTypeName(ShopBusinessType.getShopTypeByValue(Integer.valueOf(datum.getBusinessType())).getDesc());
            adminShopExcelTemplate.setContractPeriodAt(datum.getContractPeriodAt());
            datas.add(adminShopExcelTemplate);
        }
        try {
            if (datas.isEmpty()) {
                itemExcelExportHelper.downloadTemplate(AdminShopExcelTemplate.class, response.getOutputStream());
            } else {
                itemExcelExportHelper.downloadTemplate(datas, AdminShopExcelTemplate.class, response.getOutputStream());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RestException(e.getMessage());
        }
    }

    @ApiOperation("数据导出")
    @GetMapping(value = "/adminVendorTemplate")
    public void exportAdminVendorTemplate(HttpServletResponse response,@RequestBody SupplierInfoQueryListRequest request){
        Set<Long> set = Sets.newHashSet();
        set.addAll(request.getIds());
        ShopQueryByIdRequest shopQueryByIdRequest = new ShopQueryByIdRequest();
        shopQueryByIdRequest.setIdSet(set);
        Response<List<ShopInfo>> listResponse = shopReadFacade.queryById(shopQueryByIdRequest);
        Map<Long,ShopInfo> map = Maps.newHashMap();
        if (!org.springframework.util.CollectionUtils.isEmpty(listResponse.getResult())) {
            map = listResponse.getResult().stream().collect(Collectors.toMap(ShopInfo::getId,it -> it));
        }
        ShopPagingRequest shopPagingRequest = new ShopPagingRequest();
        Response<List<ShopInfo>> shopResponse =shopReadFacade.list(shopPagingRequest);
        Map<Long,String> maps = Maps.newHashMap();
        if (!org.springframework.util.CollectionUtils.isEmpty(shopResponse.getResult())) {
            maps = shopResponse.getResult().stream().collect(Collectors.toMap(ShopInfo::getId,ShopInfo::getName));
        }
        Response<List<SupplierInfoInfoResponse>> listResponse1 = supplierInfoReadFacade.list(request);
        String fileName = "admin_vendor_template.xlsx";
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-disposition", "attachment;charset=utf-8;filename=" + fileName);
        List<AdminVendorExcelTemplate> datas = new ArrayList<>();
        for (SupplierInfoInfoResponse supplierInfoInfoResponse : listResponse1.getResult()){
            String billCycle = "";
            String taxpayerType ="";
            String status = "";
            if (supplierInfoInfoResponse.getBillingCycle().equals(1)){
                billCycle = "周结";
            } else if (supplierInfoInfoResponse.getBillingCycle().equals(2)){
                billCycle = "半月结";
            } else {
                billCycle = "月结";
            }
            if (supplierInfoInfoResponse.getTaxpayerType().equals(1)){
                taxpayerType = "一般纳税人";
            } else {
                taxpayerType = "小规模纳税人";
            }
            if (map.get(supplierInfoInfoResponse.getId()).equals(1)){
                status = "正常";
            } else if (map.get(supplierInfoInfoResponse.getId()).equals(-1)){
                status = "关闭";
            } else {
                status = "冻结";
            }
            AdminVendorExcelTemplate adminVendorExcelTemplate = new AdminVendorExcelTemplate();
            adminVendorExcelTemplate.setId(supplierInfoInfoResponse.getId());
            adminVendorExcelTemplate.setShopName(supplierInfoInfoResponse.getEnterpriseNameAbbreviation());
            adminVendorExcelTemplate.setName(supplierInfoInfoResponse.getEnterpriseName());
            adminVendorExcelTemplate.setBillingCycle(billCycle);
            adminVendorExcelTemplate.setOperatorName(maps.get(supplierInfoInfoResponse.getOperatorId()));
            adminVendorExcelTemplate.setApplyName(supplierInfoInfoResponse.getApplyName());
            adminVendorExcelTemplate.setApplyPhone(supplierInfoInfoResponse.getApplyPhone());
            adminVendorExcelTemplate.setEnterpriseBusinessAddress(supplierInfoInfoResponse.getEnterpriseBusinessAddress());
            adminVendorExcelTemplate.setLegalPersonName(supplierInfoInfoResponse.getLegalPersonName());
            adminVendorExcelTemplate.setEnterpriseType(supplierInfoInfoResponse.getEnterpriseType());
            adminVendorExcelTemplate.setRegistrationCapital(supplierInfoInfoResponse.getRegistrationCapital());
            adminVendorExcelTemplate.setEnterpriseRegistrationTime(supplierInfoInfoResponse.getEnterpriseRegistrationTime().toString());
            adminVendorExcelTemplate.setTaxpayerType(taxpayerType);
            adminVendorExcelTemplate.setSettleInTime(supplierInfoInfoResponse.getUpdateTime().toString());
            adminVendorExcelTemplate.setStatus(status);
            datas.add(adminVendorExcelTemplate);
        }
        try {
            if (datas.isEmpty()) {
                itemExcelExportHelper.downloadTemplate(AdminVendorExcelTemplate.class, response.getOutputStream());
            } else {
                itemExcelExportHelper.downloadTemplate(datas, AdminVendorExcelTemplate.class, response.getOutputStream());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RestException(e.getMessage());
        }
    }

    @ApiOperation("数据导出")
    @GetMapping(value = "/adminSellerTemplate")
    public void exportAdminSellerTemplate(HttpServletResponse response,@RequestBody SupplierInfoQueryListRequest request){
        Set<Long> set = Sets.newHashSet();
        set.addAll(request.getIds());
        ShopQueryByIdRequest shopQueryByIdRequest = new ShopQueryByIdRequest();
        shopQueryByIdRequest.setIdSet(set);
        Response<List<ShopInfo>> listResponse = shopReadFacade.queryById(shopQueryByIdRequest);
        Map<Long,ShopInfo> map = Maps.newHashMap();
        if (!org.springframework.util.CollectionUtils.isEmpty(listResponse.getResult())) {
            map = listResponse.getResult().stream().collect(Collectors.toMap(ShopInfo::getId,it -> it));
        }
        Response<List<SupplierInfoInfoResponse>> listResponse1 = supplierInfoReadFacade.list(request);
        String fileName = "admin_seller_template.xlsx";
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-disposition", "attachment;charset=utf-8;filename=" + fileName);
        List<AdminSellerExcelTamplate> datas = new ArrayList<>();
        for (SupplierInfoInfoResponse supplierInfoInfoResponse : listResponse1.getResult()){
            String billCycle = "";
            String taxpayerType ="";
            String status = "";
            if (supplierInfoInfoResponse.getBillingCycle().equals(1)){
                billCycle = "周结";
            } else if (supplierInfoInfoResponse.getBillingCycle().equals(2)){
                billCycle = "半月结";
            } else {
                billCycle = "月结";
            }
            if (supplierInfoInfoResponse.getTaxpayerType().equals(1)){
                taxpayerType = "一般纳税人";
            } else {
                taxpayerType = "小规模纳税人";
            }
            if (map.get(supplierInfoInfoResponse.getId()).equals(1)){
                status = "正常";
            } else if (map.get(supplierInfoInfoResponse.getId()).equals(-1)){
                status = "关闭";
            } else {
                status = "冻结";
            }
            AdminSellerExcelTamplate adminSellerExcelTamplate = new AdminSellerExcelTamplate();
            adminSellerExcelTamplate.setId(supplierInfoInfoResponse.getId());
            adminSellerExcelTamplate.setShopName(supplierInfoInfoResponse.getEnterpriseNameAbbreviation());
            adminSellerExcelTamplate.setName(supplierInfoInfoResponse.getEnterpriseName());
            adminSellerExcelTamplate.setBillingCycle(billCycle);
            adminSellerExcelTamplate.setApplyName(supplierInfoInfoResponse.getApplyName());
            adminSellerExcelTamplate.setApplyPhone(supplierInfoInfoResponse.getApplyPhone());
            adminSellerExcelTamplate.setEnterpriseBusinessAddress(supplierInfoInfoResponse.getEnterpriseBusinessAddress());
            adminSellerExcelTamplate.setLegalPersonName(supplierInfoInfoResponse.getLegalPersonName());
            adminSellerExcelTamplate.setEnterpriseType(supplierInfoInfoResponse.getEnterpriseType());
            adminSellerExcelTamplate.setRegistrationCapital(supplierInfoInfoResponse.getRegistrationCapital());
            adminSellerExcelTamplate.setEnterpriseRegistrationTime(supplierInfoInfoResponse.getEnterpriseRegistrationTime().toString());
            adminSellerExcelTamplate.setTaxpayerType(taxpayerType);
            adminSellerExcelTamplate.setSettleInTime(supplierInfoInfoResponse.getUpdateTime().toString());
            adminSellerExcelTamplate.setStatus(status);
            datas.add(adminSellerExcelTamplate);
        }
        try {
            if (datas.isEmpty()) {
                itemExcelExportHelper.downloadTemplate(AdminSellerExcelTamplate.class, response.getOutputStream());
            } else {
                itemExcelExportHelper.downloadTemplate(datas, AdminSellerExcelTamplate.class, response.getOutputStream());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RestException(e.getMessage());
        }
    }

    @GetMapping(value = "/vendorList/sham", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(value = "根据名称联想查询与区域运营有合作关系的供应商列表（不分页 名称联想）---固定区域运营ID查询")
    public List<VendorSearchVO> vendorListSham(@RequestParam(required = false) Long id,
                                               @RequestParam(required = true) String name,
                                               @RequestParam(required = false, defaultValue = "10") Integer limit) {

        FindShopByFuzzNameFilterByIdsRequest request = new FindShopByFuzzNameFilterByIdsRequest();
        request.setName(name);
        request.setType(ShopType.VENDOR.getShopTypeByValue());
        request.setSize(limit);
        request.setId(shamOperatorId);
        request.setTenantId(RequestContext.getTenantId());
        return shopAdminConverter.info2VSvo(Assert.take(shopReadFacade.findShopByFuzzNameFilterByIds(request)));
    }


    @ApiOperation("查看所有品牌商")
    @PostMapping(value = "/pagingVendor")
    public Response<Paging<SupplierInfoInfoResponse>> pagingVendor(@RequestBody SupplierInfoPageRequest request) {
        if (request.getStatus1().equals(0)) {
            Integer status1 = null;
            ShopPagingRequest shopPagingRequest = new ShopPagingRequest();
            shopPagingRequest.setStatus(request.getStatus());
            shopPagingRequest.setPageNo(request.getPageNo());
            shopPagingRequest.setPageSize(request.getPageSize());
            shopPagingRequest.setStatusNo(-1);
            shopPagingRequest.setType(1);
            shopPagingRequest.setOperationId(request.getOperationId());
            Response<Paging<ShopInfo>> pageResponse = shopReadFacade.page(shopPagingRequest);


            List<Long> list = new ArrayList<>();
            Map<Long,Integer> map = Maps.newHashMap();
            for (ShopInfo shopInfo:pageResponse.getResult().getData()){
                list.add(shopInfo.getId());
                map.put(shopInfo.getId(),shopInfo.getStatus());
            }
            SupplierInfoQueryListRequest supplierInfoQueryListRequest = new SupplierInfoQueryListRequest();
            supplierInfoQueryListRequest.setIds(list);
            Response<List<SupplierInfoInfoResponse>> listResponse = supplierInfoReadFacade.list(supplierInfoQueryListRequest);
            List<ShopSupplierInfo> shopSupplierInfos = new ArrayList<>();
            for (SupplierInfoInfoResponse supplierInfoInfoResponse : listResponse.getResult()){
                ShopSupplierInfo shopSupplierInfo = new ShopSupplierInfo();
                status1 = map.get(supplierInfoInfoResponse.getId());
                shopSupplierInfo.setId(supplierInfoInfoResponse.getId());
                shopSupplierInfo.setBillingCycle(supplierInfoInfoResponse.getBillingCycle());
                shopSupplierInfo.setName(supplierInfoInfoResponse.getEnterpriseNameAbbreviation());
                shopSupplierInfo.setShopNames(supplierInfoInfoResponse.getEnterpriseName());
                shopSupplierInfo.setApplyName(supplierInfoInfoResponse.getApplyName());
                shopSupplierInfo.setApplyPhone(supplierInfoInfoResponse.getApplyPhone());
                shopSupplierInfo.setEnterpriseBusinessAddress(supplierInfoInfoResponse.getEnterpriseBusinessAddress());
                shopSupplierInfo.setLegalPersonName(supplierInfoInfoResponse.getLegalPersonName());
                shopSupplierInfo.setEnterpriseType(supplierInfoInfoResponse.getEnterpriseType());
                shopSupplierInfo.setRegistrationCapital(supplierInfoInfoResponse.getRegistrationCapital());
                shopSupplierInfo.setEnterpriseRegistrationTime(supplierInfoInfoResponse.getEnterpriseRegistrationTime());
                shopSupplierInfo.setTaxpayerType(supplierInfoInfoResponse.getTaxpayerType().toString());
                shopSupplierInfo.setStatus(status1);
                shopSupplierInfo.setSettleInTime(supplierInfoInfoResponse.getUpdateTime());
                shopSupplierInfos.add(shopSupplierInfo);
            }
            return Response.ok(new Paging(pageResponse.getResult().getTotal(),shopSupplierInfos));



        } else if (request.getStatus1().equals(1)){
            //审核查看品牌商
            Response<Paging<SupplierInfoInfoResponse>> pagingResponse = supplierInfoReadFacade.page(request);
            return pagingResponse;
        } else {
            throw new ServiceException("paging.vendor.failed");
        }
    }

    @ApiOperation(value = "品牌商审核列表")
    @PostMapping(value = "/getShopAuditPage", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Paging<VendorWithPartnerShipInfo> getShopAuditPage(@RequestBody PartnershipVendorListPagingRequest request) {
        request.setOperatorId(RequestContext.getShopId());
        return Assert.take(supplierInfoReadFacade.getShopAuditPage(request));
    }

    @ApiOperation("查看管理品牌商")
    @PostMapping(value = "/pageExaVendor")
    public Response<Paging<ShopInfo>> pageExaVendor(@RequestBody SupplierInfoQueryListRequest request) {
        request.setAuditStatus(2);
        Response<List<SupplierInfoInfoResponse>> response = supplierInfoReadFacade.listByRequest(request);
        Map<Long,SupplierInfoInfoResponse> map = Maps.newHashMap();
        Set<Long> list  = new HashSet<>();
        for (SupplierInfoInfoResponse supplierInfoInfoResponse : response.getResult()) {
            map.put(supplierInfoInfoResponse.getId(),supplierInfoInfoResponse);
            list.add(supplierInfoInfoResponse.getId());
        }
        ShopPagingRequest shopPagingRequest = new ShopPagingRequest();
        shopPagingRequest.setStatus(request.getStatus());
        shopPagingRequest.setIdSet(list);

        Response<Paging<ShopInfo>> listResponse = shopReadFacade.page(shopPagingRequest);
        for (ShopInfo shopInfo : listResponse.getResult().getData()) {
            shopInfo.setEnterpriseBusinessAddress(map.get(shopInfo.getId()).getEnterpriseBusinessAddress());
            shopInfo.setEnterpriseBusinessAddressCode(map.get(shopInfo.getId()).getEnterpriseBusinessAddressCode());
            shopInfo.setContract(map.get(shopInfo.getId()).getContract());
            shopInfo.setBillingCycle(map.get(shopInfo.getId()).getBillingCycle());
            shopInfo.setApplyName(map.get(shopInfo.getId()).getApplyName());
            shopInfo.setShopNames(map.get(shopInfo.getId()).getEnterpriseName());
            shopInfo.setName(map.get(shopInfo.getId()).getEnterpriseNameAbbreviation());
        }
        return listResponse;
    }

    @ApiOperation("查看单条品牌商")
    @PostMapping(value = "/viewVendor")
    public Response<Map<String,SupplierInfoInfoResponse>> viewVendor(@RequestBody SupplierInfoQueryRequest request) {
        List<SupplierInfoInfoResponse> list = new ArrayList<>();
        Map<String,SupplierInfoInfoResponse> map = Maps.newHashMap();
        SupplierInfoInfoResponse supplierInfoInfoResponse = supplierInfoReadFacade.queryByOperation(request.getId());
        log.info("supplierInfoInfoResponse:{}",supplierInfoInfoResponse);
        if (supplierInfoInfoResponse == null){
            map.put("updateBefore",supplierInfoReadFacade.view(request).getResult());
        } else {
            SupplierInfoLogQueryRequest supplierInfoLogQueryRequest = new SupplierInfoLogQueryRequest();
            supplierInfoLogQueryRequest.setId(request.getId());
            Response<SupplierInfoLogInfoResponse> viewByVendorId = supplierInfoLogReadFacade.getViewByVendorId(supplierInfoLogQueryRequest);
            if (viewByVendorId.isSuccess() && Objects.nonNull(viewByVendorId.getResult())){
                SupplierInfoInfoResponse supplierInfoInfoResponse1 = new SupplierInfoInfoResponse();
                BeanUtils.copyProperties(viewByVendorId.getResult(),supplierInfoInfoResponse1);
                SupplierInfoLogInfoResponse result = viewByVendorId.getResult();
                supplierInfoInfoResponse1.setBankBranceCode(result.getBankBranceCode());
                supplierInfoInfoResponse1.setContactName(result.getConcactName());
                supplierInfoInfoResponse1.setContactMobile(result.getConcactMobile());
                supplierInfoInfoResponse1.setContactCode(result.getConcactCode());
                supplierInfoInfoResponse1.setContactAddress(result.getConcactAddress());
                supplierInfoInfoResponse1.setId(request.getId());
                map.put("updateBefore",supplierInfoInfoResponse1);
            }else {
                map.put("updateBefore",supplierInfoInfoResponse);
            }

            map.put("updateAfter",supplierInfoInfoResponse);
        }
        return Response.ok(map);
    }

    @ApiOperation("修改")
    @PostMapping(value = "/updateVendor")
    public Response<Boolean> updateVendor(@RequestBody SupplierInfoCreateRequest request){
        ShopUpdateRequest shopUpdateRequest  = new ShopUpdateRequest();
        ShopParam param = new ShopParam();
        param.setId(request.getId());
        param.setStatus(-1);
        shopUpdateRequest.setShopParam(param);
        shopWriteFacade.upByOpera(shopUpdateRequest);
        return supplierInfoWriteFacade.createSup(request);
    }


    @ApiOperation("入驻审核")
    @PostMapping(value = "/examine")
    public Response<Boolean> examine(@RequestBody SupplierInfoExamineRequest request) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = sdf.format(new Date());
        if(request.getSettle_status().equals(0)){
            request.setTenantId(TenantContext.getTenantId());
            SupplierInfoUpdateRequest request1  = new SupplierInfoUpdateRequest();
            request1.setType(1);
            request1.setId(request.getId());
            request1.setAuditStatus(request.getAudit_status());
            request1.setTenantId(request.getTenantId());
            if (null!= request.getAudit_opinion()){
                request1.setAuditOpinion(request.getAudit_opinion());
            }
            request1.setVendorPartnershipId(request.getVendorPartnershipId());
            request1.setSettleStatus(0);
            if (request.getAudit_status().equals(2)){

                request1.setUpdateByUserId(String.valueOf(RequestContext.getUserId()));

                SupplierInfoQueryRequest supplierInfoQueryRequest = new SupplierInfoQueryRequest();
                supplierInfoQueryRequest.setId(request.getId());
                Response<SupplierInfoInfoResponse> responseResponse = supplierInfoReadFacade.view(supplierInfoQueryRequest);
                if (!responseResponse.isSuccess()){
                    return Response.fail("供应商详情查询失败...");
                }

                String content = StrUtil.format("通过了【{}】的品牌商入驻申请", request.getEnterpriseName());
                operationLogComponent.create(2,"品牌商入驻审核详情",content);

                request1.setAuditEndTime(sdf.parse(dateStr));
                return supplierInfoWriteFacade.update(request1);
            } else {
                String content = StrUtil.format("驳回了【{}】的品牌商入驻申请，理由：{}", request.getEnterpriseName(), request.getAudit_opinion());
                operationLogComponent.create(2,"品牌商入驻审核详情",content);
                request1.setAuditEndTime(sdf.parse(dateStr));
                return supplierInfoWriteFacade.update(request1);
            }

        } else if (request.getSettle_status().equals(1)){
            request.setTenantId(TenantContext.getTenantId());
            SupplierInfoUpdateRequest request1  = new SupplierInfoUpdateRequest();
            request1.setType(1);
            request1.setId(request.getId());
            request1.setAuditStatus(request.getAudit_status());
            request1.setTenantId(request.getTenantId());
            request1.setSettleStatus(request.getSettle_status());
            if (null!= request.getAudit_opinion()){
                request1.setAuditOpinion(request.getAudit_opinion());
            }
            Response<Boolean> flag;
            if (request.getAudit_status().equals(2)){
//                ShopUnfrozenRequest shopUnfrozenRequest = new ShopUnfrozenRequest();
//                shopUnfrozenRequest.setIdSet(Sets.newHashSet(request.getId()));
//                shopUnfrozenRequest.setTenantId(RequestContext.getTenantId());
//                shopUnfrozenRequest.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
//                Response<Boolean> flag2 = shopWriteFacade.unfrozen(shopUnfrozenRequest);
                SupplierInfoInfoResponse supplierInfoInfoResponse = supplierInfoReadFacade.queryByTenant(request.getId());
                request1 = shopAdminConverter.getSup(supplierInfoInfoResponse);
                request1.setId(request.getId());
                request1.setAuditStatus(request.getAudit_status());
                request1.setSettleStatus(request.getSettle_status());
                request1.setAuditEndTime(sdf.parse(dateStr));
                request1.setAuditOpinion("");
                request1.setType(1);
                flag = supplierInfoWriteFacade.update(request1);
                String content = StrUtil.format("通过了【{}】的品牌商修改申请", request.getEnterpriseName());
                operationLogComponent.create(2,"品牌商修改审核详情",content);
            } else {
                if (StringUtils.isNotEmpty(request.getAudit_opinion())){
                    request1.setAuditOpinion(request.getAudit_opinion());
                }
                request1.setAuditEndTime(sdf.parse(dateStr));
                flag = supplierInfoWriteFacade.update(request1);
                String content = StrUtil.format("驳回了【{}】的品牌商修改申请，理由：{}", request.getEnterpriseName(), request.getAudit_opinion());
                operationLogComponent.create(2,"品牌商修改审核详情",content);
            }
            return flag;
        } else {
            log.error("审核失败，settle_status:{}",request.getSettle_status());
            return Response.ok(Boolean.FALSE);
        }

    }

    @ApiOperation("上传合同")
    @PostMapping(value = "/contract")
    public Response<Boolean> contract(@RequestBody SupplierContractRequest request){
        request.setOperatorId(RequestContext.getShopId());
        return supplierInfoWriteFacade.contract(request);
    }

    @ApiOperation("查看合同")
    @GetMapping(value = "/pageContract")
    public Response<String> pageContract(@RequestParam Long id) {
        SupplierInfoQueryRequest request  = new SupplierInfoQueryRequest();
        request.setId(id);
        Response<SupplierInfoInfoResponse> response = supplierInfoReadFacade.view(request);
        String contract = response.getResult().getContract();
        return Response.ok(contract);
    }

    @ApiOperation("冻结")
    @PostMapping(value = "/freeze")
    public Response<Boolean> freeze(@RequestBody ShopFreezeRequest request){


        VendorPartnershipUpdateRequest vendorPartnershipUpdateRequest = new VendorPartnershipUpdateRequest();
        VendorPartnershipParam partnershipParam = new VendorPartnershipParam();
        partnershipParam.setOperatorId(RequestContext.getShopId());
        partnershipParam.setVendorId(request.getId());
        partnershipParam.setStatus(-1);
        vendorPartnershipUpdateRequest.setParam(partnershipParam);
        Response<Boolean> update = vendorPartnershipWriteFacade.update(vendorPartnershipUpdateRequest);
        if (update.isSuccess() && update.getResult()){
            ShopFrozenRequest shopFrozenRequest = new ShopFrozenRequest();
            shopFrozenRequest.setIdSet(Sets.newHashSet(request.getId()));
            shopFrozenRequest.setTenantId(RequestContext.getTenantId());
            shopFrozenRequest.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
            Response<Boolean> flag = shopWriteFacade.frozen(shopFrozenRequest);
            if (Boolean.TRUE == flag.getResult()){
                log.info("冻结成功，品牌商Id:{}", request.getId());
//            Boolean bool = itemWriteFacade.freezeItemStatus(request.getId(),-2);
//            if (Boolean.TRUE == bool) {
//                log.info("商品冻结成功，品牌商Id:{}", request.getId());
//            } else {
//                log.error("商品冻结失败，品牌商Id:{}", request.getId());
//            }
            } else {
                log.error("冻结失败，品牌商Id:{}", request.getId());
                throw new ServiceException("冻结失败!");
            }
        }
        return update;
    }

    @ApiOperation("解冻")
    @PostMapping(value = "/unFreeze")
    public Response<Boolean> unFreeze(@RequestBody ShopFreezeRequest request) {

        VendorPartnershipUpdateRequest vendorPartnershipUpdateRequest = new VendorPartnershipUpdateRequest();
        VendorPartnershipParam partnershipParam = new VendorPartnershipParam();
        partnershipParam.setOperatorId(RequestContext.getShopId());
        partnershipParam.setVendorId(request.getId());
        partnershipParam.setStatus(1);
        vendorPartnershipUpdateRequest.setParam(partnershipParam);
        Response<Boolean> update = vendorPartnershipWriteFacade.update(vendorPartnershipUpdateRequest);

        if (update.isSuccess() && update.getResult()){
            ShopUnfrozenRequest shopUnfrozenRequest = new ShopUnfrozenRequest();
            shopUnfrozenRequest.setIdSet(Sets.newHashSet(request.getId()));
            shopUnfrozenRequest.setTenantId(RequestContext.getTenantId());
            shopUnfrozenRequest.setUpdatedBy(String.valueOf(RequestContext.getUserId()));
            Response<Boolean> flag = shopWriteFacade.unfrozen(shopUnfrozenRequest);
            if (Boolean.TRUE == flag.getResult()){
                log.info("解冻成功，品牌商Id:{}", request.getId());
//            Boolean bool = itemWriteFacade.freezeItemStatus(request.getId(),1);
//            if (Boolean.TRUE == bool) {
//                log.info("商品解冻成功，品牌商Id:{}", request.getId());
//            } else {
//                log.error("商品解冻失败，品牌商Id:{}", request.getId());
//            }
            } else {
                log.error("解冻失败，品牌商Id:{}", request.getId());
                throw new ServiceException("解冻失败!");
            }
        }
        return update;
    }

    @ApiOperation("中台查看运营商")
    @PostMapping(value = "/pageSeller")
    public Response<Paging<ShopSupplierInfo>> pageSeller(@RequestBody ShopSupplierRequest request) {
        ShopPagingRequest req = new ShopPagingRequest();
        Integer status = null;
        req.setTenantId(RequestContext.getTenantId());
        req.setPageNo(request.getPageNo());
        req.setPageSize(request.getPageSize());
        req.setType(ShopType.AREA_OPERATOR.getShopTypeByValue());
        req.setStatus(request.getStatus());
        req.setOperationId(request.getOperatorId());
        Response<Paging<ShopInfo>> shopInfoList = shopReadFacade.page(req);
        List<Long> list = new ArrayList<>();
        Map<Long,Integer> map = Maps.newHashMap();
        for (ShopInfo shopInfo:shopInfoList.getResult().getData()){
            list.add(shopInfo.getId());
            map.put(shopInfo.getId(),shopInfo.getStatus());
        }
        SupplierInfoQueryListRequest supplierInfoQueryListRequest = new SupplierInfoQueryListRequest();
        supplierInfoQueryListRequest.setIds(list);
        Response<List<SupplierInfoInfoResponse>> listResponse = supplierInfoReadFacade.list(supplierInfoQueryListRequest);
        List<ShopSupplierInfo> shopSupplierInfos = new ArrayList<>();
        for (SupplierInfoInfoResponse supplierInfoInfoResponse : listResponse.getResult()){
            ShopSupplierInfo shopSupplierInfo = new ShopSupplierInfo();
            status = map.get(supplierInfoInfoResponse.getId());
            shopSupplierInfo.setId(supplierInfoInfoResponse.getId());
            shopSupplierInfo.setBillingCycle(supplierInfoInfoResponse.getBillingCycle());
            shopSupplierInfo.setName(supplierInfoInfoResponse.getEnterpriseNameAbbreviation());
            shopSupplierInfo.setShopNames(supplierInfoInfoResponse.getEnterpriseName());
            shopSupplierInfo.setApplyName(supplierInfoInfoResponse.getApplyName());
            shopSupplierInfo.setApplyPhone(supplierInfoInfoResponse.getApplyPhone());
            shopSupplierInfo.setEnterpriseBusinessAddress(supplierInfoInfoResponse.getEnterpriseBusinessAddress());
            shopSupplierInfo.setLegalPersonName(supplierInfoInfoResponse.getLegalPersonName());
            shopSupplierInfo.setEnterpriseType(supplierInfoInfoResponse.getEnterpriseType());
            shopSupplierInfo.setRegistrationCapital(supplierInfoInfoResponse.getRegistrationCapital());
            shopSupplierInfo.setEnterpriseRegistrationTime(supplierInfoInfoResponse.getEnterpriseRegistrationTime());
            shopSupplierInfo.setTaxpayerType(supplierInfoInfoResponse.getTaxpayerType().toString());
            shopSupplierInfo.setStatus(status);
            shopSupplierInfo.setSettleInTime(supplierInfoInfoResponse.getUpdateTime());
            shopSupplierInfos.add(shopSupplierInfo);
        }
        return Response.ok(new Paging(shopInfoList.getResult().getTotal(),shopSupplierInfos));
    }


    @ApiOperation("中台查看品牌商")
    @PostMapping(value = "/pageVendor")
    public Response<Paging<ShopInfo>> pageVendor(@RequestBody ShopSupplierRequest request) {

        SupplierInfoPageRequest supplierInfoPageRequest = new SupplierInfoPageRequest();
        supplierInfoPageRequest.setAuditStatus(2);
        supplierInfoPageRequest.setId(request.getId());
        supplierInfoPageRequest.setOperationId(request.getOperatorId());
        supplierInfoPageRequest.setBillingCycle(request.getBillingCycle());
        supplierInfoPageRequest.setEnterpriseName(request.getEnterpriseName());
        supplierInfoPageRequest.setEnterpriseNameAbbreviation(request.getEnterpriseNameAbbreviation());
        supplierInfoPageRequest.setPageNo(request.getPageNo());
        supplierInfoPageRequest.setPageSize(request.getPageSize());
        Response<Paging<SupplierInfoInfoResponse>> response = supplierInfoReadFacade.page(supplierInfoPageRequest);
        if (!response.isSuccess()) {
            log.error("supplierInfoReadFacade page request: {}, error: {}", supplierInfoPageRequest, response);
            throw new RestException(response.getError());
        }
        if (response.getResult().getData() == null || response.getResult().getData().isEmpty()) {
            return Response.ok(new Paging<>(0L,new ArrayList<>()));
        }
        Map<Long,SupplierInfoInfoResponse> map = Maps.newHashMap();
        Set<Long> list  = new HashSet<>();

        Set<Long> operatorIds = response.getResult().getData().stream().map(SupplierInfoInfoResponse::getOperatorId).collect(Collectors.toSet());
        // 查询运营商信息
        ShopPagingRequest shopPagingRequest = new ShopPagingRequest();
        shopPagingRequest.setIdSet(operatorIds);
        Response<List<ShopInfo>> listResponse1 =shopReadFacade.list(shopPagingRequest);
        Map<Long,String> maps = Maps.newHashMap();
        if (!org.springframework.util.CollectionUtils.isEmpty(listResponse1.getResult())) {
            maps = listResponse1.getResult().stream().collect(Collectors.toMap(ShopInfo::getId,ShopInfo::getName));
        }
        for (SupplierInfoInfoResponse supplierInfoInfoResponse : response.getResult().getData()) {
            map.put(supplierInfoInfoResponse.getId(),supplierInfoInfoResponse);
            list.add(supplierInfoInfoResponse.getId());
        }
        ShopQueryByIdRequest shopQueryByIdRequest = new ShopQueryByIdRequest();
        shopQueryByIdRequest.setIdSet(list);
        Response<List<ShopInfo>> listResponse = shopReadFacade.queryById(shopQueryByIdRequest);
        for (ShopInfo shopInfo : listResponse.getResult()) {
            String name = maps.get(map.get(shopInfo.getId()).getOperatorId());
            shopInfo.setEnterpriseBusinessAddress(map.get(shopInfo.getId()).getEnterpriseBusinessAddress());
            shopInfo.setEnterpriseBusinessAddressCode(map.get(shopInfo.getId()).getEnterpriseBusinessAddressCode());
            shopInfo.setBillingCycle(map.get(shopInfo.getId()).getBillingCycle());
            shopInfo.setApplyName(map.get(shopInfo.getId()).getApplyName());
            shopInfo.setApplyPhone(map.get(shopInfo.getId()).getApplyPhone());
            shopInfo.setOperatorId(map.get(shopInfo.getId()).getOperatorId());
            shopInfo.setContactName(map.get(shopInfo.getId()).getContactName());
            shopInfo.setContactMobile(map.get(shopInfo.getId()).getContactMobile());
            shopInfo.setLegalPersonName(map.get(shopInfo.getId()).getLegalPersonName());
            shopInfo.setEnterpriseType(map.get(shopInfo.getId()).getEnterpriseType());
            shopInfo.setRegistrationCapital(map.get(shopInfo.getId()).getRegistrationCapital());
            shopInfo.setEnterpriseRegistrationTime(map.get(shopInfo.getId()).getEnterpriseRegistrationTime());
            shopInfo.setOperatorName(name);
        }

        return Response.ok(new Paging<>(response.getResult().getTotal(),listResponse.getResult()));
    }

}
