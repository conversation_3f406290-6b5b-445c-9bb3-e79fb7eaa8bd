package io.terminus.parana.item.web.category.controller;

import io.swagger.annotations.ApiOperation;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.item.category.api.bean.request.CategoryExtraQueryBySingleRequest;
import io.terminus.parana.item.category.api.bean.response.CategoryExtraInfo;
import io.terminus.parana.item.category.api.facade.CategoryExtraReadFacade;
import io.terminus.parana.item.category.enums.CategoryExtraExtensionType;
import io.terminus.parana.item.web.category.bean.vo.CategoryBusinessTypeBinding;
import io.terminus.parana.item.web.category.converter.CategoryExtraVOConverter;
import io.terminus.parana.item.common.utils.Assert;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-04-12
 */
@RestController
@RequestMapping("/api/item/category-extra")
public class CategoryExtrasV2 {

    private final CategoryExtraVOConverter categoryExtraVOConverter = Mappers.getMapper(CategoryExtraVOConverter.class);

    @Autowired
    private CategoryExtraReadFacade categoryExtraReadFacade;

    @ApiOperation("获取类目上的业务类型")
    @GetMapping("/business/{categoryId}")
    public List<CategoryBusinessTypeBinding> getBusinessTypeBindingList(@PathVariable Long categoryId) {
        CategoryExtraQueryBySingleRequest request = new CategoryExtraQueryBySingleRequest();
        request.setTenantId(RequestContext.getTenantId());
        request.setCategoryId(categoryId);
        request.setExtensionType(CategoryExtraExtensionType.BUSINESS_TYPE_BINDING.getValue());
        List<CategoryExtraInfo> infoList = Assert.take(categoryExtraReadFacade.queryBySingle(request));

        return categoryExtraVOConverter.get(infoList);
    }
}
