<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (c) 2020. 深圳怡丰云智科技股份有限公司.  All rights reserved.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.yifengcloud.saas.parana.item</groupId>
        <artifactId>item-center</artifactId>
        <version>2.0.0.0-PPT-SNAPSHOT</version>
    </parent>

    <artifactId>item-server</artifactId>
    <modelVersion>4.0.0</modelVersion>

    <dependencies>
        <dependency>
            <groupId>io.terminus</groupId>
            <artifactId>excel-engine-core</artifactId>
            <version>1.1.5-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.3</version>
        </dependency>
    	<dependency>
    		<groupId>org.assertj</groupId>
    		<artifactId>assertj-core</artifactId>
    		<version>3.11.1</version>
    	</dependency>
    	<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>2.23.4</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>5.1.8.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>2.0.2</version>
            <scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>2.0.2</version>
            <scope>test</scope>
		</dependency>
        <dependency>
            <groupId>io.terminus</groupId>
            <artifactId>server-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.mybatis.shard</groupId>
            <artifactId>shard-sequence</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.mybatis</groupId>-->
<!--            <artifactId>mybatis</artifactId>-->
<!--            <version>3.4.5</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-over-slf4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.parana</groupId>
            <artifactId>elasticsearch-http-client</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <!-- <scope>test</scope> -->
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.kevinsawicki</groupId>
            <artifactId>http-request</artifactId>
            <version>6.0</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.terminus.parana</groupId>
            <artifactId>search-retrieval-service</artifactId>
        </dependency>
        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>5.1.1</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.boot</groupId>
            <artifactId>spring-boot-starter-mybatis</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.parana</groupId>
            <artifactId>search-dump-service</artifactId>
            <version>${search.center.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.boot</groupId>
            <artifactId>terminus-spring-boot-starter-jetcache</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.boot.rpc</groupId>
            <artifactId>dubbo-light-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-annotation-aspectj</artifactId>
            <version>1.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.boot.rpc</groupId>
            <artifactId>dubbo-light-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus</groupId>
            <artifactId>api-common</artifactId>
            <!-- <version>1.0.3.RELEASE</version> -->
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.2</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yifengcloud.saas.parana.item</groupId>
            <artifactId>item-api</artifactId>
            <version>${project.version}</version>
        </dependency>

		<dependency>
            <groupId>com.yifengcloud.saas.parana.misc</groupId>
            <artifactId>misc-api-autoconfigure</artifactId>
			<version>${misc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yifengcloud.saas.parana.wallet</groupId>
            <artifactId>wallet-api-autoconfigure</artifactId>
			<version>${wallet.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yifengcloud.saas.parana.ipm</groupId>
            <artifactId>ipm-api-autoconfigure</artifactId>
            <version>${ipm.solution.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yifengcloud.saas.parana.promotion</groupId>
            <artifactId>promotion-api-autoconfigure</artifactId>
            <version>${promotion.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yifengcloud.saas.parana.user</groupId>
            <artifactId>user-api-autoconfigure</artifactId>
            <version>${user.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yifengcloud.saas.parana.trade</groupId>
            <artifactId>trade-api-autoconfigure</artifactId>
            <version>${parana.trade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yifengcloud.saas.parana.settlement</groupId>
            <artifactId>settlement-api-autoconfigure</artifactId>
            <version>${settle.version}</version>
        </dependency>

        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-jpeg</artifactId>
            <version>3.4.1</version>
        </dependency>

        <dependency>
            <groupId>com.plumelog</groupId>
            <artifactId>plumelog-logback</artifactId>
            <version>3.5-YF-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty.netty</groupId>
                    <artifactId>netty</artifactId>
                    <!-- <version>3.10.6.Final</version> -->
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>
