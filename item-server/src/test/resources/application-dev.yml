server:
  port: ${SERVER_PORT:8086}

spring:
  application:
    name: item-center-server
  main:
    allow-bean-definition-overriding: true
  datasource:
#    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    driver-class-name: com.mysql.jdbc.Driver
    url: jdbc:mysql://${MYSQL_HOST:**************}:${MYSQL_PORT:3309}/${MYSQL_DATABASE:parana-item}?allowMultiQueries=true&useUnicode=true&characterEncoding=UTF8&serverTimezone=Asia/Shanghai&tcpKeepAlive=true&autoReconnect=true
    username: ${MYSQL_USERNAME:parana_item}
    password: ${MYSQL_PASSWORD:52926b22e6b62f1448808df9835ec3d36efb554b}
    hikari:
      max-lifetime: 60000
  profiles:
    include: search

mybatis:
  mapperLocations: classpath*:mapper/*Mapper.xml

dubbo:
  application:
    name: ${spring.application.name}
  consumer:
    check: false
  protocol:
    address: *************
    name: dubbo
    port: ${DUBBO_PORT:26886}
  registry:
    id: itemcenter-dubbo
    address: zookeeper://${ZOOKEEPER_DUBBO:127.0.0.1:2181}
  provider:
    filter: activelimit,executelimit

elasticjob:
  zookeeper:
    serverLists: ${ELASTICJOB_HOST}
    namespace: item-center-${spring.profiles.active}

cache:
  statIntervalMinutes: 15
  remote:
    minIdle: 5
    maxIdle: 20
    maxTotal: 50
    password: ${REDIS_PASSWORD:foobaredA123}
    database: ${REDIS_DB_INDEX:0}
    sentinels: ${REDIS_SENTINELS:**************:26883,**************:26883,**************:26883}
    masterName: ${MASTER_NAME:mymaster}

dubbo.provider.scan-packages: >
  io.terminus.parana.item.enhance.api.facade,
  io.terminus.parana.item.brand.api.facade,
  io.terminus.parana.item.category.api.facade,
  io.terminus.parana.item.attribute.api.facade,
  io.terminus.parana.item.channel.api.facade,
  io.terminus.parana.item.delivery.api.facade,
  io.terminus.parana.item.tax.api.facade,
  io.terminus.parana.item.comment.api.facade,
  io.terminus.parana.item.favorites.api.facade,
  io.terminus.parana.item.footprint.api.facade,
  io.terminus.parana.item.item.api.facade,
  io.terminus.parana.item.spu.api.facade,
  io.terminus.parana.item.recommend.api.facade,
  io.terminus.parana.item.relation.api.facade,
  io.terminus.parana.item.search.facade,
  io.terminus.parana.item.relation.api.facade,
  io.terminus.parana.item.shop.api.facade,
  io.terminus.parana.item.store.api.facade,
  io.terminus.parana.item.tag.api.facade,
  io.terminus.parana.item.open.api.facade,
  io.terminus.parana.item.b2b.support.api.facade,
  io.terminus.parana.item.cronjob.api.facade,
  io.terminus.parana.item.area.api.facade,
  io.terminus.parana.item.partnership.api.facade,
  io.terminus.parana.item.price.api.facade,
  io.terminus.parana.item.industry.api.facade,
  io.terminus.parana.item.sap.api.facade,
  io.terminus.parana.item.zhengqi.api.facade,
  io.terminus.parana.item.comminssion.api.facade,
  io.terminus.parana.item.ad.api.facade,
  io.terminus.parana.item.common.activity.api.facade,
  io.terminus.parana.item.service.api.facade,
  io.terminus.parana.item.choicelot.api.facade,
  io.terminus.parana.item.outerbrandbinding.api.facade,
  io.terminus.parana.item.outerbrand.api.facade,
  io.terminus.parana.item.task.api.facade,
  io.terminus.parana.item.distributorchoiceapply.api.facade,
  io.terminus.parana.item.distributorchoiceapplydetail.api.facade,
  io.terminus.parana.item.configuration.api.facade,
  io.terminus.parana.item.distributorskulib.api.facade,
  io.terminus.parana.item.card.api.facade,
  io.terminus.parana.item.wdt.api.facade,
  io.terminus.parana.item.third.api.facade


# MQ配置
terminus:
  clientType: ${MQ_CLIENT_TYPE:ROCKETMQ}
  mqServerAddress: ${MQ_SERVER_ADDRESS:**************:9876;**************:9876;**************:9876}
  topic: ${MQ_TOPIC:EABBC_MALL}
  producerGroup: ${MQ_PRODUCER_GROUP:GID_retail_mall_producer_dev}
  consumerGroup: ${SERVER_MQ_CONSUMER_GROUP:GID_item_center_server_consumer_dev}

item:
  third-party:
    client:
      aliyuncs:
        regionId: cn-shanghai
        accessKeyId: LTAI4Fuki1toT6VW9mXYf1PV
        accessKeySecret: ******************************
      aliyun:
        oss:
          endpoint: ${OSS_ENDPOINT:oss-cn-beijing.aliyuncs.com}
          accessKeyId: ${OSS_ACCESS_KEY:LTAI4GFRD7dHf4BEf1n9kixW}
          accessKeySecret: ${OSS_ACCESS_SECRET:******************************}
          bucketName: ${OSS_BUCKET:eabbc-dev}
    storage:
      provider: aliyun
  imageUrl:
    cdn: cdndev.ea380buy.com
    oss: eabbc-dev.oss-cn-beijing.aliyuncs.com
  envType: dev
  wdt:
    url: https://sandbox.wangdian.cn/openapi2/

#SAP GATEWAY
ea:
  sap:
    gatewayurl: http://dev.sapgw.api.it.com/api
  zgsd:
    sdpriceurl: ${SD_PRICE_URL:http://**************:8887/active/activeQuery}
primary:
  search:
    maxPageNo: 100
    maxPageSize: 1000
sd:
  domain: ${SD_STOCK_URL:http://43597e60282e463f831a4f9627b0b055.apigatewayuat.eascs.com/stlLoad2}

canal:
  mqlog:
    maxFailCount: ${CANAL_MAX_FAIL_COUNT:50}
    maxLimit: ${CANAL_MAX_LIMIG:1000}

#星链
scce:
  appkey: 1519132181206216705
  appsecret: 93397b37f17e4ddeabb23cccd5b92a73
  vendorId: **********
  deliveryFeeTempId: 217

ftp:
  host: ${FTP_HOST:**************}
  port: ${FTP_PORT:21}
  username: ${FTP_USERNAME:zqFtp}
  password: ${FTP_PASSWORD:2dBdkr4e^c8m7@}

zq:
  isZqProd: ${zq_isZqProd:false}

#苹果
apple:
  domain: https://stag-ecapi.employeechoice.cn
  accessId: 1722456639384326144
  accessKey: ****************************************************************
  vendorId: 1100866001
  deliveryFeeTempId: 309
  brandId: 122619259
  brandName: iPhone

#品诺
pinNuo:
  card:
    api:
      valid: false  # 创建卡卷是否通过品诺校验
  domain: http://test-alkaid.pinuc.com
  appId: pn2e6f4665bd0905a4
  appSecret: b766d7e7bf4945d7ba5f6c4f19b9f4a6
  h5domain: https://yyt-test.pinuc.com

ipm:
  api:
    timeout: 20000

# 候鸟api对接参数 test
#houniao:
#  appKey: d4847234448cf86c
#  secret: 50fbcb8470ae00ee3ac5991fd5cd1a9b
#  serverUrl: http://demoshopapi.ecombird.com/
#  vendorId: 1100869001
#  deliveryFeeTempId: 312

# 候鸟api对接参数prod
houniao:
  appKey: eb7428e0fcea730f
  secret: 628c5a199975941596f34306dba7bd97
  serverUrl: https://api.houniao.hk/
  vendorId: 1100869001
  deliveryFeeTempId: 312
