APPLICATION_NAME: retail-mall-web

MQ_TOPIC: retail_mall

spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    # 注意，用于单元测试的db在准备数据时会进行清表操作，建议创建新表用于测试!!!建议创建新表用于测试!!!建议创建新表用于测试!!!
    url: **********************************************************************************************************************************************
    username: root
    password:
    schema: schema.sql          # 指定schema的sql
    data: data.sql              # 指定data的sql
    initialization-mode: always # spring boot 2.x 调整，mysql必须指定为always
  application:
    name: ${APPLICATION_NAME}

mybatis:
  mapperLocations: classpath*:mapper/*Mapper.xml

cache:
  statIntervalMinutes: 15
  remote:
    host: 127.0.0.1
    port: 6379
    password: 123456
    database: 0

# MQ配置，必须配置，否则会导致MQProducer为空
terminus:
  clientType: ${MQ_CLIENT_TYPE:ROCKETMQ}
  mqServerAddress: ${ROCKETMQ_NAMESRV_HOST:127.0.0.1}:${ROCKETMQ_NAMESRV_PORT:9876}
  topic: ${MQ_TOPIC}
  producerGroup: GID_${MQ_TOPIC}_producer
  consumerGroup: GID_${MQ_TOPIC}_item_consumer