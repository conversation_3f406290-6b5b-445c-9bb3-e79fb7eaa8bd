TRUNCATE TABLE `shard_sequence`;
INSERT INTO `shard_sequence`
VALUES ('comment', 1000, now()),
       ('item', 1000, now()),
       ('relation_index', 1000,now()),
       ('item_relation_index', 1000, now()),
       ('recommend_detail', 1000, now()),
       ('recommend_index', 1000, now()),
       ('shop', 1000, now()),
       ('sku', 1000, now()),
       ('sku_template', 1000, now()),
       ('spu', 1000, now()),
       ('store', 1000, now()),
       ('category_attribute_binding', 1000, now());

-- #########################################  类目  ##############################################

-- 清空表数据
TRUNCATE TABLE `parana_back_category`;

-- 插入必要数据
INSERT INTO `parana_back_category`
VALUES (1, 0, 0, '一级类目', 1, 1, 1, 0, 1, now(), now(), 'UnitTest'),
       (2, 0, 1, '二级类目', 1, 1, 1, 0, 1, now(), now(), 'UnitTest'),
       (3, 0, 2, '三级类目', 1, 1, 0, 0, 1, now(), now(), 'UnitTest');

-- 清空表数据
TRUNCATE TABLE `parana_front_category`;

-- 插入必要数据
INSERT INTO `parana_front_category`
VALUES (1, 0, 0, '前台一级', NULL, 1, 1, 0, NULL, NULL, 1, '2019-07-27 16:01:40', '2019-07-27 16:01:40', 'UnitTest'),
       (2, 0, 1, '前台二级', NULL, 2, 1, 0, NULL, NULL, 1, '2019-07-27 16:01:40', '2019-07-27 16:01:40', 'UnitTest'),
       (3, 0, 2, '前台三级', NULL, 3, 0, 0, NULL, NULL, 1, '2019-07-27 16:01:40', '2019-07-27 16:01:40', 'UnitTest');

TRUNCATE TABLE `parana_category_binding`;
INSERT INTO `parana_category_binding`
VALUES (1, 3, 3, '2018-09-18 17:03:32', '2018-09-18 17:03:32', 'UnitTest');

TRUNCATE TABLE `parana_outer_category`;
INSERT INTO `parana_outer_category`
VALUES (1, 'out$0001', '杯子', 'test', 1, NULL, 1, now(), now(), 'UnitTest');

TRUNCATE TABLE `parana_outer_category_binding`;
INSERT INTO `parana_outer_category_binding`
VALUES (1, 1, 3, now(), now());

-- #########################################  商品  ##############################################

-- 清空表数据
TRUNCATE TABLE `parana_item`;
TRUNCATE TABLE `parana_sku`;
TRUNCATE TABLE `parana_item_detail`;

-- 插入默认的商品数据
INSERT INTO `parana_item` (`id`, `extension_type`, `spu_id`, `unit`, `tenant_id`, `outer_id`, `category_id`, `item_code`, `shop_id`, `shop_name`, `brand_id`, `brand_name`, `delivery_fee_temp_id`, `name`, `advertise`, `main_image`, `video_url`, `status`, `type`, `business_type`, `sku_attributes_json`, `other_attributes_json`, `extra_json`, `bit_tag`, `md5_info`, `version`, `created_at`, `updated_at`, `updated_by`, `audit_status`)
VALUES
	(110000100012001, 0, 110000100012001, '件', 1, '3', 3, 'item_code', 1, '单元测试供应商1', 1, '单元测试品牌1', 1, '单元测试商品1', '单元测试广告', 'https://www.baidu.com', 'https://www.baidu.com', -1, 1, 0, '[]', '[{\"group\":\"default\",\"otherAttributes\":[{\"attrKey\":\"属性1\",\"group\":\"值1\"},{\"attrKey\":\"属性2\",\"group\":\"值2\"},{\"attrKey\":\"属性3\",\"group\":\"值3\"},{\"attrKey\":\"属性4\",\"group\":\"值4\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"}]}]', '{\"categoryList\":\"[1,2,3]\"}', 0, 'b2f5d2742cdb2ac61dc70150a3b65968', 0, '2020-07-27 17:18:39', '2020-07-27 17:18:42', 'UnitTest', 3),
	(110000100012002, 0, NULL, '件', 1, '3', 3, 'item_code', 1, '单元测试供应商1', 1, '单元测试品牌1', 1, '单元测试商品2', '单元测试广告', 'https://www.baidu.com', 'https://www.baidu.com', -1, 1, 0, '[]', '[{\"group\":\"default\",\"otherAttributes\":[{\"attrKey\":\"属性1\",\"group\":\"值1\"},{\"attrKey\":\"属性2\",\"group\":\"值2\"},{\"attrKey\":\"属性3\",\"group\":\"值3\"},{\"attrKey\":\"属性4\",\"group\":\"值4\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"}]}]', '{\"categoryList\":\"[1,2,3]\"}', 0, 'b2f5d2742cdb2ac61dc70150a3b65968', 0, '2020-07-27 17:18:39', '2020-07-27 17:18:42', 'UnitTest', 3),
	(110000100012003, 0, NULL, '件', 1, '3', 3, 'item_code', 1, '单元测试供应商2', 1, '单元测试品牌1', 1, '单元测试商品3', '单元测试广告', 'https://www.baidu.com', 'https://www.baidu.com', -1, 1, 0, '[]', '[{\"group\":\"default\",\"otherAttributes\":[{\"attrKey\":\"属性1\",\"group\":\"值1\"},{\"attrKey\":\"属性2\",\"group\":\"值2\"},{\"attrKey\":\"属性3\",\"group\":\"值3\"},{\"attrKey\":\"属性4\",\"group\":\"值4\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"}]}]', '{\"categoryList\":\"[1,2,3]\"}', 0, 'b2f5d2742cdb2ac61dc70150a3b65968', 0, '2020-07-27 17:18:39', '2020-07-27 17:18:42', 'UnitTest', 3);

INSERT INTO `parana_item` (`id`, `extension_type`, `spu_id`, `unit`, `tenant_id`, `outer_id`, `category_id`, `item_code`, `shop_id`, `shop_name`, `brand_id`, `brand_name`, `delivery_fee_temp_id`, `name`, `advertise`, `main_image`, `video_url`, `status`, `type`, `business_type`, `sku_attributes_json`, `other_attributes_json`, `extra_json`, `bit_tag`, `md5_info`, `version`, `created_at`, `updated_at`, `updated_by`, `audit_status`)
VALUES
	(1010101, 0, 110000100012001, '件', 1, '3', 3, 'item_code', 1, '单元测试供应商1', 1, '单元测试品牌1', 1, '单元测试商品1', '单元测试广告', 'https://www.baidu.com', 'https://www.baidu.com', -1, 1, 0, '[]', '[{\"group\":\"default\",\"otherAttributes\":[{\"attrKey\":\"属性1\",\"group\":\"值1\"},{\"attrKey\":\"属性2\",\"group\":\"值2\"},{\"attrKey\":\"属性3\",\"group\":\"值3\"},{\"attrKey\":\"属性4\",\"group\":\"值4\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"}]}]', '{\"categoryList\":\"[1,2,3]\",\"supportReturn\":\"false\"}', 0, 'b2f5d2742cdb2ac61dc70150a3b65968', 0, '2020-07-27 17:18:39', '2020-07-27 17:18:42', 'UnitTest', 3);

INSERT INTO `parana_sku`
VALUES (110000100012001, 0, 1, 110000100012001, NULL, 'sku_code', 'item_barcode', 'item_outer_id', 1, '1558079271406',
        'https://www.baidu.com', 50, 60, -1, NULL, 1, 1, NULL, '{\"originPrice\":60}', NULL, 0, NULL, 0,
        '2019-05-17 15:47:51', '2019-05-17 15:47:51', 'UnitTest');

INSERT INTO `parana_sku` (`id`, `extension_type`, `tenant_id`, `item_id`, `sku_template_id`, `sku_code`, `barcode`, `outer_id`, `shop_id`, `name`, `image`, `price`, `original_price`, `status`, `audit_status`, `type`, `business_type`, `sku_ids_json`, `price_json`, `attrs_json`, `version`, `extra_json`, `bit_tag`, `created_at`, `updated_at`, `updated_by`)
VALUES
	(***********, 0, 1, 1010101, NULL, 'sku_code', 'item_barcode', 'item_outer_id', 1, '1558079271406', 'https://www.baidu.com', 50, 60, -1, NULL, 1, 1, NULL, '{\"originPrice\":60,\"defaultPrice\":20,\"defaultBasePrice\":15,\"defaultPCommission\":2,\"defaultCommission\":1}', NULL, 0, '{\"minQuantity\":10}', 0, '2019-05-17 15:47:51', '2019-05-17 15:47:51', 'UnitTest');

INSERT INTO `parana_item_detail`
VALUES (110000100012001, 1, NULL, '[{"title":"标题1","content":"123123"},{"title":"标题2","content":"123123"}]', NULL, NULL,
        '2019-05-17 15:47:51', '2019-05-17 15:47:51');


-- 清空表数据
TRUNCATE TABLE `parana_spu`;
TRUNCATE TABLE `parana_sku_template`;
TRUNCATE TABLE `parana_spu_detail`;

-- 插入默认的SPU数据
INSERT INTO `parana_spu`
VALUES (110000100012001, 1, 'spu_outer_id', 'spu_code', 3, NULL, NULL, 'Spu创建', NULL, 'https://www.baidu.com', NULL, 1,
        1, NULL, NULL, '{}', '0342fc8cbbf19b9a745ee4f8a1a3eb4e', now(), now(), 'UnitTest');

INSERT INTO `parana_sku_template`
VALUES (120000001, 110000100012001, 1, NULL, '987654321', '123456789', NULL, 60, NULL, 1, NULL, NULL,
        '{\"price\":\"60\"}', now(), now());

INSERT INTO `parana_spu_detail`
VALUES (110000100012001, 1, NULL, NULL, NULL, NULL, now(), now());

-- #########################################  商家  ##############################################

-- 插入SHOP的数据
TRUNCATE TABLE `parana_shop`;

INSERT INTO parana_shop
VALUES (1, 1, '1', 1, 'u106197', '自营商城', 1, 1, '***********', null, '', '北京北京市东城区东华门街道新华书店',
        '{"introduction":"自营商城-介绍"}', null, 0, '2018-08-14 17:55:14', '2019-05-16 17:06:11', '217675', 1, '123');
INSERT INTO parana_shop
VALUES (2, 1, '2', 1, 'u106197', '自营商城2', 1, 1, '***********', null, '', '北京北京市东城区东华门街道新华书店',
        '{"introduction":"自营商城-介绍"}', null, 0, '2018-08-14 17:55:14', '2019-05-16 17:06:11', '217675', 1, '234');
INSERT INTO `parana_shop` (`id`, `tenant_id`, `outer_id`, `user_id`, `user_name`, `name`, `status`, `type`, `phone`, `email`, `image_url`, `address`, `extra_json`, `tags`, `business_type`, `created_at`, `updated_at`, `updated_by`, `industry_id`, `tin`)
VALUES
	(10001, 1, '2', 1, 'u106197', '区域运营', 1, 2, '***********', NULL, '', '北京北京市东城区东华门街道新华书店', '{\"introduction\":\"自营商城-介绍\"}', NULL, 0, '2018-08-14 17:55:14', '2019-05-16 17:06:11', '217675', 1, '234');

TRUNCATE TABLE `parana_operator_category`;
INSERT INTO `parana_operator_category`
VALUES (1, 1, 1, '测试父类目', -1, null, 1, '', 0, 0, 1, 0, 1, 1, null, '2019-9-26 10:42:14', '2019-9-26 10:42:14', 'system');

TRUNCATE TABLE `parana_operator_category_binding`;
INSERT INTO `parana_operator_category_binding`
VALUES (1, 1, 1, 110000100012001, '2018-08-14 17:55:14', '2018-08-14 17:55:14', 'system');


-- #########################################  门店  ##############################################

TRUNCATE TABLE `parana_store`;

INSERT INTO parana_store
VALUES (1, 1, '1', 1, 'u106197', '自营门店', 1, 1, '***********', null, '', '北京北京市东城区东华门街道新华书店',
        '{"introduction":"自营商城-介绍"}', null, '2018-08-14 17:55:14', '2019-05-16 17:06:11', '217675');
INSERT INTO parana_store
VALUES (2, 1, '2', 1, 'u106197', '自营门店2', 1, 1, '***********', null, '', '北京北京市东城区东华门街道新华书店',
        '{"introduction":"自营商城-介绍"}', null, '2018-08-14 17:55:14', '2019-05-16 17:06:11', '217675');

-- #########################################  标签  ##############################################

-- 插入标签数据
TRUNCATE TABLE `parana_generic_tag`;
INSERT INTO `parana_generic_tag`
VALUES (1, 'test_code', NULL, 1, 1, -1, -1, NULL, now(), now(), 'UnitTest'),
       (2, 'owning', 'owings standalone tag', 1, 1, 0, 1, '{"tagDescribe":"test","contextType":"created.with.owning","itemPublishContext":"test"}', now(), now(), 'UnitTest');
TRUNCATE TABLE `parana_generic_tag_binding`;
INSERT INTO `parana_generic_tag_binding`
VALUES (1, 'test', 1, 2, 'presell', 1, 110000100012001, '2000-01-01 00:00:00', '2100-01-01 00:00:00', 1,'{\"feature\":\"xuexi\"}', now(), now(), 'UnitTest'),
       (2, 'test', 1, 1, '1', 0, 110000100012001, '2000-01-01 00:00:00', '2100-01-01 00:00:00', 1,
        '{\"feature\":\"xuexi\"}', now(), now(), 'UnitTest'),
       (3, 'test', 1, 1, '2', 0, 110000100012001, '2000-01-01 00:00:00', '2100-01-01 00:00:00', 1,
        '{\"feature\":\"xuexi\"}', now(), now(), 'UnitTest'),
       (4, 'test', 1, 1, '2', 0, 110000100012002, '2000-01-01 00:00:00', '2100-01-01 00:00:00', 1,
        '{\"feature\":\"xuexi\"}', now(), now(), 'UnitTest');

-- #########################################  推荐  ##############################################

-- 推荐数据
TRUNCATE TABLE `parana_category_recommend`;
INSERT INTO `parana_category_recommend`
VALUES (1, 1, 1, '2019-09-26 14:03:17', '2019-09-26 14:03:17');

TRUNCATE TABLE `recommend_index`;
INSERT INTO `recommend_index`
VALUES (1, 1, '推荐组测试1', 1, 1, null, null, 1, '1', 1, '2019-09-26 14:03:17', '2019-09-26 14:03:17');
INSERT INTO `recommend_index`
VALUES (3, 1, '推荐组测试3', 1, 1, null, null, 1, '1', 1, '2019-09-26 14:03:17', '2019-09-26 14:03:17');

TRUNCATE TABLE `recommend_detail`;
INSERT INTO `recommend_detail`
VALUES (1, 1, 1, null, 1, '1', null, 1, '2019-09-26 14:03:17', '2019-09-26 14:03:17');
INSERT INTO `recommend_detail`
VALUES (3, 1, 3, null, 1, '1', null, 1, '2019-09-26 14:03:17', '2019-09-26 14:03:17');



TRUNCATE TABLE `parana_comment`;
INSERT INTO `parana_comment`
VALUES (16, 1, 0, 110000100012001, -1, -1, 6, 4, 5, '11111', '[]', 0, 0, 0,
        '{\"shop_id\":1,\"sku_order_id\":400612005,\"order_id\":400612004}',
        '{\"user_avatar\":\"https://parana.oss-cn-hangzhou.aliyuncs.com/test/07edafba-3704-4469-84ed-da0d7d345e6a.jpg\",\"user_name\":\"sellerAAA\",\"sku_attributes\":\"{}\",\"item_name\":\"端点生活\"}',
        'b2f5d2742cdb2ac61dc70150a3b65968', '2019-08-06 14:03:17', '2019-08-06 14:03:17', '6', 1, 1, 1);

TRUNCATE TABLE `parana_channel`;
INSERT INTO `parana_channel`
VALUES (1, 0, 1, '默认测试渠道', 1, NULL, 'http://www.baidu.com/', '63cb1d025c0246a588eb26670d8bb2d3', '2019-08-14',
        '2029-08-15', '209-08-14 15:12:17', '2019-08-14 15:12:21', '6');
INSERT INTO `parana_channel`
VALUES (2, 1, 2, '默认测试渠道2', 1, NULL, 'http://www.baidu1.com/', '63cb1d025c0246a588eb26670d8bb2d4', '2019-08-14',
        '2029-08-15', '209-08-14 15:12:17', '2019-08-14 15:12:21', '6');

TRUNCATE TABLE `parana_delivery_fee_templates`;
INSERT INTO `parana_delivery_fee_templates`
VALUES (1, 1, '运费模版1', 1, 0, null, 1, 2, 8, null, null, null, null, null, null, null, null, null, 1, 1,
        '2019-09-27 15:12:21', '2019-09-27 15:12:21', null);

TRUNCATE TABLE `parana_special_delivery_fees`;
INSERT INTO `parana_special_delivery_fees`
VALUES (1, 1, 1,
        '{\"address\":[{\"id\":1,\"pid\":0,\"name\":\"add1\"},{\"id\":2,\"pid\":1,\"name\":\"add2\"},{\"id\":3,\"pid\":2,\"name\":\"add3\"}]}',
        '{\"id\":1,\"pid\":0,\"name\":\"add1\",\"address\":[{\"id\":2,\"pid\":1,\"name\":\"add2\"},{\"id\":3,\"pid\":2,\"name\":\"add3\"}]}',
        null, 0, 1, 2, 0, null, null, null, null, null, null, null, null, null, 1, '2019-09-29 17:42:05',
        '2019-09-29 17:42:05', 'system');

-- #########################################  足迹  ##############################################

TRUNCATE TABLE `parana_footprint`;
INSERT INTO `parana_footprint`
VALUES (1, 1, 1, 10001, 1, 1, 1, null, '2019-07-26 06:17:04', '2019-07-27 06:17:07', '2019-07-27 06:17:09');

-- #########################################  收藏  ##############################################

TRUNCATE TABLE `parana_favorites`;
INSERT INTO `parana_favorites`
VALUES (1, '111', 1, 1, 1, 1, null, 10001, 1, null, '2019-08-02 09:40:44', '2019-08-05 19:11:01');


-- #########################################  审核  ##############################################

TRUNCATE TABLE `parana_audit`;
INSERT INTO parana_audit
VALUES (1, 1, 101, 1, 1, '2019-09-05 17:51:24', '2019-09-05 17:57:24', null);

-- #########################################  任务  ##############################################

TRUNCATE TABLE `parana_cron_job`;
INSERT INTO parana_cron_job
VALUES (1, 1, '1', null, 1, 1, null, '2019-09-09 07:46:36', '2019-09-05 17:52:26', '2019-09-05 17:57:22');
INSERT INTO parana_cron_job
VALUES (2, 1, '2', null, 1, 1, null, '2019-09-09 07:46:36', '2019-09-05 17:52:26', '2019-09-05 17:57:22');

-- #########################################  快照  ###############################################
TRUNCATE TABLE `parana_item_transcript`;
INSERT INTO `parana_item_transcript`
VALUES (1, 120000100012001, 0, 1, 'b2f5d2742cdb2ac61dc70150a3b65968',
        '{\"id\":120000100012001,\"tenantId\":1,\"categoryId\":3,\"name\":\"1558079271406\",\"mainImage\":\"https://www.baidu.com\",\"status\":-1,\"type\":1,\"skuAttributes\":[],\"otherAttributes\":[{\"group\":\"default\",\"otherAttributes\":[{\"attrKey\":\"属性1\",\"group\":\"值1\"},{\"attrKey\":\"属性2\",\"group\":\"值2\"},{\"attrKey\":\"属性3\",\"group\":\"值3\"},{\"attrKey\":\"属性4\",\"group\":\"值4\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"},{\"attrKey\":\"属性5\",\"group\":\"值5\"}]}],\"bitTag\":0,\"extra\":{\"categoryList\":\"[1,2,3]\"},\"md5Info\":\"b2f5d2742cdb2ac61dc70150a3b65968\",\"createdAt\":1558079271000,\"updatedAt\":1558079271000,\"updatedBy\":\"UnitTest\",\"spuId\":110000100012001,\"unit\":\"件\",\"businessType\":1,\"itemCode\":\"item_code\",\"extensionType\":0,\"shopId\":1,\"deliveryFeeTempId\":1,\"version\":0,\"virtual\":false,\"common\":true,\"service\":false,\"group\":false,\"skipInventorySourcing\":false,\"withoutEntity\":false}',
        '[{\"id\":120000100012001,\"tenantId\":1,\"skuCode\":\"sku_code\",\"barcode\":\"item_barcode\",\"outerId\":\"item_outer_id\",\"name\":\"1558079271406\",\"status\":-1,\"enable\":true,\"type\":1,\"bitTag\":0,\"createdAt\":1558079271000,\"updatedAt\":1558079271000,\"itemId\":110000100012001,\"extensionType\":0,\"businessType\":1,\"shopId\":1,\"originalPrice\":60,\"price\":50,\"version\":0,\"priceJson\":\"{\\\"originPrice\\\":60}\",\"updatedBy\":\"UnitTest\",\"virtual\":false,\"common\":true,\"service\":false,\"group\":false,\"skipInventorySourcing\":false,\"withoutEntity\":false,\"image\":\"https://www.baidu.com\"}]',
        '{\"tenantId\":1,\"pcDetail\":\"[{\\\"title\\\":\\\"标题1\\\",\\\"content\\\":\\\"123123\\\"},{\\\"title\\\":\\\"标题2\\\",\\\"content\\\":\\\"123123\\\"}]\",\"createdAt\":1558079271000,\"updatedAt\":1558079271000,\"itemId\":120000100012001}',
        '2019-08-21 14:41:11', '2019-08-21 14:41:11', 'UnitTest');

-- #########################################  配置  ###############################################
TRUNCATE TABLE `parana_item_configuration`;
INSERT INTO `parana_item_configuration`
VALUES (1000, 1, 'conf_key', 'conf_key_default');

-- #########################################  组合关系  ###############################################
TRUNCATE TABLE `parana_item_combination`;
INSERT INTO `parana_item_combination`
VALUES (1, 1, 110000100013001, null, 1, 110000100002004, null, 2, 1, 2, 1, '2019-08-13 10:06:21', '2019-08-13 10:06:21',
        '39');


-- #########################################  属性库  ###############################################
TRUNCATE TABLE `parana_public_attribute`;
INSERT INTO `parana_public_attribute`
VALUES (1, 0, 'preparedKey', '{\"USER_DEFINED\":\"true\",\"SEARCHABLE\":\"true\"}', NULL, NULL, 1,
        '2019-09-26 10:25:23',
        '2019-09-26 10:25:23', 'system', 1);

TRUNCATE TABLE `parana_attribute_category_binding`;
INSERT INTO `parana_attribute_category_binding`
VALUES (1, 1, 1, 'BASIC', 1, 1, '2019-09-26 10:38:50', '2019-09-26 10:38:50', 'system', 1);

-- #########################################  品牌  ###############################################
TRUNCATE TABLE `parana_brand`;
INSERT INTO `parana_brand` (id, outer_id, extension_type, name, unique_name, en_name, en_cap, logo, description,
                            extra_json, created_at, updated_at, updated_by, status)
VALUES (1, '123', 0, '端点', '端点', null, null,
        'https://parana.oss-cn-hangzhou.aliyuncs.com/test/dce41fd2-8a7f-4c61-8977-b7c8d9f0398b.png', null, null,
        '2019-07-26 14:04:30', '2019-07-26 14:04:30', '1', 1);

-- #########################################  关系  ###############################################
-- TRUNCATE TABLE `item_relation_index`;
-- TRUNCATE TABLE `item_relation_detail`;
--
-- INSERT INTO item_relation_detail (id, tenant_id, shop_id, relation_id, alias, item_id, extra_json, status, created_at,
--                                   updated_at)
-- VALUES (22, 1, 1, 22, '单测别名', 110000100012002, null, 1, '2019-09-29 11:29:19', '2019-09-29 11:29:19');
-- INSERT INTO item_relation_index (id, tenant_id, shop_id, name, status, extra_json, created_at, updated_at, updated_by)
-- VALUES (22, 1, 101, '单测创建系列', 1, null, '2019-09-29 11:29:20', '2019-09-29 11:29:19', '100001');
--
-- TRUNCATE TABLE `shop_relation`;
-- INSERT INTO shop_relation (id, source_shop_id, target_shop_id, created_at, updated_at, updated_by)
-- VALUES (10, 10001, 10002, '2018-08-14 15:59:47', '2018-08-14 15:59:47', '100001');

-- #########################################  类目扩展  ###############################################
TRUNCATE TABLE `parana_category_extra`;
INSERT INTO `parana_category_extra`
VALUES (1, 2, 'TAX', '税率', '税率内容', null, '2019-10-08 13:41:17', '2019-10-08 13:41:17', null);
INSERT INTO `parana_category_extra`
VALUES (2, 2, 'POLICY', '售后政策', null, '售后政策内容', '2019-10-08 13:41:17', '2019-10-08 13:41:17', null);

-- #########################################  关系域  ##############################################

-- 插入关系域数据
-- TRUNCATE TABLE `parana_feature`;
-- TRUNCATE TABLE `parana_relation_binding`;
-- TRUNCATE TABLE `parana_relation_index`;
-- TRUNCATE TABLE `parana_relation_detail`;
--
--
-- INSERT INTO parana_relation_binding
-- VALUES (2, 1, 'relation', '1', 'category', 1, 1, null, '2019-07-22 11:07:30',
--         '2019-07-22 11:07:30', 'UnitTest');
-- INSERT INTO `parana_relation_binding` (`id`, `tenant_id`, `source_type`, `source_mark`, `target_type`, `target_mark`, `status`, `extra_json`, `created_at`, `updated_at`, `updated_by`)
-- VALUES (3, 1, 'feature', 'test-code', 'category', 2, 1,
-- 	'{\"feature\":\"{\\\"id\\\":null,\\\"tenantId\\\":1,\\\"code\\\":\\\"test-code\\\",\\\"name\\\":\\\"test-Name已被修改\\\",\\\"content\\\":\\\"test-content已被修改\\\",\\\"description\\\":null,\\\"status\\\":null,\\\"extra\\\":{},\\\"createdAt\\\":null,\\\"updatedAt\\\":null,\\\"updatedBy\\\":\\\"justTest\\\"}\"}', '2020-01-02 18:15:56', '2020-01-02 18:15:56', 'justTest');
-- INSERT INTO `parana_feature` (`id`, `tenant_id`, `code`, `name`, `content`, `description`, `status`, `extra_json`, `created_at`, `updated_at`, `updated_by`)
-- VALUES (1, 1, 'tax', '税率', 'test-content', NULL, 1,
-- 	'{\"content_type\":\"test-contentType\",\"module_type\":\"test-moduleType\"}', '2020-01-02 18:32:53', '2020-01-02 18:32:53', 'justTest');
-- 后续删除，迁移至create单测
-- INSERT INTO parana_relation_binding
-- VALUES (2, 1, 'relation', 1, 'item', 1, 1, '{"test003": "ymk","test004": "10"}', '2019-07-22 11:07:30',
--         '2019-07-22 11:07:30', 'UnitTest');

-- 推荐组
-- INSERT INTO parana_relation_index
-- VALUES (1, 1, 1, 'recommend', 'item-recommend', 'item', '日用品', '生活用品', 1, '{"test005": "ymk","test006": "10"}',
--         '2019-07-22 11:07:30',
--         '2019-07-22 11:07:30', 'UnitTest');
-- INSERT INTO parana_relation_detail
-- VALUES (1, 1, 1, 1, 'recommend', 'item-recommend', 'item', 110000100012001, '{"test007": "ymk","alias": null}',
--         '2019-07-22 11:07:30',
--         '2019-07-22 11:07:30', 'UnitTest');
-- INSERT INTO parana_relation_detail
-- VALUES (2, 1, 1, 1, 'recommend', 'item-recommend', 'item', 110000100012002, '{"test007": "ymk","alias": null}',
--         '2019-07-22 11:07:30',
--         '2019-07-22 11:07:30', 'UnitTest');
-- 系列
-- INSERT INTO parana_relation_index
-- VALUES (2, 1, 1, 'relation', 'relation', 'item', '一个测试系列', '系列别名', 1, '{"test005": "ymk","test006": "10"}',
--         '2019-07-22 11:07:30',
--         '2019-07-22 11:07:30', 'UnitTest');
-- INSERT INTO parana_relation_detail
-- VALUES (3, 1, 2, 1, 'relation', 'relation', 'item', 110000100012001, '{"test007": "ymk","alias": null}',
--         '2019-07-22 11:07:30',
--         '2019-07-22 11:07:30', 'UnitTest');

-- OPEN
TRUNCATE TABLE `parana_open_configuration`;
INSERT INTO `parana_open_configuration`
VALUES (10001, 1, '6a934e68057d4b1890a010c66217cf4b', 'c8f26f49fcff0d67f0f48f2116c87403', null, '2019-11-13 11:03:41',
        '2019-11-13 11:03:43', 'xlt');

TRUNCATE TABLE `parana_category_attribute`;
INSERT INTO `parana_category_attribute`
VALUES (1, 1, 'BASIC', '尺寸', 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest'),
       (2, 1, 'BASIC', '大小', 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest'),
       (3, 1, 'BASIC', '重量', 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest'),
       (4, 1, 'BASIC', '颜色', 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest'),
       (5, 1, 'BASIC', '产地', 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest'),
       (6, 1, 'BASIC', '材质', 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest');

TRUNCATE TABLE `parana_category_attribute_binding`;
INSERT INTO `parana_category_attribute_binding`
VALUES (1, 1, 1, 1, 'BASIC', '尺寸', 1, 0, 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest'),
       (2, 1, 2, 2, 'BASIC', '大小', 2, 0, 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest'),
       (3, 1, 2, 3, 'BASIC', '重量', 3, 0, 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest'),
       (4, 1, 3, 4, 'BASIC', '颜色', 4, 0, 1, 0, 0, 0, 0, 0, 0, NULL, NULL, now(), now(), 'UnitTest');

TRUNCATE TABLE `parana_category_attribute_unbinding`;
INSERT INTO `parana_category_attribute_unbinding`
VALUES (1, 3, 2, now(), now(), 'UnitTest');

TRUNCATE TABLE `parana_category_attribute_group`;
INSERT INTO `parana_category_attribute_group` (`id`, `name`, `order`, `preset`, `tenant_id`, `created_at`, `updated_at`, `updated_by`)
VALUES
(1, '基本属性', 1, 1, 1, '2020-04-26 12:06:54', '2020-04-26 12:06:54', 'system');

-- TRUNCATE TABLE `parana_complaint`;
-- INSERT INTO `parana_complaint` (`id`, `tenant_id`, `user_id`, `target`, `type`, `category`, `title`, `content`, `image_url`, `phone`,
--             `process_time`, `process_content`, `status`, `extra_json`, `created_at`, `updated_at`, `updated_by`)
-- VALUES (1, 1, 1, 1, 4, 99, '投诉一下试试', '我只是想投诉一下，别在意', '[]', '17858263035',
-- 	        NULL, NULL, 0, NULL, '2020-04-29 09:45:10', '2020-04-29 09:45:10', 'test'),
-- 	     (2, 1, 1, 1, 2, null, '一个建议', '真心的提一个建议（省略100字...）', '[]', '17858263035',
-- 	        '2020-04-29 09:56:03', '您所提交的建议已处理', 1, NULL, '2020-04-29 09:45:10', '2020-04-29 09:45:10', 'test');

TRUNCATE TABLE `vendor_partnership`;
INSERT INTO `vendor_partnership` (`id`, `vendor_id`, `operator_id`, `cooperation_mode`, `logistics_mode`, `warehouse_code`, `status`, `created_at`, `updated_at`, `updated_by`)
VALUES
	(1, 1, 10001, 1, 1, 0, 1, '2020-08-11 15:59:27', '2020-08-11 15:59:27', 'test');

TRUNCATE TABLE `area_item`;
TRUNCATE TABLE `area_sku`;