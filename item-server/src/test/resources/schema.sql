CREATE TABLE IF NOT EXISTS `area_item`
(
  `id`                        bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `item_id`                   bigint(20) NOT NULL COMMENT '供应商商品id',
  `vendor_id`                 bigint(20) NOT NULL COMMENT '供应商id',
  `operator_id`               bigint(20) NOT NULL COMMENT '运营商id（区域）',
  `name`                      varchar(1024)       DEFAULT NULL COMMENT '商品名',
  `main_image`                varchar(1024)       DEFAULT NULL COMMENT '主图地址',
  `delivery_fee_temp_id`      bigint(20) NOT NULL COMMENT '运费模板id',
  `support_return`            tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否支持退货',
  `is_nationwide_agency_item` tinyint(4) NOT NULL COMMENT '是否国代商品',
  `hq_operator_status`        tinyint(4) NOT NULL COMMENT '总部运营状态（优先度1）',
  `vendor_status`             tinyint(4) NOT NULL COMMENT '供应商状态（优先度2）',
  `area_operator_status`      tinyint(4) NOT NULL COMMENT '区域运营状态（优先度3）',
  `status`                    tinyint(4) NOT NULL COMMENT '最终状态（由总部运营状态、供应商状态、区域运营状态共同决定）',
  `created_at`                datetime   NOT NULL COMMENT '创建时间',
  `updated_at`                datetime   NOT NULL COMMENT '最后更新时间',
  `updated_by`                varchar(32)         DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_item` (`item_id`),
  KEY `idx_operator` (`operator_id`),
  KEY `idx_vendor` (`vendor_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='区域商品表';

CREATE TABLE IF NOT EXISTS `area_sku`
(
  `id`                   bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `area_item_id`         bigint(20)  NOT NULL COMMENT '区域商品id',
  `item_id`              bigint(20)  NOT NULL COMMENT '供应商商品id',
  `sku_id`               bigint(20)  NOT NULL COMMENT '供应商skuId',
  `sku_code`             varchar(64) NOT NULL DEFAULT '' COMMENT '供应商sku编码',
  `vendor_id`            bigint(20)  NOT NULL COMMENT '供应商id',
  `operator_id`          bigint(20)  NOT NULL COMMENT '运营商id（区域/总部）',
  `name`                 varchar(1024)        DEFAULT NULL COMMENT '商品名',
  `image`                varchar(1024)        DEFAULT NULL COMMENT '主图地址',
  `channel_price_json`   varchar(1024)        DEFAULT '' COMMENT '渠道价json',
  `default_price`        bigint(20)  NOT NULL COMMENT '默认价',
  `base_price`           bigint(20)  NOT NULL COMMENT '底价',
  `p_commission`         int(11)     NOT NULL COMMENT '一级分佣',
  `commission`           int(11)     NOT NULL COMMENT '二级分佣',
  `hq_operator_status`   tinyint(4)  NOT NULL COMMENT '总部运营状态（优先度1）',
  `vendor_status`        tinyint(4)  NOT NULL COMMENT '供应商状态（优先度2）',
  `area_operator_status` tinyint(4)  NOT NULL COMMENT '区域运营状态（优先度3）',
  `status`               tinyint(4)  NOT NULL COMMENT '最终状态（由总部运营状态、供应商状态、区域运营状态共同决定）',
  `cooperation_mode`     tinyint(4)  NOT NULL COMMENT '合作模式',
  `logistics_mode`       tinyint(4)  NOT NULL COMMENT '物流模式',
  `min_quantity`         int(11)     NOT NULL COMMENT '起售数量',
  `created_at`           datetime    NOT NULL COMMENT '创建时间',
  `updated_at`           datetime    NOT NULL COMMENT '最后更新时间',
  `updated_by`           varchar(32)          DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_area_sku` (`operator_id`, `sku_id`),
  KEY `idx_area_item` (`area_item_id`),
  KEY `idx_item` (`item_id`),
  KEY `idx_operator` (`operator_id`),
  KEY `idx_sku` (`sku_id`),
  KEY `idx_vendor` (`vendor_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='区域SKU表';

CREATE TABLE IF NOT EXISTS `index_item`
(
  `id`                 bigint(20) unsigned NOT NULL COMMENT '主键id',
  `spu_id`             bigint(20)    DEFAULT NULL COMMENT 'spu id',
  `tenant_id`          bigint(20)    DEFAULT NULL COMMENT '租户id',
  `category_id`        bigint(20)    DEFAULT NULL COMMENT '类目id',
  `item_code`          varchar(45)   DEFAULT NULL COMMENT '商品编码',
  `shop_id`            bigint(20)    DEFAULT NULL COMMENT '店铺id',
  `shop_name`          varchar(128)  DEFAULT NULL COMMENT '店铺名',
  `brand_id`           bigint(20)    DEFAULT NULL COMMENT '品牌id',
  `brand_name`         varchar(128)  DEFAULT NULL COMMENT '品牌名',
  `name`               varchar(1024) DEFAULT NULL COMMENT '商品名',
  `main_image`         varchar(512)  DEFAULT NULL COMMENT '主图地址',
  `status`             tinyint(1)    DEFAULT NULL COMMENT '商品状态：1(上架),-1(下架),-2(冻结),-3(删除)',
  `audit_status`       tinyint(1)    DEFAULT NULL COMMENT '审核状态',
  `type`               smallint(6)   DEFAULT NULL COMMENT '商品类型',
  `business_type`      smallint(6)   DEFAULT NULL COMMENT '业务类型',
  `sku_attributes`     varchar(4096) DEFAULT NULL COMMENT 'sku销售属性集合',
  `other_attributes`   varchar(4096) DEFAULT NULL COMMENT '其它属性',
  `high_price`         int(11)       DEFAULT NULL COMMENT '最高价',
  `low_price`          bigint(20)    DEFAULT NULL COMMENT '最低价',
  `updated_at`         datetime      DEFAULT NULL COMMENT '最后更新时间',
  `extra_json`         text COMMENT '其它内容',
  `sale_quantity`      bigint(20)    DEFAULT '0' COMMENT '销量',
  `in_stock`           smallint(6)   DEFAULT '0' COMMENT '是否有货',
  `shop_category_json` varchar(1024) DEFAULT NULL COMMENT '店铺类目',
  `bit_tag`            bigint(20)    DEFAULT NULL COMMENT '商品位标',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE IF NOT EXISTS `index_sku`
(
  `id`               bigint(20) unsigned NOT NULL COMMENT '主键id',
  `tenant_id`        smallint(6)   DEFAULT NULL COMMENT '租户id',
  `item_id`          bigint(20)    DEFAULT NULL COMMENT '商品id',
  `sku_template_id`  bigint(20)    DEFAULT NULL COMMENT 'sku模板id',
  `sku_code`         varchar(40)   DEFAULT NULL COMMENT 'SKU 编码 (标准库存单位编码)',
  `barcode`          varchar(32)   DEFAULT NULL COMMENT '商品条码',
  `outer_id`         varchar(40)   DEFAULT NULL COMMENT '外部id',
  `shop_id`          bigint(20)    DEFAULT NULL COMMENT '店铺id',
  `name`             varchar(1024) DEFAULT NULL COMMENT '名称',
  `image`            varchar(512)  DEFAULT NULL COMMENT '主图地址',
  `price`            bigint(20)    DEFAULT NULL COMMENT '实际售卖价格',
  `original_price`   int(11)       DEFAULT NULL COMMENT '定价',
  `status`           tinyint(1)    DEFAULT NULL COMMENT 'sku状态, 1: 上架, -1:下架, -2:冻结, -3:删除',
  `audit_status`     tinyint(1)    DEFAULT NULL COMMENT '审核状态',
  `type`             smallint(6)   DEFAULT NULL COMMENT '商品类型',
  `business_type`    smallint(6)   DEFAULT NULL COMMENT '业务类型',
  `updated_at`       datetime      DEFAULT NULL COMMENT '最后更新时间',
  `extra_json`       varchar(1024) DEFAULT NULL COMMENT 'sku额外信息',
  `bit_tag`          bigint(20)    DEFAULT NULL COMMENT '商品位标',
  `sku_attributes`   varchar(4096) DEFAULT NULL,
  `other_attributes` varchar(4096) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE IF NOT EXISTS `index_spu`
(
  `id`               bigint(20) unsigned NOT NULL COMMENT '主键id',
  `tenant_id`        bigint(20)    DEFAULT NULL COMMENT '租户id',
  `outer_id`         varchar(1024) DEFAULT NULL COMMENT '外部id',
  `spu_code`         varchar(45)   DEFAULT NULL COMMENT 'spu编码',
  `brand_id`         bigint(20)    DEFAULT NULL COMMENT '品牌id',
  `brand_name`       varchar(128)  DEFAULT NULL COMMENT '品牌名称',
  `name`             varchar(256)  DEFAULT NULL COMMENT '名称',
  `main_image`       varchar(128)  DEFAULT NULL COMMENT '主图',
  `status`           tinyint(1)    DEFAULT NULL COMMENT '状态',
  `sku_attributes`   varchar(4096) DEFAULT NULL COMMENT 'sku销售属性集合',
  `other_attributes` varchar(4096) DEFAULT NULL COMMENT '其它属性',
  `updated_at`       datetime      DEFAULT NULL COMMENT '更新时间',
  `extra_json`       varchar(1024) DEFAULT NULL COMMENT 'extra属性Json',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE IF NOT EXISTS `industry`
(
  `id`         bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name`       varchar(255) NOT NULL COMMENT '行业名称',
  `pc_url`     varchar(1024) DEFAULT NULL COMMENT 'PC域名',
  `wap_url`    varchar(1024) DEFAULT NULL COMMENT 'WAP域名',
  `created_at` datetime     NOT NULL COMMENT '创建时间',
  `updated_at` datetime     NOT NULL COMMENT '最后更新时间',
  `updated_by` varchar(32)   DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='行业信息表';

CREATE TABLE IF NOT EXISTS `item_quoted_price`
(
  `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `log_id`            bigint(20) NOT NULL COMMENT '报价日志id',
  `vendor_id`         bigint(20) NOT NULL COMMENT '供应商id',
  `operator_id`       bigint(20) NOT NULL COMMENT '运营商id（区域）',
  `item_id`           bigint(20) NOT NULL COMMENT '供应商商品id',
  `sku_id`            bigint(20) NOT NULL COMMENT '供应商skuId',
  `sku_code`          bigint(20) NOT NULL COMMENT '供应商编码',
  `channel_id`        bigint(20) NOT NULL COMMENT '渠道id',
  `base_price`        bigint(20) NOT NULL COMMENT '底价',
  `channel_price`     bigint(20) NOT NULL COMMENT '售价',
  `p_commission_rate` tinyint(4) NOT NULL COMMENT '上级返佣比例',
  `commission_rate`   tinyint(4) NOT NULL COMMENT '返佣比例',
  `cooperation_mode`  tinyint(4) NOT NULL COMMENT '合作模式',
  `created_at`        datetime   NOT NULL COMMENT '创建时间',
  `updated_at`        datetime   NOT NULL COMMENT '最后更新时间',
  `updated_by`        varchar(32) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_channel_id` (`channel_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_vendor` (`vendor_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品报价表';

CREATE TABLE IF NOT EXISTS `item_quoted_price_log`
(
  `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `log_id`            bigint(20) NOT NULL COMMENT '报价日志id',
  `vendor_id`         bigint(20) NOT NULL COMMENT '供应商id',
  `operator_id`       bigint(20) NOT NULL COMMENT '运营商id（区域）',
  `item_id`           bigint(20) NOT NULL COMMENT '供应商商品id',
  `sku_id`            bigint(20) NOT NULL COMMENT '供应商skuId',
  `sku_code`          bigint(20) NOT NULL COMMENT '供应商编码',
  `channel_id`        bigint(20) NOT NULL COMMENT '渠道id',
  `base_price`        bigint(20) NOT NULL COMMENT '底价',
  `channel_price`     bigint(20) NOT NULL COMMENT '售价',
  `p_commission_rate` tinyint(4) NOT NULL COMMENT '上级返佣比例',
  `commission_rate`   tinyint(4) NOT NULL COMMENT '返佣比例',
  `cooperation_mode`  tinyint(4) NOT NULL COMMENT '合作模式',
  `created_at`        datetime   NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_channel_id` (`channel_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_vendor` (`vendor_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品报价日志表';

CREATE TABLE IF NOT EXISTS `item_relation_detail`
(
  `id`          bigint(20)    NOT NULL AUTO_INCREMENT,
  `tenant_id`   int(10)       DEFAULT NULL,
  `shop_id`     bigint(20)    NOT NULL,
  `relation_id` bigint(20)    NOT NULL,
  `alias`       varchar(1024) NOT NULL,
  `item_id`     bigint(20)    NOT NULL,
  `extra_json`  varchar(2000) DEFAULT NULL,
  `status`      tinyint(1)    DEFAULT NULL COMMENT '状态 0:禁用 1:可用',
  `created_at`  datetime      NOT NULL,
  `updated_at`  datetime      NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_shopid_itemid` (`shop_id`, `item_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品系列关系详情';

CREATE TABLE IF NOT EXISTS `parana_attribute_category_binding`
(
  `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `category_id`  bigint(20)          NOT NULL COMMENT '类目id',
  `attribute_id` bigint(20)          NOT NULL COMMENT '公共属性id',
  `group`        varchar(20)         NOT NULL COMMENT '所属组名',
  `index`        int(20)     DEFAULT NULL COMMENT '顺序编号',
  `status`       tinyint(1)          NOT NULL COMMENT '状态：1使用中，-1未使用，-3删除',
  `created_at`   datetime            NOT NULL COMMENT '创建时间',
  `updated_at`   datetime            NOT NULL COMMENT '修改时间',
  `updated_by`   varchar(32) DEFAULT NULL COMMENT '操作用户id',
  `tenant_id`    int(20)     DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_pacb_binding` (`category_id`, `attribute_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='属性类目关联表';

CREATE TABLE IF NOT EXISTS `parana_audit`
(
  `id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id`  bigint(20)  DEFAULT NULL COMMENT '租户id',
  `audit_type` int(10)    NOT NULL COMMENT '审核类型',
  `target_id`  bigint(20) NOT NULL COMMENT '目标id',
  `status`     tinyint(1)  DEFAULT NULL COMMENT '状态',
  `created_at` datetime   NOT NULL COMMENT '创建时间',
  `updated_at` datetime   NOT NULL COMMENT '更新时间',
  `updated_by` varchar(32) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_type_id` (`audit_type`, `target_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='申请审核表';

CREATE TABLE IF NOT EXISTS `parana_back_category`
(
  `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `extension_type` smallint(6) DEFAULT '0' COMMENT '扩展类型',
  `pid`            bigint(20)          NOT NULL COMMENT '父级id',
  `name`           varchar(50)         NOT NULL COMMENT '名称',
  `level`          tinyint(1)          NOT NULL COMMENT '级别',
  `status`         tinyint(1)          NOT NULL COMMENT '状态,1启用,0删除',
  `has_children`   tinyint(1)          NOT NULL COMMENT '是否有孩子',
  `has_spu`        tinyint(1)          NOT NULL COMMENT '是否有spu关联',
  `tenant_id`      int(20)     DEFAULT NULL COMMENT '租户Id',
  `created_at`     datetime            NOT NULL,
  `updated_at`     datetime            NOT NULL,
  `updated_by`     varchar(32) DEFAULT NULL COMMENT '操作用户id',
  PRIMARY KEY (`id`),
  KEY `idx_back_categories_pid` (`pid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='后台类目表';

CREATE TABLE IF NOT EXISTS `parana_brand`
(
  `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `outer_id`       varchar(256) DEFAULT NULL COMMENT '外部 id',
  `extension_type` smallint(6)  DEFAULT '0' COMMENT '扩展类型',
  `name`           varchar(100) DEFAULT NULL COMMENT '名称',
  `unique_name`    varchar(100) DEFAULT NULL COMMENT '唯一小写名',
  `en_name`        varchar(100) DEFAULT NULL COMMENT '英文名称',
  `en_cap`         char(1)      DEFAULT NULL COMMENT '首字母',
  `logo`           varchar(128) DEFAULT NULL COMMENT '品牌logo',
  `description`    varchar(200) DEFAULT NULL COMMENT '描述',
  `created_at`     datetime     DEFAULT NULL,
  `updated_at`     datetime     DEFAULT NULL,
  `updated_by`     varchar(32)  DEFAULT NULL,
  `status`         tinyint(2)   DEFAULT '1' COMMENT '状态,1启用,-1禁用',
  `extra_json`     varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_brands_en_cap` (`en_cap`),
  KEY `idx_brands_en_name` (`en_name`),
  KEY `idx_brands_name` (`name`),
  KEY `idx_brands_unique_name` (`unique_name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='品牌表';

CREATE TABLE IF NOT EXISTS `parana_category_attribute`
(
  `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id`    smallint(6)                  DEFAULT NULL COMMENT '租户ID',
  `group`        varchar(64)                  DEFAULT NULL COMMENT '所属的属性组',
  `name`         varchar(64)         NOT NULL COMMENT '属性名',
  `status`       tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态',
  `required`     tinyint(1)          NOT NULL DEFAULT '0' COMMENT '必填标识：0否，1是',
  `salable`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '可售标识：0否，1是',
  `customizable` tinyint(1)          NOT NULL DEFAULT '0' COMMENT '可自定义标识：0否，1是',
  `searchable`   tinyint(1)          NOT NULL DEFAULT '0' COMMENT '可搜索标识：0否，1是',
  `displayable`  tinyint(1)          NOT NULL DEFAULT '0' COMMENT '前台展示标识：0否，1是',
  `value_type`   tinyint(4)          NOT NULL COMMENT '属性值类型',
  `value_json`   varchar(4096)                DEFAULT NULL COMMENT '存储的属性的值JSON数组',
  `extra_json`   varchar(2048)                DEFAULT NULL COMMENT '扩展字段',
  `created_at`   datetime            NOT NULL COMMENT '创建时间',
  `updated_at`   datetime            NOT NULL COMMENT '修改时间',
  `updated_by`   varchar(32)                  DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`),
  KEY `idx_ppa_name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='类目元属性';

CREATE TABLE IF NOT EXISTS `parana_category_attribute_binding`
(
  `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id`    smallint(6)                  DEFAULT NULL COMMENT '租户ID',
  `category_id`  bigint(20)          NOT NULL COMMENT '类目id',
  `attribute_id` bigint(20)          NOT NULL COMMENT '属性id',
  `group`        varchar(64)         NOT NULL COMMENT '所属的属性组',
  `name`         varchar(64)         NOT NULL COMMENT '所属的属性组',
  `order`        int(20)                      DEFAULT NULL COMMENT '排序号',
  `override`     tinyint(1)          NOT NULL COMMENT '复写属性标识：0否，1是',
  `status`       tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态：1启用，-1禁用，-3删除',
  `required`     tinyint(1)          NOT NULL DEFAULT '0' COMMENT '必填标识：0否，1是',
  `salable`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '可售标识：0否，1是',
  `customizable` tinyint(1)          NOT NULL DEFAULT '0' COMMENT '可自定义标识：0否，1是',
  `searchable`   tinyint(1)          NOT NULL DEFAULT '0' COMMENT '可搜索标识：0否，1是',
  `displayable`  tinyint(1)          NOT NULL DEFAULT '0' COMMENT '前台展示标识：0否，1是',
  `value_type`   tinyint(4)          NOT NULL COMMENT '属性值类型',
  `value_json`   varchar(4096)                DEFAULT NULL COMMENT '存储的属性的值JSON数组',
  `extra_json`   varchar(2048)                DEFAULT NULL COMMENT '扩展字段',
  `created_at`   datetime            NOT NULL COMMENT '创建时间',
  `updated_at`   datetime            NOT NULL COMMENT '修改时间',
  `updated_by`   varchar(32)                  DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`),
  KEY `idx_pacb_binding` (`category_id`, `attribute_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='类目与属性关联';

CREATE TABLE IF NOT EXISTS `parana_category_attribute_group`
(
  `id`         int(11)     NOT NULL AUTO_INCREMENT,
  `name`       varchar(32) NOT NULL COMMENT '属性组名称',
  `order`      smallint(6) NOT NULL COMMENT '排序',
  `preset`     tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否预置',
  `tenant_id`  smallint(6) NOT NULL,
  `created_at` datetime    NOT NULL,
  `updated_at` datetime    NOT NULL,
  `updated_by` varchar(32) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unq_name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='属性分组';

CREATE TABLE IF NOT EXISTS `parana_category_attribute_unbinding`
(
  `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `category_id`  bigint(20)          NOT NULL COMMENT '后台类目id',
  `attribute_id` bigint(20)          NOT NULL COMMENT '属性id',
  `created_at`   datetime            NOT NULL,
  `updated_at`   datetime    DEFAULT NULL,
  `updated_by`   varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_attribute_category` (`attribute_id`, `category_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='类目属性拒绝继承关系';

CREATE TABLE IF NOT EXISTS `parana_category_binding`
(
  `id`                bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `front_category_id` bigint(20)  DEFAULT NULL COMMENT '前台叶子类目id',
  `back_category_id`  bigint(20)  DEFAULT NULL COMMENT '后台叶子类目id',
  `created_at`        datetime    DEFAULT NULL,
  `updated_at`        datetime    DEFAULT NULL,
  `updated_by`        varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='前后台叶子类目映射表';

CREATE TABLE IF NOT EXISTS `parana_category_extra`
(
  `id`          bigint(20) NOT NULL AUTO_INCREMENT,
  `category_id` bigint(20) NOT NULL COMMENT 'back category id',
  `type`        varchar(32) DEFAULT NULL COMMENT '类型',
  `name`        varchar(32) DEFAULT NULL COMMENT '名称',
  `content`     varchar(64) DEFAULT NULL COMMENT '内容',
  `big_content` text COMMENT '大内容',
  `created_at`  datetime    DEFAULT NULL,
  `updated_at`  datetime    DEFAULT NULL,
  `updated_by`  varchar(32) DEFAULT NULL COMMENT '操作用户id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_type_category` (`category_id`, `type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='类目扩展表';

CREATE TABLE IF NOT EXISTS `parana_category_extra_v2`
(
  `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `category_id`    bigint(20)          NOT NULL COMMENT '后台类目id',
  `extension_type` smallint(6)         NOT NULL DEFAULT '0' COMMENT '扩展类型',
  `status`         tinyint(4)          NOT NULL DEFAULT '1' COMMENT '状态,1:启用,-1:禁用,-3:删除',
  `name`           varchar(64)                  DEFAULT NULL COMMENT '名称',
  `description`    varchar(128)                 DEFAULT NULL COMMENT '描述信息',
  `content`        text COMMENT '具体内容',
  `extra_json`     varchar(512)                 DEFAULT NULL COMMENT '扩展信息',
  `created_at`     datetime            NOT NULL COMMENT '创建时间',
  `updated_at`     datetime            NOT NULL COMMENT '更新时间',
  `updated_by`     varchar(64)         NOT NULL COMMENT '更新',
  PRIMARY KEY (`id`),
  KEY `idx_parana_category_extra` (`category_id`, `extension_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='类目扩展v2';

CREATE TABLE IF NOT EXISTS `parana_category_recommend`
(
  `id`               bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `back_category_id` bigint(20) DEFAULT NULL COMMENT '后台类目id',
  `recommend_id`     bigint(20) DEFAULT NULL COMMENT '推荐组id',
  `created_at`       datetime   DEFAULT NULL,
  `updated_at`       datetime   DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_category_id` (`back_category_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='后台类目推荐表';

CREATE TABLE IF NOT EXISTS `parana_channel`
(
  `id`             bigint(20)   NOT NULL AUTO_INCREMENT,
  `pid`            bigint(20)   DEFAULT NULL COMMENT '上级id\n',
  `level`          int(11)      NOT NULL COMMENT '级别',
  `name`           varchar(128) NOT NULL COMMENT '渠道名称',
  `status`         tinyint(4)   NOT NULL COMMENT '渠道状态，0:关闭,1:开启',
  `discount`       smallint(6)  DEFAULT NULL COMMENT '渠道折扣，取值范围：0-100',
  `source_link`    varchar(128) DEFAULT NULL COMMENT '渠道连接',
  `token`          varchar(128) DEFAULT NULL COMMENT '令牌',
  `take_effect_at` datetime     DEFAULT NULL COMMENT '生效开始时间',
  `lose_effect_at` datetime     DEFAULT NULL COMMENT '失效时间',
  `created_at`     datetime     NOT NULL,
  `updated_at`     datetime     NOT NULL,
  `updated_by`     varchar(32)  DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_channel_name` (`name`),
  KEY `idx_channel_token` (`token`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='渠道';

CREATE TABLE IF NOT EXISTS `parana_comment`
(
  `id`          bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id`   smallint(6)          DEFAULT NULL COMMENT '租户id',
  `type`        smallint(6) NOT NULL COMMENT '评价类型，订单:1，文章:2',
  `target_id`   bigint(11)           DEFAULT NULL COMMENT '目标id，来源域控制',
  `top_id`      int(11)              DEFAULT NULL COMMENT '根节点id',
  `parent_id`   int(11)              DEFAULT NULL COMMENT '父节点',
  `user_id`     bigint(20)  NOT NULL COMMENT '用户id',
  `user_type`   tinyint(4)  NOT NULL COMMENT '用户类型',
  `rate`        smallint(6)          DEFAULT NULL COMMENT '评分，非必要',
  `context`     varchar(1024)        DEFAULT NULL COMMENT '买家评论',
  `image_json`  varchar(1024)        DEFAULT NULL COMMENT '评论图片',
  `status`      smallint(3) NOT NULL COMMENT '状态',
  `has_pursue`  tinyint(1)           DEFAULT '0',
  `pass_count`  smallint(6) NOT NULL DEFAULT '0',
  `index_json`  varchar(1024)        DEFAULT NULL COMMENT 'es索引内容',
  `extra_json`  varchar(1024)        DEFAULT NULL,
  `item_md5`    varchar(64)          DEFAULT NULL COMMENT '商品的md5值',
  `created_at`  datetime    NOT NULL,
  `updated_at`  datetime    NOT NULL,
  `updated_by`  varchar(32) NOT NULL,
  `operator_id` bigint(20)           DEFAULT NULL COMMENT '区域运营id',
  `orderId`     bigint(20)           DEFAULT NULL COMMENT '订单id\n',
  `orderLineId` bigint(20)           DEFAULT NULL COMMENT '订单行id',
  PRIMARY KEY (`id`),
  KEY `idx_type_target` (`type`, `target_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='评价表';

CREATE TABLE IF NOT EXISTS `parana_contract`
(
  `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '合同主键',
  `contract_name`   varchar(200)  DEFAULT NULL COMMENT '合同名称',
  `contract_no`     varchar(50)   DEFAULT NULL COMMENT '合同编号',
  `partion_id`      bigint(20)    DEFAULT NULL COMMENT '项目主键',
  `partion_name`    varchar(100)  DEFAULT NULL COMMENT '项目名称',
  `tenant_code`     varchar(200)  DEFAULT NULL COMMENT '租户号',
  `tenant_name`     varchar(200)  DEFAULT NULL COMMENT '租户名称',
  `effective_date`  date          DEFAULT NULL COMMENT '生效日期',
  `cutoff_date`     date          DEFAULT NULL COMMENT '截止日期',
  `business_type`   smallint(6)   DEFAULT NULL COMMENT '合同类型 1:自营 2:代销 3:专柜 4:租赁 5:联营 6:物业 7:服务',
  `contract_status` smallint(6)   DEFAULT NULL COMMENT '状态 10:新增 20:修改 30:确认 40:终止',
  `remark`          varchar(1000) DEFAULT NULL COMMENT '备注',
  `version`         int(11)       DEFAULT '0' COMMENT '版本',
  `created_at`      datetime   NOT NULL COMMENT '创建时间',
  `updated_at`      datetime   NOT NULL COMMENT '最后更新时间',
  `updated_by`      varchar(32)   DEFAULT NULL COMMENT '更新者',
  `status`          tinyint(1)    DEFAULT '1' COMMENT '1:正常,-3:删除(默认1)',
  `extra_json`      varchar(2048) DEFAULT '' COMMENT '扩展字段',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='合同信息';

CREATE TABLE IF NOT EXISTS `parana_cron_job`
(
  `id`           bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id`    bigint(20)  NOT NULL COMMENT '租户ID',
  `target_code`  varchar(64) NOT NULL COMMENT '目标code',
  `execute_json` varchar(64) DEFAULT NULL COMMENT '执行参数',
  `type`         int(10)     NOT NULL COMMENT '类型',
  `status`       tinyint(1)  NOT NULL COMMENT '状态',
  `log`          text COMMENT '日志',
  `execute_at`   datetime    DEFAULT NULL COMMENT '执行时间',
  `created_at`   datetime    NOT NULL COMMENT '创建时间',
  `updated_at`   datetime    NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_target_code_type` (`target_code`, `type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='定时任务表';

CREATE TABLE IF NOT EXISTS `parana_delivery_fee_templates`
(
  `id`                 bigint(20)  NOT NULL AUTO_INCREMENT,
  `shop_id`            bigint(20)  NOT NULL COMMENT '店铺id',
  `name`               varchar(64) NOT NULL COMMENT '模板名称',
  `is_free`            tinyint(4)  NOT NULL COMMENT '是否包邮',
  `is_freight_collect` tinyint(4)  NOT NULL COMMENT '是否到付',
  `amount_limit`       int(11)              DEFAULT NULL COMMENT '包邮时重量约束',
  `deliver_method`     tinyint(4)  NOT NULL COMMENT '运送方式:1-快递,2-EMS,3-平邮',
  `charge_method`      tinyint(4)  NOT NULL COMMENT '计价方式:1-按计量单位,2-固定运费',
  `fee`                int(11)              DEFAULT NULL COMMENT '运费,当计价方式为固定运费时使用',
  `low_price`          int(11)              DEFAULT NULL COMMENT '订单不满该金额时，运费为lowFee',
  `low_fee`            int(11)              DEFAULT NULL COMMENT '订单不满low_price时，运费为lowFee',
  `high_price`         int(11)              DEFAULT NULL COMMENT '订单高于该金额时，运费为highFee',
  `high_fee`           int(11)              DEFAULT NULL COMMENT '订单高于high_price时，运费为highFee',
  `middle_fee`         int(11)              DEFAULT NULL COMMENT '订单价格在lowFee，highFee之间时，运费为middleFee',
  `init_amount`        int(11)              DEFAULT NULL COMMENT '首费数量',
  `init_fee`           int(11)              DEFAULT NULL COMMENT '首费金额',
  `incr_amount`        int(11)              DEFAULT NULL COMMENT '增费数量',
  `incr_fee`           int(11)              DEFAULT NULL COMMENT '曾费金额',
  `is_default`         tinyint(1)           DEFAULT NULL COMMENT '是否是默认模板',
  `status`             tinyint(4)  NOT NULL DEFAULT '1' COMMENT '状态',
  `created_at`         datetime    NOT NULL,
  `updated_at`         datetime    NOT NULL,
  `updated_by`         varchar(32)          DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_delivery_fee_template_shop_id` (`shop_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='运费模板';

CREATE TABLE IF NOT EXISTS `parana_favorites`
(
  `id`              bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `unique_key`      varchar(128) NOT NULL COMMENT '收藏唯一标识',
  `tenant_id`       bigint(20)        DEFAULT NULL COMMENT '租户id',
  `operator_id`     bigint(20)   NOT NULL COMMENT '区域运营id',
  `target_id`       bigint(20)        DEFAULT NULL COMMENT '收藏目标id',
  `target_type`     tinyint(1)        DEFAULT NULL COMMENT '收藏目标类型',
  `target_sub_type` tinyint(1)        DEFAULT NULL COMMENT '收藏目标子类型',
  `user_id`         bigint(20)        DEFAULT NULL COMMENT '用户id',
  `status`          tinyint(1)        DEFAULT NULL COMMENT '状态 1:正常 -3:删除',
  `extra_json`      text COMMENT 'extra属性Json',
  `created_at`      timestamp    NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at`      timestamp    NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_idx_key` (`unique_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='收藏表';

CREATE TABLE IF NOT EXISTS `parana_feature`
(
  `id`          bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id`   int(10)       DEFAULT NULL COMMENT '租户id',
  `type`        varchar(32)   DEFAULT NULL COMMENT '实体类型',
  `name`        varchar(128) NOT NULL COMMENT '实体名称',
  `content`     varchar(512)  DEFAULT NULL COMMENT '内容',
  `description` text COMMENT '描述',
  `status`      tinyint(1)    DEFAULT NULL COMMENT '状态 -3:删除 0:禁用 1:可用',
  `extra_json`  varchar(2000) DEFAULT NULL COMMENT '扩展信息',
  `created_at`  datetime     NOT NULL,
  `updated_at`  datetime     NOT NULL,
  `updated_by`  varchar(32)   DEFAULT NULL COMMENT '操作用户id',
  PRIMARY KEY (`id`),
  KEY `idx_entity_name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='关系实体表';

CREATE TABLE IF NOT EXISTS `parana_footprint`
(
  `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id`   bigint(20)      DEFAULT NULL COMMENT '租户id',
  `operator_id` bigint(20)      DEFAULT NULL COMMENT '区域运营id\n',
  `user_id`     bigint(20)      DEFAULT NULL COMMENT '用户id',
  `target_id`   bigint(20)      DEFAULT NULL COMMENT '目标id',
  `target_type` int(11)         DEFAULT NULL COMMENT '目标类型',
  `status`      tinyint(1)      DEFAULT NULL,
  `extra_json`  varchar(255)    DEFAULT NULL COMMENT 'extra属性Json',
  `visit_at`    timestamp  NULL DEFAULT NULL COMMENT '访问时间',
  `created_at`  timestamp  NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at`  timestamp  NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_footprint` (`user_id`, `target_id`, `target_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='足迹持久化表';

CREATE TABLE IF NOT EXISTS `parana_front_category`
(
  `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `extension_type` smallint(6)   DEFAULT '0' COMMENT '扩展类型',
  `pid`            bigint(20)    DEFAULT NULL COMMENT '父级id',
  `name`           varchar(50)   DEFAULT NULL COMMENT '名称',
  `logo`           varchar(256)  DEFAULT NULL COMMENT 'logo',
  `level`          tinyint(1)    DEFAULT NULL COMMENT '级别',
  `has_children`   tinyint(1)    DEFAULT NULL COMMENT '是否有孩子',
  `deleted`        tinyint(1)    DEFAULT NULL COMMENT '逻辑删除标识',
  `options`        tinyint(2)    DEFAULT NULL COMMENT '操作',
  `extra_json`     varchar(1024) DEFAULT NULL COMMENT '其他属性',
  `tenant_id`      int(20)       DEFAULT NULL COMMENT '租户Id',
  `created_at`     datetime      DEFAULT NULL,
  `updated_at`     datetime      DEFAULT NULL,
  `updated_by`     varchar(32)   DEFAULT NULL COMMENT '操作用户id',
  PRIMARY KEY (`id`),
  KEY `idx_front_categories_name` (`name`, `extension_type`),
  KEY `idx_front_categories_pid` (`pid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='前台类目表';

CREATE TABLE IF NOT EXISTS `parana_generic_tag`
(
  `id`           bigint(20)  NOT NULL AUTO_INCREMENT,
  `code`         varchar(64) NOT NULL DEFAULT '' COMMENT '标签编码',
  `display_name` varchar(64)          DEFAULT NULL COMMENT '标签名',
  `status`       tinyint(4)  NOT NULL COMMENT '标签状态',
  `type`         tinyint(6)  NOT NULL COMMENT '类型',
  `owning_type`  smallint(6) NOT NULL COMMENT '标签拥有者类型',
  `owning_id`    bigint(20)  NOT NULL COMMENT '标签拥有者id',
  `content`      varchar(256)         DEFAULT NULL COMMENT '标签自定义内容',
  `created_at`   datetime    NOT NULL,
  `updated_at`   datetime    NOT NULL,
  `updated_by`   varchar(32) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='抽象标签表';

CREATE TABLE IF NOT EXISTS `parana_generic_tag_binding`
(
  `id`           bigint(20)  NOT NULL AUTO_INCREMENT,
  `batch_id`     varchar(64) NOT NULL DEFAULT 'unset' COMMENT '标签批次号',
  `status`       tinyint(4)  NOT NULL COMMENT '1:有效;-3:删除',
  `tag_type`     tinyint(4)  NOT NULL COMMENT '标签绑定类型:1独立标签,2简单标记',
  `tag_code`     varchar(32) NOT NULL COMMENT '标签id',
  `target_type`  smallint(6) NOT NULL COMMENT '绑定目标类型',
  `target_id`    bigint(20)  NOT NULL COMMENT '绑定的目标id',
  `effect_at`    datetime             DEFAULT NULL COMMENT '标签生效时间戳',
  `expire_at`    datetime             DEFAULT NULL COMMENT '标签失效时间戳',
  `auto_release` tinyint(4)  NOT NULL COMMENT '自动清除',
  `extra_json`   varchar(256)         DEFAULT NULL COMMENT '额外信息',
  `created_at`   datetime    NOT NULL,
  `updated_at`   datetime    NOT NULL,
  `updated_by`   varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `idx_tag_code_target_id` (`tag_code`, `target_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='抽象标签绑定表';

CREATE TABLE IF NOT EXISTS `parana_item`
(
  `id`                    bigint(20)    NOT NULL,
  `extension_type`        smallint(6)            DEFAULT '0' COMMENT '扩展类型',
  `spu_id`                bigint(20)             DEFAULT NULL,
  `unit`                  varchar(256)           DEFAULT NULL COMMENT '商品计量单位',
  `tenant_id`             bigint(20)             DEFAULT NULL COMMENT '租户id',
  `outer_id`              varchar(40)            DEFAULT NULL,
  `category_id`           bigint(20)    NOT NULL COMMENT '类目id',
  `item_code`             varchar(45)            DEFAULT NULL COMMENT '商品编码',
  `shop_id`               bigint(20)    NOT NULL COMMENT '店铺id',
  `shop_name`             varchar(128)           DEFAULT '' COMMENT '店铺名',
  `brand_id`              bigint(20)             DEFAULT NULL COMMENT '品牌id',
  `brand_name`            varchar(128)           DEFAULT NULL COMMENT '品牌名',
  `delivery_fee_temp_id`  bigint(20)    NOT NULL COMMENT '运费模板id',
  `name`                  varchar(1024) NOT NULL DEFAULT '' COMMENT '商品名',
  `advertise`             varchar(1024)          DEFAULT '' COMMENT '广告',
  `main_image`            varchar(512)  NOT NULL DEFAULT '' COMMENT '主图地址',
  `video_url`             varchar(512)           DEFAULT NULL COMMENT '视频地址',
  `status`                smallint(6)   NOT NULL COMMENT '商品状态：1(上架),-1(下架),-2(冻结),-3(删除)',
  `type`                  smallint(6)   NOT NULL COMMENT '商品类型',
  `business_type`         smallint(6)   NOT NULL DEFAULT '0' COMMENT '业务类型',
  `sku_attributes_json`   varchar(4096)          DEFAULT NULL COMMENT 'sku销售属性集合',
  `other_attributes_json` varchar(4096)          DEFAULT NULL COMMENT '其它属性',
  `extra_json`            mediumtext COMMENT '其它内容',
  `bit_tag`               bigint(20)    NOT NULL DEFAULT '0' COMMENT '位标',
  `md5_info`              varchar(45)            DEFAULT NULL,
  `version`               int(10)       NOT NULL COMMENT '信息版本号',
  `created_at`            datetime      NOT NULL COMMENT '创建时间',
  `updated_at`            datetime      NOT NULL COMMENT '最后更新时间',
  `updated_by`            varchar(32)            DEFAULT NULL COMMENT '更新者',
  `audit_status`          tinyint(1)             DEFAULT NULL COMMENT '审核状态 1-审核中 2-审核拒绝 3-审核通过',
  PRIMARY KEY (`id`),
  KEY `fk_item_spu1_idx` (`spu_id`),
  KEY `idx_parana_item_category_id` (`category_id`),
  KEY `idx_parana_item_item_code` (`item_code`),
  KEY `idx_parana_item_shop_id` (`shop_id`),
  KEY `idx_parana_item_shop_id_create_at_status` (`shop_id`, `created_at`, `status`),
  KEY `idx_parana_item_updated_at` (`updated_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品主表';

CREATE TABLE IF NOT EXISTS `parana_item_combination`
(
  `id`          bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id`   smallint(6) NOT NULL COMMENT '租户id',
  `source_id`   bigint(64)  DEFAULT NULL COMMENT '源id',
  `source_code` varchar(64) DEFAULT NULL COMMENT '源code',
  `source_type` tinyint(1)  DEFAULT NULL COMMENT '2:sku 1:item',
  `target_id`   bigint(64)  DEFAULT NULL COMMENT '目标id',
  `target_code` varchar(64) DEFAULT '' COMMENT '目标code',
  `target_type` tinyint(1)  DEFAULT NULL COMMENT '2:sku 1:item',
  `link_count`  bigint(64)  DEFAULT NULL COMMENT '连接数',
  `link_type`   tinyint(1)  DEFAULT NULL COMMENT ' sku-sku:0 sku-mm:1 sku-item:2 item-item:3',
  `status`      tinyint(1)  DEFAULT NULL COMMENT '状态 1:生效 -1:失效',
  `created_at`  datetime    NOT NULL COMMENT '创建时间',
  `updated_at`  datetime    NOT NULL COMMENT '更新时间',
  `updated_by`  varchar(32) NOT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `source_code_index` (`source_code`),
  KEY `source_id_index` (`source_id`),
  KEY `target_code_index` (`target_code`),
  KEY `target_id_index` (`target_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='组合关系表';

CREATE TABLE IF NOT EXISTS `parana_item_configuration`
(
  `id`        bigint(20)  NOT NULL AUTO_INCREMENT,
  `tenant_id` int(11)      DEFAULT NULL,
  `key`       varchar(64) NOT NULL COMMENT '配置key',
  `value`     varchar(256) DEFAULT NULL COMMENT '配置value',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_conf_key` (`key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品配置表';

CREATE TABLE IF NOT EXISTS `parana_item_detail`
(
  `item_id`    bigint(20) NOT NULL COMMENT '商品id',
  `tenant_id`  smallint(6)   DEFAULT NULL,
  `image_json` longtext COMMENT '图片列表, json表示',
  `pc_detail`  longtext COMMENT 'pc版富文本详情',
  `wap_detail` longtext COMMENT '移动版富文本详情',
  `extra_json` varchar(1024) DEFAULT NULL COMMENT '其它信息',
  `created_at` datetime   NOT NULL,
  `updated_at` datetime   NOT NULL,
  PRIMARY KEY (`item_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='商品详情';

CREATE TABLE IF NOT EXISTS `parana_item_transcript`
(
  `id`          bigint(20)    NOT NULL AUTO_INCREMENT,
  `item_id`     bigint(20)    NOT NULL COMMENT '商品id',
  `type`        tinyint(4)    NOT NULL COMMENT '类型',
  `status`      tinyint(4)    NOT NULL COMMENT '状态',
  `md5`         varchar(64)   NOT NULL COMMENT '商品md5值',
  `item_json`   varchar(4096) NOT NULL COMMENT '商品json',
  `sku_json`    varchar(4096) NOT NULL COMMENT 'sku的json',
  `detail_json` varchar(4096) NOT NULL COMMENT 'detail的json',
  `created_at`  datetime      NOT NULL,
  `updated_at`  datetime      NOT NULL,
  `updated_by`  varchar(64)   NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_item_id_type_md5` (`item_id`, `type`, `md5`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品副本表';

CREATE TABLE IF NOT EXISTS `parana_new_category_attribute`
(
  `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id`    smallint(6)                  DEFAULT NULL COMMENT '租户ID',
  `name`         varchar(64)         NOT NULL COMMENT '属性名',
  `status`       tinyint(1)          NOT NULL DEFAULT '1' COMMENT '状态',
  `required`     tinyint(1)          NOT NULL DEFAULT '0' COMMENT '必填标识：0否，1是',
  `salable`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '可售标识：0否，1是',
  `customizable` tinyint(1)          NOT NULL DEFAULT '0' COMMENT '可自定义标识：0否，1是',
  `searchable`   tinyint(1)          NOT NULL DEFAULT '0' COMMENT '可搜索标识：0否，1是',
  `displayable`  tinyint(1)          NOT NULL DEFAULT '0' COMMENT '前台展示标识：0否，1是',
  `value_type`   tinyint(4)          NOT NULL COMMENT '属性值类型',
  `value_json`   varchar(4096)                DEFAULT NULL COMMENT '存储的属性的值JSON数组',
  `extra_json`   varchar(2048)                DEFAULT NULL COMMENT '扩展字段',
  `created_at`   datetime            NOT NULL COMMENT '创建时间',
  `updated_at`   datetime            NOT NULL COMMENT '修改时间',
  `updated_by`   varchar(32)                  DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`),
  KEY `idx_ppa_name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='类目元属性';

CREATE TABLE IF NOT EXISTS `parana_open_configuration`
(
  `entity_id`   int(11)    NOT NULL COMMENT '实体id',
  `entity_type` tinyint(4) NOT NULL COMMENT '实体类型',
  `app_key`     char(64)   NOT NULL COMMENT 'appKey',
  `app_secret`  char(64)   NOT NULL COMMENT 'appSecret',
  `extra_json`  varchar(1024) DEFAULT NULL COMMENT '附加信息',
  `created_at`  datetime   NOT NULL,
  `updated_at`  datetime   NOT NULL,
  `updated_by`  varchar(32)   DEFAULT NULL COMMENT '操作用户id',
  PRIMARY KEY (`entity_id`, `entity_type`),
  UNIQUE KEY `uidx_appKey` (`app_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='开放平台配置表';

CREATE TABLE IF NOT EXISTS `parana_outer_category`
(
  `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `outer_id`   varchar(64) DEFAULT NULL COMMENT '外部系统类目唯一键',
  `name`       varchar(50) DEFAULT NULL COMMENT '名称',
  `type`       varchar(128)        NOT NULL COMMENT '类型（来源）',
  `status`     smallint(6) DEFAULT NULL COMMENT '类目状态 1: 已绑定 -1：未绑定',
  `extra_json` text,
  `tenant_id`  int(20)     DEFAULT NULL COMMENT '租户Id',
  `created_at` datetime    DEFAULT NULL,
  `updated_at` datetime    DEFAULT NULL,
  `updated_by` varchar(32) DEFAULT NULL COMMENT '操作用户id',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='前台类目表';

CREATE TABLE IF NOT EXISTS `parana_outer_category_binding`
(
  `id`                bigint(20) NOT NULL AUTO_INCREMENT,
  `outer_category_id` bigint(20) DEFAULT NULL,
  `back_category_id`  bigint(20) DEFAULT NULL,
  `created_at`        datetime   DEFAULT NULL,
  `updated_at`        datetime   DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`outer_category_id`, `back_category_id`),
  KEY `idx_outer_id` (`outer_category_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='外部类目绑定表';

CREATE TABLE IF NOT EXISTS `parana_public_attribute`
(
  `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '属性id',
  `extension_type`  smallint(6)   DEFAULT '0' COMMENT '扩展类型',
  `attr_key`        varchar(20)         NOT NULL COMMENT '属性名称',
  `attr_metas_json` varchar(255)  DEFAULT NULL COMMENT 'json 格式存储的属性元信息',
  `attr_vals_json`  varchar(4096) DEFAULT NULL COMMENT 'json 格式存储的属性值信息',
  `extra_json`      varchar(2048) DEFAULT NULL COMMENT '附加字段',
  `status`          tinyint(1)          NOT NULL COMMENT '状态：1启用，0删除',
  `created_at`      datetime            NOT NULL COMMENT '创建时间',
  `updated_at`      datetime            NOT NULL COMMENT '修改时间',
  `updated_by`      varchar(32)   DEFAULT NULL COMMENT '操作用户id',
  `tenant_id`       int(20)       DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_ppa_name` (`attr_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE IF NOT EXISTS `parana_relation_binding`
(
  `id`          bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id`   int(10)     NOT NULL COMMENT '租户id',
  `source_type` varchar(32) NOT NULL DEFAULT '' COMMENT '源类型：如发票,税率,售后服务……,关系组等等',
  `source_mark` varchar(32) NOT NULL DEFAULT '' COMMENT '源id',
  `target_type` varchar(32) NOT NULL DEFAULT '' COMMENT '目标类型：如品牌，后台，商品，商家等等',
  `target_mark` varchar(32) NOT NULL DEFAULT '' COMMENT '目标id',
  `status`      tinyint(1)  NOT NULL COMMENT '状态 0:禁用 1:可用',
  `extra_json`  varchar(2000)        DEFAULT '',
  `created_at`  datetime    NOT NULL,
  `updated_at`  datetime    NOT NULL,
  `updated_by`  varchar(32)          DEFAULT '' COMMENT '操作用户id',
  PRIMARY KEY (`id`),
  KEY `idx_relation_binding` (`source_mark`, `target_mark`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='关系绑定表';

CREATE TABLE IF NOT EXISTS `parana_relation_detail`
(
  `id`          bigint(20)  NOT NULL AUTO_INCREMENT,
  `tenant_id`   int(10)       DEFAULT NULL,
  `relation_id` bigint(20)  NOT NULL,
  `operator_id` bigint(20)    DEFAULT NULL,
  `group`       varchar(32) NOT NULL COMMENT '分组：如 推荐 或 系列 或 组合',
  `type`        varchar(32)   DEFAULT NULL COMMENT '类型：如 推荐类型 或 ……',
  `target_type` varchar(32) NOT NULL COMMENT '如品牌，后台，商品，商家等等',
  `target_id`   bigint(20)  NOT NULL,
  `extra_json`  varchar(2000) DEFAULT NULL,
  `created_at`  datetime    NOT NULL,
  `updated_at`  datetime    NOT NULL,
  `updated_by`  varchar(32)   DEFAULT NULL COMMENT '操作用户id',
  PRIMARY KEY (`id`),
  KEY `idx_detail_group_type` (`group`, `type`),
  KEY `idx_relationId` (`relation_id`),
  KEY `idx_target_type_targetId` (`target_type`, `target_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='关系详情表';

CREATE TABLE IF NOT EXISTS `parana_relation_index`
(
  `id`          bigint(20)  NOT NULL AUTO_INCREMENT,
  `tenant_id`   int(10)       DEFAULT NULL,
  `operator_id` bigint(20)    DEFAULT NULL COMMENT '归属id',
  `group`       varchar(32) NOT NULL COMMENT '分组：如 推荐 或 系列 或 组合',
  `type`        varchar(32)   DEFAULT NULL COMMENT '类型：如 推荐类型 或 ……',
  `target_type` varchar(32) NOT NULL COMMENT '如品牌，后台，商品，商家等等',
  `name`        varchar(32)   DEFAULT NULL COMMENT '名称',
  `alias`       varchar(64)   DEFAULT NULL COMMENT '别名',
  `status`      tinyint(1)    DEFAULT NULL COMMENT '状态 0:禁用 1:可用',
  `extra_json`  varchar(2048) DEFAULT NULL,
  `created_at`  datetime    NOT NULL,
  `updated_at`  datetime    NOT NULL,
  `updated_by`  varchar(32)   DEFAULT NULL COMMENT '操作用户id',
  PRIMARY KEY (`id`),
  KEY `idx_group_name` (`operator_id`, `name`, `status`, `group`),
  KEY `idx_group_type` (`group`, `type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='关系组内容表';

CREATE TABLE IF NOT EXISTS `parana_shop`
(
  `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id`     bigint(20) unsigned NOT NULL COMMENT '租户ID',
  `outer_id`      varchar(32)   DEFAULT NULL COMMENT '外部店铺编码',
  `user_id`       bigint(20)    DEFAULT NULL COMMENT '商家id',
  `user_name`     varchar(32)   DEFAULT NULL COMMENT '商家名称',
  `name`          varchar(64)         NOT NULL COMMENT '店铺名称',
  `status`        tinyint(2)          NOT NULL COMMENT '状态 1:正常, -1:关闭, -2:冻结',
  `type`          tinyint(2)          NOT NULL COMMENT '店铺类型 1:门店 2:商家 3:出版社',
  `phone`         varchar(32)   DEFAULT NULL COMMENT '联系电话',
  `email`         varchar(32)   DEFAULT NULL COMMENT '电子邮件',
  `image_url`     varchar(128)  DEFAULT NULL COMMENT '店铺图片url',
  `address`       varchar(128)  DEFAULT NULL COMMENT '店铺地址',
  `extra_json`    varchar(4096) DEFAULT NULL COMMENT '店铺额外信息,建议json字符串',
  `tags`          bigint(20)    DEFAULT NULL COMMENT '店铺标签',
  `business_type` tinyint(6)    DEFAULT '0',
  `created_at`    datetime            NOT NULL COMMENT '创建时间',
  `updated_at`    datetime            NOT NULL COMMENT '更新时间',
  `updated_by`    varchar(20)   DEFAULT NULL COMMENT '更新者',
  `industry_id`   bigint(20)          NOT NULL COMMENT '行业',
  `tin`           varchar(32)         NOT NULL COMMENT '纳税人识别号',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_outer_id` (`outer_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='店铺表';

CREATE TABLE IF NOT EXISTS `parana_operator_category`
(
  `id`           bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `operator_id`  bigint(20)           DEFAULT NULL COMMENT '运营id',
  `tenant_id`    bigint(20)           DEFAULT NULL COMMENT '租户id',
  `name`         varchar(255)         DEFAULT NULL COMMENT '类目名',
  `pid`          bigint(20)  NOT NULL COMMENT '父id，类目模板根节点pid为-1，非模板一级类目为0',
  `logo`         varchar(255)         DEFAULT NULL COMMENT '类目logo',
  `level`        tinyint(6)  NOT NULL COMMENT '本类目所属层级',
  `path`         varchar(255)         DEFAULT NULL COMMENT '父类目路径,分隔',
  `has_children` tinyint(1)           DEFAULT '0' COMMENT '标识是否有孩子类目',
  `type`         tinyint(2)           DEFAULT NULL COMMENT '叶子类目时绑定目标类型，0：后台类目，1：商品',
  `index`        smallint(6) NOT NULL COMMENT '类目在本级中的排序',
  `disclosed`    tinyint(1)           DEFAULT '0' COMMENT '是否默认展开',
  `status`       tinyint(2)           DEFAULT NULL COMMENT '状态',
  `has_bind`     tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否有绑定',
  `extra_json`   varchar(255)         DEFAULT NULL COMMENT 'extra属性json',
  `created_at`   datetime             DEFAULT NULL COMMENT '创建时间',
  `updated_at`   datetime             DEFAULT NULL COMMENT '更新时间',
  `updated_by`   varchar(32)          DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营类目表';

CREATE TABLE IF NOT EXISTS `parana_operator_category_binding`
(
  `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `operator_id`          bigint(20)  DEFAULT NULL COMMENT '店铺类目id',
  `operator_category_id` bigint(20) NOT NULL COMMENT '运营类目id',
  `back_category_id`     bigint(20)  DEFAULT NULL COMMENT '后台类目id',
  `created_at`           datetime    DEFAULT NULL COMMENT '创建时间',
  `updated_at`           datetime    DEFAULT NULL COMMENT '更新时间',
  `updated_by`           varchar(32) DEFAULT NULL COMMENT '更新者id',
  PRIMARY KEY (`id`),
  KEY `idx_shop_category_id_target_id` (`operator_category_id`, `back_category_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营类目绑定表';

CREATE TABLE IF NOT EXISTS `parana_sku`
(
  `id`              bigint(20) unsigned NOT NULL,
  `extension_type`  smallint(6)                  DEFAULT '0' COMMENT '扩展类型',
  `tenant_id`       smallint(6)                  DEFAULT NULL COMMENT '租户id',
  `item_id`         bigint(20)          NOT NULL COMMENT '商品id',
  `sku_template_id` bigint(20)                   DEFAULT NULL COMMENT 'sku模板id',
  `sku_code`        varchar(40)                  DEFAULT NULL COMMENT 'SKU 编码 (标准库存单位编码)',
  `barcode`         varchar(32)                  DEFAULT NULL COMMENT '商品条码',
  `outer_id`        varchar(40)                  DEFAULT NULL COMMENT '外部id',
  `shop_id`         bigint(20)          NOT NULL,
  `name`            varchar(1024)                DEFAULT NULL COMMENT '名称',
  `image`           varchar(1024)                DEFAULT NULL COMMENT '图片url',
  `price`           bigint(20)                   DEFAULT NULL COMMENT '实际售卖价格',
  `original_price`  bigint(20)                   DEFAULT NULL COMMENT '定价',
  `status`          tinyint(1)          NOT NULL COMMENT 'sku状态, 1: 上架, -1:下架, -2:冻结, -3:删除',
  `audit_status`    smallint(6)                  DEFAULT NULL COMMENT '审核状态',
  `type`            smallint(6)         NOT NULL COMMENT '商品类型',
  `business_type`   smallint(6)         NOT NULL DEFAULT '0' COMMENT '业务类型',
  `sku_ids_json`    varchar(512)                 DEFAULT NULL,
  `price_json`      varchar(255)                 DEFAULT NULL COMMENT 'sku其他各种价格的json表示形式',
  `attrs_json`      varchar(1024)                DEFAULT NULL COMMENT 'json存储的sku属性键值对',
  `version`         int(10)             NOT NULL COMMENT 'sku信息版本',
  `extra_json`      text COMMENT 'sku额外信息',
  `bit_tag`         bigint(20)          NOT NULL DEFAULT '0' COMMENT '位标',
  `created_at`      datetime            NOT NULL COMMENT '创建时间',
  `updated_at`      datetime            NOT NULL COMMENT '最后更新时间',
  `updated_by`      varchar(32)                  DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_parana_item_barcode` (`barcode`),
  KEY `idx_parana_sku_shop_id` (`shop_id`),
  KEY `idx_parana_sku_sku_code` (`sku_code`),
  KEY `idx_parana_sku_updated_at` (`updated_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='商品SKU表';

CREATE TABLE IF NOT EXISTS `parana_sku_template`
(
  `id`             bigint(11)  NOT NULL COMMENT '主键id',
  `spu_id`         bigint(20)  NOT NULL COMMENT 'spuId',
  `tenant_id`      int(5)        DEFAULT NULL COMMENT '租户id',
  `outer_id`       varchar(45)   DEFAULT NULL COMMENT '外部id',
  `sku_code`       varchar(45)   DEFAULT NULL COMMENT 'sku编码',
  `barcode`        varchar(45)   DEFAULT NULL COMMENT '条码',
  `image`          varchar(128)  DEFAULT NULL COMMENT '图片',
  `name`           varchar(256)  DEFAULT NULL COMMENT '名称',
  `original_price` bigint(20)    DEFAULT NULL COMMENT '定价',
  `status`         smallint(6) NOT NULL COMMENT '状态',
  `type`           smallint(6)   DEFAULT NULL COMMENT '类型',
  `attrs_json`     varchar(1024) DEFAULT NULL COMMENT '属性json',
  `extra_json`     text COMMENT 'extra属性json',
  `created_at`     datetime      DEFAULT NULL COMMENT '创建时间',
  `updated_at`     datetime      DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_spu_id` (`spu_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='SKU模板表';

CREATE TABLE IF NOT EXISTS `parana_special_delivery_fees`
(
  `id`                       bigint(20)    NOT NULL AUTO_INCREMENT,
  `delivery_fee_template_id` bigint(20)    NOT NULL COMMENT '所属运费模板id',
  `shop_id`                  bigint(20)    NOT NULL COMMENT '店铺id',
  `address_json`             varchar(1024) NOT NULL COMMENT '区域id列表,json存储',
  `address_tree_json`        text          NOT NULL COMMENT '层级结构的区域json,主要用于前端展示',
  `desc`                     varchar(64)            DEFAULT NULL COMMENT '描述',
  `is_free`                  tinyint(1)    NOT NULL COMMENT '是否包邮',
  `deliver_method`           tinyint(4)    NOT NULL COMMENT '运送方式:1-快递,2-EMS,3-平邮',
  `charge_method`            tinyint(4)    NOT NULL COMMENT '计价方式:1-按计量单位,2-固定运费',
  `fee`                      int(11)                DEFAULT NULL COMMENT '运费,当计价方式为固定运费时使用',
  `low_price`                int(11)                DEFAULT NULL COMMENT '订单不满该金额时，运费为lowFee',
  `low_fee`                  int(11)                DEFAULT NULL COMMENT '订单不满low_price时，运费为lowFee',
  `high_price`               int(11)                DEFAULT NULL COMMENT '订单高于该金额时，运费为highFee',
  `high_fee`                 int(11)                DEFAULT NULL COMMENT '订单高于high_price时，运费为highFee',
  `middle_fee`               int(11)                DEFAULT NULL COMMENT '订单价格在lowFee，highFee之间时，运费为middleFee',
  `init_amount`              int(11)                DEFAULT NULL COMMENT '首费数量',
  `init_fee`                 int(11)                DEFAULT NULL COMMENT '首费金额',
  `incr_amount`              int(11)                DEFAULT NULL COMMENT '增费数量',
  `incr_fee`                 int(11)                DEFAULT NULL COMMENT '增费金额',
  `status`                   tinyint(4)    NOT NULL DEFAULT '1' COMMENT '状态',
  `created_at`               datetime      NOT NULL,
  `updated_at`               datetime      NOT NULL,
  `updated_by`               varchar(32)            DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_specail_delivery_fees_dfti` (`delivery_fee_template_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='特殊区域运费';

CREATE TABLE IF NOT EXISTS `parana_spu`
(
  `id`                    bigint(20)   NOT NULL COMMENT '主键id',
  `tenant_id`             smallint(6)   DEFAULT NULL COMMENT '租户id',
  `outer_id`              varchar(128)  DEFAULT NULL COMMENT '外部id',
  `spu_code`              varchar(45)   DEFAULT NULL COMMENT 'spu编码',
  `category_id`           bigint(20)   NOT NULL COMMENT '类目id',
  `brand_id`              bigint(20)    DEFAULT NULL COMMENT '品牌id',
  `brand_name`            varchar(128)  DEFAULT NULL COMMENT '品牌名称',
  `name`                  varchar(256) NOT NULL COMMENT '名称',
  `video_url`             varchar(128)  DEFAULT NULL COMMENT '视频地址',
  `main_image`            varchar(128)  DEFAULT NULL COMMENT '主图',
  `advertise`             varchar(1024) DEFAULT '' COMMENT '广告语',
  `status`                tinyint(2)   NOT NULL COMMENT '状态',
  `type`                  tinyint(2)    DEFAULT '1' COMMENT '类型',
  `sku_attributes_json`   text COMMENT 'sku属性json',
  `other_attributes_json` text COMMENT '其他属性json',
  `extra_json`            text COMMENT 'extra属性Json',
  `md5_info`              varchar(45)   DEFAULT NULL COMMENT 'md5信息',
  `created_at`            datetime      DEFAULT NULL COMMENT '创建时间',
  `updated_at`            datetime      DEFAULT NULL COMMENT '更新时间',
  `updated_by`            varchar(45)   DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_categoryid` (`category_id`, `status`),
  KEY `idx_outerid` (`outer_id`),
  KEY `idx_updatedat` (`updated_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='SPU表';

CREATE TABLE IF NOT EXISTS `parana_spu_detail`
(
  `spu_id`     bigint(20) NOT NULL COMMENT 'spuId',
  `tenant_id`  int(11)  DEFAULT NULL,
  `image_json` longtext,
  `pc_detail`  longtext COMMENT 'pc版富文本详情',
  `wap_detail` longtext COMMENT '移动版富文本详情',
  `extra_json` text COMMENT 'extra属性Json',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`spu_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='SPU详情';

CREATE TABLE IF NOT EXISTS `parana_store`
(
  `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id`  bigint(20) unsigned NOT NULL COMMENT '租户ID',
  `outer_id`   varchar(32)   DEFAULT NULL COMMENT '外部店铺编码',
  `user_id`    bigint(20)    DEFAULT NULL COMMENT '商家id',
  `user_name`  varchar(32)   DEFAULT NULL COMMENT '商家名称',
  `name`       varchar(64)         NOT NULL COMMENT '店铺名称',
  `status`     tinyint(1)          NOT NULL COMMENT '状态 1:正常, -1:关闭, -2:冻结',
  `type`       tinyint(1)          NOT NULL COMMENT '店铺类型 1:门店 2:商家 3:出版社',
  `phone`      varchar(32)   DEFAULT NULL COMMENT '联系电话',
  `email`      varchar(32)   DEFAULT NULL COMMENT '电子邮件',
  `image_url`  varchar(128)  DEFAULT NULL COMMENT '店铺图片url',
  `address`    varchar(128)  DEFAULT NULL COMMENT '店铺地址',
  `extra_json` varchar(4096) DEFAULT NULL COMMENT '店铺额外信息,建议json字符串',
  `tags`       bigint(20)    DEFAULT NULL COMMENT '店铺标签',
  `created_at` datetime            NOT NULL COMMENT '创建时间',
  `updated_at` datetime            NOT NULL COMMENT '更新时间',
  `updated_by` varchar(20)   DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_outer_id` (`outer_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='门店表';

CREATE TABLE IF NOT EXISTS `recommend_detail`
(
  `id`           bigint(19)   NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id`    int(11)               DEFAULT NULL COMMENT '租户id',
  `recommend_id` bigint(19)   NOT NULL COMMENT '推荐索引id',
  `name`         varchar(100)          DEFAULT NULL COMMENT '规则名称',
  `value_type`   int(10)      NOT NULL COMMENT '值类型',
  `value`        varchar(100) NOT NULL,
  `extra_json`   varchar(2000)         DEFAULT NULL COMMENT '扩展信息',
  `status`       tinyint(1)   NOT NULL DEFAULT '1' COMMENT '状态 0禁用  1可用',
  `created_at`   datetime     NOT NULL COMMENT '创建时间',
  `updated_at`   datetime     NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_recommendid_value` (`recommend_id`, `value`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品推荐详情';

CREATE TABLE IF NOT EXISTS `recommend_index`
(
  `id`          bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_id`   int(11)              DEFAULT NULL COMMENT '租户id',
  `name`        varchar(50) NOT NULL COMMENT '推荐名称',
  `type`        int(10)     NOT NULL COMMENT '推荐类型 : 1商品系列(关联)推荐  2商品列表推荐',
  `target_type` int(10)              DEFAULT NULL COMMENT '推荐目标地址类型 : item商品 page活动页面 category类目',
  `target_id`   varchar(64)          DEFAULT NULL COMMENT '目标地址id',
  `extra_json`  varchar(2000)        DEFAULT NULL COMMENT '扩展信息',
  `value_type`  int(10)     NOT NULL COMMENT '推荐内容类型 : json ids expression',
  `value`       longtext    NOT NULL COMMENT '推荐内容',
  `status`      tinyint(4)  NOT NULL DEFAULT '1' COMMENT '状态 : 0禁用 1可用',
  `created_at`  datetime    NOT NULL COMMENT '创建时间',
  `updated_at`  datetime    NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name_status` (`name`, `status`),
  KEY `idx_status_type_targettype_targetid` (`type`, `target_type`, `target_id`, `status`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品推荐';

CREATE TABLE IF NOT EXISTS `shard_sequence`
(
  `name`       varchar(128) NOT NULL COMMENT '序列名称',
  `value`      bigint(20)   NOT NULL COMMENT '当前已使用的id',
  `updated_at` datetime     NOT NULL COMMENT '上次更新时间',
  PRIMARY KEY (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE IF NOT EXISTS `shop_relation`
(
  `id`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `source_shop_id` bigint(20)  DEFAULT NULL COMMENT '源店铺id',
  `target_shop_id` bigint(11)  DEFAULT NULL COMMENT '目标店铺id',
  `created_at`     datetime    DEFAULT NULL COMMENT '创建时间',
  `updated_at`     datetime    DEFAULT NULL COMMENT '修改时间',
  `updated_by`     varchar(20) DEFAULT NULL COMMENT '修改者',
  PRIMARY KEY (`id`),
  KEY `idx_source_shop_id` (`source_shop_id`),
  KEY `idx_target_shop_id` (`target_shop_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='店铺关系';

CREATE TABLE IF NOT EXISTS `vendor_partnership`
(
  `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `vendor_id`        bigint(20) NOT NULL COMMENT '供应商id',
  `operator_id`      bigint(20) NOT NULL COMMENT '运营商id（区域）',
  `cooperation_mode` tinyint(4) NOT NULL COMMENT '合作模式',
  `logistics_mode`   tinyint(4) NOT NULL COMMENT '物流模式',
  `warehouse_code`   bigint(20) NOT NULL COMMENT '仓库编码',
  `status`           tinyint(4) NOT NULL COMMENT '状态',
  `created_at`       datetime   NOT NULL COMMENT '创建时间',
  `updated_at`       datetime   NOT NULL COMMENT '最后更新时间',
  `updated_by`       varchar(32) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_operator` (`operator_id`),
  KEY `idx_vendor` (`vendor_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='供应商&运营合作关系表';

CREATE TABLE IF NOT EXISTS `parana_shop_auth_category`
(
  `id`          int(11)     NOT NULL AUTO_INCREMENT,
  `shop_id`     bigint(20)  NOT NULL COMMENT '店铺id',
  `category_id` bigint(20)  NOT NULL COMMENT '类目id',
  `path`        varchar(64) NOT NULL COMMENT '类目路径',
  `tenant_id`   smallint(6) NOT NULL COMMENT '租户id',
  `created_at`  datetime    NOT NULL,
  `updated_by`  varchar(32) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_shop_id_category_id` (`shop_id`, `category_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1667
  DEFAULT CHARSET = utf8mb4 COMMENT ='店铺授权类目';
