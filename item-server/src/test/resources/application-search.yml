# 搜索配置
search:
  retrieval:
    maxResultWindow: 1000000
  primary:
    match:
      fields: name^2,keyword
    activity:
      limit: 1
      codes: simple,presell,seckill
      fields: CATEGORY_ID:categoryIds,SHOP_ID:shopId
    aggregate:
      attrSize: 8
      valueSize: 20
  datasource:
    address: http://${ELASTICSEARCH_HOST:**************}:${ELASTICSEARCH_PORT:9200}
    username: ${ELASTICSEARCH_USERNAME:}
    password: ${ELASTICSEARCH_PASSWORD:}
    environment: ${ELASTICSEARCH_ENVIRONMENT:}
    mapping-locations: classpath*:mapping/*.json
  build:
    table:
      consumerGroup: GID_search_build_table_${spring.profiles.active}
      topic: ${terminus.topic}
      tag: parana_item||parana_sku||area_item||area_sku
    index:
      consumerGroup: GID_search_build_index_${spring.profiles.active}
      topic: ${terminus.topic}
      tag: index_item||index_sku||index_area_item||index_area_sku||parana_shop||parana_comment
    key-field:
      parana_shop: id
      parana_comment: id
    json-field:
      index_area_item: sku_attributes,other_attributes,extra_json,activity_json,price_json
      index_area_sku: extra_json
      index_item: extra_json
      index_sku: extra_json
      index_spu: extra_json
      parana_shop: extra_json
      parana_comment: index_json
    mapping:
      area_item:
        index_area_item:
          key:
            id: id
          column:
            item_id: item_id
            vendor_id: vendor_id
            operator_id: operator_id
            spu_id: spu_id
            category_id: category_id
            item_code: item_code
            shop_id: shop_id
            shop_name: shop_name
            brand_id: brand_id
            brand_name: brand_name
            name: name
            main_image: main_image
            status: status
            type: type
            business_type: business_type
            bit_tag: bit_tag
            updated_at: updated_at
            hq_operator_status: hq_operator_status
            vendor_status: vendor_status
            area_operator_status: area_operator_status
            is_nationwide_agency_item: is_nationwide_agency_item
          extra-column:
            - low_price
            - high_price
            - price_json
            - extra_json
            - sku_attributes
            - other_attributes
          custom-sql:
            update: UPDATE `index_area_item` SET `hq_operator_status`=#{hq_operator_status},`item_code`=#{item_code},`operator_id`=#{operator_id},`main_image`=#{main_image},`type`=#{type},`category_id`=#{category_id},`updated_at`=#{updated_at},`business_type`=#{business_type},`sku_attributes`=#{sku_attributes},`price_json`=#{price_json},`spu_id`=#{spu_id},`low_price`=#{low_price},`item_id`=#{item_id},`high_price`=#{high_price},`vendor_status`=#{vendor_status},`brand_name`=#{brand_name},`shop_name`=#{shop_name},`area_operator_status`=#{area_operator_status},`brand_id`=#{brand_id},`shop_id`=#{shop_id},`vendor_id`=#{vendor_id},`bit_tag`=#{bit_tag},`name`=#{name},`extra_json`=#{extra_json},`other_attributes`=#{other_attributes},`status`=#{status},`is_nationwide_agency_item`=#{is_nationwide_agency_item} WHERE item_id=#{item_id} and `operator_id`=#{operator_id}
            delete: DELETE FROM `index_area_item` WHERE item_id=#{item_id} and `operator_id`=#{operator_id}
      area_sku:
        index_area_sku:
          key:
            id: id
          column:
            sku_id: sku_id
            area_item_id: area_item_id
            vendor_id: vendor_id
            operator_id: operator_id
            item_id: item_id
            sku_code: sku_code
            barcode: barcode
            outer_id: outer_id
            shop_id: shop_id
            name: name
            image: image
            price: price
            original_price: original_price
            status: status
            type: type
            business_type: business_type
            bit_tag: bit_tag
            updated_at: updated_at
            hq_operator_status: hq_operator_status
            vendor_status: vendor_status
            area_operator_status: area_operator_status
          extra-column:
            - extra_json
          custom-sql:
            update: UPDATE `index_area_sku` SET `hq_operator_status`=#{hq_operator_status},`image`=#{image},`original_price`=#{original_price},`item_id`=#{item_id},`operator_id`=#{operator_id},`vendor_status`=#{vendor_status},`sku_id`=#{sku_id},`type`=#{type},`outer_id`=#{outer_id},`area_operator_status`=#{area_operator_status},`shop_id`=#{shop_id},`area_item_id`=#{area_item_id},`updated_at`=#{updated_at},`price`=#{price},`vendor_id`=#{vendor_id},`business_type`=#{business_type},`bit_tag`=#{bit_tag},`name`=#{name},`extra_json`=#{extra_json},`sku_code`=#{sku_code},`barcode`=#{barcode},`status`=#{status} WHERE `id`=#{id} and `operator_id`=#{operator_id}
            delete: DELETE FROM `index_area_sku` WHERE `id`=#{id} and `operator_id`=#{operator_id}
      parana_item:
        index_item:
          key:
            id: id
          column:
            spu_id: spu_id
            tenant_id: tenant_id
            category_id: category_id
            item_code: item_code
            shop_id: shop_id
            shop_name: shop_name
            brand_id: brand_id
            brand_name: brand_name
            name: name
            main_image: main_image
            status: status
            type: type
            business_type: business_type
            bit_tag: bit_tag
            updated_at: updated_at
          extra-column:
            - low_price
            - high_price
            - extra_json
      parana_sku:
        index_sku:
          key:
            id: id
          column:
            tenant_id: tenant_id
            item_id: item_id
            sku_code: sku_code
            barcode: barcode
            outer_id: outer_id
            shop_id: shop_id
            name: name
            image: image
            price: price
            original_price: original_price
            status: status
            type: type
            business_type: business_type
            bit_tag: bit_tag
            updated_at: updated_at
          extra-column:
            - extra_json
