<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2016 杭州端点网络科技有限公司
  -->
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <property name="LOG_FILE" value="/tmp/logs"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <include resource="logback-include.xml"/>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="OTHER"/>
    </root>

    <logger name="io.terminus" level="DEBUG" addtivity="false">
        <appender-ref ref="RUNNING"/>
    </logger>


    <logger name="org.springframework.web" level="WARN">
        <appender-ref ref="SPRING"/>
    </logger>

    <logger name="org.apache.dubbo" level="ERROR">
        <appender-ref ref="DUBBO"/>
    </logger>

</configuration>

