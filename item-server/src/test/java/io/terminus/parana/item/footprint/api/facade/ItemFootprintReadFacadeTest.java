package io.terminus.parana.item.footprint.api.facade;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.footprint.api.bean.request.FootprintCountRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintPagingRequest;
import io.terminus.parana.item.footprint.api.bean.response.FootprintInfo;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@TestDescription("店铺读")
public class ItemFootprintReadFacadeTest extends AbstractFootprint {

    @Autowired
    private FootprintReadFacade itemFootprintReadFacade;

    @Test
    @TestDescription("分页")
    public void pagingOnRedis() {
        FootprintPagingRequest request = new FootprintPagingRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setPageNo(TEST_PAGE_NO);
        request.setPageNo(TEST_PAGE_SIZE);
        Response<Paging<FootprintInfo>> response = itemFootprintReadFacade.pagingOnRedis(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("统计")
    public void count() {
        FootprintCountRequest request = new FootprintCountRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        Response<Long> response = itemFootprintReadFacade.count(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }


}
