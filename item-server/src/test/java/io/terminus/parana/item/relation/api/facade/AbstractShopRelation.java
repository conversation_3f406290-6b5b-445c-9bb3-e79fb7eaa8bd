package io.terminus.parana.item.relation.api.facade;

import io.terminus.parana.item.ItemContinuousTest;
import org.junit.AfterClass;
import org.junit.Ignore;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-15
 */
@Ignore
public class AbstractShopRelation extends ItemContinuousTest {

    @AfterClass
    public static void removeCache() {
        flushDB();
    }

    protected final Long TEST_USER_ID = 100001L;
    protected final Long TEST_SHOP_ID_1 = 10001L;
    protected final Long TEST_SHOP_ID_2 = 10002L;
    protected final Long TEST_ID = 10L;
    protected final Integer TEST_PAGE_NO = 1;
    protected final Integer TEST_PAGE_SIZE = 10;

}
