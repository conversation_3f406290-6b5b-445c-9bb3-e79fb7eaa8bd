package io.terminus.parana.item.industry.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.industry.api.bean.request.IndustryQueryByIdRequest;
import io.terminus.parana.item.industry.api.bean.request.IndustryQueryByNameRequest;
import io.terminus.parana.item.industry.api.bean.request.IndustryQueryByUrlAndDomainRequest;
import io.terminus.parana.item.industry.api.bean.response.IndustryInfo;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@TestDescription("审核申请读")
public class IndustryReadFacadeTest extends AbstractIndustry {

    @Autowired
    private IndustryReadFacade industryReadFacade;

    @Test
    @TestDescription("按id查行业")
    public void queryById() {
        IndustryQueryByIdRequest request = new IndustryQueryByIdRequest();
        request.setId(TEST_ID);
        Response<IndustryInfo> response = industryReadFacade.queryById(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("按名称查行业")
    public void queryByName() {
        IndustryQueryByNameRequest request = new IndustryQueryByNameRequest();
        request.setName(TEST_NAME);
        Response<List<IndustryInfo>> response = industryReadFacade.queryByName(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("按url查询")
    public void queryByPcUrl() {
        IndustryQueryByUrlAndDomainRequest request = new IndustryQueryByUrlAndDomainRequest();
        request.setPcUrl(TEST_PC_URL);
        request.setWapUrl(TEST_WAP_URL);
        Response<IndustryInfo> response = industryReadFacade.queryByUrlAndDomain(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

}
