package io.terminus.parana.item.relation.api.facade;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.relation.api.bean.request.QueryShopRelationByIdRequest;
import io.terminus.parana.item.relation.api.bean.request.QueryShopRelationRequest;
import io.terminus.parana.item.relation.api.bean.response.ShopRelationInfo;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-14
 */
@TestDescription("商品关系")
public class ShopRelationReadFacadeTest extends AbstractShopRelation {

    @Autowired
    private ShopRelationReadFacade shopRelationReadFacade;

    @Test
    @TestDescription("根据关联关系源店铺id获取关联关系列表")
    public void test001() {
        QueryShopRelationByIdRequest request = new QueryShopRelationByIdRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setShopId(TEST_SHOP_ID_1);

        Response<List<ShopRelationInfo>> response = shopRelationReadFacade.queryBySource(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("根据关联关系目标店铺id获取关联关系列表")
    public void test002() {
        QueryShopRelationByIdRequest request = new QueryShopRelationByIdRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setShopId(TEST_SHOP_ID_2);

        Response<List<ShopRelationInfo>> response = shopRelationReadFacade.queryByTarget(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("根据关联关系源店铺id和目标店铺id获取关联关系")
    public void test003() {
        QueryShopRelationRequest request = new QueryShopRelationRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setSourceShopId(TEST_SHOP_ID_1);
        request.setTargetShopId(TEST_SHOP_ID_2);

        Response<ShopRelationInfo> response = shopRelationReadFacade.queryBySourceAndTarget(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("根据关联关系源店铺id和目标店铺id获取关联关系分页")
    public void test004() {
        QueryShopRelationRequest request = new QueryShopRelationRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setTenantId(TEST_TENANT_ID);
        request.setSourceShopId(TEST_SHOP_ID_1);
        request.setTargetShopId(TEST_SHOP_ID_2);
        request.setPageNo(TEST_PAGE_NO);
        request.setPageSize(TEST_PAGE_SIZE);

        Response<Paging<ShopRelationInfo>> response = shopRelationReadFacade.pagingBySourceAndTarget(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }
}
