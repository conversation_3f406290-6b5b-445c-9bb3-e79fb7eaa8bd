package io.terminus.parana.item.favorites.api.facade;

import io.terminus.parana.item.ItemContinuousTest;
import org.junit.AfterClass;

public abstract class AbstractFavorites extends ItemContinuousTest {

    @AfterClass
    public static void removeCache() {
        flushDB();
    }

    protected final Long TEST_ID = 110000100012001L;
    protected final Integer TEST_TARGET_TYPE = 1;
    protected final Long TEST_USER_ID = 100001L;
    protected final Long TEST_ITEM_ID = 110000100012001L;
    protected final Integer TEST_PAGE_NO = 1;
    protected final Integer TEST_PAGE_SIZE = 10;
    protected final Integer TEST_SIZE = 10;

}
