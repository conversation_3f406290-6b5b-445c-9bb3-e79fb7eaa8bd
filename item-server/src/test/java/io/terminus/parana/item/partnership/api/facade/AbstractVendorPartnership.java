package io.terminus.parana.item.partnership.api.facade;

import io.terminus.parana.item.ItemContinuousTest;
import org.junit.AfterClass;

public abstract class AbstractVendorPartnership extends ItemContinuousTest {

    @AfterClass
    public static void removeCache() {
        flushDB();
    }

    protected final Long TEST_ID = 1L;
    protected final String TEST_WAREHOUSE_CODE = "1010101";
    protected final Integer TEST_PAGE_NO = 1;
    protected final Integer TEST_PAGE_SIZE = 20;
    protected final Long TEST_VENDOR_ID = 10001L;
    protected final Long TEST_OPERATOR_ID = 10002L;
    protected final Long TEST_TENANT_ID = 1L;
    protected final Integer TEST_LOGISTICS_MODE = 2;
    protected final Integer TEST_COOPERATION_MODE = 1;

}
