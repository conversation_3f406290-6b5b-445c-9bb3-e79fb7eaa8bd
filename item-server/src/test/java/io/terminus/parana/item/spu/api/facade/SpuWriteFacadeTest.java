package io.terminus.parana.item.spu.api.facade;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.model.Response;
import io.terminus.parana.item.spu.api.bean.request.*;
import io.terminus.parana.item.spu.api.bean.request.param.SkuTemplateParam;
import io.terminus.parana.item.spu.api.bean.request.param.SpuDetailParam;
import io.terminus.parana.item.spu.api.bean.request.param.SpuParam;
import io.terminus.parana.item.spu.api.bean.response.FullSpuInfo;
import io.terminus.parana.item.spu.api.bean.response.SkuTemplateInfo;
import io.terminus.parana.item.spu.api.bean.response.SpuInfo;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-21
 */
@TestDescription("Spu写")
public class SpuWriteFacadeTest extends AbstractSpu {

    @Autowired
    private SpuWriteFacade spuWriteFacade;

    @Autowired
    private SpuReadFacade spuReadFacade;

    private static Long TEST_CREATED_SPU_ID;

    @Test
    @TestDescription("创建spu")
    public void test001() {
        SpuCreateRequest request = makeValidRequest();
        Response<Long> response = spuWriteFacade.create(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());
        TEST_CREATED_SPU_ID = response.getResult();
    }

    private FullSpuInfo readSpu(Long id) {
        FullSpuQueryByIdRequest request = new FullSpuQueryByIdRequest();
        request.setId(id);
        request.setTenantId(TEST_TENANT_ID);

        Response<FullSpuInfo> response = spuReadFacade.queryFullById(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        return response.getResult();
    }

    @Test
    @TestDescription("更新spu")
    public void test002() {
        Assert.assertNotNull("必须先执行创建方法", TEST_CREATED_SPU_ID);
        FullSpuInfo fullSpuInfo = readSpu(TEST_CREATED_SPU_ID);
        SpuInfo spuInfo = fullSpuInfo.getSpuInfo();
        SkuTemplateInfo skuTemplateInfo = fullSpuInfo.getSkuTemplateInfoList().iterator().next();

        SpuUpdateRequest request = new SpuUpdateRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        SpuParam spuParam = new SpuParam();
        spuParam.setId(spuInfo.getId());
        spuParam.setMainImage("https://www.modify.com/");
        request.setSpuParam(spuParam);

        SkuTemplateParam skuTemplateParam = new SkuTemplateParam();
        skuTemplateParam.setId(skuTemplateInfo.getId());
        skuTemplateParam.setName("updated_name");
        request.setSkuTemplateParamList(Collections.singletonList(skuTemplateParam));

        SpuDetailParam spuDetailParam = new SpuDetailParam();
        spuDetailParam.setSpuId(spuInfo.getId());
        spuDetailParam.setExtra(ImmutableMap.of("k1", "v1", "k2", "v2"));
        request.setSpuDetailParam(spuDetailParam);

        Response<Boolean> response = spuWriteFacade.update(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("更新spu详情")
    public void test003() {
        SpuDetailUpdateRequest request = new SpuDetailUpdateRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        SpuDetailParam spuDetailParam = new SpuDetailParam();
        spuDetailParam.setSpuId(TEST_PREPARED_SPU_ID);
        spuDetailParam.setExtra(ImmutableMap.of("k1", "v1", "k2", "v2"));
        request.setSpuDetailParam(spuDetailParam);

        Response<Boolean> response = spuWriteFacade.updateSpuDetail(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("删除spu")
    public void test004() {
        Assert.assertNotNull("必须先执行创建方法", TEST_CREATED_SPU_ID);

        SpuDeleteRequest spuDeleteRequest = new SpuDeleteRequest();
        spuDeleteRequest.setIdSet(Collections.singleton(TEST_CREATED_SPU_ID));
        spuDeleteRequest.setTenantId(TEST_TENANT_ID);
        spuDeleteRequest.setUpdatedBy(TEST_UPDATED_BY);

        Response<Boolean> response = spuWriteFacade.delete(spuDeleteRequest);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }
}
