package io.terminus.parana.item.favorites.api.facade;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.favorites.api.bean.request.FavoritesCheckRequest;
import io.terminus.parana.item.favorites.api.bean.request.FavoritesCountRequest;
import io.terminus.parana.item.favorites.api.bean.request.FavoritesPagingRequest;
import io.terminus.parana.item.favorites.api.bean.request.FavoritesQueryRequest;
import io.terminus.parana.item.favorites.api.bean.response.FavoritesInfo;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@TestDescription("收藏读")
public class FavoritesReadFacadeTest extends AbstractFavorites {

    @Autowired
    private FavoritesReadFacade favoritesReadFacade;

    @Test
    @TestDescription("列表查询")
    public void findByConditions() {
        FavoritesQueryRequest request = new FavoritesQueryRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setTargetType(TEST_TARGET_TYPE);
        Response<List<FavoritesInfo>> response = favoritesReadFacade.findByConditions(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("分页")
    public void paging() {
        FavoritesPagingRequest request = new FavoritesPagingRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setTargetType(TEST_TARGET_TYPE);
        Response<Paging<FavoritesInfo>> response = favoritesReadFacade.paging(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("检查")
    public void checkByConditions() {
        FavoritesCheckRequest request = new FavoritesCheckRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setTargetId(TEST_ITEM_ID);
        request.setTargetType(TEST_TARGET_TYPE);
        Response<Boolean> response = favoritesReadFacade.checkFavorite(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }


    @Test
    @TestDescription("统计")
    public void countByType() {
        FavoritesCountRequest request = new FavoritesCountRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setTargetType(TEST_TARGET_TYPE);
        Response<Long> response = favoritesReadFacade.countByIdAndTypeAndUserId(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }


}
