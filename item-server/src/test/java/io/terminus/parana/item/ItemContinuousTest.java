package io.terminus.parana.item;

import io.terminus.parana.item.test.continous.SpringDelegateRunner;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-07
 */
@Slf4j
@Rollback
@RunWith(SpringDelegateRunner.class)
@Ignore
@SpringBootTest(classes = ItemCenterTestConfiguration.class)
public class ItemContinuousTest {

    private static Pool<Jedis> JEDIS_POOL;

    @Autowired
    private Pool<Jedis> jedisPool;

    @PostConstruct
    public void init() {
        JEDIS_POOL = jedisPool;
    }

    private Long id = System.currentTimeMillis();

    protected final Long TEST_SHOP_ID = 1L;
    protected final Integer TEST_TENANT_ID = 1;
    protected final String TEST_UPDATED_BY = "UnitTest";
    protected final Long TEST_PREPARED_ITEM_ID = 110000100012001L;
    protected final Long TEST_PREPARED_SKU_ID = 110000100012001L;
    protected final String TEST_PREPARED_ITEM_CODE = "item_code";
    protected final String TEST_PREPARED_SKU_CODE = "sku_code";
    protected final String TEST_PREPARED_ITEM_BARCODE = "item_barcode";
    protected final String TEST_PREPARED_SKU_OUTER_ID = "item_outer_id";
    protected final Long TEST_PREPARED_SPU_ID = 110000100012001L;
    protected final String TEST_PREPARED_SPU_CODE = "spu_code";
    protected final String TEST_PREPARED_SPU_OUTER_ID = "spu_outer_id";
    protected final Long TEST_USER_ID = 1L;
    protected final Long TEST_BUYER_ID = 1L;
    protected final String TEST_BUYER_USERNAME = "buyer";
    protected final String TEST_CONF_KEY = "conf_key";
    protected final String TEST_CONF_VALUE = "conf_value";
    protected final String TEST_CONF_KEY_2 = "conf_key_2";
    protected final String TEST_CONF_VALUE_2 = "conf_value_2";
    protected final Long TEST_SOURCE_ID = 110000100013001L;
    protected final Long TEST_SOURCE_ID_2 = 110000100013002L;
    protected final Integer TEST_SOURCE_TYPE = 1;
    protected final Long TEST_TARGET_ID = 110000100002004L;
    protected final Long TEST_TARGET_ID_2 = 110000100002005L;
    protected final Integer TEST_TARGET_TYPE = 2;

    protected final Long TEST_BACK_CATEGORY_LEVEL1 = 1L;
    protected final Long TEST_BACK_CATEGORY_LEVEL2 = 2L;
    protected final Long TEST_BACK_CATEGORY_LEVEL3 = 3L;
    protected final Long TEST_BACK_CATEGORY_NOT_EXIST = Long.MAX_VALUE;

    protected final Long TEST_DELIVERY_TEMPLATE_FEE_ID = 1L;

    protected final Long TEST_TAG_ID = 1L;
    protected final Long TEST_PARTNER_SHIP_ID = 11L;

    @Ignore
    public synchronized Long nextLong() {
        return ++id;
    }

    public static void flushDB() {
        if (JEDIS_POOL != null) {
            try (Jedis jedis = JEDIS_POOL.getResource()) {
                jedis.flushDB();
            }
        }
    }
}
