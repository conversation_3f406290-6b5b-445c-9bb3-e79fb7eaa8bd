package io.terminus.parana.item.relation.api.facade.feature;

import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.request.feature.tax.TaxCreateRequest;
import io.terminus.parana.item.category.api.bean.request.feature.tax.TaxDeleteRequest;
import io.terminus.parana.item.category.api.bean.request.feature.tax.TaxFindListByPidRequest;
import io.terminus.parana.item.category.api.bean.request.feature.tax.TaxUpdateRequest;
import io.terminus.parana.item.category.api.facade.feature.TaxFacade;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date : 2019/11/26
 */
@TestDescription("税率")
public class TaxFacadeTest extends AbstractFeature {

    @Autowired
    private TaxFacade taxFacade;

    @Test
    @TestDescription("税率创建")
    public void test000() {
        TaxCreateRequest request = new TaxCreateRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setCategoryId(TEST_CATEGORY_ID_1);
        request.setTaxCode(TEST_WRITE_TAX_NAME_1);
        request.setTaxRate(TEST_WRITE_TAX_CONTENT);
        Response<Boolean> result = taxFacade.create(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getResult());

        request.setTenantId(TEST_TENANT_ID);
        request.setCategoryId(TEST_CATEGORY_ID_2);
        request.setTaxCode(TEST_WRITE_TAX_NAME_2);
        request.setTaxRate(TEST_WRITE_TAX_CONTENT);
        result = taxFacade.create(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());
    }

    @Test
    @TestDescription("税率更新")
    public void test001() {
        TaxUpdateRequest request = new TaxUpdateRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("税率更新");
        request.setCategoryId(TEST_CATEGORY_ID_1);
        request.setTaxCode(TEST_WRITE_TAX_NAME_1);
        request.setTaxRate(TEST_WRITE_TAX_CONTENT + "已被修改");
        Response<Boolean> result = taxFacade.update(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());
    }


    @Test
    @TestDescription("已绑定税率的类目列表查询")
    public void test003() {
        TaxFindListByPidRequest request = new TaxFindListByPidRequest();
        request.setPid(TEST_CATEGORY_ID_1);
        request.setExtensionType(0);
        request.setTenantId(TEST_TENANT_ID);
        Response<List<Long>> result = taxFacade.queryChildrenBindingList(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getResult());
    }

    @Test
    @TestDescription("删除税率")
    public void test004() {
        TaxDeleteRequest request = new TaxDeleteRequest();
        request.setCategoryId(TEST_CATEGORY_ID_1);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("已被删除");
        Response<Boolean> result = taxFacade.delete(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());
    }
}
