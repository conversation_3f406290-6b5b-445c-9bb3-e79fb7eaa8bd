package io.terminus.parana.item.partnership.api.facade;

import com.google.common.collect.Sets;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.partnership.api.bean.request.*;
import io.terminus.parana.item.partnership.api.bean.response.VendorPartnershipInfo;
import io.terminus.parana.item.partnership.api.bean.response.VendorPartnershipListInfo;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShip;
import io.terminus.parana.item.partnership.api.bean.response.VendorWithPartnerShipInfo;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Set;

@TestDescription("合作关系读")
public class VendorPartnershipReadFacadeTest extends AbstractVendorPartnership {

    @Autowired
    private VendorPartnershipReadFacade vendorPartnershipReadFacade;

    @Test
    @TestDescription("按id查合作关系")
    public void queryById() {
        VendorPartnershipQueryByIdRequest request = new VendorPartnershipQueryByIdRequest();
        request.setId(TEST_ID);
        Response<VendorPartnershipInfo> response = vendorPartnershipReadFacade.queryById(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }


    @Test
    @TestDescription("根据供应商id和供应商商品id 查询合作关系(商品列表页面)")
    public void Test0001() {

        VendorPartnershipQueryListInfoRequest request = new VendorPartnershipQueryListInfoRequest();

        request.setVendorId(10001L);
        request.setItemId(110000100026001L);
        request.setTenantId(1);
        Response<List<VendorPartnershipListInfo>> response = vendorPartnershipReadFacade.findByVendorIdAndOperatorId(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("")
    public void Test0004() {

        VendorPartnershipQueryByIdRequest request = new VendorPartnershipQueryByIdRequest();
        request.setId(4L);
        Response<VendorWithPartnerShip> response = vendorPartnershipReadFacade.queryVendorShipById(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("")
    public void Test0005() {

        VendorPartnershipQueryByOperatorIdRequest request = new VendorPartnershipQueryByOperatorIdRequest();
        request.setOperatorId(1L);

        Response<Set<Long>> response = vendorPartnershipReadFacade.findVendorIdsByOperatorId(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("")
    public void Test0006() {

        VendorPartnershipQueryByVendorIdRequest request = new VendorPartnershipQueryByVendorIdRequest();
        request.setVendorId(3L);
//        request.setTenantId(1);
        Response<Set<Long>> response = vendorPartnershipReadFacade.findOperatorIdsByVendorId(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("供应商列表分页")
    public void vendorPaging() {
        PartnershipVendorListPagingRequest request = new PartnershipVendorListPagingRequest();
        request.setPageNo(TEST_PAGE_NO);
        request.setPageSize(TEST_PAGE_SIZE);
        Set<Long> ss = Sets.newHashSet(3L);
//        request.setVendorIds(ss);
        request.setOperatorId(1L);
        request.setCooperationMode(1);
        request.setStatus(1);
        Response<Paging<VendorWithPartnerShipInfo>> response = vendorPartnershipReadFacade.vendorPaging(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("区域运营列表分页")
    public void operatorPaging() {
        PartnershipOperatorListPagingRequest request = new PartnershipOperatorListPagingRequest();
        request.setPageNo(TEST_PAGE_NO);
        request.setPageSize(TEST_PAGE_SIZE);
        Set<Long> ss = Sets.newHashSet(2L);
        request.setVendorId(3L);
        request.setOperatorIds(ss);
        request.setStatus(1);
        request.setCooperationMode(1);

        Response<Paging<VendorWithPartnerShipInfo>> response = vendorPartnershipReadFacade.operatorPaging(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

}
