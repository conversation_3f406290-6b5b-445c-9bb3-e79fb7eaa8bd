package io.terminus.parana.item.relation.api.facade.feature;

import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.request.feature.policy.PolicyCreateRequest;
import io.terminus.parana.item.category.api.bean.request.feature.policy.PolicyDeleteRequest;
import io.terminus.parana.item.category.api.bean.request.feature.policy.PolicyQueryListByPidRequest;
import io.terminus.parana.item.category.api.bean.request.feature.policy.PolicyUpdateRequest;
import io.terminus.parana.item.category.api.facade.feature.PolicyFacade;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date : 2019/11/26
 */
@TestDescription("售后政策")
public class PolicyFacadeTest extends AbstractFeature {

    @Autowired
    private PolicyFacade policyFacade;

    @Test
    @TestDescription("售后政策创建")
    public void test000() {
        PolicyCreateRequest request = new PolicyCreateRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setCategoryId(TEST_CATEGORY_ID_1);
        request.setName(TEST_WRITE_POLICY_NAME_1);
        request.setContent(TEST_WRITE_POLICY_CONTENT);
        Response<Boolean> result = policyFacade.create(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());

        request.setTenantId(TEST_TENANT_ID);
        request.setCategoryId(TEST_CATEGORY_ID_2);
        request.setName(TEST_WRITE_POLICY_NAME_2);
        request.setContent(TEST_WRITE_POLICY_CONTENT);
        result = policyFacade.create(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());
    }

    @Test
    @TestDescription("售后政策更新")
    public void test001() {
        PolicyUpdateRequest request = new PolicyUpdateRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("售后政策更新");
        request.setCategoryId(TEST_CATEGORY_ID_1);
        request.setName(TEST_WRITE_POLICY_NAME_1);
        request.setContent(TEST_WRITE_POLICY_CONTENT+"已被修改");
        Response<Boolean> result = policyFacade.update(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());
    }

    @Test
    @TestDescription("已绑定售后政策的类目列表查询")
    public void test003() {
        PolicyQueryListByPidRequest request = new PolicyQueryListByPidRequest();
        request.setPid(TEST_CATEGORY_ID_1);
        request.setExtensionType(0);
        request.setTenantId(TEST_TENANT_ID);
        Response<List<Long>> result = policyFacade.queryChildrenBindingList(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getResult());
    }

    @Test
    @TestDescription("删除售后政策")
    public void test004() {
        PolicyDeleteRequest request = new PolicyDeleteRequest();
        request.setCategoryId(TEST_CATEGORY_ID_1);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("已被删除");
        Response<Boolean> result = policyFacade.delete(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());
    }
}
