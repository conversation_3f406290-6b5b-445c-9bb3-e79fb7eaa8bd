package io.terminus.parana.item.relation.api.facade;

import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import io.terminus.parana.item.recommend.api.bean.request.RecommendCreateRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendDeleteRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendUpdateRequest;
import io.terminus.parana.item.recommend.api.bean.request.param.RecommendDetailParam;
import io.terminus.parana.item.recommend.api.bean.request.param.RecommendIndexParam;
import io.terminus.parana.item.recommend.api.facade.RecommendWriteFacade;
import io.terminus.parana.item.relation.enums.RelationType;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date: 2019/10/23
 */
@TestDescription("推荐组写测试")
public class RecommendWriteFacadeTest extends AbstractRelation{

    @Autowired
    RecommendWriteFacade recommendWriteFacade;

    @Test
    @TestDescription("全量创建推荐组")
    public void test001() {
        RecommendCreateRequest request = new RecommendCreateRequest();
        request.setTenantId(TEST_TENANT_ID);

        // index
        RecommendIndexParam index = new RecommendIndexParam();
        index.setName(TEST_RECOMMEND_INDEX_WRITE_NAME_2);
        index.setRecommendTargetType(RelationType.CATEGORY_LEVEL.getName());
        index.setOperatorId(TEST_RECOMMEND_INDEX_OPERATOR_ID);
        request.setRecommendIndexParam(index);

        // detail
        RecommendDetailParam detail = new RecommendDetailParam();
        detail.setItemId(TEST_ITEM_ID_3);
        detail.setOperatorId(TEST_RECOMMEND_INDEX_OPERATOR_ID);
        request.setRecommendDetailParamList(Lists.newArrayList(detail));

        Response<Long> response = recommendWriteFacade.createRecommend(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("更新推荐组信息（包括推荐组类型改动、推荐组名称改动、商品的增删）")
    public void test003() {
        RecommendUpdateRequest request = new RecommendUpdateRequest();

        // index
        request.setType(RelationType.CATEGORY_LEVEL.getName());
        request.setId(TEST_RECOMMEND_INDEX_ID);
        request.setName(TEST_RECOMMEND_INDEX_WRITE_NAME_3);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        request.setTargetType("item");
        request.setOperatorId(TEST_RECOMMEND_INDEX_OPERATOR_ID);

        // 新增
        RecommendDetailParam detail2 = new RecommendDetailParam();
        detail2.setItemId(TEST_ITEM_ID_3);
        detail2.setRecommendId(TEST_RECOMMEND_INDEX_ID);
        detail2.setOperatorId(TEST_RECOMMEND_INDEX_OPERATOR_ID_2);


        List<RecommendDetailParam> detailParamList = Lists.newArrayList();
        detailParamList.add(detail2);
        request.setRecommendDetailParamList(detailParamList);

        Response<Boolean> response = recommendWriteFacade.updateRecommend(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("删除推荐组")
    public void test005() {
        RecommendDeleteRequest request = new RecommendDeleteRequest();

        request.setId(TEST_RECOMMEND_INDEX_ID);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        Response<Boolean> response = recommendWriteFacade.deleteRecommend(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }
}
