package io.terminus.parana.item.relation.api.facade.feature;

import io.terminus.common.model.Response;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.relation.api.bean.request.FeatureBindingDeleteRequest;
import io.terminus.parana.item.relation.api.bean.request.FeatureBindingUpdateRequest;
import io.terminus.parana.item.relation.api.bean.request.FeatureCreateAndBindingRequest;
import io.terminus.parana.item.relation.api.facade.FeatureWriteFacade;
import io.terminus.parana.item.relation.enums.RelationBindingSourceType;
import io.terminus.parana.item.relation.enums.RelationBindingTargetType;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> ymk
 * @date : 2019/12/13
 */
@TestDescription("特征库读")
public class FeatureWriteFacadeTest extends AbstractFeature {

    @Autowired
    private FeatureWriteFacade featureWriteFacade;

    @Test
    @TestDescription("创建特征")
    public void test001() {
        FeatureCreateAndBindingRequest request = new FeatureCreateAndBindingRequest();
        request.setTargetType(RelationBindingTargetType.ITEM.getValue());
        request.setTargetMark(TEST_ITEM_ID_1);
        request.setCode(TEST_WRITE_CODE);
        request.setName(TEST_WRITE_NAME);
        request.setContent(TEST_WRITE_CONTENT);
        request.setContentType(TEST_WRITE_CONTENT_TYPE);
        request.setModuleType(TEST_WRITE_MODULE_TYPE);
        request.setTenantId(TEST_TENANT_ID);
        Response<Boolean> response = featureWriteFacade.createAndBinding(request);
        Assert.take(response);
    }

    @Test
    @TestDescription("更新特征")
    public void test002() {
        FeatureBindingUpdateRequest request = new FeatureBindingUpdateRequest();
        request.setTargetType(RelationBindingTargetType.ITEM.getValue());
        request.setTargetMark(TEST_ITEM_ID_1);
        request.setCode(TEST_WRITE_CODE);
        request.setName(TEST_WRITE_NAME + "已被修改");
        request.setContent(TEST_WRITE_CONTENT + "已被修改");
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("justTest");
        Response<Boolean> response = featureWriteFacade.update(request);
        Assert.take(response);
    }

    @Test
    @TestDescription("删除特征")
    public void test003() {
        FeatureBindingDeleteRequest request = new FeatureBindingDeleteRequest();
        request.setSourceType(RelationBindingSourceType.FEATURE.getValue());
        request.setSourceMark(TEST_WRITE_CODE);
        request.setTargetType(RelationBindingTargetType.ITEM.getValue());
        request.setTargetMark(TEST_ITEM_ID_1);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("justTest");
        Response<Boolean> response = featureWriteFacade.delete(request);
        Assert.take(response);
    }
}
