package io.terminus.parana.item.relation.api.facade;

import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.relation.api.bean.request.ItemRelationFindByIdRequest;
import io.terminus.parana.item.relation.api.bean.request.ItemRelationFindByItemRequest;
import io.terminus.parana.item.relation.api.bean.request.ItemRelationIndexPagingRequest;
import io.terminus.parana.item.relation.api.bean.request.ItemRelationJudgeItemRequest;
import io.terminus.parana.item.relation.api.bean.response.ItemRelationIndexInfo;
import io.terminus.parana.item.relation.api.bean.response.ItemRelationInfo;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-14
 */
@TestDescription("商品关系")
public class ItemRelationReadFacadeTest extends AbstractRelation {

    @Autowired
    private ItemRelationReadFacade itemRelationReadFacade;

    @Test
    @TestDescription("分页查询系列")
    public void test002() {
        ItemRelationIndexPagingRequest request = new ItemRelationIndexPagingRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setName(TEST_RELATION_INDEX_NAME);
        request.setShopId(1L);
        request.checkParam();
        Response<Paging<ItemRelationIndexInfo>> response = itemRelationReadFacade.pagingIndex(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("基于商品来查询")
    public void test004() {
        ItemRelationFindByItemRequest request = new ItemRelationFindByItemRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setShopId(TEST_SHOP_ID);
        request.setItemId(TEST_ITEM_ID);

        Response<ItemRelationInfo> response = itemRelationReadFacade.findByItem(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("根据id精确查找系列")
    public void test006() {
        ItemRelationFindByIdRequest request = new ItemRelationFindByIdRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setShopId(TEST_SHOP_ID);
        request.setRelationId(TEST_RELATION_INDEX_ID);

        Response<ItemRelationInfo> response = itemRelationReadFacade.findByIdNoCache(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("判断已在系列中的商品")
    public void test007() {
        ItemRelationJudgeItemRequest request = new ItemRelationJudgeItemRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setOperatorId(TEST_SHOP_ID);
        request.setRelationId(TEST_RELATION_ID);
        request.setIds(Lists.newArrayList(TEST_RELATION_DETAIL_ID));

        Response<List<Long>> response = itemRelationReadFacade.judgeItemIds(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }
}
