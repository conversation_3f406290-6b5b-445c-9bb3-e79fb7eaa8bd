package io.terminus.parana.item.relation.api.facade.feature;

import com.google.common.collect.Sets;
import io.terminus.common.model.Response;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.relation.api.bean.request.FeatureTemplateCreateRequest;
import io.terminus.parana.item.relation.api.bean.request.FeatureTemplateDeleteRequest;
import io.terminus.parana.item.relation.api.bean.request.FeatureTemplateUpdStatusRequest;
import io.terminus.parana.item.relation.api.bean.request.FeatureTemplateUpdateRequest;
import io.terminus.parana.item.relation.api.facade.FeatureTemplateWriteFacade;
import io.terminus.parana.item.relation.enums.FeatureTemplateStatus;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> ymk
 * @date : 2019/12/13
 */
@TestDescription("特征库读")
public class FeatureTemplateWriteFacadeTest extends AbstractFeature {

    @Autowired
    private FeatureTemplateWriteFacade featureTemplateWriteFacade;

    private static Long createdId;

    @Test
    @TestDescription("特征模版创建")
    public void test001() {
        FeatureTemplateCreateRequest request = new FeatureTemplateCreateRequest();
        request.setCode(TEST_WRITE_CODE);
        request.setName(TEST_WRITE_NAME);
        request.setContent(TEST_WRITE_CONTENT);
        request.setContentType(TEST_WRITE_CONTENT_TYPE);
        request.setModuleType(TEST_WRITE_MODULE_TYPE);
        request.setStatus(1);
        request.setTenantId(TEST_TENANT_ID);
        Response<Long> response = featureTemplateWriteFacade.create(request);
        createdId = Assert.take(response);
    }

    @Test
    @TestDescription("特征模版编辑")
    public void test002() {
        FeatureTemplateUpdateRequest request = new FeatureTemplateUpdateRequest();
        request.setId(createdId);
        request.setName(TEST_WRITE_NAME);
        request.setContent(TEST_WRITE_CONTENT);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("justTest");
        Response<Boolean> response = featureTemplateWriteFacade.update(request);
        Assert.take(response);
    }

    @Test
    @TestDescription("特征模版状态更新")
    public void test003() {
        FeatureTemplateUpdStatusRequest request = new FeatureTemplateUpdStatusRequest();
        request.setIds(Sets.newHashSet(createdId));
        request.setStatus(FeatureTemplateStatus.DISABLE.getValue());
        request.setUpdatedBy("justTest");
        Response<Boolean> response = featureTemplateWriteFacade.batchUpdateStatus(request);
        Assert.take(response);
    }

    @Test
    @TestDescription("特征模版删除")
    public void test004() {
        FeatureTemplateDeleteRequest request = new FeatureTemplateDeleteRequest();
        request.setId(createdId);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("justTest");
        Response<Boolean> response = featureTemplateWriteFacade.delete(request);
        Assert.take(response);
    }


}
