package io.terminus.parana.item.attribute.api.facade;

import io.terminus.parana.item.ItemContinuousTest;
import org.junit.AfterClass;
import org.junit.Ignore;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-08
 */
@Ignore
public abstract class AbstractCategoryAttribute extends ItemContinuousTest {

    @AfterClass
    public static void removeCache() {
        flushDB();
    }

    protected static final Long PREPARED_CATEGORY_ATTRIBUTE_ID = 5L;
    protected static final Long PREPARED_CATEGORY_ATTRIBUTE_ID_2 = 6L;
    protected static final Long PREPARED_BIND_CATEGORY_ATTRIBUTE_ID = 1L;

}
