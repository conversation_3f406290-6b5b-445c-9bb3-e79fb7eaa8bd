package io.terminus.parana.item.relation.api.facade.feature;

import io.terminus.parana.item.ItemContinuousTest;
import org.junit.Ignore;

/**
 * <AUTHOR> ymk
 * @date : 2019/11/26
 */
@Ignore
public abstract class AbstractFeature extends ItemContinuousTest {

    protected final Long TEST_CATEGORY_ID_1 = 1L;
    protected final Long TEST_CATEGORY_ID_2 = 2L;
    protected final Long TEST_CATEGORY_ID_3 = 3L;

    protected final Long TEST_ITEM_ID_1 = 110000100012001L;

    protected final String TEST_WRITE_CODE = "test-code";
    protected final String TEST_WRITE_NAME = "test-Name";
    protected final String TEST_WRITE_CONTENT = "test-content";
    protected final String TEST_WRITE_CONTENT_TYPE = "test-contentType";
    protected final String TEST_WRITE_MODULE_TYPE = "test-moduleType";
    protected final String TEST_WRITE_DESCRIPTION = "test-description";

    // tax
    protected final String TEST_WRITE_TAX_TYPE = "tax";
    protected final String TEST_WRITE_TAX_NAME_1 = "1234567890123456789";
    protected final String TEST_WRITE_TAX_NAME_2 = "1234567890123456788";
    protected final String TEST_WRITE_TAX_CONTENT = "12";

    // policy
    protected final String TEST_WRITE_POLICY_TYPE = "policy";
    protected final String TEST_WRITE_POLICY_NAME_1 = "售后服务1";
    protected final String TEST_WRITE_POLICY_NAME_2 = "售后服务2";
    protected final String TEST_WRITE_POLICY_CONTENT = "售后内容1";

    // invoice
    protected final String TEST_WRITE_INVOICE_TYPE = "invoice";
    protected final String TEST_WRITE_INVOICE_NAME_1 = "发票1";
    protected final String TEST_WRITE_INVOICE_NAME_2 = "发票2";
    protected final String TEST_WRITE_INVOICE_CONTENT = "发票内容";
}
