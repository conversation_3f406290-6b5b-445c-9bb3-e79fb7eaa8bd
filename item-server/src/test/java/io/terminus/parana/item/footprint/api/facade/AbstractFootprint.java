package io.terminus.parana.item.footprint.api.facade;

import io.terminus.parana.item.ItemContinuousTest;
import org.junit.AfterClass;

public abstract class AbstractFootprint extends ItemContinuousTest {

    @AfterClass
    public static void removeCache() {
        flushDB();
    }

    protected final Integer TEST_TENANT_ID = 1;
    protected final Long TEST_ID_1 = 1L;
    protected final Long TEST_ID_2 = 1L;
    protected final Long TEST_USER_ID = 100001L;
    protected final Long TEST_ITEM_ID_1 = 123456L;
    protected final Long TEST_ITEM_ID_2 = 654321L;
    protected final Integer TEST_PAGE_NO = 1;
    protected final Integer TEST_PAGE_SIZE = 10;
    protected final Integer TEST_SIZE = 10;

    protected final Long TEST_OPERATOR_ID = 1L;

}
