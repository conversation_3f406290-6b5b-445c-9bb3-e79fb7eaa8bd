package io.terminus.parana.item.relation.api.facade.feature;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.relation.api.bean.request.FeatureTemplateFindByCodeRequest;
import io.terminus.parana.item.relation.api.bean.request.FeatureTemplatePagingRequest;
import io.terminus.parana.item.relation.api.bean.response.FeatureTemplateInfo;
import io.terminus.parana.item.relation.api.facade.FeatureTemplateReadFacade;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> ymk
 * @date : 2019/12/13
 */
@TestDescription("特征库读")
public class FeatureTemplateReadFacadeTest extends AbstractFeature {

    @Autowired
    private FeatureTemplateReadFacade featureTemplateReadFacade;

    @Test
    @TestDescription("特征模版分页")
    public void test001() {
        FeatureTemplatePagingRequest request = new FeatureTemplatePagingRequest();
        request.setCode(TEST_WRITE_TAX_TYPE);
        request.setName("税率");
        request.setStatus(1);
        request.setTenantId(TEST_TENANT_ID);
        Response<Paging<FeatureTemplateInfo>> response = featureTemplateReadFacade.paging(request);
        Assert.take(response);
    }

    @Test
    @TestDescription("查找特征模版")
    public void test002() {
        FeatureTemplateFindByCodeRequest request = new FeatureTemplateFindByCodeRequest();
        request.setCode(TEST_WRITE_TAX_TYPE);
        request.setTenantId(TEST_TENANT_ID);
        Response<FeatureTemplateInfo> response = featureTemplateReadFacade.findByCode(request);
        Assert.take(response);
    }
}
