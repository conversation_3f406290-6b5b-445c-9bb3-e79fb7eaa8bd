package io.terminus.parana.item.relation.api.facade;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.common.model.Response;
import io.terminus.parana.item.relation.api.bean.request.ItemRelationCreateRequest;
import io.terminus.parana.item.relation.api.bean.request.ItemRelationDeleteRequest;
import io.terminus.parana.item.relation.api.bean.request.ItemRelationUpdateRequest;
import io.terminus.parana.item.relation.api.bean.request.param.ItemRelationDetailParam;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-14
 */
@TestDescription("商品系列")
public class ItemRelationWriteFacadeTest extends AbstractRelation {

    @Autowired
    private ItemRelationWriteFacade itemRelationWriteFacade;

    private static Long currentRelationId;

    @Test
    @TestDescription("全量创建商品系列")
    public void test001() {
        ItemRelationCreateRequest request = new ItemRelationCreateRequest();
        request.setShopId(TEST_SHOP_ID);
        request.setName(TEST_WRITE_INDEX_NAME);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        ItemRelationDetailParam detailParam = new ItemRelationDetailParam();
        detailParam.setTenantId(TEST_TENANT_ID);
        detailParam.setItemId(TEST_ITEM_ID_3);
        detailParam.setAlias(TEST_WRITE_DETAIL_ALIAS);

        request.setItemRelationDetailParamList(Lists.newArrayList(detailParam));

        Response<Long> response = itemRelationWriteFacade.createItemRelation(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
        currentRelationId = response.getResult();
    }

    @Test
    @TestDescription("删除整个系列")
    public void test003() {
        ItemRelationDeleteRequest request = new ItemRelationDeleteRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        request.setIdSet(Sets.newHashSet(currentRelationId));

        Response<Boolean> response = itemRelationWriteFacade.delete(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("更新商品系列（包括修改系列名称、商品别名、删除商品）")
    public void test005() {
        ItemRelationUpdateRequest request = new ItemRelationUpdateRequest();
        request.setName(TEST_WRITE_INDEX_NAME+"已被修改");
        request.setShopId(TEST_SHOP_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        request.setId(TEST_RELATION_ID);
        request.setTenantId(TEST_TENANT_ID);

        // 删除
//        ItemRelationDetailParam detailParam1 = new ItemRelationDetailParam();
//        detailParam1.setId(TEST_RELATION_DETAIL_ID);
//        detailParam1.setShopId(TEST_SHOP_ID);
//        detailParam1.setTenantId(TEST_TENANT_ID);
//        detailParam1.setRelationId(TEST_RELATION_ID);
//        detailParam1.setIsDelete(Boolean.TRUE);

        // 更新
        ItemRelationDetailParam detailParam2 = new ItemRelationDetailParam();
        detailParam2.setTenantId(TEST_TENANT_ID);
        detailParam2.setItemId(TEST_ITEM_ID_2);
        detailParam2.setAlias(TEST_WRITE_DETAIL_ALIAS_2+"已被修改");

        // 新增
        ItemRelationDetailParam detailParam3 = new ItemRelationDetailParam();
        detailParam3.setTenantId(TEST_TENANT_ID);
        detailParam3.setItemId(TEST_ITEM_ID_3);

        List<ItemRelationDetailParam> detailLists = Lists.newArrayList();
        detailLists.add(detailParam2);
        detailLists.add(detailParam3);

        request.setItemRelationDetailParamList(detailLists);
        Response<Boolean> response = itemRelationWriteFacade.updateItemRelation(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }
}
