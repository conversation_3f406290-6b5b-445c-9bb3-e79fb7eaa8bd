package io.terminus.parana.item.snapshot.api.facade;

import io.terminus.parana.item.ItemContinuousTest;
import org.junit.AfterClass;
import org.junit.Ignore;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-21
 */
@Ignore
public class AbstractSnapshot extends ItemContinuousTest {

    @AfterClass
    public static void removeCache() {
        flushDB();
    }

    protected final Long SNAPSHOT_PREPARED_ITEM_ID = 120000100012001L;
    protected final String SNAPSHOT_PREPARED_MD5 = "b2f5d2742cdb2ac61dc70150a3b65968";
}
