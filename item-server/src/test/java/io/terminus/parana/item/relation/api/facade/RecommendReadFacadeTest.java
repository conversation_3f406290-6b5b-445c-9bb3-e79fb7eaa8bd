package io.terminus.parana.item.relation.api.facade;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.recommend.api.bean.request.RecommendIndexPageRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendQueryByIdRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendQueryByTargetRequest;
import io.terminus.parana.item.recommend.api.bean.request.RecommendQueryByTypeRequest;
import io.terminus.parana.item.recommend.api.bean.response.RecommendIndexInfo;
import io.terminus.parana.item.recommend.api.bean.response.RecommendInfo;
import io.terminus.parana.item.recommend.api.facade.RecommendReadFacade;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date: 2019/10/23
 */
@TestDescription("推荐组读测试")
public class RecommendReadFacadeTest extends AbstractRelation{

    @Autowired
    RecommendReadFacade recommendReadFacade;

    @Test
    @TestDescription("根据条件分页查找推荐组信息")
    public void test001() {

        RecommendIndexPageRequest request = new RecommendIndexPageRequest();
        request.setName(TEST_RECOMMEND_INDEX_NAME_LIKE);
        request.setType(TEST_RECOMMEND_TYPE);
        request.setTenantId(TEST_TENANT_ID);
        request.setOperatorId(TEST_RECOMMEND_INDEX_OPERATOR_ID);
        request.checkParam();
        Response<Paging<RecommendIndexInfo>> response = recommendReadFacade.pagingIndex(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("根据id查找推荐组，可以限制透出数量")
    public void test002() {

        RecommendQueryByIdRequest request = new RecommendQueryByIdRequest();
        request.setId(TEST_RECOMMEND_INDEX_ID);
        request.setTenantId(TEST_TENANT_ID);

        Response<RecommendInfo> response = recommendReadFacade.findById(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("通过绑定目标获取推荐组信息，并限制推荐数量")
    public void test003() {

        RecommendQueryByTargetRequest request = new RecommendQueryByTargetRequest();
        request.setTargetId(TEST_BINDING_TARGET_ID);
        request.setTargetType(TEST_BINDING_TARGET_TYPE);
        request.setTenantId(TEST_TENANT_ID);
        request.setOperatorId(TEST_RECOMMEND_INDEX_OPERATOR_ID);

        Response<RecommendInfo> response = recommendReadFacade.findByTargetAndLimitCountAndOperatorId(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("获取推荐组列表")
    public void test004() {

        RecommendQueryByTypeRequest request = new RecommendQueryByTypeRequest();
        request.setType(TEST_RECOMMEND_TYPE);
        request.setOperatorId(TEST_RECOMMEND_INDEX_OPERATOR_ID);

        Response<List<RecommendIndexInfo>> response = recommendReadFacade.findByTypeAndOperatorId(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("分页查找推荐组,暂时未实现，采用分步查询效率更高")
    public void test005() {

    }

    @Test
    @TestDescription("通过目标查找推荐组基础信息")
    public void test006() {
        RecommendQueryByTargetRequest request = new RecommendQueryByTargetRequest();
        request.setTargetId(TEST_BINDING_TARGET_ID);
        request.setTargetType(TEST_BINDING_TARGET_TYPE);
        request.setTenantId(TEST_TENANT_ID);
        request.setOperatorId(TEST_RECOMMEND_INDEX_OPERATOR_ID);

        Response<RecommendIndexInfo> response = recommendReadFacade.findIndexByBindingTargetAndOperatorId(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

}
