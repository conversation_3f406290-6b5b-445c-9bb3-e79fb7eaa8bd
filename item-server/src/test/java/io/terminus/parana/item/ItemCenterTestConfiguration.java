package io.terminus.parana.item;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.rocketmq.producer.TerminusMQProducer;
import io.terminus.parana.item.filter.TestServiceAop;
import io.terminus.parana.item.item.api.bean.request.item.ChannelItemBaseRequest;
import io.terminus.parana.item.item.api.bean.response.item.ChannelItemBaseInfo;
import io.terminus.parana.item.search.docobject.ItemDO;
import io.terminus.parana.item.search.facade.ItemSearchFacade;
import io.terminus.parana.item.search.request.ItemSearchRequest;
import io.terminus.parana.search.client.result.SearchResult;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-07
 */
@Configuration
@EnableAutoConfiguration
@ComponentScan({
        "io.terminus.server",
        "io.terminus.parana.item",
})
public class ItemCenterTestConfiguration {

//    @Bean
//    public ESqlSessionTemplate eSqlSessionTemplate() {
//        return new ESqlSessionTemplate();
//    }
//
    @Bean
    public ItemSearchFacade itemSearchFacade() {
        return new ItemSearchFacade() {
            @Override
            public <Request extends ItemSearchRequest, DocObject extends ItemDO> Response<Paging<DocObject>> search(Request request) {
                return null;
            }

            @Override
            public Response<Paging<ChannelItemBaseInfo>> channelRecommend(ChannelItemBaseRequest request) {
                return null;
            }
        };
    }
//
//    @Bean
//    public SearchClientProperties searchClientProperties() {
//        return new SearchClientProperties();
//    }

//    @Bean
//    public SearchRequestParserHolder searchRequestParserHolder() {
//        return new SearchRequestParserHolder();
//    }
//
//    @Bean
//    public SearchClient searchClient() {
//        return new SearchClient() {
//            @Override
//            public <T> io.terminus.parana.search.client.result.SearchResult<T> search(SearchRequest searchRequest) {
//                return null;
//            }
//
//            @Override
//            public BulkResult bulk(BulkRequest bulkRequest) {
//                return null;
//            }
//
//            @Override
//            public boolean exist(String s) {
//                return false;
//            }
//
//            @Override
//            public boolean setup(String s, String s1) {
//                return false;
//            }
//
//            @Override
//            public <T> T getClient() {
//                return null;
//            }
//        };
//    }

    @Bean
    public TestServiceAop testServiceAop() {
        return new TestServiceAop();
    }

    @Bean
    @Primary
    @ConditionalOnMissingBean
    public TerminusMQProducer messageProducer() {
        return new MockMessageProducer();
    }
}
