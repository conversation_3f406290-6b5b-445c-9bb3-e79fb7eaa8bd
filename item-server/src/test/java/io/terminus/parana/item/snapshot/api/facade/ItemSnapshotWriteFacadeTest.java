package io.terminus.parana.item.snapshot.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.ItemContinuousTest;
import io.terminus.parana.item.snapshot.api.bean.ItemSnapshotTryCreateRequest;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-21
 */
@TestDescription("商品快照写")
public class ItemSnapshotWriteFacadeTest extends ItemContinuousTest {

    @Autowired
    private ItemSnapshotWriteFacade itemSnapshotWriteFacade;

    @Test
    @TestDescription("创建商品快照")
    public void test001() {
        ItemSnapshotTryCreateRequest request = new ItemSnapshotTryCreateRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setItemId(TEST_PREPARED_ITEM_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        Response<Long> response = itemSnapshotWriteFacade.tryCreateSnapshot(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }
}
