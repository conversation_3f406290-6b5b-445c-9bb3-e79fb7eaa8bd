package io.terminus.parana.item.footprint.api.facade;

import com.google.common.collect.Sets;
import io.terminus.common.model.Response;
import io.terminus.parana.item.footprint.api.bean.request.FootprintClearByUserIdRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintCreateOnRedisRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintDeleteOnRedisRequest;
import io.terminus.parana.item.test.TestDescription;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@TestDescription("店铺读")
public class ItemFootprintWriteFacadeTest extends AbstractFootprint {

    @Autowired
    private FootprintWriteFacade itemFootprintWriteFacade;

    @Test
    @TestDescription("添加商品足迹")
    public void createOnRedis() {
        FootprintCreateOnRedisRequest request = new FootprintCreateOnRedisRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setTargetId(TEST_ITEM_ID_1);
        request.setOperatorId(TEST_OPERATOR_ID);
        request.setCurrentOperatorId(TEST_OPERATOR_ID);
        Response<Boolean> response = itemFootprintWriteFacade.createOnRedis(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("删除指定商品足迹")
    public void batchDeleteOnRedis() {
        FootprintDeleteOnRedisRequest request = new FootprintDeleteOnRedisRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setTargetIdList(Lists.newArrayList(TEST_ITEM_ID_1+"_"+TEST_OPERATOR_ID, TEST_ITEM_ID_2+"_"+TEST_OPERATOR_ID));
        Response<Boolean> response = itemFootprintWriteFacade.batchDeleteOnRedis(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("清空足迹")
    public void clearByUserId() {
        FootprintClearByUserIdRequest request = new FootprintClearByUserIdRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        Response<Boolean> response = itemFootprintWriteFacade.clearByUserId(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

}
