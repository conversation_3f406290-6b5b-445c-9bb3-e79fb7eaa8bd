package io.terminus.parana.item;

import io.terminus.parana.item.test.continous.DebugMaskUtil;
import io.terminus.parana.item.test.continous.protocol.JettyServer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-21
 */
@SpringBootApplication
public class DebugRunner {

    public static void main(String... args) {
        SpringApplication.run(DebugRunner.class, args);
    }

    @Value("${delegate.server.port:8091}")
    private Integer port;

    private void listenForShutdown() {
        Runtime.getRuntime().addShutdownHook(new Thread(DebugMaskUtil::removeDebugMode));
    }

    @EventListener
    public void onReady(ContextRefreshedEvent event) {
        // 注册程序退出事件
        listenForShutdown();
        DebugMaskUtil.maskDebugMode(port);
    }

    @Bean
    public JettyServer jettyServer(ApplicationContext applicationContext) {
        return new JettyServer(port, applicationContext);
    }
}
