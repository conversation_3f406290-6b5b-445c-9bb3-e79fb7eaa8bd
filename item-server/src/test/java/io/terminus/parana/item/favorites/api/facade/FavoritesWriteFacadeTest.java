package io.terminus.parana.item.favorites.api.facade;

import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import io.terminus.parana.item.favorites.api.bean.request.*;
import io.terminus.parana.item.favorites.api.bean.request.param.FavoritesParam;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@TestDescription("收藏写")
public class FavoritesWriteFacadeTest extends AbstractFavorites {

    @Autowired
    private FavoritesWriteFacade favoritesWriteFacade;

    @Test
    @TestDescription("新增")
    public void add() {
        FavoritesAddRequest request = new FavoritesAddRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setTargetId(TEST_ITEM_ID);
        request.setTargetType(TEST_TARGET_TYPE);
        Response<Boolean> response = favoritesWriteFacade.add(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("取消收藏")
    public void cancelByTargetId() {
        FavoritesCancelRequest request = new FavoritesCancelRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setTargetId(TEST_ITEM_ID);
        request.setTargetType(TEST_TARGET_TYPE);
        Response<Boolean> response = favoritesWriteFacade.cancel(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("取消收藏")
    public void cancelById() {
        FavoritesCancelByIdsRequest request = new FavoritesCancelByIdsRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setIds(Lists.newArrayList(TEST_ID));
        Response<Boolean> response = favoritesWriteFacade.batchCancelById(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("取消全部收藏")
    public void cancelByUserId() {
        FavoritesCancelByUserIdRequest request = new FavoritesCancelByUserIdRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUserId(TEST_USER_ID);
        request.setTargetType(TEST_TARGET_TYPE);
        Response<Boolean> response = favoritesWriteFacade.cancelByUserId(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("购物车加入收藏")
    public void recordInCart() {
        FavoritesParam favoritesParam = new FavoritesParam();
        favoritesParam.setUserId(TEST_USER_ID);
        favoritesParam.setTargetId(TEST_ITEM_ID);
        favoritesParam.setTargetType(TEST_TARGET_TYPE);

        FavoritesRecordInCartRequest request = new FavoritesRecordInCartRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setFavoritesParamList(Lists.newArrayList(favoritesParam));
        Response<Boolean> response = favoritesWriteFacade.recordInCart(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertTrue(response.getResult());
    }


}
