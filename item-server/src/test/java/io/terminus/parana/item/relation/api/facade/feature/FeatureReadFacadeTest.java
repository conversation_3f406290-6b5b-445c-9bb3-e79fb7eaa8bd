package io.terminus.parana.item.relation.api.facade.feature;

import io.terminus.common.model.Response;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.relation.api.bean.request.FeatureFindBdListByTargetRequest;
import io.terminus.parana.item.relation.api.bean.request.FeatureFindByTargetAndCodeRequest;
import io.terminus.parana.item.relation.api.bean.response.FeatureInfo;
import io.terminus.parana.item.relation.api.facade.FeatureReadFacade;
import io.terminus.parana.item.relation.enums.RelationBindingTargetType;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date : 2019/12/13
 */
@TestDescription("特征库读")
public class FeatureReadFacadeTest extends AbstractFeature {

    @Autowired
    private FeatureReadFacade featureReadFacade;

    @Test
    @TestDescription("查找特征")
    public void test001() {
        FeatureFindByTargetAndCodeRequest request = new FeatureFindByTargetAndCodeRequest();
        request.setTargetType(RelationBindingTargetType.BACK_CATEGORY.getValue());
        request.setTargetMark(TEST_CATEGORY_ID_3);
        request.setCode("test-code");
        request.setTenantId(TEST_TENANT_ID);
        Response<FeatureInfo> response = featureReadFacade.findByTargetAndCode(request);
        Assert.take(response);
    }

    @Test
    @TestDescription("获取特征绑定列表")
    public void test002() {
        FeatureFindBdListByTargetRequest request = new FeatureFindBdListByTargetRequest();
        request.setTargetType(RelationBindingTargetType.BACK_CATEGORY.getValue());
        request.setTargetMark(TEST_CATEGORY_ID_3);
        request.setTenantId(TEST_TENANT_ID);
        Response<List<FeatureInfo>> response = featureReadFacade.findBdListByTarget(request);
        Assert.take(response);
    }
}
