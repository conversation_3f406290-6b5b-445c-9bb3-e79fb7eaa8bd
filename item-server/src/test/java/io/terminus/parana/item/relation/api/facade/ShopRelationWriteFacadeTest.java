package io.terminus.parana.item.relation.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.relation.api.bean.request.CreateShopRelationRequest;
import io.terminus.parana.item.relation.api.bean.request.DeleteShopRelationRequest;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-14
 */
@TestDescription("商品关系")
public class ShopRelationWriteFacadeTest extends AbstractShopRelation {

    @Autowired
    private ShopRelationWriteFacade shopRelationWriteFacade;

    @Test
    @TestDescription("创建店铺关联关系")
    public void test001() {
        CreateShopRelationRequest request = new CreateShopRelationRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setSourceShopId(TEST_SHOP_ID_2);
        request.setTargetShopId(TEST_SHOP_ID_1);
        request.setUpdatedBy(TEST_UPDATED_BY);

        Response<Long> response = shopRelationWriteFacade.create(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("删除店铺关联关系")
    public void test002() {
        DeleteShopRelationRequest request = new DeleteShopRelationRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setId(TEST_ID);

        Response<Boolean> response = shopRelationWriteFacade.delete(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }
}
