package io.terminus.parana.item.partnership.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.area.enums.CooperationMode;
import io.terminus.parana.item.area.enums.LogisticsMode;
import io.terminus.parana.item.partnership.api.bean.request.VendorPartnershipCreateRequest;
import io.terminus.parana.item.partnership.api.bean.request.VendorPartnershipDeleteRequest;
import io.terminus.parana.item.partnership.api.bean.request.VendorPartnershipStopRequest;
import io.terminus.parana.item.partnership.api.bean.request.VendorPartnershipUpdateRequest;
import io.terminus.parana.item.partnership.api.bean.request.param.VendorPartnershipParam;
import io.terminus.parana.item.partnership.manager.VendorPartnershipTranslationManager;
import io.terminus.parana.item.partnership.model.VendorPartnership;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@TestDescription("合作关系写")
public class VendorPartnershipWriteFacadeTest extends AbstractVendorPartnership {

    @Autowired
    private VendorPartnershipWriteFacade vendorPartnershipWriteFacade;


    @Autowired
    private VendorPartnershipTranslationManager manager;

    @Test
    @TestDescription("创建")
    public void create() {
        VendorPartnershipCreateRequest request = new VendorPartnershipCreateRequest();

        VendorPartnershipParam param = new VendorPartnershipParam();
        param.setOperatorId(TEST_OPERATOR_ID);
        param.setVendorId(TEST_VENDOR_ID);
        param.setCooperationMode(CooperationMode.BASE_PRICE.getValue());
        param.setLogisticsMode(LogisticsMode.GOODS_COLLECTION.getValue());
//        param.setStatus(VendorPartnershipStatus.AVAILABLE.getStatus());

        request.setParam(param);

        Response<Boolean> response = vendorPartnershipWriteFacade.create(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }


    @Test
    @TestDescription("绑定")
    public void binding() {
        VendorPartnershipCreateRequest request = new VendorPartnershipCreateRequest();
        VendorPartnershipParam param = new VendorPartnershipParam();
        param.setOperatorId(1L);
        param.setVendorId(3L);
        param.setSettlementPeriod("260天");
        param.setFeeRate(8.9);
        param.setCooperationMode(1);
        param.setLogisticsMode(1);

        request.setParam(param);

        Response<Boolean> response = vendorPartnershipWriteFacade.binding(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }


    @Test
    @TestDescription("解绑")
    public void unBinding() {
        VendorPartnershipCreateRequest request = new VendorPartnershipCreateRequest();
        VendorPartnershipParam param = new VendorPartnershipParam();
        param.setOperatorId(1L);
        param.setVendorId(3L);

        request.setParam(param);
        Response<Boolean> response = vendorPartnershipWriteFacade.unBinding(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("更新")
    public void update() {
        VendorPartnershipUpdateRequest request = new VendorPartnershipUpdateRequest();

        VendorPartnershipParam param = new VendorPartnershipParam();
//        param.setStatus(VendorPartnershipStatus.UNAVAILABLE.getStatus());
        param.setOperatorId(TEST_OPERATOR_ID);
        param.setVendorId(TEST_VENDOR_ID);
        param.setLogisticsMode(TEST_LOGISTICS_MODE);
        param.setCooperationMode(TEST_COOPERATION_MODE);
        param.setFeeRate(9.89);
        param.setSettlementPeriod("7");

        request.setParam(param);
        request.setUpdatedBy("test");

        Response<Boolean> response = vendorPartnershipWriteFacade.update(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }


    @Test
    public void testForLogisticModeUpdate() {
        VendorPartnership vendorPartnership = new VendorPartnership();
        vendorPartnership.setOperatorId(1L);
        vendorPartnership.setVendorId(1L);
        vendorPartnership.setLogisticsMode(LogisticsMode.NON_GOODS_COLLECTION.getValue());
        vendorPartnership.setWarehouseCode("12345");
        manager.updateLogisticsMode(vendorPartnership);
    }
    @Test
    public void testDelete() {
        VendorPartnershipDeleteRequest request = new VendorPartnershipDeleteRequest();

        request.setUpdatedBy("test");
        request.setId(10L);
        Response<Boolean> response = vendorPartnershipWriteFacade.delete(request);
        Assert.assertTrue(response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }


}
