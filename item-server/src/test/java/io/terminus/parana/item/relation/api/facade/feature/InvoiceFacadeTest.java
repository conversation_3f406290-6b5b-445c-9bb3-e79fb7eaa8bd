package io.terminus.parana.item.relation.api.facade.feature;

import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.request.feature.invoice.*;
import io.terminus.parana.item.category.api.bean.response.feature.InvoiceInfo;
import io.terminus.parana.item.category.api.facade.feature.InvoiceFacade;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> ymk
 * @date : 2019/11/26
 */
@TestDescription("发票")
public class InvoiceFacadeTest extends AbstractFeature {

    @Autowired
    private InvoiceFacade invoiceFacade;

    @Test
    @TestDescription("发票创建")
    public void test000() {
        InvoiceCreateRequest request = new InvoiceCreateRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setCategoryId(TEST_CATEGORY_ID_1);
        request.setName(TEST_WRITE_INVOICE_NAME_1);
        request.setContent(TEST_WRITE_INVOICE_CONTENT);
        Response<Boolean> result = invoiceFacade.create(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());

        request.setTenantId(TEST_TENANT_ID);
        request.setCategoryId(TEST_CATEGORY_ID_2);
        request.setName(TEST_WRITE_INVOICE_NAME_2);
        request.setContent(TEST_WRITE_INVOICE_CONTENT);
        result = invoiceFacade.create(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());
    }

    @Test
    @TestDescription("发票更新")
    public void test001() {
        InvoiceUpdateRequest request = new InvoiceUpdateRequest();
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("发票更新");
        request.setCategoryId(TEST_CATEGORY_ID_1);
        request.setName(TEST_WRITE_INVOICE_NAME_1);
        request.setContent(TEST_WRITE_INVOICE_CONTENT+"已被修改");
        Response<Boolean> result = invoiceFacade.update(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());
    }

    @Test
    @TestDescription("发票查询")
    public void test002() {
        InvoiceFindByCategoryRequest request = new InvoiceFindByCategoryRequest();
        request.setCategoryId(TEST_CATEGORY_ID_3);
        request.setTenantId(TEST_TENANT_ID);
        Response<InvoiceInfo> result = invoiceFacade.findByCategory(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getResult());
    }

    @Test
    @TestDescription("已绑定发票的类目列表查询")
    public void test003() {
        InvoiceFindListByPidRequest request = new InvoiceFindListByPidRequest();
        request.setPid(TEST_CATEGORY_ID_1);
        request.setExtensionType(0);
        request.setTenantId(TEST_TENANT_ID);
        Response<List<Long>> result = invoiceFacade.queryChildrenBindingList(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getResult());
    }

    @Test
    @TestDescription("删除发票")
    public void test004() {
        InvoiceDeleteRequest request = new InvoiceDeleteRequest();
        request.setCategoryId(TEST_CATEGORY_ID_1);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy("已被删除");
        Response<Boolean> result = invoiceFacade.delete(request);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getResult());
    }
}
