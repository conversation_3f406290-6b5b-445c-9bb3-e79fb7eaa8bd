package io.terminus.parana.item.relation.api.facade;

import io.terminus.parana.item.ItemContinuousTest;
import org.junit.AfterClass;
import org.junit.Ignore;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-15
 */
@Ignore
public class AbstractItemRelation extends ItemContinuousTest {

    @AfterClass
    public static void removeCache() {
        flushDB();
    }

    protected final Long TEST_USER_ID = 100001L;
    protected final Long TEST_ITEM_ID = 110000100012001L;
    protected final Long TEST_ITEM_ID_2 = 110000100012002L;
    protected final Long TEST_SHOP_ID = 1L;
    protected final Long TEST_RELATION_ID = 1L;
    protected final Long TEST_RELATION_ID_2 = 22L;
    protected final String TEST_NAME = "单测创建系列";
    protected final String TEST_ALIAS = "单测别名";
    protected final Integer TEST_PAGE_NO = 1;
    protected final Integer TEST_PAGE_SIZE = 10;
    protected final Integer TEST_SIZE = 10;

}
