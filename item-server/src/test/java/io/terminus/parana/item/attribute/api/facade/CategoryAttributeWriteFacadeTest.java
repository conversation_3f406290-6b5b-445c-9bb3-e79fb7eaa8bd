package io.terminus.parana.item.attribute.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.attribute.api.bean.request.*;
import io.terminus.parana.item.attribute.enums.CategoryAttributeValueType;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-08
 */
@TestDescription("类目属性写")
public class CategoryAttributeWriteFacadeTest extends AbstractCategoryAttribute {

    private static Long ATTRIBUTE_ID;
    private static Long ATTRIBUTE_BINDING_ID;

    @Autowired
    private CategoryAttributeWriteFacade categoryAttributeWriteFacade;

    @Test
    @TestDescription("创建类目属性")
    public void test001() {
        CategoryAttributeCreateRequest request = new CategoryAttributeCreateRequest();
        request.setName("test001");
        request.setValueType(CategoryAttributeValueType.TEXT.getValue());
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        Response<Long> response = categoryAttributeWriteFacade.create(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());
        ATTRIBUTE_ID = response.getResult();
    }

    @Test
    @TestDescription("更新类目属性")
    public void test002() {
        Assert.assertNotNull("必须先执行创建方法", ATTRIBUTE_ID);

        CategoryAttributeUpdateRequest request = new CategoryAttributeUpdateRequest();
        request.setId(ATTRIBUTE_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        request.setTenantId(TEST_TENANT_ID);


        Response<Boolean> response = categoryAttributeWriteFacade.update(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("删除类目属性")
    public void test003() {
        CategoryAttributeDeleteRequest request = new CategoryAttributeDeleteRequest();
        request.setId(ATTRIBUTE_ID);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        Response<Boolean> response = categoryAttributeWriteFacade.delete(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    private Long createBinding(Long attributeId) {
        CategoryAttributeBindingCreateRequest request = new CategoryAttributeBindingCreateRequest();
        request.setAttributeId(attributeId);
        request.setCategoryId(TEST_BACK_CATEGORY_LEVEL2);

        request.setOverride(false);
        request.setUpdatedBy(TEST_UPDATED_BY);
        request.setTenantId(TEST_TENANT_ID);

        Response<Long> response = categoryAttributeWriteFacade.bindingCreate(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());

        return response.getResult();
    }

    @Test
    @TestDescription("在类目上创建类目属性")
    public void test004() {
        ATTRIBUTE_BINDING_ID = createBinding(PREPARED_CATEGORY_ATTRIBUTE_ID);

        // 多创建一个，以备使用
        createBinding(PREPARED_CATEGORY_ATTRIBUTE_ID_2);
    }

    @Test
    @TestDescription("更新类目上的绑定属性")
    public void test005() {
        Assert.assertNotNull("必须先执行绑定方法", ATTRIBUTE_BINDING_ID);

        CategoryAttributeBindingUpdateRequest request = new CategoryAttributeBindingUpdateRequest();
        request.setId(ATTRIBUTE_BINDING_ID);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        request.setSalable(true);
        request.setGroup("普通属性");

        Response<Boolean> response = categoryAttributeWriteFacade.bindingUpdate(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("绑定调整顺序(已绑定间)")
    public void test006() {
        Assert.assertNotNull("必须先执行绑定方法", ATTRIBUTE_BINDING_ID);

        CategoryAttributeBindingExchangeOrderRequest request = new CategoryAttributeBindingExchangeOrderRequest();
        request.setCategoryId(TEST_BACK_CATEGORY_LEVEL2);
        request.setFirstBindingId(2L);
        request.setOtherBindingId(3L);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        Response<Boolean> response = categoryAttributeWriteFacade.bindingExchangeOrder(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("绑定调整顺序(继承与绑定间)")
    public void test007() {
        Assert.assertNotNull("必须先执行绑定方法", ATTRIBUTE_BINDING_ID);

        CategoryAttributeBindingExchangeOrderRequest request = new CategoryAttributeBindingExchangeOrderRequest();
        request.setCategoryId(TEST_BACK_CATEGORY_LEVEL3);
        request.setFirstBindingId(3L);
        request.setOtherBindingId(4L);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        Response<Boolean> response = categoryAttributeWriteFacade.bindingExchangeOrder(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("绑定调整顺序(继承间)[数据不够，需要调整]")
    public void test008() {
        Assert.assertNotNull("必须先执行绑定方法", ATTRIBUTE_BINDING_ID);

        CategoryAttributeBindingExchangeOrderRequest request = new CategoryAttributeBindingExchangeOrderRequest();
        request.setCategoryId(TEST_BACK_CATEGORY_LEVEL3);
        request.setFirstBindingId(1L);
        request.setOtherBindingId(3L);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        Response<Boolean> response = categoryAttributeWriteFacade.bindingExchangeOrder(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("删除类目上的绑定属性")
    public void test009() {
        Assert.assertNotNull("必须先执行绑定方法", ATTRIBUTE_BINDING_ID);
        CategoryAttributeBindingDeleteRequest request = new CategoryAttributeBindingDeleteRequest();
        request.setId(ATTRIBUTE_BINDING_ID);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);

        Response<Boolean> response = categoryAttributeWriteFacade.bindingDelete(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    private static Long NO_ATTRIBUTE_BINDING;

    @Test
    @TestDescription("在类目上创建类目属性(无属性id)")
    public void test010() {
        CategoryAttributeBindingCreateRequest request = new CategoryAttributeBindingCreateRequest();
        request.setCategoryId(4L);

        request.setOverride(false);
        request.setUpdatedBy(TEST_UPDATED_BY);
        request.setTenantId(TEST_TENANT_ID);
        request.setName("无属性id");
        request.setGroup("普通属性");
        request.setRequired(true);
        request.setSalable(true);
        request.setCustomizable(true);
        request.setSearchable(true);
        request.setDisplayable(true);
        request.setValueType(CategoryAttributeValueType.TEXT.getValue());

        Response<Long> response = categoryAttributeWriteFacade.bindingCreate(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());
        NO_ATTRIBUTE_BINDING = response.getResult();
    }

    @Test
    @TestDescription("在类目上更新类目属性(无属性id)")
    public void test011() {
        Assert.assertNotNull("必须先执行创建", NO_ATTRIBUTE_BINDING);
        CategoryAttributeUpdateRequest request = new CategoryAttributeUpdateRequest();
        request.setId(NO_ATTRIBUTE_BINDING);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        request.setSearchable(true);
        Response<Boolean> response = categoryAttributeWriteFacade.update(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("拒绝继承的属性绑定")
    public void test012() {
        CategoryAttributeBindingRefuseRequest request = new CategoryAttributeBindingRefuseRequest();
        request.setAttributeId(ATTRIBUTE_ID);
        request.setCategoryId(TEST_BACK_CATEGORY_LEVEL3);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        Response<Boolean> response = categoryAttributeWriteFacade.bindingRefuse(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("取消拒绝继承的属性绑定")
    public void test013() {
        Assert.assertNotNull("必须先执行创建方法", ATTRIBUTE_ID);
        CategoryAttributeBindingRefuseRequest request = new CategoryAttributeBindingRefuseRequest();
        request.setCategoryId(TEST_BACK_CATEGORY_LEVEL3);
        request.setAttributeId(ATTRIBUTE_ID);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        Response<Boolean> response = categoryAttributeWriteFacade.bindingCancelRefuse(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    private static Long GROUP_ID = null;

    private Long createAttributeGroup(String name) {
        AttributeGroupCreateRequest request = new AttributeGroupCreateRequest();
        request.setName(name);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        Response<Long> response = categoryAttributeWriteFacade.createGroup(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());

        return response.getResult();
    }

    @Test
    @TestDescription("创建属性组")
    public void test014() {
        GROUP_ID = createAttributeGroup("创建的属性组");
    }

    @Test
    @TestDescription("更新属性组名字")
    public void test015() {
        Assert.assertNotNull("必须先创建属性组", GROUP_ID);

        AttributeGroupUpdateNameRequest request = new AttributeGroupUpdateNameRequest();
        request.setId(GROUP_ID);
        request.setName("名字更新测试");
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        Response<Boolean> response = categoryAttributeWriteFacade.updateGroupName(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("交换属性组排序")
    public void test016() {
        Long otherId = createAttributeGroup("另一个");
        AttributeGroupExchangeOrderRequest request = new AttributeGroupExchangeOrderRequest();
        request.setFirstId(GROUP_ID);
        request.setOtherId(otherId);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        Response<Boolean> response = categoryAttributeWriteFacade.exchangeGroupOrder(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }

    @Test
    @TestDescription("删除属性组")
    public void test017() {
        AttributeGroupDeleteRequest request = new AttributeGroupDeleteRequest();
        request.setId(GROUP_ID);
        request.setTenantId(TEST_TENANT_ID);
        request.setUpdatedBy(TEST_UPDATED_BY);
        Response<Boolean> response = categoryAttributeWriteFacade.deleteGroup(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertTrue(response.getResult());
    }
}
