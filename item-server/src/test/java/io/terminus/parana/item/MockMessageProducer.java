package io.terminus.parana.item;

import io.terminus.common.rocketmq.common.TerminusMessage;
import io.terminus.common.rocketmq.common.TerminusSendCallback;
import io.terminus.common.rocketmq.common.TerminusSendResult;
import io.terminus.common.rocketmq.producer.TerminusMQProducer;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-02-12
 */
public class MockMessageProducer extends TerminusMQProducer {
    public MockMessageProducer() {
        super(null);
    }

    @Override
    public TerminusSendResult send(String s, String s1, Object o, String s2) {
        return null;
    }

    @Override
    public void asyncSend(String s, String s1, Object o, TerminusSendCallback terminusSendCallback) {

    }

    @Override
    public TerminusSendResult sendOrderly(String s, String s1, Object o, Object o1) {
        return null;
    }

    @Override
    public void asyncSendOrderly(String s, String s1, Object o, Object o1, TerminusSendCallback terminusSendCallback) {

    }

    @Override
    public TerminusSendResult send(TerminusMessage terminusMessage) {
        return null;
    }

    @Override
    public TerminusSendResult sendOrderly(TerminusMessage terminusMessage) {
        return null;
    }

    @Override
    public void asyncSend(TerminusMessage terminusMessage, TerminusSendCallback terminusSendCallback) {

    }

    @Override
    public void asyncSendOrderly(TerminusMessage terminusMessage, TerminusSendCallback terminusSendCallback) {

    }

    @Override
    public void release() {

    }
}
