package io.terminus.parana.item.relation.api.facade;

import io.terminus.parana.item.ItemContinuousTest;
import org.junit.AfterClass;
import org.junit.Ignore;

/**
 * <AUTHOR> ymk
 * @date: 2019/7/22
 */
@Ignore
public class AbstractRelation extends ItemContinuousTest {

    @AfterClass
    public static void removeCache() {
        flushDB();
    }

    protected final Long TEST_ITEM_ID = 110000100012001L;
    protected final Long TEST_ITEM_ID_2 = 110000100012002L;
    protected final Long TEST_ITEM_ID_3 = 110000100012003L;

    //relation_binding
    protected final Long TEST_RELATION_BINDING_ID = 1L;
    protected final String TEST_SOURCE_TYPE_RELATION = "relation";
    protected final Long TEST_SOURCE_ID = 1L;
    protected final String TEST_TARGET_TYPE = "item";
    protected final Long TEST_TARGET_ID = 1L;
    protected final Integer TEST_STATUS = 1;


    //relation_index
    protected final Long TEST_RELATION_INDEX_ID = 2L;

    protected final String TEST_RELATION_INDEX_NAME = "系列";
    protected final String TEST_RELATION_INDEX_ALIAS = "生活用品";

    protected final String TEST_WRITE_INDEX_NAME = "新建系列名称";
    protected final String TEST_WRITE_INDEX_NAME_2 = "修改系列名称2";


    //relation_detail
    protected final Long TEST_RELATION_DETAIL_ID = 3L;
    protected final Long TEST_RELATION_DETAIL_ID_2 = 4L;
    protected final Long TEST_RELATION_DETAIL_ID_3 = 5L;
    protected final Long TEST_RELATION_ID = 2L;
    protected final String TEST_DETAIL_TARGET_TYPE = "item";

    protected final String TEST_WRITE_DETAIL_ALIAS = "系列中商品1别名";
    protected final String TEST_WRITE_DETAIL_ALIAS_2 = "系列中商品2别名";

    //relation_binding
    protected final String TEST_BINDING_SOURCE_TYPE = "relation";
    protected final String TEST_BINDING_TARGET_TYPE = "category";
    protected final Long TEST_BINDING_SOURCE_ID = 2L;
    protected final Long TEST_BINDING_TARGET_ID = 1L;

    //recommend_index
    protected final Long TEST_RECOMMEND_INDEX_ID = 1L;
    protected final String TEST_RECOMMEND_GROUP = "recommend";
    protected final String TEST_RECOMMEND_TYPE = "item-recommend";
    protected final String TEST_RECOMMEND_INDEX_NAME_LIKE = "用品";
    protected final Long TEST_RECOMMEND_INDEX_OPERATOR_ID = 1111111111L;
    protected final Long TEST_RECOMMEND_INDEX_OPERATOR_ID_2 = 222222222L;

    protected final String TEST_RECOMMEND_INDEX_WRITE_NAME = "试着建一个推荐组";
    protected final String TEST_RECOMMEND_INDEX_WRITE_NAME_2 = "试着改个名字";
    protected final String TEST_RECOMMEND_INDEX_WRITE_NAME_3 = "试着改个名字3";
    protected final String TEST_RECOMMEND_INDEX_TARGET_TYPE = "item";

    //recommend_detal
    protected final Long TEST_RECOMMEND_DETAIL_ID = 1L;
    protected final Long TEST_RECOMMEND_DETAIL_ID_2 = 2L;
    protected final Integer TEST_RECOMMEND_DETAIL_LIMITCOUNT = 5;
}
