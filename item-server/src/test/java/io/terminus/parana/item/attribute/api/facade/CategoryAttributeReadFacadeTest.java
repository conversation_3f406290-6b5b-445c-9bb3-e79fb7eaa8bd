package io.terminus.parana.item.attribute.api.facade;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.attribute.api.bean.request.AttributeGroupListAllRequest;
import io.terminus.parana.item.attribute.api.bean.request.CategoryAttributeBindingRenderRequest;
import io.terminus.parana.item.attribute.api.bean.request.CategoryAttributePagingRequest;
import io.terminus.parana.item.attribute.api.bean.request.CategoryAttributeQueryByIdRequest;
import io.terminus.parana.item.attribute.api.bean.response.CategoryAttributeBindingInfo;
import io.terminus.parana.item.attribute.api.bean.response.CategoryAttributeGroupInfo;
import io.terminus.parana.item.attribute.api.bean.response.CategoryAttributeInfo;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-08
 */
@TestDescription("类目属性读")
public class CategoryAttributeReadFacadeTest extends AbstractCategoryAttribute {

    @Autowired
    private CategoryAttributeReadFacade categoryAttributeReadFacade;

    @Test
    @TestDescription("属性库分页")
    public void test001() {
        CategoryAttributePagingRequest request = new CategoryAttributePagingRequest();
        Response<Paging<CategoryAttributeInfo>> response = categoryAttributeReadFacade.paging(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertFalse(response.getResult().isEmpty());
    }

    @Test
    @TestDescription("标记非继承的属性渲染")
    public void test002() {
        CategoryAttributeBindingRenderRequest request = new CategoryAttributeBindingRenderRequest();
        request.setCategoryId(TEST_BACK_CATEGORY_LEVEL3);
        Response<Map<String, List<CategoryAttributeBindingInfo>>> response = categoryAttributeReadFacade.renderMarkRefuse(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertFalse(CollectionUtils.isEmpty(response.getResult()));

        boolean hasInherit = false;
        boolean hasRefuse = false;
        for (List<CategoryAttributeBindingInfo> bindingInfoList : response.getResult().values()) {
            for (CategoryAttributeBindingInfo info : bindingInfoList) {
                hasInherit |= info.getInherit();

                if (!CollectionUtils.isEmpty(info.getExtra())) {
                    String refuse = info.getExtra().get("refuse");

                    if ("true".equals(refuse)) {
                        hasRefuse = true;
                    }
                }
            }
        }

        Assert.assertTrue(hasInherit);
        Assert.assertTrue(hasRefuse);
    }

    @Test
    @TestDescription("剔除非继承的属性渲染")
    public void test003() {
        CategoryAttributeBindingRenderRequest request = new CategoryAttributeBindingRenderRequest();
        request.setCategoryId(TEST_BACK_CATEGORY_LEVEL3);

        Response<Map<String, List<CategoryAttributeBindingInfo>>> response = categoryAttributeReadFacade.renderExcludeRefuse(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertFalse(CollectionUtils.isEmpty(response.getResult()));

        boolean hasInherit = false;
        boolean hasRefuse = false;
        for (List<CategoryAttributeBindingInfo> bindingInfoList : response.getResult().values()) {
            for (CategoryAttributeBindingInfo info : bindingInfoList) {
                hasInherit |= info.getInherit();

                if (!CollectionUtils.isEmpty(info.getExtra())) {
                    String refuse = info.getExtra().get("refuse");

                    if ("true".equals(refuse)) {
                        hasRefuse = true;
                    }
                }
            }
        }

        Assert.assertTrue(hasInherit);
        Assert.assertFalse(hasRefuse);
    }

    @Test
    @TestDescription("获取指定类目的属性")
    public void test004() {
        CategoryAttributeBindingRenderRequest request = new CategoryAttributeBindingRenderRequest();
        request.setCategoryId(TEST_BACK_CATEGORY_LEVEL3);
        Response<List<CategoryAttributeBindingInfo>> response = categoryAttributeReadFacade.renderByCategory(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertFalse(CollectionUtils.isEmpty(response.getResult()));

        boolean hasInherit = false;
        boolean hasRefuse = false;
        for (CategoryAttributeBindingInfo info : response.getResult()) {
            hasInherit |= info.getInherit();

            if (!CollectionUtils.isEmpty(info.getExtra())) {
                String refuse = info.getExtra().get("refuse");

                if ("true".equals(refuse)) {
                    hasRefuse = true;
                }
            }
        }

        Assert.assertFalse(hasInherit);
        Assert.assertFalse(hasRefuse);
    }

    @Test
    @TestDescription("获取属性的使用信息")
    public void test005() {
        CategoryAttributeQueryByIdRequest request = new CategoryAttributeQueryByIdRequest();
        request.setAttributeId(PREPARED_BIND_CATEGORY_ATTRIBUTE_ID);
        Response<List<CategoryAttributeBindingInfo>> response = categoryAttributeReadFacade.attributeUsage(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertFalse(CollectionUtils.isEmpty(response.getResult()));

        for (CategoryAttributeBindingInfo bindingInfo : response.getResult()) {
            Assert.assertFalse(CollectionUtils.isEmpty(bindingInfo.getExtra()));
            Assert.assertNotNull(bindingInfo.getExtra().get("category"));
        }
    }

    @Test
    @TestDescription("按序获取所有的属性组")
    public void test006() {
        AttributeGroupListAllRequest request = new AttributeGroupListAllRequest();
        request.setTenantId(TEST_TENANT_ID);
        Response<List<CategoryAttributeGroupInfo>> response = categoryAttributeReadFacade.listAllGroup(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        List<CategoryAttributeGroupInfo> groupInfoList = response.getResult();
        Assert.assertFalse(CollectionUtils.isEmpty(groupInfoList));
        int order = -1;
        for (CategoryAttributeGroupInfo groupInfo : groupInfoList) {
            Assert.assertTrue("排序错误", groupInfo.getOrder() > order);
            order = groupInfo.getOrder();
        }
    }
}
