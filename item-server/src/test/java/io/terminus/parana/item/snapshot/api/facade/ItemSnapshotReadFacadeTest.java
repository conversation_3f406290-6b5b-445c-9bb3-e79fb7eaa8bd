package io.terminus.parana.item.snapshot.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.common.response.MapBatchResult;
import io.terminus.parana.item.item.api.bean.response.item.FullItemWithDetailInfo;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.snapshot.api.bean.ItemSnapshotBatchQueryRequest;
import io.terminus.parana.item.snapshot.api.bean.ItemSnapshotQueryFullRequest;
import io.terminus.parana.item.test.TestDescription;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-21
 */
@TestDescription("快照读接口")
public class ItemSnapshotReadFacadeTest extends AbstractSnapshot {

    @Autowired
    private ItemSnapshotReadFacade itemSnapshotReadFacade;

    @Test
    @TestDescription("查询完整的快照内容")
    public void test001() {
        ItemSnapshotQueryFullRequest request = new ItemSnapshotQueryFullRequest();
        request.setItemId(SNAPSHOT_PREPARED_ITEM_ID);
        request.setItemMd5(SNAPSHOT_PREPARED_MD5);
        request.setTenantId(TEST_TENANT_ID);
        Response<FullItemWithDetailInfo> response = itemSnapshotReadFacade.queryFullSnapshot(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    @TestDescription("批量查询商品")
    public void test002() {
        ItemSnapshotBatchQueryRequest request = new ItemSnapshotBatchQueryRequest();
        request.setItemIdSet(Collections.singleton(SNAPSHOT_PREPARED_ITEM_ID));
        request.setTenantId(TEST_TENANT_ID);
        Response<MapBatchResult<Long, ItemInfo>> response = itemSnapshotReadFacade.batchQueryItem(request);
        Assert.assertTrue(response.getError(), response.isSuccess());
        Assert.assertEquals(1, response.getResult().getHitCount());
    }
}
