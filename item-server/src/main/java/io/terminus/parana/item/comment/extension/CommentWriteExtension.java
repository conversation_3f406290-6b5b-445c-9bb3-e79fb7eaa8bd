package io.terminus.parana.item.comment.extension;

import io.terminus.parana.item.comment.enums.CommentStatus;
import io.terminus.parana.item.comment.model.Comment;
import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.common.extension.ProcessResult;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-15
 */
public interface CommentWriteExtension {

    /**
     * 内容合规校验
     *
     * @param commentList 评价列表
     * @return 是否通过
     */
    default ExtensionResult contentCheck(List<Comment> commentList) {
        return ExtensionResult.internal();
    }

    /**
     * 评价创建时接口
     *
     * @param comment 评价
     * @return 处理结果
     */
    ProcessResult<Comment> create(Comment comment);

    /**
     * 评价更新状态接口
     *
     * @param origin       原评价记录
     * @param targetStatus 目标状态
     * @param operatorType 操作人类型
     * @return 处理结果
     */
    ProcessResult<Comment> updateStatus(Comment origin, CommentStatus targetStatus, Integer operatorType);
}
