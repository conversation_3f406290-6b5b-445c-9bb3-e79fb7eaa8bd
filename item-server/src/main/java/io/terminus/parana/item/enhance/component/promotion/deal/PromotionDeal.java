package io.terminus.parana.item.enhance.component.promotion.deal;

import io.terminus.parana.item.enhance.api.bean.response.DynamicRenderInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.third.info.ActivityMatchedLine;
import io.terminus.parana.item.third.info.ActivityResult;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2018-12-18
 */
public interface PromotionDeal {

    /**
     * 获取活动编码
     *
     * @return 编码
     */
    String getCode();

    /**
     * 处理活动信息
     *
     * @param vo             返回vo对象
     * @param selectedSku    选中的skuDTO对象
     * @param resultInfoList 活动结果信息
     * @param lineInfoList   活动匹配的行信息
     * @param shopInfoList   店铺活动信息
     * @return 是否已经完成处理
     */
    boolean handle(DynamicRenderInfo vo,
                   SkuInfo selectedSku,
                   List<ActivityResult> resultInfoList,
                   List<ActivityMatchedLine> lineInfoList,
                   List<ActivityMatchedLine> shopInfoList);
}
