package io.terminus.parana.item.item.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.collect.Maps;
import com.eascs.user.enterprise.api.bean.response.EnterpriseAuthenticationInfo;
import com.eascs.user.enterprise.api.facade.EnterpriseAuthenticationReadFacade;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.api.utils.StringUtil;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.inventory.api.enums.EntityType;
import io.terminus.parana.ipm.api.bean.request.inventory.IpmInventorySetRequest;
import io.terminus.parana.ipm.api.bean.request.inventory.param.InventorySetParam;
import io.terminus.parana.ipm.api.facade.IpmInventoryWriteFacade;
import io.terminus.parana.item.area.api.bean.response.VendorItemChannlRelationInfo;
import io.terminus.parana.item.area.bo.AreaItemOperateBO;
import io.terminus.parana.item.area.component.ItemInsertAndPublishHelper;
import io.terminus.parana.item.area.enums.AreaItemAuditStatus;
import io.terminus.parana.item.area.enums.AreaItemAuditType;
import io.terminus.parana.item.area.enums.AreaItemStatus;
import io.terminus.parana.item.area.enums.LogisticsMode;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.model.ItemAudit;
import io.terminus.parana.item.area.service.AreaItemAuditReadDomainService;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.attribute.api.converter.ItemCategoryAttributeApiInfoConverter;
import io.terminus.parana.item.attribute.model.CategoryAttributeBinding;
import io.terminus.parana.item.attribute.service.CategoryAttributeReadDomainService;
import io.terminus.parana.item.brand.api.manager.BrandService;
import io.terminus.parana.item.brand.model.Brand;
import io.terminus.parana.item.category.manager.BackCategoryService;
import io.terminus.parana.item.category.model.BackCategory;
import io.terminus.parana.item.category.repository.BackCategoryDao;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceSkuUpdateMarkupRequest;
import io.terminus.parana.item.common.base.IdVersionPairBO;
import io.terminus.parana.item.common.base.JsonSupport;
import io.terminus.parana.item.common.cache.SimpleRedisHelper;
import io.terminus.parana.item.common.constants.ShopItemFrozenFlag;
import io.terminus.parana.item.common.enums.AttributeGroupEnum;
import io.terminus.parana.item.common.extension.AbstractNormalExtensionDeal;
import io.terminus.parana.item.common.extension.ProcessResultPack;
import io.terminus.parana.item.common.mq.MessageSendHelper;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.common.utils.EaFileUtil;
import io.terminus.parana.item.export.thirdparty.storage.aliyun.EascsAliyunOssFactory;
import io.terminus.parana.item.item.api.bean.request.item.OpenItemCreateRequest;
import io.terminus.parana.item.item.api.bean.request.item.param.ImageParam;
import io.terminus.parana.item.item.api.bean.request.item.param.ItemDetailParam;
import io.terminus.parana.item.item.api.bean.request.item.param.ItemImportImage;
import io.terminus.parana.item.item.api.bean.request.item.param.ItemImportImageFail;
import io.terminus.parana.item.item.api.bean.request.sku.SkuDefaultPriceSaveRequest;
import io.terminus.parana.item.item.api.bean.request.sku.SkuQueryByItemIdsRequest;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.converter.input.ItemApiConverter;
import io.terminus.parana.item.item.api.converter.output.ItemApiInfoConverter;
import io.terminus.parana.item.item.bo.ItemImportImageBO;
import io.terminus.parana.item.item.bo.VendorDefaultPriceCellBO;
import io.terminus.parana.item.item.cache.*;
import io.terminus.parana.item.item.common.Digestors;
import io.terminus.parana.item.item.common.ItemAggHelper;
import io.terminus.parana.item.item.common.ItemImportAggHelper;
import io.terminus.parana.item.item.common.ItemOpenAggHelper;
import io.terminus.parana.item.item.enums.AuditStatus;
import io.terminus.parana.item.item.enums.ItemStatus;
import io.terminus.parana.item.item.enums.ItemType;
import io.terminus.parana.item.item.enums.ItemZqPushlStatusEnum;
import io.terminus.parana.item.item.event.ItemCreateEvent;
import io.terminus.parana.item.item.event.ItemDeleteEvent;
import io.terminus.parana.item.item.event.ItemUpdateEvent;
import io.terminus.parana.item.item.extension.ItemUpdateStatusExtension;
import io.terminus.parana.item.item.extension.tag.BitTagBuilder;
import io.terminus.parana.item.item.manager.ItemManager;
import io.terminus.parana.item.item.model.*;
import io.terminus.parana.item.partnership.model.VendorPartnership;
import io.terminus.parana.item.partnership.service.VendorPartnershipReadDomainService;
import io.terminus.parana.item.plugin.third.api.misc.api.ExcelReportWriteApi;
import io.terminus.parana.item.plugin.third.api.misc.api.ParanaThirdMessageWriteApi;
import io.terminus.parana.item.relation.model.BaseSku;
import io.terminus.parana.item.relation.utils.GenerateHelper;
import io.terminus.parana.item.sap.api.bean.request.param.ItemCommonUploadCreateRequest;
import io.terminus.parana.item.sap.api.bean.request.param.ItemImportDetail;
import io.terminus.parana.item.sap.model.SkuYL;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.service.ShopReadDomainService;
import io.terminus.parana.item.third.param.ThirdParanaThirdMessageBean;
import io.terminus.parana.item.third.param.ThirdParanaThirdMessageCreateRequest;
import io.terminus.parana.item.third.param.ThirdUploadReportCreateRequest;
import io.terminus.parana.item.third.param.ThirdUploadReportDetailRequst;
import io.terminus.parana.item.transcript.model.FullItemImportCreateBO;
import io.terminus.parana.item.util.FtpUtil;
import io.terminus.parana.misc.excel.api.bean.request.BatchUploadReportUpdateRequest;
import io.terminus.parana.misc.excel.api.bean.request.UploadReportDetailCreateRequest;
import io.terminus.parana.misc.excel.api.bean.request.UploadReportQueryRequest;
import io.terminus.parana.misc.excel.api.bean.request.UploadReportUpdateRequest;
import io.terminus.parana.misc.excel.api.bean.response.UploadReportInfoResponse;
import io.terminus.parana.misc.excel.api.facade.UploadReportReadFacade;
import io.terminus.parana.misc.excel.api.facade.UploadReportWriteFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-08-17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ItemWriteDomainService extends AbstractNormalExtensionDeal implements JsonSupport {

    private final ItemManager itemManager;
    private final ItemAggHelper itemAggHelper;
    private final SimpleRedisHelper simpleRedisHelper;
    private final MessageSendHelper messageSendHelper;
    private final BitTagBuilder bitTagBuilder;
    private final ItemReadDomainService itemReadDomainService;
    private final CacheSkuById cacheSkuById;
    private final CacheSkuIdByItemId cacheSkuIdByItemId;
    private final CacheItemById cacheItemById;
    private final CacheItemDetailById cacheItemDetailById;
    private final CacheSkuYLByItemId cacheSkuYLByItemId;
    private final AreaItemReadDomainService areaItemReadDomainService;
    private final AreaItemAuditReadDomainService areaItemAuditReadDomainService;
    private final VendorPartnershipReadDomainService vendorPartnershipReadDomainService;
    private final ItemUpdateLongReadDomainService itemUpdateLongReadDomainService;
    private final ItemDetailReadDomainService itemDetailReadDomainService;
    private final UploadReportReadFacade uploadReportReadFacade;
    private final UploadReportWriteFacade uploadReportWriteFacade;
    private final BackCategoryService backCategoryService;
    private final SkuReadDomainService skuReadDomainService;
    private final EascsAliyunOssFactory eascsAliyunOssFactory;
    private final IpmInventoryWriteFacade ipmInventoryWriteFacade;
    @Autowired
    private final GenerateHelper generateHelper;
    private final BackCategoryDao backCategoryDao;
    @Autowired
    private final ExcelReportWriteApi excelReportWriteApi;
    private final CategoryAttributeReadDomainService categoryAttributeReadDomainService;
    private final ItemCategoryAttributeApiInfoConverter itemCategoryAttributeApiInfoConverter;
    private final ShopReadDomainService shopReadDomainService;
    @Autowired(required = false)
    private ItemUpdateStatusExtension itemUpdateStatusExtension;
    @Autowired
    private BrandService brandService;
    private final ItemOpenAggHelper itemOpenAggHelper;
    private final ItemInsertAndPublishHelper itemInsertAndPublishHelper;
    private final ParanaThirdMessageWriteApi paranaThirdMessageWriteApi;
    private final ItemImportAggHelper itemImportAggHelper;
    private final ItemApiInfoConverter itemApiInfoConverter;
    private final EnterpriseAuthenticationReadFacade enterpriseAuthenticationReadFacade;
    private final VendorItemChannlRelationReadService vendorItemChannlRelationReadService;
    private final VendorItemChannlSkuRelationReadService vendorItemChannlSkuRelationReadService;

    private final SkuWriteDomainService skuWriteDomainService;
    private final ItemApiConverter itemApiConverter;
    private final static Integer PC_DETAIL_WIDTH=790;
    private final static Integer WAP_DETAIL_WIDTH=750;

    @Value("${ftp.host}")
    private String ftpHost;
    @Value("${ftp.port}")
    private Integer ftpPort;
    @Value("${ftp.username}")
    private String ftpUserName;
    @Value("${ftp.password}")
    private String ftpPassword;


    /**
     * 校验数据入库
     * @param shopId
     * @param createBy
     * @param createName
     * @param itemImportList
     * @return
     */
    public List<ItemImport> itemBatchImport(Long shopId, Long createBy, String createName,  List<ItemImport> itemImportList){
        try {
            //数据解析封装
            log.info("itemBatchImport req::{}",JSONUtil.toJsonStr(itemImportList));
            FullItemImportCreateBO fullItemImportCreateBO = itemImportAggHelper.itemImportHandler(shopId,createBy,createName,itemImportList);
            //批量保存
            itemManager.batchImportCreateItem(fullItemImportCreateBO,createName);
            return fullItemImportCreateBO.getItemImportList();
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));

            ThirdUploadReportDetailRequst thirdUploadReportDetailRequst = new ThirdUploadReportDetailRequst();
            thirdUploadReportDetailRequst.setCol(0);
            thirdUploadReportDetailRequst.setRow(0);
            thirdUploadReportDetailRequst.setUploadReportId(itemImportList.get(0).getReportId());
            thirdUploadReportDetailRequst.setReason("导入失败系统错误");
            thirdUploadReportDetailRequst.setSuggest("导入失败系统错误");
            thirdUploadReportDetailRequst.setCreateAt(new Date());
            thirdUploadReportDetailRequst.setUpdatedAt(new Date());
            excelReportWriteApi.detailCreate(thirdUploadReportDetailRequst);
            updateReport(itemImportList.get(0).getReportId(),-1);
            log.error("item.import.fail msg:{}",printErrorStack(e));

            ItemImport itemImport = new ItemImport();
            itemImport.setStatus(1);
            List<ItemImport> itemImports = new ArrayList<>();
            itemImports.add(itemImport);
            return itemImports;
        }
    }

    /**
     * 回填关键属性
     *
     * @param itemId       商品id
     * @param tenantId     租户id
     * @param toUpdateItem 待更新item信息
     * @param toUpdateSkus 待更新sku集合信息
     */
    private void backFillKeyProperties(Long itemId, Integer tenantId, Item toUpdateItem, @Nullable Collection<Sku> toUpdateSkus) {
        Item persistedItem = itemReadDomainService.findById(itemId, tenantId, null, null);
        Assert.nonNull(persistedItem, "item.not.found");
        Assert.nonNull(toUpdateItem, "to.update.item.is.null");
        toUpdateItem.setType(persistedItem.getType());
        toUpdateItem.setBitTag(persistedItem.getBitTag());
        toUpdateItem.setBusinessType(persistedItem.getBusinessType());
        if (CollectionUtils.isEmpty(toUpdateSkus)) {
            return;
        }
        for (Sku toUpdateSku : toUpdateSkus) {
            toUpdateSku.setBusinessType(persistedItem.getBusinessType());
            toUpdateSku.setType(persistedItem.getType());
            toUpdateSku.setBitTag(persistedItem.getBitTag());
        }
    }

//    //TODO
//    public Long createFullItemCommon(ItemTypeAndAttributeBO bitTagBO, FullItemBO fullItemBO, Long shopId, Integer tenantId,
//                                     String dimensionType, String dimensionCode) {
//
//        Item item = fullItemBO.getItem();
//
////        List<Sku> skuList = fullItemBO.getSkuList();
//        FullSkuBO fullSkuBO = fullItemBO.getFullSkuBO();
//
//        List<Sku> skuList = new ArrayList<Sku>();
//        skuList.add(fullSkuBO.getSku());
//        SkuYL skuYL = fullSkuBO.getSkuYL();
//
//        List<ItemUnit> itemUnitList = fullItemBO.getItemUnitList();
//
//        ItemDetail itemDetail = fullItemBO.getItemDetail();
//        item.setType(bitTagBO.getItemType().getValue());
//        item.setShopId(shopId);
//        item.setBusinessType(bitTagBO.getBusinessType());
//
//
//        try {
//            Long bitTagValue = bitTagBuilder.build(bitTagBO.getBusinessType(), bitTagBO.getItemType(), bitTagBO.getMetaAttributeKeys());
//            item.setBitTag(bitTagValue);
//            skuList.forEach(it -> it.setBitTag(bitTagValue));
//
//            ProcessResultPack<FullItemOperateBO> resultPack = itemAggHelper.createHandlerCommon(item, skuList, itemDetail,
//                    tenantId, dimensionType, dimensionCode,itemUnitList,skuYL);
//
//            Long itemId = itemManager.create(resultPack.getResultModel(), resultPack.getParamMap());
//            messageSendHelper.sendMessage(new ItemCreateEvent(itemId, tenantId));
//
//            if(StringUtils.isNotBlank(item.getSourcetype())){
//                messageSendHelper.sendMessage(new SapItemCreateEvent(itemId, tenantId));
//            }
//
//            return itemId;
//        } catch (ServiceException e) {
//            throw e;
//        } catch (Exception e) {
//            log.error("fail to create full item with fullItemBO: {}, shopId: {}, tenantId: {}, cause: {}",
//                    fullItemBO, shopId, tenantId, printErrorStack(e));
//            throw new ServiceException("item.create.fail");
//        }
//    }
//
//    public Boolean updateFullItemCommon(FullItemBO fullItemBO, Long shopId, Integer tenantId, String updatedBy,
//                                        String dimensionType, String dimensionCode) {
//        try {
//            Item item = fullItemBO.getItem();
//            //List<Sku> skuList = fullItemBO.getSkuList();
//            List<Sku> skuList = new ArrayList<Sku>();
//            ItemDetail itemDetail = fullItemBO.getItemDetail();
//            FullSkuBO fullSkuBO = fullItemBO.getFullSkuBO();
//            Sku sku = fullSkuBO.getSku();
//            SkuYL skuYL = fullSkuBO.getSkuYL();
//            skuList.add(sku);
//            List<ItemUnit> itemUnitList = fullItemBO.getItemUnitList();
//
//            // 回填下关键属性
//            item.setShopId(shopId);
//            backFillKeyProperties(item.getId(), tenantId, item, skuList);
//
//            ProcessResultPack<FullItemOperateBO> resultPack = itemAggHelper.updateHandlerCommon(item, skuList, itemDetail,
//                    tenantId, updatedBy, dimensionType, dimensionCode,itemUnitList,skuYL);
//
//            FullItemOperateBO bo = resultPack.getResultModel();
//
//            itemManager.update(bo, resultPack.getParamMap());
//
//            cacheItemById.remove(item.getId());
//            cacheSkuById.remove(AssembleDataUtils.list2set(bo.getToUpdateSkuList(), BaseSku::getId));
//            cacheSkuIdByItemId.remove(item.getId());
//            cacheItemDetailById.remove(item.getId());
//            this.sendMessage(shopId, item.getId(), tenantId, item.getStatus(), bo.getToDeleteSkuList());
//            return Boolean.TRUE;
//        } catch (ServiceException e) {
//            throw e;
//        } catch (Exception e) {
//            log.error("fail to update full item with fullItemBO: {}, shopId: {}, tenantId: {}, updatedBy: {}, cause: {}",
//                    fullItemBO, shopId, tenantId, updatedBy, printErrorStack(e));
//            throw new ServiceException("item.update.fail");
//        }
//    }

    /**
     * 批量导入商品
     *
     * @param request
     * @return
     */
    public Boolean banchCreateItem(ItemCommonUploadCreateRequest request) {
        log.info("进入商品导入批处理...");

        List<ItemImportDetail> list = request.getItemImportDetails();

        //缓存分类 减少查询db 提升效率
        Map<Long, BackCategory> categoryMap = Maps.newHashMap();
        Map<String, Brand> brandMap = Maps.newHashMap();// 品牌
        Map<Long, Map<String, List<CategoryAttributeBinding>>> attriMap = new HashMap<>();// 类目id绑定的属性

        boolean haveError = false;
        for (ItemImportDetail itemImportDetail : list) {

            //商品导入信息有效性检查
            boolean isOk = this.dataVerify(itemImportDetail, categoryMap, brandMap, attriMap);
            if (!isOk) {
                haveError = true;
            }
        }
        if (haveError) {
            return false;
            //return Response.fail("校验数据失败");
        }
        // TODO 处理sku 和item, itemDetail
        List<FullItemImportCreateBO> createBOList = this.doSkuTOItem(list, brandMap, attriMap);
        if (CollectionUtils.isEmpty(createBOList)) {
            return false;
        }

        log.info("开始批量插入商品...");
        boolean isOk = true;
        for (FullItemImportCreateBO createBo : createBOList) {
            try {
                // 1、TODO 校验商品重复性
                // Item item = createBo.getItem();

                //2.商品创建 一个品一个事务
                isOk = itemManager.batchCreateItem(createBo);

                //3.初始化库存
                // itemImportManager.itemStockInit(createBo.getItem().getShopId(),createBo.getOperatorIds(),createBo.getSku(),createBo.getStock());
            } catch (Exception e) {
                log.error("商品导入创建失败： 参数：{}", JSONObject.toJSON(createBo));
            }
        }

        return isOk;
    }

    /**
     * 根据商品名称分组，多sku整合在一个商品里面
     *
     * @param list
     * @return
     */
    private List<FullItemImportCreateBO> doSkuTOItem(List<ItemImportDetail> list, Map<String, Brand> brandMap,
                                                     Map<Long, Map<String, List<CategoryAttributeBinding>>> attriMap) {
        log.info("开始整合多sku到一个item里面...");
        // TODO 根据商品名称分组，整合sku
        Map<String, List<ItemImportDetail>> listMap = new HashMap<>();
        for (ItemImportDetail detail : list) {
            String name = detail.getName();
            if (MapUtils.isNotEmpty(listMap)) {
                List<ItemImportDetail> data = listMap.get(name);
                if (CollectionUtil.isNotEmpty(data)) {
                    data.add(detail);
                    listMap.put(name, data);
                } else {
                    List<ItemImportDetail> newDetail = new ArrayList<>();
                    newDetail.add(detail);
                    listMap.put(name, newDetail);
                }
            } else {
                List<ItemImportDetail> newDetail = new ArrayList<>();
                newDetail.add(detail);
                listMap.put(name, newDetail);
            }

            /*// 属性key不存在，建立次级 val -> skuAttribute (Map结构)
            listMap.putIfAbsent(detail.getName(), new ArrayList<>());

            // 次级map，value不存在，放入skuAttribute
            listMap.get(detail.getName()).add(detail);*/
        }
        //缓存分类 减少查询db 提升效率
        Map<Long, List<Long>> categoryTreeMap = Maps.newHashMap();

        List<FullItemImportCreateBO> boArrayList = null;
        // TODO 同一个商品，只创建一个item
        if (MapUtils.isNotEmpty(listMap)) {
            boArrayList = new ArrayList<>();
            for (Map.Entry<String, List<ItemImportDetail>> entry : listMap.entrySet()) {
                List<ItemImportDetail> list1 = entry.getValue();
                List<Sku> skuList = new ArrayList<>();
                Item item = null;
                Map<String, List<CategoryAttributeBinding>> categoryAttr = new HashMap<>();
                FullItemImportCreateBO bo = new FullItemImportCreateBO();
                if (CollectionUtils.isNotEmpty(list1)) {
                    for (ItemImportDetail detail : list1) {
                        // 已经创建item，则判断下一个
                        if (item == null) {
                            log.info("初始化item...");
                            Shop shop = shopReadDomainService.findById(detail.getVendorId(), 1, null);
                            item = createItem(detail, shop, categoryTreeMap, brandMap);

                            categoryAttr = attriMap.get(detail.getCategoryId());
                            if (MapUtils.isEmpty(categoryAttr)) {
                                categoryAttr = categoryAttributeReadDomainService.renderExcludeRefuse(detail.getCategoryId(), false);
                                if (MapUtils.isNotEmpty(categoryAttr)) {
                                    attriMap.put(detail.getCategoryId(), categoryAttr);
                                }
                            }
                        }

                        // 多sku 放到一个item里面
                        log.info("item.id :{}", item.getId());
                        Sku sku = createSku(detail, item, categoryAttr);
                        skuList.add(sku);
                    }
                    // 处理销售属性
                    boolean isOk = handleSellAttribute(list1.get(0).getReportId(), item, skuList);
                    if (!isOk) {// 校验不通过，则返回
                        return null;
                    }
                    log.info("skuLlist:{}", skuList.size());

                    //商品详情渲染
                    ItemDetail itemDetail = createItemDetail(item);
                    //初始化图片信息
                    itemImageInit(item, itemDetail, null);
                    bo.setItemDetail(itemDetail);
                    bo.setStock(999l);// 库存 默认设置999

                    // 生成md5值
                    item.setMd5Info(Digestors.itemDigest(item, itemDetail));
                    bo.setItem(item);
                    bo.setSkuList(skuList);
                    boArrayList.add(bo);
                }
            }
        }
        return boArrayList;
    }

    /**
     * 整合item （整购B 导入商品需求）
     *
     * @param itemImportDetail
     * @return
     */
    private Item createItem(ItemImportDetail itemImportDetail, Shop shop,
                            Map<Long, List<Long>> categoryTreeMap, Map<String, Brand> brandMap) {
        Item item = new Item();
        item.setExtensionType(0);
        item.setSpuId(0L);
        item.setUnit(itemImportDetail.getUnit());
        item.setTenantId(1);
        item.setCategoryId(itemImportDetail.getCategoryId());
        item.setItemCode(itemImportDetail.getSkuCode());
        item.setShopId(shop.getId());
        item.setShopName(shop.getName());
        item.setDeliveryFeeTempId(0l);// 运费模板id
        item.setName(itemImportDetail.getName());
        item.setVideoUrl(itemImportDetail.getVideoUrl());
        item.setAdvertise(itemImportDetail.getAdvertise());
        if (StringUtils.isNotBlank(itemImportDetail.getBrandName())) {
            item.setBrandName(itemImportDetail.getBrandName());
            Brand brand = brandMap.get(itemImportDetail.getBrandName());
            if (brand == null) {
                List<Brand> brands = brandService.findByNamePrefix(itemImportDetail.getBrandName(), 10);
                if (CollectionUtils.isNotEmpty(brands)) {
                    brand = brands.get(0);
                    brandMap.put(itemImportDetail.getBrandName(), brands.get(0));
                }
            }
            if (brand != null) {
                item.setBrandId(brand.getId());
            } else {
                item.setBrandId(0l);
            }
        }
        //item.setUniversalName(itemImportDetail.getUniversalName());// 通用名称
//        if("Y".equals(itemImportDetail.getIfShelf())){
//            item.setStatus(ItemStatus.ON_SHELF.getValue());
//        }else{
        item.setStatus(ItemStatus.OFF_SHELF.getValue());// 默认下架
        //}
        item.setType(ItemType.COMMON.getValue());
        item.setBusinessType(ItemType.COMMON.getValue());
        Map<String, String> itemExtra = Maps.newHashMap();
        if (StringUtils.isNotBlank(itemImportDetail.getKeyword())) {
            itemExtra.put("keyword", itemImportDetail.getKeyword());
        }
        itemExtra.put("supportReturn", itemImportDetail.getSupportReturn() == null ? "1" : itemImportDetail.getSupportReturn().toString());
        List<Long> categoryIdList = categoryTreeMap.get(item.getCategoryId());
        if (CollectionUtil.isEmpty(categoryIdList)) {
            categoryIdList = backCategoryService.findAncestorIdsOf(item.getCategoryId());
            categoryTreeMap.put(item.getCategoryId(), categoryIdList);
        }
        itemExtra.put("categoryList", object2json(categoryIdList, ""));
        item.setExtra(itemExtra);
        item.setBitTag(0L);
        item.setVersion(0);
        item.setCreatedAt(new Date());
        item.setUpdatedAt(item.getCreatedAt());
        item.setIsmedicalgoods("0");// 是否医药商品
        item.setAuditStatus(AuditStatus.PASS.getValue());
        // item.setPmodel(itemImportDetail.getPModel());// 规格
        //item.setSalesArea(itemImportDetail.getSalesArea());// 销售区域
        item.setSkuAttributesJson("[]");
        item.setOtherAttributesJson("[]");
        generateHelper.generateId(item);
        return item;
    }


    /**
     * sku渲染 （整购B 导入商品需求）
     *
     * @param itemImportDetail
     * @param item
     * @return
     */
    public Sku createSku(ItemImportDetail itemImportDetail, Item item, Map<String, List<CategoryAttributeBinding>> result) {
        Sku sku = new Sku();
        sku.setExtensionType(item.getExtensionType());
        sku.setTenantId(item.getTenantId());
        sku.setItemId(item.getId());
        sku.setSkuTemplateId(0L);
        sku.setSkuCode(itemImportDetail.getSkuCode());
        sku.setBarcode(itemImportDetail.getBarcode());
        sku.setShopId(item.getShopId());
        sku.setName(item.getName());
        sku.setImage(item.getMainImage());
        sku.setPrice(0L);
        sku.setOriginalPrice(0L);
        sku.setStatus(item.getStatus());
        sku.setAuditStatus(item.getAuditStatus());
        sku.setType(item.getType());
        sku.setBusinessType(item.getType());
        Map<String, Long> priceMap = Maps.newHashMap();
        priceMap.put("defaultBasePrice", 0L);
        priceMap.put("defaultPrice", 0L);
        priceMap.put("defaultCommission", 0L);
        priceMap.put("defaultPCommission", 0L);
        sku.setPriceJson(object2json(priceMap, ""));
        sku.setVersion(item.getVersion());
        //sku.setAttributes();
        Long minQuantity = 1l;// 起售数量不填默认为1
        if (itemImportDetail.getMinQuantity() != null) {
            minQuantity = itemImportDetail.getMinQuantity();
        }
        sku.setExtraJson("{\"minQuantity\":\"" + minQuantity + "\",\"maxQuantity\":\"\"}");
        sku.setBitTag(item.getBitTag());
        sku.setCreatedAt(item.getCreatedAt());
        sku.setUpdatedBy(item.getUpdatedBy());
        sku.setIsmedicalgoods(item.getIsmedicalgoods());

        // 处理销售属性
        // 属性设置
        // 根据类目id，查询类目绑定的属性
        log.info("开始处理普通属性到 item otherJson里面...");
        Map<String, CategoryAttributeBinding> putongMap = new HashMap<>();         // 普通属性
        Map<String, CategoryAttributeBinding> putongRequiredMap = new HashMap<>(); // 普通属性且必填
        Map<String, CategoryAttributeBinding> sellMap = new HashMap<>();          // 销售属性
        Map<String, CategoryAttributeBinding> sellRequiredMap = new HashMap<>(); // 销售属性且必填
        if (MapUtils.isNotEmpty(result)) {
            for (String s : result.keySet()) {
                List<CategoryAttributeBinding> attrList = result.get(s);
                if (CollectionUtils.isNotEmpty(attrList)) {
                    for (CategoryAttributeBinding attr : attrList) {
                        if (!attr.getSalable()) { // 非销售属性
                            putongMap.put(attr.getName(), attr);
                            if (attr.getRequired()) {     // 普通属性 且 必填，则放入集合
                                putongRequiredMap.put(attr.getName(), attr);
                            }
                        } else {
                            sellMap.put(attr.getName(), attr);// 销售属性集合
                            if (attr.getRequired()) {
                                sellRequiredMap.put(attr.getName(), attr);// 销售属性 且 必填，则放入集合
                            }
                        }
                    }
                }
            }
        }

        Map<String, String> groupMap = itemImportDetail.getGroupValues();// Excel普通属性
        List<OtherAttribute> putongAttrList = new ArrayList<>();
        List<OtherAttribute> userDefinedAttrList = new ArrayList<>();
        List<GroupedOtherAttribute> otherAttributes = new ArrayList<>();
        if (MapUtils.isNotEmpty(groupMap)) {
            for (Map.Entry<String, String> entry : groupMap.entrySet()) {
                OtherAttribute attribute = new OtherAttribute();
                if (MapUtils.isNotEmpty(putongMap)) {
                    // 如果是绑定的普通属性，则赋值普通属性
                    CategoryAttributeBinding attr = putongMap.get(entry.getKey());
                    if (attr != null) {
                        attribute.setId(attr.getAttributeId());
                        attribute.setAttrKey(entry.getKey());
                        attribute.setAttrVal(entry.getValue());
                        attribute.setGroup(AttributeGroupEnum.DEFAULT.getDescription());
                        putongAttrList.add(attribute);
                        continue;
                    }
                }
                // 如果是不是绑定的普通属性，则为自定义属性
                attribute.setGroup(AttributeGroupEnum.USER_DEFINED.toString());
                attribute.setAttrKey(entry.getKey());
                attribute.setAttrVal(entry.getValue());
                userDefinedAttrList.add(attribute);
                log.info("key= " + entry.getKey() + " and value= " + entry.getValue());
            }
            if (CollectionUtils.isNotEmpty(putongAttrList)) {
                GroupedOtherAttribute attribute = new GroupedOtherAttribute();
                attribute.setGroup(AttributeGroupEnum.DEFAULT.getDescription());
                attribute.setOtherAttributes(putongAttrList);
                otherAttributes.add(attribute);
            }
            if (CollectionUtils.isNotEmpty(userDefinedAttrList)) {
                GroupedOtherAttribute attribute = new GroupedOtherAttribute();
                attribute.setGroup(AttributeGroupEnum.USER_DEFINED.toString());
                attribute.setOtherAttributes(userDefinedAttrList);
                otherAttributes.add(attribute);
            }

        }
        log.info("OtherAttributes:  ：[" + otherAttributes + "]");
        if (CollectionUtils.isNotEmpty(otherAttributes)) {
            item.setOtherAttributes(otherAttributes);
            //item.setOtherAttributesJson(com.alibaba.fastjson.JSON.toJSONString(otherAttributes));
        }
        log.info("处理普通属性完成");

        log.info("开始处理销售属性到item Json里面...");
        Map<String, String> sellGroup = itemImportDetail.getSellGroupValues();// Excel销售属性
        if (MapUtils.isNotEmpty(sellMap)) {
            List<GroupedSkuAttribute> groupedSkuAttributeList = new ArrayList<>();
            List<SkuAttribute> attributeList = new ArrayList<>();
            Map<String, List<SkuAttribute>> resultMap = new HashMap<>();
            for (Map.Entry<String, String> entry : sellGroup.entrySet()) {
                String key = entry.getKey();
                CategoryAttributeBinding attr = sellMap.get(entry.getKey());
                SkuAttribute skuAttribute = new SkuAttribute();
                if (attr != null) {
                    skuAttribute.setId(attr.getAttributeId());
                    skuAttribute.setAttrKey(entry.getKey());
                    skuAttribute.setAttrVal(entry.getValue());
                    skuAttribute.setShowImage(false);
                    attributeList.add(skuAttribute);
                }
                if (MapUtils.isNotEmpty(resultMap)) {
                    List<SkuAttribute> list = resultMap.get(key);
                    if (CollectionUtils.isNotEmpty(list)) {
                        list.addAll(attributeList);
                    } else {
                        resultMap.put(key, attributeList);
                    }
                } else {
                    resultMap.put(key, attributeList);
                }
            }
            if (MapUtils.isNotEmpty(resultMap)) {
                for (Map.Entry<String, List<SkuAttribute>> entry : resultMap.entrySet()) {
                    GroupedSkuAttribute groupedSkuAttribute = new GroupedSkuAttribute();
                    groupedSkuAttribute.setAttrKey(entry.getKey());
                    groupedSkuAttribute.setSkuAttributes(entry.getValue());
                    groupedSkuAttributeList.add(groupedSkuAttribute);
                }
            }
            if (CollectionUtil.isNotEmpty(attributeList)) {
                sku.setAttributes(attributeList);//sku的销售属性
            }
            log.info("SkuAttributes:  ：[" + groupedSkuAttributeList + "]");
            item.setSkuAttributes(groupedSkuAttributeList);
            //item.setSkuAttributesJson(com.alibaba.fastjson.JSON.toJSONString(groupedSkuAttributeList));
        }
        log.info("处理销售属性完成");

        log.info("generateHelper", generateHelper);
        log.info("type：{}，shopid:{}", sku.getType(), sku.getShopId());
        Long skuId = generateHelper.generateId(sku);
        sku.setId(skuId);
        return sku;
    }

    /**
     * 商品图片信息初始化 （整购B 导入商品需求）
     *
     * @param item
     * @param itemDetail
     */
    public void itemImageInit(Item item, ItemDetail itemDetail, List<AreaItem> areaItemList) {
        log.info("初始化图片...");
        List<Map<String, String>> imageList = Lists.newArrayList();
        if (item.getMainImage() == null) {
            //默认图片
            item.setMainImage("https://cdndev.ea380buy.com/images/ec76f8cf-9d65-4443-a7bc-2ea51cb9221e.png");
        }
        if (CollectionUtil.isNotEmpty(areaItemList)) {
            for (AreaItem areaItem : areaItemList) {
                areaItem.setMainImage(item.getMainImage());
            }
        }
        itemDetail.setImageJson(JSONObject.toJSONString(imageList));
    }

    /**
     * 商品详情渲染 （整购B 导入商品需求）
     *
     * @param item
     * @return
     */
    public ItemDetail createItemDetail(Item item) {
        log.info("初始化detail...");
        ItemDetail itemDetail = new ItemDetail();
        itemDetail.setItemId(item.getId());
        itemDetail.setTenantId(item.getTenantId());
        itemDetail.setCreatedAt(item.getCreatedAt());
        itemDetail.setUpdatedAt(item.getUpdatedAt());
        return itemDetail;
    }


    /**
     * 处理sku属性 （整购B 导入商品需求）
     *
     * @param item    商品
     * @param skuList sku集合
     */
    public boolean handleSellAttribute(Long reportId, Item item, List<Sku> skuList) {

        // 索引形式为： 属性key -> 属性val -> skuAttribute
        Map<String, Map<String, SkuAttribute>> map = Maps.newLinkedHashMap();

        if (CollectionUtil.isNotEmpty(skuList)) {
            for (Sku sku : skuList) {
                List<SkuAttribute> attributes = sku.getAttributes();

                // 判断sku是否存在销售属性
                if (CollectionUtils.isEmpty(attributes)) {
                    log.warn("no sku attribute for sku: {}", sku);
                    continue;
                }

                for (SkuAttribute attr : attributes) {
                    // 属性key不存在，建立次级 val -> skuAttribute (Map结构)
                    map.putIfAbsent(attr.getAttrKey(), Maps.newLinkedHashMap());

                    // 次级map，value不存在，放入skuAttribute
                    map.get(attr.getAttrKey()).putIfAbsent(attr.getAttrVal(), attr);
                }
            }
        }

        // 计算Sku数量和销售属性是否为笛卡尔积，否，则提示SKU数量和销售属性数量不一致
        int skuSize = 1;
        if (MapUtils.isNotEmpty(map)) {
            List<GroupedSkuAttribute> list = new LinkedList<>();
            for (Map.Entry<String, Map<String, SkuAttribute>> entry : map.entrySet()) {
                List<SkuAttribute> attributeList = new ArrayList<>(entry.getValue().values());
                if (attributeList.size() != 0) {
                    skuSize = skuSize * attributeList.size();
                }
                String attributeName = entry.getKey();
                list.add(new GroupedSkuAttribute(attributeName, attributeList));
            }
            item.setSkuAttributes(list);
        }

        boolean isOk = true;
        String failCause = "";
        if (CollectionUtils.isNotEmpty(skuList)) {
            if (skuSize != skuList.size()) {
                failCause = "商品为（" + item.getName() + "）的，SKU数量和销售属性数量不一致，请添加sku！";
                isOk = false;
                // 生成错误记录
                insertErrorExcel(reportId, 0, failCause, 0, failCause);
            }
        }
        return isOk;
    }


    /**
     * 新增导入记录详情 生成错误记录 （整购B 导入商品需求）
     *
     * @param reportId
     * @param col
     * @param reason
     * @param row
     * @param suggest
     */
    public void insertErrorExcel(Long reportId, Integer col, String reason, Integer row, String suggest) {
        log.info("插入商品错误信息..." + suggest);
        ThirdUploadReportDetailRequst thirdUploadReportDetailRequst = new ThirdUploadReportDetailRequst();
        thirdUploadReportDetailRequst.setCreateAt(new Date());
        thirdUploadReportDetailRequst.setUpdatedAt(new Date());
        thirdUploadReportDetailRequst.setUploadReportId(reportId);
        thirdUploadReportDetailRequst.setCol(col);
        thirdUploadReportDetailRequst.setReason(reason);
        thirdUploadReportDetailRequst.setRow(row);
        thirdUploadReportDetailRequst.setSuggest(suggest);
        excelReportWriteApi.detailCreate(thirdUploadReportDetailRequst);
    }


    /**
     * 商品导入参数校验 （整购B 导入商品需求）
     *
     * @param itemImportDetail
     * @return
     */
    public boolean dataVerify(ItemImportDetail itemImportDetail, Map<Long, BackCategory> categoryMap,
                              Map<String, Brand> brandMap, Map<Long, Map<String, List<CategoryAttributeBinding>>> attriMap) {
        log.info("开始校验Excel商品信息...");
        boolean isOk = true;
        String failCause = "";
        Long categoryId = itemImportDetail.getCategoryId();
        if (categoryId == null) {
            failCause = "商品为（" + itemImportDetail.getName() + "）的，分类id为空！";
            isOk = false;
        }
        if (isOk && categoryId != null) {
            BackCategory category = categoryMap.get(categoryId);
            if (category == null) {
                category = backCategoryDao.findById(categoryId);
                if (category != null) {
                    categoryMap.put(categoryId, category);
                }
            }
            if (category == null) {
                failCause = "商品为（" + itemImportDetail.getName() + "）的，分类未找到！";
                isOk = false;
            }
            /*else if(category.getLevel() != 3){
                failCause =  "商品为（"+itemImportDetail.getName()+"）的，分类等级错误！"+category.getLevel()+"分类id为："+categoryId ;
                isOk = false;
            }*/
        }

        if (StringUtils.isBlank(itemImportDetail.getBrandName())) {
            failCause = "商品为（" + itemImportDetail.getName() + "）的，品牌不能为空！";
            isOk = false;
        } else {
            Brand brand = brandMap.get(itemImportDetail.getBrandName());
            if (brand == null) {
                List<Brand> brands = brandService.findByNamePrefix(itemImportDetail.getBrandName(), 10);
                if (CollectionUtils.isNotEmpty(brands)) {
                    brand = brands.get(0);
                    brandMap.put(itemImportDetail.getBrandName(), brands.get(0));
                }
            }
            if (brand == null) {
                failCause = "商品为（" + itemImportDetail.getName() + "）的，品牌未找到，请重新确认！";
                isOk = false;
            }
        }


        // TODO 校验销售属性必填项，是否为绑定属性
        if (categoryId != null) {
            log.info("校验类目绑定属性...");
            // 根据类目id，查询类目绑定的属性
            Map<String, List<CategoryAttributeBinding>> result = attriMap.get(categoryId);
            if (MapUtils.isEmpty(result)) {
                result = categoryAttributeReadDomainService.renderExcludeRefuse(categoryId, false);
                if (MapUtils.isNotEmpty(result)) {
                    attriMap.put(categoryId, result);
                }
            }

            Set<CategoryAttributeBinding> sellAttr = new HashSet<>();// 销售属性必填
            Set<CategoryAttributeBinding> normalAttr = new HashSet<>(); // 普通属性必填
            Set<String> sellAttr2 = new HashSet<>();
            if (MapUtils.isNotEmpty(result)) {
                for (String s : result.keySet()) {
                    List<CategoryAttributeBinding> attrList = result.get(s);
                    if (CollectionUtils.isNotEmpty(attrList)) {
                        for (CategoryAttributeBinding attr : attrList) {
                            if (attr.getSalable() != null && attr.getSalable()) {
                                sellAttr2.add(attr.getName());// 销售属性集合
                                if (attr.getRequired() != null && attr.getRequired()) {    // 销售属性 且 必填，则放入集合
                                    sellAttr.add(attr);
                                }
                            } else {
                                if (attr.getRequired() != null && attr.getRequired()) {
                                    normalAttr.add(attr); // 普通属性 且 必填
                                }
                            }
                        }
                    }
                }
            } else {
                failCause = "商品为（" + itemImportDetail.getName() + "）的类目id未找到相关的属性，请检查类目id是否正确！";
                isOk = false;
            }


            // TODO 判断是否存在必填销售属性
            Map<String, String> sellMap = itemImportDetail.getSellGroupValues(); // Excel表的销售属性
            log.info("Excel销售属性：" + sellMap);
            if (MapUtils.isNotEmpty(sellMap)) {
                for (Map.Entry<String, String> entry : sellMap.entrySet()) {
                    String attr = entry.getKey();
                    String value = entry.getValue();
                    log.info("key = " + attr + ", value = " + value);
                    // 判断销售属性必填项
                    if (!sellAttr.isEmpty()) {
                        for (CategoryAttributeBinding cateAttr : sellAttr) {
                            if (cateAttr.getName().equals(attr)) {
                                if (StringUtils.isBlank(value)) {
                                    failCause = "商品为（" + itemImportDetail.getName() + "）的,销售属性为（" + attr + "）的,对应的属性值为必填！";
                                    isOk = false;
                                }
                            }
                            // 判断Excel里面的销售属性是否在必填销售属性里面，如果所有的销售属性，不存在必填的集合里面，则记录错误
                            boolean contantsAttr = false;
                            log.info("类目销售属性：" + cateAttr.getName());
                            for (String attr1 : sellMap.keySet()) {
                                log.info("Excel销售属性：" + attr1);
                                log.info("是否相等（有trim）：" + cateAttr.getName().equals(attr1.trim()));
                                log.info("是否相等（无trim）：" + cateAttr.getName().equals(attr1));
                                if (cateAttr.getName().equals(attr1)) {
                                    contantsAttr = true;
                                }
                            }
                            if (!contantsAttr) {
                                failCause = "商品为（" + itemImportDetail.getName() + "）的，销售属性（" + cateAttr.getName() + "）是必须的,请添加此销售属性！";
                                isOk = false;
                            }
                        }
                    }
                    // 判断销售属性是否为类目绑定的
                    if (!sellAttr2.isEmpty()) {
                        if (!sellAttr2.contains(attr)) {
                            failCause = "商品为（" + itemImportDetail.getName() + "）的，销售属性为（" + attr + "）不是分类绑定的！请添加分类绑定的销售属性";
                            isOk = false;
                        }
                    } else {
                        failCause = "商品为（" + itemImportDetail.getName() + "）的，销售属性为（" + attr + "）不是分类绑定的！请添加分类绑定的销售属性";
                        isOk = false;
                    }
                }
            } else if (!sellAttr.isEmpty()) {
                failCause = "商品为（" + itemImportDetail.getName() + "）的，有必填的销售属性！请添加必填的销售属性";
                isOk = false;
            }

            // TODO 判断是否存在必填的普通属性
            Map<String, String> normalMap = itemImportDetail.getGroupValues(); // Excel表的普通属性
            log.info("Excel普通属性：" + normalMap);
            if (MapUtils.isNotEmpty(normalMap)) {
                for (Map.Entry<String, String> entry : sellMap.entrySet()) {
                    String attr = entry.getKey();
                    String value = entry.getValue();
                    // 判断普通属性必填项
                    if (!normalAttr.isEmpty()) {
                        for (CategoryAttributeBinding cateAttr : normalAttr) {
                            if (cateAttr.getName().equals(attr)) {
                                if (StringUtils.isBlank(value)) {
                                    failCause = "商品为（" + itemImportDetail.getName() + "）的，普通属性为（" + attr + "）的，对应普通属性值为必填！";
                                    isOk = false;
                                }
                            }

                            // 判断Excel里面的销售属性是否在必填销售属性里面，如果所有的销售属性，不存在必填的集合里面，则记录错误
                            boolean contantsAttr = false;
                            for (String attr1 : normalMap.keySet()) {
                                if (cateAttr.getName().equals(attr1)) {
                                    contantsAttr = true;
                                }
                            }
                            if (!contantsAttr) {
                                failCause = "商品为（" + itemImportDetail.getName() + "）的，普通属性（" + cateAttr.getName() + "）是必须的,请添加此普通属性！";
                                isOk = false;
                            }

                        }

                    }
                }

            } else if (!normalAttr.isEmpty()) {
                failCause = "商品为（" + itemImportDetail.getName() + "）的，有必填的普通属性！请添加必填的普通属性";
                isOk = false;
            }

        }

        if (isOk && StringUtil.isEmpty(itemImportDetail.getName())) {
            failCause = "商品名称不能为空！";
            isOk = false;
        }

        if (isOk && StringUtil.isEmpty(itemImportDetail.getUnit())) {
            failCause = "商品为（" + itemImportDetail.getName() + "）的计量单位不能为空！";
            isOk = false;
        }

        if (isOk) {
            //商品编码
            if (StringUtil.isEmpty(itemImportDetail.getSkuCode())) {
                itemImportDetail.setSkuCode(random(6));
            }
            //条形码
            if (StringUtil.isEmpty(itemImportDetail.getBarcode())) {
                itemImportDetail.setBarcode(random(6));
            }
            if (StringUtil.isEmpty(itemImportDetail.getSalesArea())) {
                //默认发全国
                itemImportDetail.setSalesArea("110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000");
            }

        } else {
            // 生成错误记录
            insertErrorExcel(itemImportDetail.getReportId(), 0, failCause, 0, failCause);
        }
        return isOk;
    }


    /**
     * 生成随机数
     *
     * @param len
     * @return
     */
    public static String random(int len) {
        int rs = (int) ((Math.random() * 9 + 1) * Math.pow(10, len - 1));
        return String.valueOf(rs);
    }

    public Long createFullItemPlus(ItemTypeAndAttributeBO bitTagBO, FullItemBO fullItemBO, Long shopId, Integer tenantId,
                                   String dimensionType, String dimensionCode,Integer type,Set<Long> operatorIdSet,
                                   Long userId,String userName) {
        Item item = fullItemBO.getItem();
        List<Sku> skuList = fullItemBO.getSkuList();
        SkuYL skuYL = fullItemBO.getSkuYL();
        ItemDetail itemDetail = fullItemBO.getItemDetail();
        List<VendorItemChannlRelationModel> vendorItemChannlRelationModels = fullItemBO.getVendorItemChannlRelationModels();
        item.setType(bitTagBO.getItemType().getValue());
        item.setShopId(shopId);
        item.setBusinessType(bitTagBO.getBusinessType());
        log.info("createFullItemPlus============1");
        try {
            Long bitTagValue = bitTagBuilder.build(bitTagBO.getBusinessType(), bitTagBO.getItemType(), bitTagBO.getMetaAttributeKeys());
            item.setBitTag(bitTagValue);
            skuList.forEach(it -> it.setBitTag(bitTagValue));
            log.info("createFullItemPlus============2");
            ProcessResultPack<FullItemOperateBO> resultPack = itemAggHelper.createHandler(item, skuList, skuYL, itemDetail,vendorItemChannlRelationModels,
                    tenantId, dimensionType, dimensionCode);
            AreaItemOperateBO areaItemOperateBO = null;
            if(type != null && type == 2){
                areaItemOperateBO = itemInsertAndPublishHelper.process(resultPack.getResultModel().getItem(), resultPack.getResultModel().getToCreateSkuList(), operatorIdSet, userId, userName);
            }
            log.info("createFullItemPlus============3");
            Long itemId = itemManager.createPlus(resultPack.getResultModel(), resultPack.getParamMap(),areaItemOperateBO);
            log.info("createFullItemPlus============4");
            messageSendHelper.sendMessage(new ItemCreateEvent(itemId, tenantId));
            return itemId;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to create full item with fullItemBO: {}, shopId: {}, tenantId: {}, cause: {}",
                    fullItemBO, shopId, tenantId, printErrorStack(e));
            throw new ServiceException("item.create.fail");
        }
    }

    public Long createFullItemForApple(ItemTypeAndAttributeBO bitTagBO, FullItemBO fullItemBO, Long shopId, Integer tenantId,
                                       String dimensionType, String dimensionCode, Integer appleStatus, Set<Long> operatorIdSet,
                                       Long userId, String userName) {
        Item item = fullItemBO.getItem();
        List<Sku> skuList = fullItemBO.getSkuList();
        SkuYL skuYL = fullItemBO.getSkuYL();
        ItemDetail itemDetail = fullItemBO.getItemDetail();
        List<VendorItemChannlRelationModel> vendorItemChannlRelationModels = fullItemBO.getVendorItemChannlRelationModels();
        item.setType(bitTagBO.getItemType().getValue());
        item.setShopId(shopId);
        item.setBusinessType(bitTagBO.getBusinessType());
        try {
            Long bitTagValue = bitTagBuilder.build(bitTagBO.getBusinessType(), bitTagBO.getItemType(), bitTagBO.getMetaAttributeKeys());
            item.setBitTag(bitTagValue);
            skuList.forEach(it -> it.setBitTag(bitTagValue));
            ProcessResultPack<FullItemOperateBO> resultPack = itemAggHelper.createHandler(item, skuList, skuYL, itemDetail, vendorItemChannlRelationModels,
                    tenantId, dimensionType, dimensionCode);
            AreaItemOperateBO areaItemOperateBO = itemInsertAndPublishHelper.process(resultPack.getResultModel().getItem(), resultPack.getResultModel().getToCreateSkuList(), operatorIdSet, userId, userName);
            if (!ObjectUtil.isEmpty(areaItemOperateBO)) {
                // 置空商品审核记录 无需审核
                areaItemOperateBO.setToCreateItemAuditList(null);
                areaItemOperateBO.setToUpdateItemAuditList(null);
                //并且areaItem 和 areaSku直接上架
                if (!CollectionUtil.isEmpty(areaItemOperateBO.getToCreateAreaItemList())) {
                    for (AreaItem areaItem : areaItemOperateBO.getToCreateAreaItemList()) {
                        if (!ObjectUtil.isEmpty(appleStatus) && 1 == appleStatus) {
                            // 上架
                            areaItem.setStatus(AreaItemStatus.ON_SHELF.getValue());
                            areaItem.setAreaOperatorStatus(AreaItemStatus.ON_SHELF.getValue());
                        }else {
                            // 下架
                            areaItem.setStatus(AreaItemStatus.OFF_SHELF.getValue());
                            areaItem.setAreaOperatorStatus(AreaItemStatus.OFF_SHELF.getValue());
                        }
                    }
                }
                if (!CollectionUtil.isEmpty(areaItemOperateBO.getToCreateAreaSkuList())) {
                    for (AreaSku areaSku : areaItemOperateBO.getToCreateAreaSkuList()) {
                        if (!ObjectUtil.isEmpty(appleStatus) && 1 == appleStatus) {
                            // 上架
                            areaSku.setStatus(AreaItemStatus.ON_SHELF.getValue());
                            areaSku.setAreaOperatorStatus(AreaItemStatus.ON_SHELF.getValue());
                        }else {
                            // 下架
                            areaSku.setStatus(AreaItemStatus.OFF_SHELF.getValue());
                            areaSku.setAreaOperatorStatus(AreaItemStatus.OFF_SHELF.getValue());
                        }
                    }
                }
            }
            Long itemId = itemManager.createPlus(resultPack.getResultModel(), resultPack.getParamMap(), areaItemOperateBO);
            messageSendHelper.sendMessage(new ItemCreateEvent(itemId, tenantId));
            return itemId;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to create full item for apple with fullItemBO: {}, shopId: {}, tenantId: {}, cause: {}.", JSONUtil.toJsonStr(fullItemBO), shopId, tenantId, Throwables.getStackTraceAsString(e));
            throw new ServiceException("item.create.fail");
        }
    }

    public Boolean updateFullItem(FullItemBO fullItemBO, Long shopId, Integer tenantId, String updatedBy,
                                  String dimensionType, String dimensionCode, List<ItemAudit> baseAdoptList, List<ItemAudit> priceItemAuditList,
                                  List<Long> operatorIdList,List<VendorItemChannlSkuRelationModel> models,
                                  Integer type,Set<Long> operatorIdSet, Long userId,String userName) {
        try {

            Item itemPre = itemReadDomainService.findById(fullItemBO.getItem().getId(), tenantId, dimensionType,
                    dimensionCode);
            ItemDetail itemDetailPre = itemDetailReadDomainService.findByItemId(fullItemBO.getItem().getId(), tenantId);
            List<Sku> skuListPre = skuReadDomainService.findByItemId(fullItemBO.getItem().getId(), tenantId, null, null);
            //查询渠道价
            List<VendorItemChannlRelationInfo> channlInfosPre = itemChannlInfo(itemPre);

            Item item = fullItemBO.getItem();
            List<Sku> skuList = fullItemBO.getSkuList();
            SkuYL skuYL = fullItemBO.getSkuYL();
            ItemDetail itemDetail = fullItemBO.getItemDetail();
            // 回填下关键属性
            item.setShopId(shopId);
            //用作对比
            item.setImages(itemDetail.getImages());
            backFillKeyProperties(item.getId(), tenantId, item, skuList);

            ProcessResultPack<FullItemOperateBO> resultPack = itemAggHelper.updateHandler(item, skuList, skuYL, itemDetail,
                    tenantId, updatedBy, dimensionType, dimensionCode, itemPre,shopId, itemDetailPre, skuListPre, channlInfosPre);
            FullItemOperateBO bo = resultPack.getResultModel();
            bo.setBaseAuditAdoptList(baseAdoptList);
            bo.setPriceAuditList(priceItemAuditList);
            bo.setPublishedOperatorIdList(operatorIdList);
            bo.setUpdatedBy(updatedBy);
            bo.setShopId(shopId);
            //如果已发布过到区域运营，同时要删除商品sku，则提示“请先下架商品，再进行修改SKU”
            List<AreaItem> onShelfAreaItemList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(fullItemBO.getAreaItemList())) {
                onShelfAreaItemList = fullItemBO.getAreaItemList().stream().filter(result -> AreaItemStatus.ON_SHELF.getValue() == result.getStatus()).collect(Collectors.toList());
            }
//            if (!CollectionUtils.isEmpty(bo.getToDeleteSkuList()) && !CollectionUtils.isEmpty(onShelfAreaItemList)) {
//                throw new ServiceException("请先下架商品，再进行修改SKU");
//            }
            bo.setModels(models);
            bo.setVendorItemChannlRelationModels(fullItemBO.getVendorItemChannlRelationModels());
//            bo.setToCreateSkuList(fullItemBO.getSkuList());

            AreaItemOperateBO areaItemOperateBO = null;
            if(type != null && type == 2){
                List<Sku> allSkus = Lists.newArrayList();
                if(CollectionUtil.isNotEmpty(resultPack.getResultModel().getToCreateSkuList())){
                    allSkus.addAll(resultPack.getResultModel().getToCreateSkuList());
                }
                if(CollectionUtil.isNotEmpty(resultPack.getResultModel().getToUpdateSkuList())){
                    allSkus.addAll(resultPack.getResultModel().getToUpdateSkuList());

                    if (!org.springframework.util.CollectionUtils.isEmpty(bo.getSkuIds()) && !bo.getPriceCellBO().isEmpty()){
                        //编辑商品价格变动 走价格审核
//                        FullItemQueryBySkusRequest fullItemQueryBySkusRequest = new FullItemQueryBySkusRequest();
//                        fullItemQueryBySkusRequest.setUpdatedBy(bo.getUpdatedBy());
//                        fullItemQueryBySkusRequest.setSkuIds(bo.getSkuIds());
//                        fullItemQueryBySkusRequest.setPriceCellBO(bo.getPriceCellBO());
//                        fullItemQueryBySkusRequest.setShopId(bo.getShopId());
//                        log.info("fullItemQueryBySkusRequest|req::{}",JSONUtil.toJsonStr(fullItemQueryBySkusRequest));
//                        skuWriteFacade.setSkuChannelPriceFromVendors(fullItemQueryBySkusRequest);
                        Map<Long, VendorDefaultPriceCellBO> boMap = Maps.newHashMap();
                        for(Map.Entry<Long, SkuDefaultPriceSaveRequest> entry : bo.getPriceCellBO().entrySet()){
                            VendorDefaultPriceCellBO priceCellBO = new VendorDefaultPriceCellBO();
                            SkuDefaultPriceSaveRequest value = entry.getValue();
                            priceCellBO.setDefaultPrice(value.getDefaultPrice());
                            priceCellBO.setDefaultBasePrice(value.getDefaultBasePrice());
                            priceCellBO.setDefaultPCommission(value.getDefaultPCommission());
                            priceCellBO.setDefaultCommission(value.getDefaultCommission());
                            priceCellBO.setDefaultOriginalPrice(value.getDefaultOriginalPrice());
                            priceCellBO.setChannelId(value.getChannelId());
                            priceCellBO.setOperatorId(value.getOperatorId());
                            priceCellBO.setBeforeBasePrice(value.getBeforeBasePrice());
                            boMap.put(entry.getKey(),priceCellBO);
                        }
                        skuWriteDomainService.setSkuDefaultPriceFromVendors(bo.getUpdatedBy(), bo.getSkuIds(), boMap, bo.getShopId());
                    }
                }

//                try {
//                    //同步到库存
//                    List<InventorySetParam> physicalInventory1 = skuList.stream().map(sku -> {
//                        InventorySetParam setParam = new InventorySetParam();
//                        setParam.setEntityId(String.valueOf(sku.getId()));
//                        setParam.setEntityType(EntityType.SKU_ID.getCode());
//                        Map<String, String> extra = sku.getExtra();
//                        String physicalInventory = extra.get("physicalInventory");
//                        setParam.setRealQuantity(Long.valueOf(physicalInventory));
//                        setParam.setWarehouseCode(bo.getWarehouseCode());
//                        setParam.setOperatorId(bo.getOperatorId());
//                        setParam.setVendorId(shopId);
//                        return setParam;
//                    }).collect(Collectors.toList());
//                    IpmInventorySetRequest ipmInventorySetRequest = new IpmInventorySetRequest();
//                    ipmInventorySetRequest.setParams(physicalInventory1);
//                    ipmInventoryWriteFacade.adjust(ipmInventorySetRequest);
//                } catch (Exception e) {
//                    log.error("编辑商品同步库存失败原因error::{}", Throwables.getStackTraceAsString(e));
//                }
                areaItemOperateBO = itemInsertAndPublishHelper.process(resultPack.getResultModel().getItem(), allSkus, operatorIdSet, userId, userName);
            }
            log.info("push.operation::{}",JSONUtil.toJsonStr(areaItemOperateBO));
            log.info("itemManager.update.request:{},map:{}", bo, resultPack.getParamMap());
            itemManager.update(bo, resultPack.getParamMap(),areaItemOperateBO);

//            List<ThirdParanaThirdMessageCreateRequest> messageList = bo.getMessageList();
//            if (CollectionUtils.isNotEmpty(messageList)){
//                paranaThirdMessageWriteApi.batchCreate(messageList);
//            }

            cacheItemById.remove(item.getId());
            cacheSkuById.remove(AssembleDataUtils.list2set(bo.getToUpdateSkuList(), BaseSku::getId));
            cacheSkuIdByItemId.remove(item.getId());
            cacheItemDetailById.remove(item.getId());
            cacheSkuYLByItemId.remove(item.getId());
            this.sendMessage(shopId, item.getId(), tenantId, item.getStatus(), bo.getToDeleteSkuList());
            return Boolean.TRUE;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to update full item with fullItemBO: {}, shopId: {}, tenantId: {}, updatedBy: {}, cause: {}",
                    fullItemBO, shopId, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("item.update.fail");
        }
    }

    public List<VendorItemChannlRelationInfo> itemChannlInfo(Item item){
        List<VendorItemChannlRelationInfo> channlRelationInfoList = new ArrayList<>();

        // 查询商品
        VendorItemChannlRelationModel queryItemChannlRelationModel = new VendorItemChannlRelationModel();
        queryItemChannlRelationModel.setItemId(item.getId());
        queryItemChannlRelationModel.setVendorId(item.getShopId());
        queryItemChannlRelationModel.setIsDelete(0);
        List<VendorItemChannlRelationModel> itemChannlList = vendorItemChannlRelationReadService.findItemChannl(queryItemChannlRelationModel);

        if(!org.springframework.util.CollectionUtils.isEmpty(itemChannlList)){
            Set<Long> channlIdSet = itemChannlList.stream().map(VendorItemChannlRelationModel::getChannelId).collect(Collectors.toSet());
            List<EnterpriseAuthenticationInfo> enterpriseAuthenticationInfoByIds = org.assertj.core.util.Lists.newArrayList();

            ///////////////////////////
            Response<List<EnterpriseAuthenticationInfo>> response = enterpriseAuthenticationReadFacade.getEnterpriseAuthenticationInfoByIds(org.assertj.core.util.Lists.newArrayList(channlIdSet));
            if (response.isSuccess()) {
                enterpriseAuthenticationInfoByIds =  response.getResult();
            }
            ///////////////////////////

            if(!org.springframework.util.CollectionUtils.isEmpty(enterpriseAuthenticationInfoByIds)){
                Map<Long, EnterpriseAuthenticationInfo> channlMap = enterpriseAuthenticationInfoByIds.stream().collect(Collectors.toMap(EnterpriseAuthenticationInfo::getId, Function.identity()));
                List<Long> channelId = itemChannlList.stream().map(VendorItemChannlRelationModel::getChannelId).collect(Collectors.toList());
                // 查询渠道价
                List<Long> relationIdList = itemChannlList.stream().map(VendorItemChannlRelationModel::getId).collect(Collectors.toList());
                Map<Long, String> maxAndMinChannlPrice = vendorItemChannlSkuRelationReadService.getMaxAndMinChannlPrice(relationIdList,channelId);

                for (VendorItemChannlRelationModel vendorItemChannlRelationModel : itemChannlList) {
                    VendorItemChannlRelationInfo info = new VendorItemChannlRelationInfo();
                    info.setRelationId(vendorItemChannlRelationModel.getId());
                    info.setChannlId(vendorItemChannlRelationModel.getChannelId());
                    info.setChannlName(channlMap.get(vendorItemChannlRelationModel.getChannelId()).getCompanyName());
                    info.setChannlPrice(maxAndMinChannlPrice.get(vendorItemChannlRelationModel.getId()));
                    channlRelationInfoList.add(info);
                }
            }
        }
        return channlRelationInfoList;
    }

    public Boolean openItemUpdateFullItem(FullItemBO fullItemBO, Long shopId, Integer tenantId, String updatedBy,
                                  String dimensionType, String dimensionCode, List<Long> operatorIdList) {
        try {

            Item itemPre = itemReadDomainService.findById(fullItemBO.getItem().getId(), tenantId, dimensionType,
                    dimensionCode);

            ItemDetail itemDetailPre = itemDetailReadDomainService.findByItemId(fullItemBO.getItem().getId(), tenantId);
            List<Sku> skuListPre = skuReadDomainService.findByItemId(fullItemBO.getItem().getId(), tenantId, null, null);
            //查询渠道价
            List<VendorItemChannlRelationInfo> channlInfosPre = itemChannlInfo(itemPre);

            Item item = fullItemBO.getItem();
            List<Sku> skuList = fullItemBO.getSkuList();
            ItemDetail itemDetail = fullItemBO.getItemDetail();
            // 回填下关键属性
            item.setShopId(shopId);
            backFillKeyProperties(item.getId(), tenantId, item, skuList);
            ProcessResultPack<FullItemOperateBO> resultPack = itemAggHelper.openItemUpdateHandler(item, skuList, itemDetail,
                    tenantId, updatedBy, dimensionType, dimensionCode, itemPre, Sets.newHashSet(operatorIdList),shopId, itemDetailPre, skuListPre, channlInfosPre);
            FullItemOperateBO bo = resultPack.getResultModel();
            bo.setPublishedOperatorIdList(operatorIdList);
            bo.setVendorItemChannlRelationModels(fullItemBO.getVendorItemChannlRelationModels());
            bo.setUpdatedBy(updatedBy);
            bo.setShopId(shopId);
            log.info("itemManager.update.request:{},map:{}", bo, resultPack.getParamMap());

                List<Sku> allSkus = Lists.newArrayList();
                if(CollectionUtil.isNotEmpty(resultPack.getResultModel().getToCreateSkuList())){
                    allSkus.addAll(resultPack.getResultModel().getToCreateSkuList());
                }
                if(CollectionUtil.isNotEmpty(resultPack.getResultModel().getToUpdateSkuList())){
                    allSkus.addAll(resultPack.getResultModel().getToUpdateSkuList());

                    if (!org.springframework.util.CollectionUtils.isEmpty(bo.getSkuIds()) && !bo.getPriceCellBO().isEmpty()){
                        //编辑商品价格变动 走价格审核
//                        FullItemQueryBySkusRequest fullItemQueryBySkusRequest = new FullItemQueryBySkusRequest();
//                        fullItemQueryBySkusRequest.setUpdatedBy(bo.getUpdatedBy());
//                        fullItemQueryBySkusRequest.setSkuIds(bo.getSkuIds());
//                        fullItemQueryBySkusRequest.setPriceCellBO(bo.getPriceCellBO());
//                        fullItemQueryBySkusRequest.setShopId(bo.getShopId());
//                        log.info("fullItemQueryBySkusRequest|req::{}",JSONUtil.toJsonStr(fullItemQueryBySkusRequest));
//                        skuWriteFacade.setSkuChannelPriceFromVendors(fullItemQueryBySkusRequest);
                        Map<Long, VendorDefaultPriceCellBO> boMap = Maps.newHashMap();
                        for(Map.Entry<Long, SkuDefaultPriceSaveRequest> entry : bo.getPriceCellBO().entrySet()){
                            VendorDefaultPriceCellBO priceCellBO = new VendorDefaultPriceCellBO();
                            SkuDefaultPriceSaveRequest value = entry.getValue();
                            priceCellBO.setDefaultPrice(value.getDefaultPrice());
                            priceCellBO.setDefaultBasePrice(value.getDefaultBasePrice());
                            priceCellBO.setDefaultPCommission(value.getDefaultPCommission());
                            priceCellBO.setDefaultCommission(value.getDefaultCommission());
                            priceCellBO.setDefaultOriginalPrice(value.getDefaultOriginalPrice());
                            priceCellBO.setChannelId(value.getChannelId());
                            priceCellBO.setOperatorId(value.getOperatorId());
                            priceCellBO.setBeforeBasePrice(value.getBeforeBasePrice());
                            boMap.put(entry.getKey(),priceCellBO);
                        }
                        skuWriteDomainService.setSkuDefaultPriceFromVendors(bo.getUpdatedBy(), bo.getSkuIds(), boMap, bo.getShopId());
                    }
                }

                try {
                    //同步到库存
                    List<InventorySetParam> physicalInventory1 = skuList.stream().map(sku -> {
                        InventorySetParam setParam = new InventorySetParam();
                        setParam.setEntityId(String.valueOf(sku.getId()));
                        setParam.setEntityType(EntityType.SKU_ID.getCode());
                        Map<String, String> extra = sku.getExtra();
                        String physicalInventory = extra.get("physicalInventory");
                        setParam.setRealQuantity(Long.valueOf(physicalInventory));
                        setParam.setWarehouseCode(bo.getWarehouseCode());
                        setParam.setOperatorId(bo.getOperatorId());
                        setParam.setVendorId(shopId);
                        return setParam;
                    }).collect(Collectors.toList());
                    IpmInventorySetRequest ipmInventorySetRequest = new IpmInventorySetRequest();
                    ipmInventorySetRequest.setParams(physicalInventory1);
                    ipmInventoryWriteFacade.adjust(ipmInventorySetRequest);
                } catch (Exception e) {
                    log.error("编辑商品同步库存失败原因error::{}", Throwables.getStackTraceAsString(e));
                }
            if (fullItemBO.getSource() == null || !"ZQ".equals(fullItemBO.getSource())) {
                Set<Long> set = new HashSet<>(operatorIdList);
                AreaItemOperateBO   areaItemOperateBO = itemInsertAndPublishHelper.process(resultPack.getResultModel().getItem(), allSkus,set , null, null);
            }



            itemManager.openItemUpdate(bo, resultPack.getParamMap());

//            if (CollectionUtils.isNotEmpty(resultPack.getResultModel().getMessageList())){
//                List<ThirdParanaThirdMessageCreateRequest> messageList = resultPack.getResultModel().getMessageList();
//                paranaThirdMessageWriteApi.batchCreate(messageList);
//            }


            cacheItemById.remove(item.getId());
            cacheSkuById.remove(AssembleDataUtils.list2set(bo.getToUpdateSkuList(), BaseSku::getId));
            cacheSkuIdByItemId.remove(item.getId());
            cacheItemDetailById.remove(item.getId());
            cacheSkuYLByItemId.remove(item.getId());
//            this.sendMessage(shopId, item.getId(), tenantId, item.getStatus(), bo.getToDeleteSkuList());
            return Boolean.TRUE;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to update full item with fullItemBO: {}, shopId: {}, tenantId: {}, updatedBy: {}, cause: {}",
                    fullItemBO, shopId, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("item.update.fail");
        }
    }


    public Boolean sellerCancel(Long itemId, Long shopId, Integer version, Integer tenantId, String updatedBy) {
        try {
            Item item = itemReadDomainService.findById(itemId, tenantId, null, null);
            if (ItemStatus.FROZEN.getValue() != item.getStatus()) {
                throw new ServiceException("只有冻结状态可以作废");
            }
            List<ItemAudit> itemAuditList = areaItemAuditReadDomainService.findByItemId(itemId, Lists.newArrayList(AreaItemAuditStatus.UNDER_REVIEW.getValue()), AreaItemAuditType.BASE.getValue());
            if (!itemAuditList.isEmpty()) {
                throw new ServiceException("该商品审核中无法作废");
            }
            List<AreaItem> areaItemList = areaItemReadDomainService.findByVendorIdAndItemIds(shopId, Sets.newHashSet(itemId));
            Set<Long> operatorIds = areaItemList.stream().map(AreaItem::getOperatorId).collect(Collectors.toSet());
            Set<Long> skuIdSet = itemManager.sellerCancel(operatorIds, itemId, shopId, version, tenantId, updatedBy);
            cacheItemById.remove(itemId);
            cacheSkuById.remove(skuIdSet);
            return Boolean.TRUE;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to delete item by itemId: {}, shopId: {}, version: {}, tenantId: {}, updatedBy: {}, cause: {}",
                    itemId, shopId, version, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("item.delete.fail");
        }
    }

    public Boolean sellerCancelAll(Set<Long> itemIds, Long shopId, Integer version, Integer tenantId, String updatedBy) {
        for (Long itemId : itemIds) {
            try {
                Item item = itemReadDomainService.findById(itemId, tenantId, null, null);
                List<AreaItem> areaItemList = areaItemReadDomainService.findByVendorIdAndItemIds(shopId, Sets.newHashSet(itemId));
                Set<Long> operatorIds = areaItemList.stream().map(AreaItem::getOperatorId).collect(Collectors.toSet());
                Set<Long> skuIdSet = itemManager.sellerCancel(operatorIds, itemId, shopId, item.getVersion(), tenantId, updatedBy);
                cacheItemById.remove(itemId);
                cacheSkuById.remove(skuIdSet);
                return Boolean.TRUE;
            } catch (ServiceException e) {
                throw e;
            } catch (Exception e) {
                log.error("fail to delete item by itemId: {}, shopId: {}, version: {}, tenantId: {}, updatedBy: {}, cause: {}",
                        itemId, shopId, version, tenantId, updatedBy, printErrorStack(e));
                throw new ServiceException("item.delete.fail");
            }
        }
        return Boolean.TRUE;
    }

    public Boolean openItemDelete(Set<String> outerIds, Long shopId, Integer tenantId, String updatedBy) {
        Boolean flag = Boolean.TRUE;
        try {
            for (String id : outerIds) {
                Item item = itemReadDomainService.openFindByOutId(id, tenantId);
                if(!Objects.isNull(item)){
                    if(!shopId.equals(item.getShopId())){
                        throw new ServiceException(id + "非供应商商品, 删除失败");
                    }
                }else {
                    throw new ServiceException(id + "商品不存在");
                }

                List<AreaItem> areaItemList = areaItemReadDomainService.findByVendorIdAndItemIds(shopId, Sets.newHashSet(item.getId()));
                Set<Long> operatorIds = areaItemList.stream().map(AreaItem::getOperatorId).collect(Collectors.toSet());
                flag = itemManager.openItemDelete(operatorIds, id, shopId, tenantId, updatedBy);
                if(flag){
                    log.info("供应商api删除商品");
                    ThirdParanaThirdMessageCreateRequest messageCreateRequest = new ThirdParanaThirdMessageCreateRequest();
                    messageCreateRequest.setUpdatedBy(updatedBy);
                    messageCreateRequest.setUpdatedAt(new Date());
                    messageCreateRequest.setMessageType(10);
                    for (Long operatorId : operatorIds) {
                        messageCreateRequest.setOperatorId(operatorId);
                        ThirdParanaThirdMessageBean thirdParanaThirdMessageBean = new ThirdParanaThirdMessageBean();
                        thirdParanaThirdMessageBean.setStatus("-3");
                        ChoiceSkuUpdateMarkupRequest choiceSkuUpdateMarkupRequest = new ChoiceSkuUpdateMarkupRequest();
                        Set<Long> set = Sets.newHashSet();
                        set.add(item.getId());
                        choiceSkuUpdateMarkupRequest.setItemIds(set);
                        thirdParanaThirdMessageBean.setItemMessage(com.alibaba.fastjson.JSON.toJSONString(choiceSkuUpdateMarkupRequest));
                        messageCreateRequest.setBean(thirdParanaThirdMessageBean);
                        paranaThirdMessageWriteApi.create(messageCreateRequest);
                    }
                    break;
                }
            }
            return flag;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public Boolean sellerDelete(List<IdVersionPairBO> targetList, Long shopId, Integer tenantId, String updatedBy) {
        try {
            Set<Long> itemIdSet = targetList.stream().map(IdVersionPairBO::getId).collect(Collectors.toSet());
            Set<Long> deletedSkuIdSet = itemManager.sellerDelete(targetList, shopId, tenantId, updatedBy);
            cacheItemById.remove(itemIdSet);
            cacheSkuById.remove(deletedSkuIdSet);
            messageSendHelper.sendMessage(new ItemDeleteEvent(new LinkedList<>(itemIdSet), new LinkedList<>(deletedSkuIdSet), tenantId));
            return Boolean.TRUE;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to delete item by targetList: {}, shopId: {}, tenantId: {}, updatedBy: {}, cause: {}",
                    targetList, shopId, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("item.delete.fail");
        }
    }

    public Boolean sellerUpdateStatus(Long itemId, Long shopId, Integer status, Integer version, Integer tenantId, String updatedBy) {
        try {
            Set<Long> updatedSkuIdSet = itemManager.sellerUpdateStatus(itemId, version, shopId, status, tenantId, updatedBy);
            cacheItemById.remove(itemId);
            cacheSkuById.remove(updatedSkuIdSet);
            messageSendHelper.sendMessage(new ItemUpdateEvent(shopId, Collections.singletonList(itemId), tenantId, status));
            return Boolean.TRUE;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to update item status by itemId: {}, shopId: {}, status: {}, version: {}, " +
                            "tenantId: {}, updatedBy: {}, cause: {}",
                    itemId, shopId, status, version, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("item.status.update.fail");
        }
    }

    public Boolean sellerUpdateStatus(List<IdVersionPairBO> targetList, Long shopId, Integer status, Integer tenantId, String updatedBy) {
        if (CollectionUtils.isEmpty(targetList)) {
            return Boolean.TRUE;
        }

        try {
            Set<Long> itemIdSet = AssembleDataUtils.list2set(targetList, IdVersionPairBO::getId);

            if (itemUpdateStatusExtension != null) {
                invokeExtension(itemUpdateStatusExtension::checkItem, targetList, status);
            }

            Set<Long> updatedSkuIdSet = itemManager.sellerUpdateStatus(targetList, shopId, status, tenantId, updatedBy);
            cacheItemById.remove(itemIdSet);
            cacheSkuById.remove(updatedSkuIdSet);
            messageSendHelper.sendMessage(new ItemUpdateEvent(shopId, new LinkedList<>(itemIdSet), tenantId, status));
            return Boolean.TRUE;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to update item status by targetList: {}, shopId: {}, status: {}, tenantId: {}, " +
                            "updatedBy: {}, cause: {}",
                    targetList, shopId, status, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("item.status.update.fail");
        }
    }


    public Boolean adminUpdateStatus(List<IdVersionPairBO> targetList, Integer status, Integer tenantId, String updatedBy) {
        try {
            Set<Long> itemIdSet = targetList.stream().map(IdVersionPairBO::getId).collect(Collectors.toSet());
            Set<Long> updatedSkuIdSet = itemManager.adminUpdateStatus(targetList, status, tenantId, updatedBy);
            boolean isOk = Boolean.TRUE;
            cacheItemById.remove(itemIdSet);
            cacheSkuById.remove(updatedSkuIdSet);
            messageSendHelper.sendWhenOk(isOk, new ItemUpdateEvent(null, new LinkedList<>(itemIdSet), tenantId, status));
            return isOk;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to update item status by targetList: {}, status: {}, tenantId: {}, updatedBy: {}, cause: {}",
                    targetList, status, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("item.status.update.fail");
        }
    }


    public Boolean adminFrozenShop(Long shopId, Integer tenantId, String updatedBy) {
        try {
            itemManager.adminUpdateItemByShopId(true, shopId, tenantId, updatedBy);
            return dealShopItemFrozenResult(shopId, true);
        } catch (Exception e) {
            dealShopItemFrozenResult(shopId, false);

            if (e instanceof ServiceException) {
                throw e;
            }

            log.error("fail to frozen shop by shopId: {}, tenantId: {}, updatedBy: {}, cause: {}",
                    shopId, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("frozen.shop.fail");
        }
    }


    public Boolean adminUnfrozenShop(Long shopId, Integer tenantId, String updatedBy) {
        try {
            itemManager.adminUpdateItemByShopId(false, shopId, tenantId, updatedBy);
            return dealShopItemFrozenResult(shopId, true);
        } catch (Exception e) {
            dealShopItemFrozenResult(shopId, false);

            if (e instanceof ServiceException) {
                throw e;
            }

            log.error("fail to unfrozen shop by shopId: {}, tenantId: {}, updatedBy: {}, cause: {}",
                    shopId, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("unfrozen.shop.fail");
        }
    }

    /**
     * 依赖redis的进度阻塞锁结果回填
     *
     * @param shopId  店铺id
     * @param success 状态
     * @return 处理结果
     */
    private boolean dealShopItemFrozenResult(Long shopId, boolean success) {
        if (success) {
            simpleRedisHelper.setValue(ShopItemFrozenFlag.SHOP_ITEM_OPERATING_MASK + shopId,
                    ShopItemFrozenFlag.SHOP_ITEM_OPERATE_OK);
        } else {
            simpleRedisHelper.setValue(ShopItemFrozenFlag.SHOP_ITEM_OPERATING_MASK + shopId,
                    ShopItemFrozenFlag.SHOP_ITEM_OPERATE_FAIL);
        }

        return success;
    }

    public void sendMessage(Long shopId, Long itemId, Integer tenantId, Integer status, List<Sku> skuList) {
        List<Long> deleteSkuList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(skuList)) {
            deleteSkuList = skuList.stream().map(Sku::getId).collect(Collectors.toList());
        }
        messageSendHelper.sendMessage(new ItemUpdateEvent(shopId, itemId, tenantId, status, deleteSkuList));
    }

    public Boolean updateItemSapId(Long itemId, String sapId, Integer tenantId, String updatedBy) {

        //TODO  代码实现于之前方法冲突 先注释掉
//        try {
//
//            Set<Long> updatedSkuIdSet = itemManager.updateItemSapId(itemId,sapId,tenantId,updatedBy);
//
//            boolean isOk = Boolean.TRUE;
//            cacheItemById.remove(itemId);
//            cacheSkuById.remove(updatedSkuIdSet);
//            messageSendHelper.sendWhenOk(isOk, new ItemUpdateEvent(null, itemId, tenantId,null));
//            return isOk;
//        } catch (ServiceException e) {
//            throw e;
//        } catch (Exception e) {
//            log.error("fail to update item sapId by itemId:{},sapId:{}, tenantId: {}, updatedBy: {}, cause: {}",
//                    itemId, sapId, tenantId, updatedBy, printErrorStack(e));
//            throw new ServiceException("item.status.update.fail");
//        }
        return false;
    }

    public Boolean updateAreaAndChannelByVendor(Long vendorId, Long itemId, Integer version, String salesArea, String salesChannel, Integer tenantId, String updatedBy) {
        try {
            List<VendorPartnership> vendorPartnershipList = vendorPartnershipReadDomainService.queryByVendor(vendorId, null);
            Set<Long> operatorIdSet = vendorPartnershipList.stream()
                    .filter(vendorPartnership -> vendorPartnership.getLogisticsMode() == LogisticsMode.NON_GOODS_COLLECTION.getValue())
                    .map(VendorPartnership::getOperatorId).collect(Collectors.toSet());
            log.info("update area and channel by vendorId: {}, itemId: {}, version: {}, salesArea: {}, salesChannel: {}, tenantId: {}, updatedBy: {}, operatorIdSet: {}",
                    vendorId, itemId, version, salesArea, salesChannel, tenantId, updatedBy, operatorIdSet);
            Boolean isOk = itemManager.updateAreaAndChannelByVendor(itemId, operatorIdSet, version, salesArea, salesChannel, tenantId, updatedBy);
            cacheItemById.remove(itemId);
            return isOk;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to update area and channel by vendorId: {}, itemId: {}, version: {}, salesArea: {}, salesChannel: {}, tenantId: {}, updatedBy: {}, cause: {}",
                    vendorId, itemId, version, salesArea, salesChannel, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("item.delete.fail");
        }
    }

    public boolean updateZqPushStatus(Long itemId, Integer zqPushStatus, String remarks, Integer version, Integer tenantId, String updatedBy, boolean isRevoker) {

        try {

            Boolean isOk = false;

            if (null != zqPushStatus && ItemZqPushlStatusEnum.ZQ_PUSH_STATUS_RUN.getValue() == zqPushStatus.intValue() && !isRevoker) {

                ItemUpdateLog updateLog = itemUpdateLongReadDomainService.findZqUpdateLogByItemId(itemId);

                if (null != updateLog) {
                    Item itemVo = itemReadDomainService.findById(itemId, tenantId, null, null);

                    itemVo.setName(updateLog.getName());
                    itemVo.setUnit(updateLog.getUnit());
                    itemVo.setBrandId(updateLog.getBrandId());
                    itemVo.setBrandName(updateLog.getBrandName());
                    itemVo.setAdvertise(updateLog.getAdvertise());
                    itemVo.setMainImage(updateLog.getMainImage());

                    itemVo.setVideoUrl(updateLog.getVideoUrl());
                    itemVo.setSkuAttributes(updateLog.getSkuAttributes());
                    itemVo.setOtherAttributes(updateLog.getOtherAttributes());
                    itemVo.setExtra(updateLog.getItemExtra());
                    itemVo.setUpdatedAt(new Date());
                    itemVo.setUpdatedBy(updatedBy);
                    if (null != updateLog.getTaxcode()) {
                        itemVo.setTaxcode(updateLog.getTaxcode());
                    }
                    if (null != updateLog.getTaxname()) {
                        itemVo.setTaxname(updateLog.getTaxname());
                    }
                    if (null != updateLog.getVatrate()) {
                        itemVo.setVatrate(updateLog.getVatrate());
                    }
                    if (null != updateLog.getZqCustDesc()) {
                        itemVo.setZqCustDesc(updateLog.getZqCustDesc());
                    }

                    ItemDetail itemDetailVo = itemDetailReadDomainService.findByItemId(itemId, tenantId);

                    if (null != itemDetailVo) {
                        itemDetailVo.setImageJson(updateLog.getImageJson());
                        itemDetailVo.setPcDetail(updateLog.getPcDetail());
                        itemDetailVo.setWapDetail(updateLog.getWapDetail());
                        itemDetailVo.setUpdatedAt(new Date());
                    }
                    isOk = itemManager.updateZqPushStatus(itemId, zqPushStatus, remarks, version, tenantId, updatedBy, itemVo, itemDetailVo, isRevoker);
                } else {
                    isOk = itemManager.updateZqPushStatus(itemId, zqPushStatus, remarks, version, tenantId, updatedBy, null, null, isRevoker);
                }

            } else {
                isOk = itemManager.updateZqPushStatus(itemId, zqPushStatus, remarks, version, tenantId, updatedBy, null, null, isRevoker);
            }

            cacheItemById.remove(itemId);
            cacheSkuIdByItemId.remove(itemId);
            cacheItemDetailById.remove(itemId);
            cacheItemById.remove(itemId);
            return isOk;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to update zq pool status by  itemId: {}, version: {}, zqPushStatus: {}, tenantId: {}, updatedBy: {}, cause: {}",
                    itemId, version, zqPushStatus, tenantId, updatedBy, printErrorStack(e));
            throw new ServiceException("item.update.zqpushstatus.fail");
        }

    }

    public Boolean openItemBatchCreate(OpenItemCreateRequest request) throws Exception {
        try {
            // 商品信息校验组装
            List<FullItemOpenCreateBO> fullItemOpenCreateBOS = itemOpenAggHelper.itemOpenCreateHandler(request);
            // 执行创建商品
            Boolean flag = Boolean.FALSE;
            for (FullItemOpenCreateBO fullItemOpenCreateBO : fullItemOpenCreateBOS) {
                flag = itemManager.batchCreateItem(fullItemOpenCreateBO);
                if(!flag){
                    throw new Exception("item.create.fail");
                }
            }
            return flag;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
            log.error("item.create.fail msg：{}", e.getMessage());
            throw new Exception("item.create.fail");
        }
    }
    public void updateItemCategoryId(Item datum) {
        this.updateItemCategoryId(datum,null);
    }


    public void updateItemCategoryId(Item item, ItemDetailParam itemDetailParam) {
        ItemDetail itemDetail = null;
        if (itemDetailParam != null) {
            itemDetail = itemApiConverter.get(itemDetailParam);
        }
        itemManager.updateItemCategoryId(item, itemDetail);
        cacheItemById.remove(item.getId());
        cacheSkuIdByItemId.remove(item.getId());
        cacheItemById.remove(item.getId());
        cacheItemDetailById.remove(item.getId());
        cacheSkuYLByItemId.remove(item.getId());
    }


    public Long openCreateFullItemPlus(ItemTypeAndAttributeBO bitTagBO, FullItemBO fullItemBO, Long shopId, Integer tenantId,
                                   String dimensionType, String dimensionCode) {
        Item item = fullItemBO.getItem();
        List<Sku> skuList = fullItemBO.getSkuList();
        SkuYL skuYL = fullItemBO.getSkuYL();
        ItemDetail itemDetail = fullItemBO.getItemDetail();
        List<VendorItemChannlRelationModel> vendorItemChannlRelationModels = fullItemBO.getVendorItemChannlRelationModels();
        item.setType(bitTagBO.getItemType().getValue());
        item.setShopId(shopId);
        item.setBusinessType(bitTagBO.getBusinessType());

        try {
            Long bitTagValue = bitTagBuilder.build(bitTagBO.getBusinessType(), bitTagBO.getItemType(), bitTagBO.getMetaAttributeKeys());
            item.setBitTag(bitTagValue);
            skuList.forEach(it -> it.setBitTag(bitTagValue));

            ProcessResultPack<FullItemOperateBO> resultPack = itemAggHelper.createHandler(item, skuList, skuYL, itemDetail,vendorItemChannlRelationModels,
                    tenantId, dimensionType, dimensionCode);

            Long itemId = itemManager.openCreatePlus(resultPack.getResultModel(), resultPack.getParamMap());
//            messageSendHelper.sendMessage(new ItemCreateEvent(itemId, tenantId));
            return itemId;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to create full item with fullItemBO: {}, shopId: {}, tenantId: {}, cause: {}",
                    fullItemBO, shopId, tenantId, printErrorStack(e));
            throw new ServiceException("item.create.fail");
        }
    }

    public void updateItemStatus(Map<Integer,List<Long>> itemMap, Set<Long> operatorIds){
        itemManager.updateItemStatus(itemMap, operatorIds);
    }

    /**
     * 异步导入压缩包图片
     *
     */
    public void analysisImportImg() {
        try {
            //删除超过一周的数据
            excelReportWriteApi.deleteWeekAgo("item-upload");
            UploadReportQueryRequest queryRequest = new UploadReportQueryRequest();
            queryRequest.setCenter("item-upload");
            queryRequest.setReportType("item_Image_zip");
            queryRequest.setUploadStauts(0);
            Response<List<UploadReportInfoResponse>> listResponse = uploadReportReadFacade.list(queryRequest);
            if (!listResponse.isSuccess() || CollectionUtils.isEmpty(listResponse.getResult())) {
                log.info("查询批量导入图片可执行任务失败");
                return;
            }
            log.info("listResponse res size::{}",listResponse.getResult().size());
            List<ThirdUploadReportDetailRequst> importLogDetailList = new ArrayList<>();
            List<UploadReportQueryRequest> importLogList = new ArrayList<>();
            List<UploadReportInfoResponse> result = listResponse.getResult();
            for (UploadReportInfoResponse importLogInfo : result) {
                String extendJson = importLogInfo.getExtendJson();
                Long shopId = null;
                if (org.apache.commons.lang3.StringUtils.isNotBlank(extendJson)) {
                    JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(extendJson);
                    shopId = jsonObject.getLong("shopId");
                }

                if (Objects.isNull(shopId)) {
                    UploadReportQueryRequest importLogDTO = new UploadReportQueryRequest();
                    importLogDTO.setId(importLogInfo.getId());
                    importLogDTO.setUploadStauts(-1);
                    importLogList.add(importLogDTO);

                    ThirdUploadReportDetailRequst failDetailDTO = new ThirdUploadReportDetailRequst();
                    failDetailDTO.setUploadReportId(importLogInfo.getId());
                    failDetailDTO.setCol(0);
                    failDetailDTO.setReason("供应商ID未找到");
                    failDetailDTO.setSuggest("请检查供应商信息");
                    failDetailDTO.setRow(0);
                    failDetailDTO.setCreateAt(new Date());
                    failDetailDTO.setUpdatedAt(new Date());
                    importLogDetailList.add(failDetailDTO);
                    continue;
                }

                File file = null;
                ZipInputStream zipInputStream = null;
                try {
                    // 设置为处理中
                    // 更新导入记录状态
                    updateReport(importLogInfo.getId(), 1); // 处理中
                    if (StrUtil.startWith(importLogInfo.getFileUrl(), "ftp")) {
                        boolean isOK = FtpUtil.downloadFile(ftpHost, ftpPort, ftpUserName, ftpPassword, "/", importLogInfo.getFileUrl(), "/opt/god");
                        if (!isOK) {
                            UploadReportQueryRequest importLogDTO = new UploadReportQueryRequest();
                            importLogDTO.setId(importLogInfo.getId());
                            importLogDTO.setUploadStauts(-1);
                            importLogList.add(importLogDTO);

                            ThirdUploadReportDetailRequst failDetailDTO = new ThirdUploadReportDetailRequst();
                            failDetailDTO.setUploadReportId(importLogInfo.getId());
                            failDetailDTO.setCol(0);
                            failDetailDTO.setRow(0);
                            failDetailDTO.setReason("文件下载失败");
                            failDetailDTO.setSuggest("发生异常，请联系技术支持");
                            failDetailDTO.setCreateAt(new Date());
                            failDetailDTO.setUpdatedAt(new Date());
                            importLogDetailList.add(failDetailDTO);
                            continue;
                        }
                        file = new File("/opt/god/" + importLogInfo.getFileUrl());
                    } else {
                        file = EaFileUtil.downloadFile(importLogInfo.getFileUrl());
                    }

                    zipInputStream = new ZipInputStream(new FileInputStream(file), CharsetUtil.CHARSET_GBK);
                    ZipEntry entry;
                    // 获取压缩包里面的文件
                    Map<String, Map<String, List<Map<String, InputStream>>>> skuMap = new HashMap<>();
                    while (Objects.nonNull(entry = zipInputStream.getNextEntry())) {
                        if (!entry.isDirectory()) {
                            // 这方法可获取zip压缩包内的当前文件的流
                            byte[] bytes = IoUtil.readBytes(zipInputStream, false);
                            InputStream input = new ByteArrayInputStream(bytes);
                            String fileName = entry.getName();
                            boolean contains = fileName.contains(".DS_Store");
                            boolean ref = fileName.contains(".rtf");
                            boolean mac = fileName.contains("MACOSX");
                            if(contains || ref || mac){
                                continue;
                            }
                            String[] filePathArr = fileName.split("/");
                            if(filePathArr.length < 3) {
                                UploadReportQueryRequest importLogDTO = new UploadReportQueryRequest();
                                importLogDTO.setId(importLogInfo.getId());
                                importLogDTO.setUploadStauts(-1);
                                importLogList.add(importLogDTO);


                                ThirdUploadReportDetailRequst failDetailDTO = new ThirdUploadReportDetailRequst();
                                failDetailDTO.setUploadReportId(importLogInfo.getId());
                                failDetailDTO.setCol(0);
                                failDetailDTO.setRow(0);
                                failDetailDTO.setReason(fileName+"上传格式不正确");
                                failDetailDTO.setSuggest("请检查压缩包图片");
                                failDetailDTO.setCreateAt(new Date());
                                failDetailDTO.setUpdatedAt(new Date());
                                importLogDetailList.add(failDetailDTO);
                                break;
                            }
                            String sku = filePathArr[(filePathArr.length - 3)];
                            String packageName = filePathArr[(filePathArr.length - 2)];
                            String imgName = filePathArr[(filePathArr.length - 1)];
                            if (skuMap.containsKey(sku)) {
                                Map<String, List<Map<String, InputStream>>> imgListMap = skuMap.get(sku);
                                List<Map<String, InputStream>> imgList;
                                Map<String, InputStream> fileMap = new HashMap<>();
                                if (imgListMap.containsKey(packageName)) {
                                    imgList = imgListMap.get(packageName);
                                } else {
                                    imgList = new ArrayList<>();
                                }
                                fileMap.put(sku + "_" + packageName + "_" + imgName, input);
                                imgList.add(fileMap);
                                imgListMap.put(packageName, imgList);
                                skuMap.put(sku, imgListMap);
                            } else {
                                Map<String, List<Map<String, InputStream>>> imgMap = new HashMap<>();
                                List<Map<String, InputStream>> imgList = new ArrayList<>();
                                Map<String, InputStream> fileMap = new HashMap<>();
                                fileMap.put(sku + "_" + packageName + "_" + imgName, input);
                                imgList.add(fileMap);
                                imgMap.put(packageName, imgList);
                                skuMap.put(sku, imgMap);
                            }
                        }
                    }

                    if (org.springframework.util.CollectionUtils.isEmpty(skuMap)) {
                        UploadReportQueryRequest importLogDTO = new UploadReportQueryRequest();
                        importLogDTO.setId(importLogInfo.getId());
                        importLogDTO.setUploadStauts(-1);
                        importLogList.add(importLogDTO);


                        ThirdUploadReportDetailRequst failDetailDTO = new ThirdUploadReportDetailRequst();
                        failDetailDTO.setUploadReportId(importLogInfo.getId());
                        failDetailDTO.setCol(0);
                        failDetailDTO.setRow(0);
                        failDetailDTO.setReason("压缩包中未解析到itemId和图片信息");
                        failDetailDTO.setSuggest("请检查压缩包图片");
                        failDetailDTO.setCreateAt(new Date());
                        failDetailDTO.setUpdatedAt(new Date());
                        importLogDetailList.add(failDetailDTO);
                        continue;
                    }

                    List<ItemImportImage> itemImportImageList = new ArrayList<>();
                    List<ItemImportImageFail> itemImportImageFailList = new ArrayList<>();
                    Set<String> itemIds = skuMap.keySet();

                    SkuQueryByItemIdsRequest req = new SkuQueryByItemIdsRequest();
                    req.setItemIds(itemIds);
                    req.setTenantId(1);
                    req.setShopId(shopId);
                    List<ItemInfo> skuInfoList = checkToImport(req);
                    if (org.springframework.util.CollectionUtils.isEmpty(skuInfoList)) {
//                        log.info("异步解析压缩包图片,获取sku信息失败，importLogInfo={}", importLogInfo);
                        for (String itemId : itemIds) {
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(itemId);
                            fail.setRemark(itemId+"商品不存在或非待推送状态");
                            itemImportImageFailList.add(fail);
                        }

                        UploadReportQueryRequest importLogDTO = new UploadReportQueryRequest();
                        importLogDTO.setId(importLogInfo.getId());
                        importLogDTO.setUploadStauts(-1);
                        importLogList.add(importLogDTO);

                        ThirdUploadReportDetailRequst failDetailDTO = new ThirdUploadReportDetailRequst();
                        failDetailDTO.setUploadReportId(importLogInfo.getId());
                        failDetailDTO.setCol(0);
                        failDetailDTO.setRow(0);
                        failDetailDTO.setReason("商品不存在或非待推送状态");
                        failDetailDTO.setSuggest("请检查商品信息");
                        failDetailDTO.setCreateAt(new Date());
                        failDetailDTO.setUpdatedAt(new Date());
                        importLogDetailList.add(failDetailDTO);

                        continue;
                    }
                    for (int i =0;i<skuInfoList.size();i++) {
                        if (!skuInfoList.get(i).getShopId().equals(shopId)){
                            log.info("异步解析压缩包图片,SKU编码={}主图不能为空", skuInfoList.get(i).getId());
//                            ItemImportImageFail fail = new ItemImportImageFail();
//                            fail.setSkuCode(String.valueOf(skuInfoList.get(i).getId()));
//                            fail.setItemId(skuInfoList.get(i).getId());
//                            fail.setRemark(skuInfoList.get(i).getId()+"商品不存在");
//                            itemImportImageFailList.add(fail);
                            skuInfoList.remove(skuInfoList.get(i));
                        }
                    }

                    for (ItemInfo skuInfo : skuInfoList) {
                        Map<String, List<Map<String, InputStream>>> imgMap = skuMap.get(String.valueOf(skuInfo.getId()));
                        List<Map<String, InputStream>> mainImgList = imgMap.get("zhutu");
                        List<Map<String, InputStream>> pcImgList = imgMap.get("xiangqing");
//                        List<Map<String, InputStream>> wapImgList = imgMap.get("移动详情图");
                        if (org.springframework.util.CollectionUtils.isEmpty(mainImgList)) {
                            log.info("异步解析压缩包图片,SKU编码={}主图不能为空", skuInfo.getId());
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"主图不能为空");
                            itemImportImageFailList.add(fail);
                            continue;
                        }

//                        if (mainImgList.size() < 5) {
//                            log.info("异步解析压缩包图片,SKU编码={}主图不能少于5张", skuInfo.getSkuCode());
//                            ItemImportImageFail fail = new ItemImportImageFail();
//                            fail.setSkuCode(skuInfo.getSkuCode());
//                            fail.setItemId(skuInfo.getItemId());
//                            fail.setRemark("主图不能少于5张");
//                            itemImportImageFailList.add(fail);
//                            continue;
//                        }

                        if (org.springframework.util.CollectionUtils.isEmpty(pcImgList)) {
                            log.info("异步解析压缩包图片,SKU编码={}商品详情不能为空", skuInfo.getId());
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"商品详情不能为空");
                            itemImportImageFailList.add(fail);
                            continue;
                        }

                        // 获取第一张主图
                        String mainFileName = "";
                        InputStream mainInput = null;
                        boolean mainNotImg = false;
                        boolean mainSizeNotAllow = false;
                        boolean mainIsNull = false;
                        boolean otherSizeNotAllow = false;
                        List<Map<String, InputStream>> otherImgList = new ArrayList<>();
                        for (Map<String, InputStream> indexMap : mainImgList) {
                            log.error("mainImage,indexMap={},skuCode={}", indexMap, skuInfo.getId());
                            Set<String> keySet = indexMap.keySet();
                            ArrayList<String> keyList = new ArrayList<>(keySet);
                            String key = keyList.get(0);
                            if (checkImageFormat(key)) {
                                mainNotImg = true;
                                break;
                            }

                            if (key.contains(skuInfo.getId() + "_zhutu_01.")) {
                                mainInput = indexMap.get(key);
                                mainFileName = key;
                                BufferedImage bufferedImage = ImageIO.read(mainInput);
                                if (Objects.isNull(bufferedImage)) {
                                    log.error("mainImage bufferedImage,indexMap={},skuCode={},key={}", indexMap, skuInfo.getId(), key);
                                    mainIsNull = true;
                                    break;
                                }

                                int width = bufferedImage.getWidth();
                                int height = bufferedImage.getHeight();
                                if (width != height) {
                                    mainSizeNotAllow = true;
                                    break;
                                }

                                ByteArrayOutputStream os = new ByteArrayOutputStream();
                                ImageIO.write(bufferedImage, getImageFormat(key), os);
                                mainInput = new ByteArrayInputStream(os.toByteArray());
                            } else {
                                InputStream  otherInput = indexMap.get(key);
                                BufferedImage bufferedImage = ImageIO.read(otherInput);
                                if (Objects.isNull(bufferedImage)) {
                                    log.error("otherImage bufferedImage,indexMap={},skuCode={},key={}", indexMap, skuInfo.getId(), key);
                                    mainIsNull = true;
                                    break;
                                }

                                int width = bufferedImage.getWidth();
                                int height = bufferedImage.getHeight();
                                if (width != height) {
                                    otherSizeNotAllow = true;
                                    break;
                                }
                                ByteArrayOutputStream os = new ByteArrayOutputStream();
                                ImageIO.write(bufferedImage, getImageFormat(key), os);
                                otherInput = new ByteArrayInputStream(os.toByteArray());
                                indexMap.put(key,otherInput);
                                otherImgList.add(indexMap);
                            }
                        }

                        if (mainNotImg) {
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"仅支持png、jpg、jpeg的图片格式");
                            itemImportImageFailList.add(fail);
                            continue;
                        }

                        if (mainIsNull) {
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"主图未找到");
                            itemImportImageFailList.add(fail);
                            continue;
                        }

                        if (mainSizeNotAllow) {
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark("请上传1:1的主图图片");
                            itemImportImageFailList.add(fail);
                            continue;
                        }
//
                        if (otherSizeNotAllow) {
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark("请上传1:1的附图图片");
                            itemImportImageFailList.add(fail);
                            continue;
                        }

                        if (Objects.isNull(mainInput)) {
                            log.info("异步解析压缩包图片,SKU编码={}主图不能为空", skuInfo.getId());
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"主图不能为空");
                            itemImportImageFailList.add(fail);
                            continue;
                        }
                        String uuid = UUID.randomUUID().toString().replaceAll("-","");
                        // 上传主图
                        String mainUrl = eascsAliyunOssFactory.uploadFile(uuid+ "." + getImageFormat(mainFileName), mainInput);
                        IoUtil.close(mainInput);
                        if (org.apache.commons.lang3.StringUtils.isBlank(mainUrl)) {
                            log.info("异步解析压缩包图片,SKU编码={}上传主图失败", skuInfo.getId());
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"上传主图失败");
                            itemImportImageFailList.add(fail);
                            continue;
                        }

                        // 上传附属图
                        List<ImageParam> imageParamList = new ArrayList<>();
                        boolean otherUploadSuccess = true;
                        for (Map<String, InputStream> indexMap : otherImgList) {
                            Set<String> keySet = indexMap.keySet();
                            ArrayList<String> keyList = new ArrayList<>(keySet);
                            String key = keyList.get(0);
                            uuid = UUID.randomUUID().toString().replaceAll("-","");
                            String otherFilePath = eascsAliyunOssFactory.uploadFile(uuid+ "." + getImageFormat(key), indexMap.get(key));
                            IoUtil.close(indexMap.get(key));
                            if (org.apache.commons.lang3.StringUtils.isBlank(otherFilePath)) {
                                otherUploadSuccess = false;
                                break;
                            } else {
                                ImageParam imageParam = new ImageParam();
                                imageParam.setName(key);
                                imageParam.setUrl(otherFilePath);
                                imageParamList.add(imageParam);
                            }
                        }

                        if (!otherUploadSuccess) {
                            log.info("异步解析压缩包图片,SKU编码={}上传附属图失败", skuInfo.getId());
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"上传附属图失败");
                            itemImportImageFailList.add(fail);
                            continue;
                        }

                        // 处理商品详情信息
                        boolean pcNotImg = false;
                        boolean pcUploadSuccess = true;
                        boolean pcDetailIsNull = false;
                        boolean pcDetailSizeNotAllow = false;
                        StringBuilder pcDetail = new StringBuilder();
                        pcDetail.append("[{\"title\": \"\",\"content\": \"<p></p>");
                        for (Map<String, InputStream> indexMap : pcImgList) {
                            Set<String> keySet = indexMap.keySet();
                            ArrayList<String> keyList = new ArrayList<>(keySet);
                            String key = keyList.get(0);
                            if (checkImageFormat(key)) {
                                pcNotImg = true;
                                continue;
                            }

                            //检查详情图图片大小
                            InputStream  pcInput = indexMap.get(key);
                            BufferedImage bufferedImage = ImageIO.read(pcInput);
                            if (Objects.isNull(bufferedImage)) {
                                log.error("pcDetailImage bufferedImage,indexMap={},skuCode={},key={}", indexMap, skuInfo.getId(), key);
                                pcDetailIsNull = true;
                                break;
                            }

                            int width = bufferedImage.getWidth();
//                            if (width > PC_DETAIL_WIDTH ) {
//                                pcDetailSizeNotAllow = true;
//                                break;
//                            }
                            ByteArrayOutputStream os = new ByteArrayOutputStream();
                            ImageIO.write(bufferedImage, getImageFormat(key), os);
                            pcInput = new ByteArrayInputStream(os.toByteArray());
                            indexMap.put(key,pcInput);
                            //检查详情图图片大小

                            uuid = UUID.randomUUID().toString().replaceAll("-","");
                            String pcFilePath = eascsAliyunOssFactory.uploadFile(uuid+ "." + getImageFormat(key), indexMap.get(key));
                            IoUtil.close(indexMap.get(key));
                            if (org.apache.commons.lang3.StringUtils.isBlank(pcFilePath)) {
                                pcUploadSuccess = false;
                                break;
                            } else {
                                pcDetail.append("<div class=\\\"media-wrap image-wrap\\\"><img src=\\\"").append(pcFilePath).append("\\\"/></div><p></p>");
                            }
                        }
                        pcDetail.append("\"}]");

                        if (pcNotImg) {
                            log.info("异步解析压缩包图片,SKU编码={}商品详情包含非图片文件", skuInfo.getId());
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"商品详情包含非图片文件");
                            itemImportImageFailList.add(fail);
                            continue;
                        }
                        if (pcDetailIsNull) {
                            log.info("异步解析压缩包PC端详情图片,SKU编码={}商品详情片为空", skuInfo.getId());
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"商品详情片为空");
                            itemImportImageFailList.add(fail);
                            continue;
                        }
//                        if (pcDetailSizeNotAllow) {
//                            log.info("异步解析压缩包PC端详情图片,SKU编码={}PC详情图片宽度不能大于"+PC_DETAIL_WIDTH, skuInfo.getSkuCode());
//                            ItemImportImageFail fail = new ItemImportImageFail();
//                            fail.setSkuCode(skuInfo.getSkuCode());
//                            fail.setItemId(skuInfo.getItemId());
//                            fail.setRemark("PC详情图片宽度不能大于"+PC_DETAIL_WIDTH);
//                            itemImportImageFailList.add(fail);
//                            continue;
//                        }

                        if (!pcUploadSuccess) {
                            log.info("异步解析压缩包图片,SKU编码={}上传商品详情失败", skuInfo.getId());
                            ItemImportImageFail fail = new ItemImportImageFail();
                            fail.setSkuCode(String.valueOf(skuInfo.getId()));
                            fail.setItemId(skuInfo.getId());
                            fail.setRemark(skuInfo.getId()+"上传商品详情失败");
                            itemImportImageFailList.add(fail);
                            continue;
                        }

                        StringBuilder wapDetail = new StringBuilder();
//                        if (!org.springframework.util.CollectionUtils.isEmpty(wapImgList)) {
//                            boolean wapNotImg = false;
//                            boolean wapUploadSuccess = true;
//                            boolean wapDetailIsNull = false;
//                            boolean wapDetailSizeNotAllow = false;
//                            wapDetail.append("[{\"title\": \"\",\"content\": \"<p></p>");
//                            for (Map<String, InputStream> indexMap : wapImgList) {
//                                Set<String> keySet = indexMap.keySet();
//                                ArrayList<String> keyList = new ArrayList<>(keySet);
//                                String key = keyList.get(0);
//                                if (checkImageFormat(key)) {
//                                    wapNotImg = true;
//                                    continue;
//                                }
//
//
//                                //检查详情图图片大小
//                                InputStream  wapInput = indexMap.get(key);
//                                BufferedImage bufferedImage = ImageIO.read(wapInput);
//                                if (Objects.isNull(bufferedImage)) {
//                                    log.error("wapDetailImage bufferedImage,indexMap={},skuCode={},key={}", indexMap, skuInfo.getSkuCode(), key);
//                                    wapDetailIsNull = true;
//                                    break;
//                                }
//
//                                int width = bufferedImage.getWidth();
//                                if (width > WAP_DETAIL_WIDTH ) {
//                                    wapDetailSizeNotAllow = true;
//                                    break;
//                                }
//                                ByteArrayOutputStream os = new ByteArrayOutputStream();
//                                ImageIO.write(bufferedImage, getImageFormat(key), os);
//                                wapInput = new ByteArrayInputStream(os.toByteArray());
//                                indexMap.put(key,wapInput);
//                                //检查详情图图片大小
//
//                                uuid = UUID.randomUUID().toString().replaceAll("-","");
//                                String wapFilePath = eascsAliyunOssFactory.uploadFile(uuid+ "." + getImageFormat(key), indexMap.get(key));
//                                IoUtil.close(indexMap.get(key));
//                                if (org.apache.commons.lang3.StringUtils.isBlank(wapFilePath)) {
//                                    wapUploadSuccess = false;
//                                    break;
//                                } else {
//                                    wapDetail.append("<div class=\\\"media-wrap image-wrap\\\"><img src=\\\"").append(wapFilePath).append("\\\"/></div><p></p>");
//                                }
//                            }
//                            wapDetail.append("\"}]");
//
////                            if (wapNotImg) {
////                                log.info("异步解析压缩包图片,SKU编码={}移动详情图包含非图片文件", skuInfo.getSkuCode());
////                                ItemImportImageFail fail = new ItemImportImageFail();
////                                fail.setSkuCode(skuInfo.getSkuCode());
////                                fail.setRemark("移动详情图包含非图片文件");
////                                itemImportImageFailList.add(fail);
////                                continue;
////                            }
////                            if (wapDetailIsNull) {
////                                log.info("异步解析压缩包移动端详情图片,SKU编码={}移动详情图片为空", skuInfo.getSkuCode());
////                                ItemImportImageFail fail = new ItemImportImageFail();
////                                fail.setSkuCode(skuInfo.getSkuCode());
////                                fail.setItemId(skuInfo.getItemId());
////                                fail.setRemark("移动端详情图片为空");
////                                itemImportImageFailList.add(fail);
////                                continue;
////                            }
////                            if (wapDetailSizeNotAllow) {
////                                log.info("异步解析压缩包移动端详情图片,SKU编码={}移动端详情图片宽度不能大于"+WAP_DETAIL_WIDTH, skuInfo.getSkuCode());
////                                ItemImportImageFail fail = new ItemImportImageFail();
////                                fail.setSkuCode(skuInfo.getSkuCode());
////                                fail.setItemId(skuInfo.getItemId());
////                                fail.setRemark("移动端详情图片宽度不能大于"+WAP_DETAIL_WIDTH);
////                                itemImportImageFailList.add(fail);
////                                continue;
////                            }
////
////                            if (!wapUploadSuccess) {
////                                log.info("异步解析压缩包图片,SKU编码={}上传移动详情图失败", skuInfo.getSkuCode());
////                                ItemImportImageFail fail = new ItemImportImageFail();
////                                fail.setSkuCode(skuInfo.getSkuCode());
////                                fail.setRemark("上传移动详情图失败");
////                                itemImportImageFailList.add(fail);
////                                continue;
////                            }
//                        }

                        ItemImportImage itemImportImage = new ItemImportImage();
                        itemImportImage.setSkuId(skuInfo.getId());
                        itemImportImage.setItemId(skuInfo.getId());
                        itemImportImage.setMainImage(mainUrl);
                        itemImportImage.setTitle("");
                        itemImportImage.setImages(imageParamList);
                        itemImportImage.setPcDetail(pcDetail.toString());
                        itemImportImage.setWapDetail(wapDetail.toString());
                        itemImportImageList.add(itemImportImage);
                    }

//                    if (itemIds.size() != skuInfoList.size()) {
//                        Set<Long> skuInfoCodeSet = skuInfoList.stream().map(ItemInfo::getId).collect(Collectors.toSet());
//                        for (String skuCode : itemIds) {
//                            if (!skuInfoCodeSet.contains(Long.valueOf(skuCode))) {
//                                ItemImportImageFail fail = new ItemImportImageFail();
//                                fail.setSkuCode(skuCode);
//                                fail.setRemark("商品ID"+skuCode+"商品不存在或非待推送状态");
//                                itemImportImageFailList.add(fail);
//                            }
//                        }
//                    }

                    ItemImportImageBO itemImportImageBO = new ItemImportImageBO();
                    itemImportImageBO.setTenantId(1);
                    itemImportImageBO.setItemImportImageList(itemImportImageList);
                    itemImportImageBO.setItemImportImageFailList(itemImportImageFailList);
//                    itemImportImageBO.setUpdatedBy(importLogInfo.getCreatedBy());
//                    itemImportImageBO.setUpdatedName(importLogInfo.getCreatedBy());
                    Boolean isOk = itemManager.updateImageForZip(itemImportImageBO);
                    if (isOk) {
                        for (ItemImportImage itemImportImage : itemImportImageBO.getItemImportImageList()) {
                            // 清除缓存
                            Long itemId = itemImportImage.getItemId();
                            cacheItemById.remove(itemId);
                            cacheSkuById.remove(Sets.newHashSet(itemId));
                            cacheSkuIdByItemId.remove(itemId);
                            cacheItemDetailById.remove(itemId);

                            ThirdUploadReportDetailRequst failDetailDTO = new ThirdUploadReportDetailRequst();
                            failDetailDTO.setUploadReportId(importLogInfo.getId());
                            failDetailDTO.setCol(0);
                            failDetailDTO.setReason("图片导入成功的商品"+itemImportImage.getItemId());
                            failDetailDTO.setRow(0);
                            failDetailDTO.setCreateAt(new Date());
                            failDetailDTO.setUpdatedAt(new Date());
                            importLogDetailList.add(failDetailDTO);
                        }

                        for (ItemImportImageFail itemImportImageFail : itemImportImageBO.getItemImportImageFailList()) {
                            ThirdUploadReportDetailRequst failDetailDTO = new ThirdUploadReportDetailRequst();
                            failDetailDTO.setUploadReportId(importLogInfo.getId());
                            failDetailDTO.setCol(0);
                            failDetailDTO.setRow(0);
                            failDetailDTO.setReason(itemImportImageFail.getRemark());
                            failDetailDTO.setSuggest("请检查压缩包图片");
                            failDetailDTO.setCreateAt(new Date());
                            failDetailDTO.setUpdatedAt(new Date());
                            importLogDetailList.add(failDetailDTO);
                        }

                        UploadReportQueryRequest importLogDTO = new UploadReportQueryRequest();
                        importLogDTO.setId(importLogInfo.getId());
                        int successSize = itemImportImageBO.getItemImportImageList().size();
                        int failSize = itemImportImageBO.getItemImportImageFailList().size();
                        if (successSize == 0) {
                            importLogDTO.setUploadStauts(-1);
                        } else if (failSize == 0) {
                            importLogDTO.setUploadStauts(2);
                        }
                        importLogList.add(importLogDTO);
                    }
                } catch (Exception e) {
                    log.error("analysisImportImg", e);
                    UploadReportQueryRequest importLogDTO = new UploadReportQueryRequest();
                    importLogDTO.setId(importLogInfo.getId());
                    importLogDTO.setUploadStauts(-1);
                    importLogList.add(importLogDTO);

                    ThirdUploadReportDetailRequst failDetailDTO = new ThirdUploadReportDetailRequst();
                    failDetailDTO.setUploadReportId(importLogInfo.getId());
                    failDetailDTO.setCol(0);
                    failDetailDTO.setRow(0);
                    failDetailDTO.setReason(e.getMessage());
                    failDetailDTO.setSuggest("发生异常，请联系技术支持");
                    failDetailDTO.setCreateAt(new Date());
                    failDetailDTO.setUpdatedAt(new Date());
                    importLogDetailList.add(failDetailDTO);

                } finally {
                    if (Objects.nonNull(zipInputStream)) {
                        zipInputStream.close();
                    }
                    if (Objects.nonNull(file)) {
                        FileUtil.del(file);
                    }

                    // 如果是ftp文件，删除ftp文件
                    if (StrUtil.startWith(importLogInfo.getFileUrl(), "ftp")) {
                        // 删除ftp文件
                        FtpUtil.deleteFile(ftpHost, ftpPort, ftpUserName, ftpPassword, "/", importLogInfo.getFileUrl());
                    }
                }
            }

            batchUpdate(importLogDetailList,importLogList);
        } catch (Exception e) {
            log.error("analysisImportImg", e);
        }
    }

    private void batchUpdate(List<ThirdUploadReportDetailRequst> importLogDetailList,List<UploadReportQueryRequest> importLogList){

        BatchUploadReportUpdateRequest batchUploadReportUpdateRequest = new BatchUploadReportUpdateRequest();

        List<UploadReportUpdateRequest> logList = Lists.newArrayList();

        List<UploadReportDetailCreateRequest> detailCreateRequests = Lists.newArrayList();

        importLogDetailList.forEach(detailRequst->{
            UploadReportDetailCreateRequest uploadReportDetailCreateRequest = new UploadReportDetailCreateRequest();
            BeanUtils.copyProperties(detailRequst,uploadReportDetailCreateRequest);
            detailCreateRequests.add(uploadReportDetailCreateRequest);
        });

        importLogList.forEach(importLog->{
            UploadReportUpdateRequest updateRequest = new UploadReportUpdateRequest();
            BeanUtils.copyProperties(importLog,updateRequest);
            logList.add(updateRequest);
        });
        batchUploadReportUpdateRequest.setImportLogDetailList(detailCreateRequests);
        batchUploadReportUpdateRequest.setImportLogList(logList);

        uploadReportWriteFacade.batchUpdate(batchUploadReportUpdateRequest);
    }


    /**
     * 更新导入记录状态
     *
     * @param id
     * @param status
     */
    private void updateReport(Long id, int status) {
        // 更新导入记录状态
        ThirdUploadReportCreateRequest uploadReportUpdateRequest = new ThirdUploadReportCreateRequest();
        uploadReportUpdateRequest.setUploadStauts(status);
        uploadReportUpdateRequest.setId(id);
        uploadReportUpdateRequest.setUpdatedAt(new Date());
        excelReportWriteApi.update(uploadReportUpdateRequest);
    }

    private List<ItemInfo> checkToImport(SkuQueryByItemIdsRequest request) {
        List<ItemInfo> checkSuccessList = new ArrayList<>();
        Set<Long> collect = request.getItemIds().stream().mapToLong(Long::valueOf).boxed().collect(Collectors.toSet());
        List<Item> byIdSet = itemReadDomainService.findByIdSets(collect,request.getShopId(),  request.getTenantId(),request.getDimensionType(), request.getDimensionType());
        if (org.springframework.util.CollectionUtils.isEmpty(byIdSet)) {
            return checkSuccessList;
        }

        for (Item sku : byIdSet) {
            Item item = itemReadDomainService.findById(sku.getId(), sku.getTenantId(), null, null);
            if (Objects.isNull(item)) {
                continue;
            }

            // 校验状态是否是待推送 或者 被驳回
//            if (!(ItemZqPushlStatusEnum.ZQ_PUSH_STATUS_NEW.getValue() == item.getZqPushStatus() || ItemZqPushlStatusEnum.ZQ_PUSH_STATUS_REJ.getValue() == item.getZqPushStatus())) {
//                //69临时商品 非新建状态 仍允许批量修改图片
////                if(!(org.apache.commons.lang3.StringUtils.isNotBlank(sku.getZqSkuCode()) && org.apache.commons.lang3.StringUtils.startsWith(sku.getZqSkuCode(),"69"))){
////                    continue;
////                }
//            }
//            ItemInfo itemInfo= itemApiInfoConverter.get(sku);
            ItemInfo itemInfo = new ItemInfo();
            BeanUtils.copyProperties(sku,itemInfo);
            checkSuccessList.add(itemInfo);
        }

        return checkSuccessList;
    }

    /**
     * 校验图片后缀格式
     * @param image
     * @return
     */
    private static boolean checkImageFormat(String image){
        if (!image.endsWith("jpg") && !image.endsWith("JPG")
                && !image.endsWith("png") && !image.endsWith("PNG")
                && !image.endsWith("jpeg") && !image.endsWith("JPEG")) {
            return true;
        }
        return false;
    }

    /**
     * 获取图片后缀格式
     * @param image
     * @return
     */
    private String getImageFormat(String image){
        if(StringUtil.isEmpty(image)){
            return "";
        }
        String[] split = image.split("\\.");
        return split[split.length-1];
    }

    public Boolean updateItemForWdt(Long itemId,  Integer tenantId, String isWdtCreate) {
        try {
            Boolean isOk = itemManager.updateItemForWdt(itemId,  tenantId, isWdtCreate);
            cacheItemById.remove(itemId);
            return Boolean.TRUE;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to updateItemForWdt item by itemId: {},  tenantId: {}, isWdtCreate {} cause: {}",
                    itemId, tenantId, isWdtCreate, printErrorStack(e));
            throw new ServiceException("item.delete.fail");
        }
    }
}
