package io.terminus.parana.item.category.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2018-08-06 16:24:43
 */
@Data
public class OuterCategory implements Serializable {

    private static final long serialVersionUID = 8723698376448520382L;
    /**
     * id
     */
    private Long id;
    /**
     * 外部系统类目唯一键
     */
    private String outerId;
    /**
     * 类目名称
     */
    private String name;
    /**
     * 类目状态 1: 已绑定 0：未绑定
     */
    private Boolean status;
    /**
     * 外部类目类型（来源）
     */
    private String type;
    /**
     * extraJson
     */
    private String extraJson;
    /**
     * 租户id
     */
    private Integer tenantId;
    /**
     * createdAt
     */
    private Date createdAt;
    /**
     * updatedAt
     */
    private Date updatedAt;

    /**
     * updatedBy
     */
    private String updatedBy;

    /**
     * 供应商ID
     */
    private Long vendorId;

    /**
     * 类目层级
     */
    private Integer level;

    /**
     * 外部父类目id
     */
    private String outerPid;


    /**
     * 后台类目ID
     */
    private Long backCategoryId;
}
