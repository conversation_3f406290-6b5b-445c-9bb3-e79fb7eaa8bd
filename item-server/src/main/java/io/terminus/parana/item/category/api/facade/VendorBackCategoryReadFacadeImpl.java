package io.terminus.parana.item.category.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.request.VendorBackCategoryQueryByVendorIdRequest;
import io.terminus.parana.item.category.model.VendorBackCategory;
import io.terminus.parana.item.category.service.VendorBackCategoryReadDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class VendorBackCategoryReadFacadeImpl implements VendorBackCategoryReadFacade {

    private final VendorBackCategoryReadDomainService vendorBackCategoryReadDomainService;

    @Override
    public Response<List<Long>> findByVendorId(VendorBackCategoryQueryByVendorIdRequest request) {
        List<VendorBackCategory> datas = vendorBackCategoryReadDomainService.findByVendorId(request.getVendorId());
        return Response.ok(datas.stream().map(VendorBackCategory::getCategoryId).collect(Collectors.toList()));
    }
}
