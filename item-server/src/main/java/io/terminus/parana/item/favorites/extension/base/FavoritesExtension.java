package io.terminus.parana.item.favorites.extension.base;

import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.favorites.model.Favorites;
import io.terminus.parana.item.interaction.extension.base.InteractionExtension;

import java.util.List;

/**
 * 收藏扩展
 *
 * <AUTHOR>
 */
public interface FavoritesExtension extends InteractionExtension {

    /**
     * 写前扩展
     */
    ExtensionResult beforeWrite(List<Favorites> favoritesList);

    /**
     * 查询后扩展
     */
    ExtensionResult afterQuery(List<Favorites> favoritesList, Long referrerId, Long authId);

}
