package io.terminus.parana.item.enhance.component.inventory;

import com.google.common.collect.Lists;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.enhance.model.LocationBO;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.third.enums.ThirdEntityType;
import io.terminus.parana.item.third.enums.ThirdWarehouseScopeDimensionType;
import io.terminus.parana.item.plugin.third.api.inventory.api.InventoryReadApi;
import io.terminus.parana.item.third.info.InventorySourcing;
import io.terminus.parana.item.third.info.InventorySourcingByDimensionInfo;
import io.terminus.parana.item.third.info.InventorySourcingInventoryInfo;
import io.terminus.parana.item.third.param.InventorySourcingByDimensionParam;
import io.terminus.parana.item.third.param.InventorySourcingInventoryParam;
import io.terminus.parana.item.third.param.ThirdInventorySourcingRequest;
import io.terminus.parana.item.third.param.WarehouseBaseParam;
import io.terminus.parana.item.third.info.ActivityMatchedLine;
import io.terminus.parana.item.third.info.LineInventoryInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.*;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-06-20
 */
public abstract class AbstractInventoryInvoker {

    @Autowired
    private InventoryReadApi inventoryReadApi;

    private String[] buildDivisionIds(LocationBO bo) {
        return new String[]{String.valueOf(bo.getProvinceId()), String.valueOf(bo.getCityId()), String.valueOf(bo.getDistrictId())};
    }

    /**
     * 构建寻源的bundle信息
     *
     * @param skuIds   后端sku映射
     * @param bundleId 组id
     * @return bundle信息
     */
    private List<InventorySourcingInventoryParam> buildSouringInventoryParam(Map<Long, Integer> skuIds, Long bundleId) {
        List<InventorySourcingInventoryParam> result = new LinkedList<>();

        for (Map.Entry<Long, Integer> backSku : skuIds.entrySet()) {
            InventorySourcingInventoryParam inventorySourcingInventoryParam = new InventorySourcingInventoryParam();
            inventorySourcingInventoryParam.setBundleId(String.valueOf(bundleId));
            inventorySourcingInventoryParam.setEntityId(String.valueOf(backSku.getKey()));
            inventorySourcingInventoryParam.setEntityType(ThirdEntityType.SKU_ID.getCode());
            inventorySourcingInventoryParam.setQuantity(backSku.getValue().longValue());

            result.add(inventorySourcingInventoryParam);
        }

        return result;
    }

    public Long queryGroupStock(SkuInfo skuInfo, LocationBO location) {
        // STEP 1： 设置寻源的基础信息，包括：省、市、区、店铺id
        InventorySourcingByDimensionParam inventorySourcingByDimensionParam = new InventorySourcingByDimensionParam();
        inventorySourcingByDimensionParam.setDivisionIds(buildDivisionIds(location));

        // 设置寻源目标维度为商家
        inventorySourcingByDimensionParam.setDimensionId(String.valueOf(skuInfo.getShopId()));
        inventorySourcingByDimensionParam.setDimensionType(ThirdWarehouseScopeDimensionType.MERCHANT.getCode());
        inventorySourcingByDimensionParam.setVendorId(skuInfo.getAreaSku().getVendorId());
        inventorySourcingByDimensionParam.setOperatorId(skuInfo.getAreaSku().getOperatorId());

        // STEP 2: 填充商品组信息的基本信息
        List<InventorySourcingInventoryParam> bundleParams =
                buildSouringInventoryParam(skuInfo.getSkuIds(), skuInfo.getId());
        inventorySourcingByDimensionParam.setSourcingInventoryParams(bundleParams);

        ThirdInventorySourcingRequest request = new ThirdInventorySourcingRequest();
        request.setSourcingByDimensionParams(Lists.newArrayList(inventorySourcingByDimensionParam));
        request.setTenantId(skuInfo.getTenantId());

        InventorySourcing inventorySourcingInfo = inventoryReadApi.sourcing(request);

        // 忽略这个操蛋的空指针判断
        Long minQuantity = Long.MAX_VALUE;

        for (InventorySourcingInventoryInfo info : inventorySourcingInfo.getSourcingByDimensionInfos().get(0).getInventories()) {
            // 库存约定，为null时取0
            if (null == info.getMaxSellableQuantity()) {
                return 0L;
            } else {
                minQuantity = info.getMaxSellableQuantity() < minQuantity
                        ? info.getMaxSellableQuantity()
                        : minQuantity;
            }
        }

        return minQuantity;
    }

    /**
     * 设置门店/仓库信息
     *
     * @param param         库存请求参数
     * @param inventoryInfo 营销返回的库存信息
     */
    private void setWarehouseInfo(InventorySourcingInventoryParam param, LineInventoryInfo inventoryInfo) {
        if (inventoryInfo == null) {
            return;
        }

        Set<WarehouseBaseParam> warehouseBaseParams = AssembleDataUtils.list2set(inventoryInfo.getWarehouses(), it -> {
            WarehouseBaseParam warehouseBaseParam = new WarehouseBaseParam();
            warehouseBaseParam.setWarehouseCode(it.getCode());
            warehouseBaseParam.setWarehouseType(it.getType());

            return warehouseBaseParam;
        });

        param.setChannelCode(inventoryInfo.getChannelCode());
        param.setWarehouses(warehouseBaseParams);
    }

    /**
     * 设置门店信息
     *
     * @param param               请求param
     * @param activityMatchedLine 促销行信息
     */
    private void setWarehouseInfo(@Nullable Long activityId, InventorySourcingInventoryParam param, ActivityMatchedLine activityMatchedLine) {
        if (activityId == null || activityMatchedLine == null) {
            return;
        }

        Map<String, LineInventoryInfo> lineInventories = activityMatchedLine.getLineInventories();

        if (!CollectionUtils.isEmpty(lineInventories)) {
            LineInventoryInfo lineInventoryInfo = lineInventories.values().iterator().next();

            if (lineInventoryInfo != null) {
                setWarehouseInfo(param, lineInventoryInfo);
            }
        }
    }

    public Long queryStock(@Nullable Long activityId, SkuInfo skuInfo,
                           LocationBO location, @Nullable ActivityMatchedLine activityMatchedLine) {

        ThirdInventorySourcingRequest request = new ThirdInventorySourcingRequest();
        InventorySourcingByDimensionParam dimensionParam = new InventorySourcingByDimensionParam();
        InventorySourcingInventoryParam inventoryParam = new InventorySourcingInventoryParam();

        setWarehouseInfo(activityId, inventoryParam, activityMatchedLine);

        inventoryParam.setEntityId(String.valueOf(skuInfo.getId()));
        inventoryParam.setEntityType(ThirdEntityType.SKU_ID.getCode());
        List<InventorySourcingInventoryParam> inventoryParams = Collections.singletonList(inventoryParam);

        dimensionParam.setDivisionIds(buildDivisionIds(location));
        dimensionParam.setDimensionId(String.valueOf(skuInfo.getShopId()));
        dimensionParam.setDimensionType(ThirdWarehouseScopeDimensionType.MERCHANT.getCode());
        dimensionParam.setSourcingInventoryParams(inventoryParams);
        dimensionParam.setVendorId(skuInfo.getAreaSku().getVendorId());
        dimensionParam.setOperatorId(skuInfo.getAreaSku().getOperatorId());
        List<InventorySourcingByDimensionParam> dimensionParams = Collections.singletonList(dimensionParam);

        request.setSourcingByDimensionParams(dimensionParams);
        request.setTenantId(skuInfo.getTenantId());

        InventorySourcing sourcingInfo = inventoryReadApi.sourcing(request);
        if (sourcingInfo == null || CollectionUtils.isEmpty(sourcingInfo.getSourcingByDimensionInfos())) {
            return 0L;
        }

        List<InventorySourcingByDimensionInfo> dimensionInfos = sourcingInfo.getSourcingByDimensionInfos();
        // 寻源结果读取库存：1、普通商品寻源 2、商品组寻源-取库存最小值
        Long stock = Long.MAX_VALUE;
        for (InventorySourcingByDimensionInfo inventory : dimensionInfos) {
            if (CollectionUtils.isEmpty(inventory.getInventories())) {
                continue;
            }
            for (InventorySourcingInventoryInfo info : inventory.getInventories()) {
                Long maxSellableQuantity = info.getMaxSellableQuantity();
                if (info.getSatisfied() && maxSellableQuantity != null) {
                    stock = maxSellableQuantity < stock ? maxSellableQuantity : stock;
                }
            }
        }
        return stock.equals(Long.MAX_VALUE) ? 0 : stock;
    }

}
