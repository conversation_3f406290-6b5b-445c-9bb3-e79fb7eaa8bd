package io.terminus.parana.item.channel.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.channel.api.bean.request.ChannelCreateRequest;
import io.terminus.parana.item.channel.api.bean.request.ChannelDeleteRequest;
import io.terminus.parana.item.channel.api.bean.request.ChannelUpdateRequest;
import io.terminus.parana.item.channel.api.bean.request.ChannelUpdateStatusRequest;
import io.terminus.parana.item.channel.api.converter.ChannelApiConverter;
import io.terminus.parana.item.channel.app.ChannelApp;
import io.terminus.parana.item.channel.model.Channel;
import io.terminus.parana.item.channel.service.ChannelWriteDomainService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-27
 */
@Service
@AllArgsConstructor
public class ChannelWriteFacadeImpl implements ChannelWriteFacade {

    private final ChannelWriteDomainService channelWriteDomainService;

    private final ChannelApiConverter channelApiConverter;

    private final ChannelApp channelApp;

    @Override
    public Response<Long> create(ChannelCreateRequest request) {
        try {
            Channel channel = channelApiConverter.get(request.getChannel());
            Long id = channelWriteDomainService.create(channel);
            return Response.ok(id);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> update(ChannelUpdateRequest request) {
        try {
            Channel channel = channelApiConverter.get(request.getChannel());
            Boolean ok = channelWriteDomainService.update(channel);
            return Response.ok(ok);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> updateStatus(ChannelUpdateStatusRequest request) {
        try {
            Boolean ok = channelWriteDomainService.updateStatus(request.getId(), request.getStatus());
            return Response.ok(ok);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> delete(ChannelDeleteRequest request) {
        try {
            Boolean ok = channelApp.delete(request.getId(), request.getTenantId());
            return Response.ok(ok);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }
}
