package io.terminus.parana.item.category.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-04-02
 */
@Data
public class CategoryExtra implements Serializable {
    private static final long serialVersionUID = 5220861763637682579L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("类目id")
    private Long categoryId;

    @ApiModelProperty("扩展类型")
    private Integer extensionType;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("具体内容")
    private String content;

    @ApiModelProperty("大内容")
    private String bigContent;

    @ApiModelProperty("扩展内容")
    private String extraJson;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("更新时间")
    private Date updatedAt;

    @ApiModelProperty("操作人")
    private String updatedBy;

}
