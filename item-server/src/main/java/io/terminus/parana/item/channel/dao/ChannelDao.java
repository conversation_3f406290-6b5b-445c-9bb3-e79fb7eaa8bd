package io.terminus.parana.item.channel.dao;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.channel.bo.ChannelPagingCriteria;
import io.terminus.parana.item.channel.model.Channel;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-05-22
 */
@Repository
public class ChannelDao extends AbstractMybatisDao<Channel> {

    public boolean updateStatus(Long id, Integer status) {
        return sqlSession.update(sqlId("updateStatus"), ImmutableMap.of("id", id, "status", status)) == 1;
    }

    public Channel findByToken(String token) {
        return sqlSession.selectOne(sqlId("findByToken"), token);
    }

    public List<Channel> findByPid(Long pid) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(1);
        map.put("pid", pid);
        return sqlSession.selectList(sqlId("findByPid"), map);
    }

    public List<Channel> findByIds(List<Long> ids) {
        return CollectionUtils.isEmpty(ids) ? Collections.emptyList() : sqlSession.selectList(sqlId("findByIds"), ids);
    }

    public List<Channel> findByPidAndLevel(Long pid, int level) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(2);
        if (pid != null) {
            map.put("pid", pid);
        }
        map.put("level", level);
        return sqlSession.selectList(sqlId("findByPidAndLevel"), map);
    }

    public Channel findByName(String name) {
        return sqlSession.selectOne(sqlId("findByName"), name);
    }

    public Paging<Channel> paging(ChannelPagingCriteria bo) {
        return super.paging(bo);
    }

    public Long countChildren(Long id) {
        return sqlSession.selectOne(sqlId("countChildren"), id);
    }
}
