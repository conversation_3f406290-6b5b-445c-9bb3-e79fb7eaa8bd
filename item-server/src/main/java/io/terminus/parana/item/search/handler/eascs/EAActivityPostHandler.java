package io.terminus.parana.item.search.handler.eascs;

import io.terminus.parana.item.common.dimension.DimensionContext;
import io.terminus.parana.item.search.docobject.AreaItemDO;
import io.terminus.parana.item.search.docobject.ItemDO;
import io.terminus.parana.item.search.handler.BaseActivityPostHandler;
import io.terminus.parana.item.search.request.PrimarySearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2019-04-28 下午1:53
 * @deprecated not-used
 */
@Slf4j
public class EAActivityPostHandler extends BaseActivityPostHandler {

    public <ItemDoc extends AreaItemDO> void handle(List<ItemDoc> items, PrimarySearchRequest request) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        DimensionContext context = request.getDimension();
        // 未登录时，不显示价格和活动
        if (Objects.isNull(context) || null == context.getUserId()) {
            for (ItemDoc item : items) {
                item.setLowPrice(null);
                item.setHighPrice(null);
                item.setActivities(null);
            }
            return;
        }
        super.handle(items, request);
    }

}
