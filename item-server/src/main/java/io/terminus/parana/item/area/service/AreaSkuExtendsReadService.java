package io.terminus.parana.item.area.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.terminus.parana.item.area.api.bean.request.param.AdminAreaSkuExcelParam;
import io.terminus.parana.item.area.api.bean.response.AreaSkuExtendsAttInfo;
import io.terminus.parana.item.area.api.bean.response.AreaSkuExtendsInfo;
import io.terminus.parana.item.area.model.AreaSkuExtends;
import io.terminus.parana.item.area.model.AreaSkuExtendsAttribute;
import io.terminus.parana.item.area.repository.AreaSkuExtendsAttributeDAO;
import io.terminus.parana.item.area.repository.AreaSkuExtendsDAO;
import io.terminus.parana.item.brand.model.OuterBrand;
import io.terminus.parana.item.brand.model.OuterBrandBinding;
import io.terminus.parana.item.brand.repository.OuterBrandBindingDAO;
import io.terminus.parana.item.brand.repository.OuterBrandDAO;
import io.terminus.parana.item.category.model.OuterCategoryAttribute;
import io.terminus.parana.item.category.model.OuterCategoryBinding;
import io.terminus.parana.item.category.model.OuterCategoryNew;
import io.terminus.parana.item.category.repository.OuterCategoryAttributeDAO;
import io.terminus.parana.item.category.repository.OuterCategoryBindingDAO;
import io.terminus.parana.item.category.repository.OuterCategoryNewDAO;
import io.terminus.parana.item.category.util.Constant;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.ItemDetail;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.repository.ItemDao;
import io.terminus.parana.item.item.repository.ItemDetailDao;
import io.terminus.parana.item.item.repository.SkuDao;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.price.api.bean.param.AreaSkuExtendsAttributeParam;
import io.terminus.parana.item.price.api.bean.request.AreaSkuExtendsfindBySkuIdRequest;
import io.terminus.parana.item.price.api.bean.response.AreaSkuExtendsAllInfo;
import io.terminus.parana.item.price.api.bean.response.ParanaItemDetailInfoResponse;
import io.terminus.parana.item.price.api.bean.response.ParanaSkuInfoResponse;
import io.terminus.parana.item.relation.model.BaseItem;
import io.terminus.parana.item.zhengqi.model.OuterShopModel;
import io.terminus.parana.item.zhengqi.repository.OuterShopDao;
import io.terminus.parana.misc.zq.api.bean.request.ZqClientConfigQueryRequest;
import io.terminus.parana.misc.zq.api.bean.response.ZqClientConfigInfoResponse;
import io.terminus.parana.misc.zq.api.facade.ZqClientConfigReadFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AreaSkuExtendsReadService {

    @Autowired
    private AreaSkuExtendsAttributeDAO areaSkuExtendsAttributeDAO;
    @Autowired
    private SkuDao skuDao;
    @Autowired
    private ItemReadDomainService itemReadDomainService;
    @Autowired
    private ItemDetailDao itemDetailDao;
    @Autowired
    private ZqClientConfigReadFacade zqClientConfigReadFacade;
    @Autowired
    private SkuReadDomainService skuReadDomainService;
    @Autowired
    private AreaSkuExtendsDAO areaSkuExtendsDAO;
    @Autowired
    private OuterCategoryAttributeDAO outerCategoryAttributeDAO;
    @Autowired
    private ItemDao itemDao;
    @Autowired
    private OuterCategoryNewDAO outerCategoryNewDAO;
    @Autowired
    private OuterCategoryBindingDAO outerCategoryBindingDAO;
    @Autowired
    private OuterBrandDAO outerBrandDAO;
    @Autowired
    private OuterBrandBindingDAO outerBrandBindingDAO;


    public AreaSkuExtendsAllInfo findExtendsBySkuId(AreaSkuExtendsfindBySkuIdRequest request) {
        AreaSkuExtendsAllInfo info = new AreaSkuExtendsAllInfo();

        List<ZqClientConfigInfoResponse> channels = this.getZqClientConfigInfoResponses(request);
        Long projectId = Constant.ZQ_DEFAULT_OPERATOR_ID;
        if (! CollectionUtils.isEmpty(channels)){
            if (null != channels.get(0).getProjectId()){
                projectId = channels.get(0).getProjectId();
            }
        }
        request.setOperatorId(projectId);
        AreaSkuExtends areaSkuExtend = areaSkuExtendsDAO.findBySkuId(request);
        List<AreaSkuExtendsAttribute> attList = areaSkuExtendsAttributeDAO.findBySkuId(request);

        Map<Long, OuterCategoryAttribute> attributeMap = null;
        Set<Long> attributeIdSet = attList.stream().map(AreaSkuExtendsAttribute::getAttributeId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(attributeIdSet)) {
            OuterCategoryAttribute oc = new OuterCategoryAttribute();
            oc.setTenantId(areaSkuExtend.getTenantId());
            oc.setOperatorId(areaSkuExtend.getOperatorId());
            List<OuterCategoryAttribute> outerCategoryAttributes = outerCategoryAttributeDAO.selectByList(oc, Lists.newArrayList(attributeIdSet));
            attributeMap = outerCategoryAttributes.stream().collect(Collectors.toMap(OuterCategoryAttribute::getId, Function.identity()));
        }
        if (null == areaSkuExtend) {
            Item item = itemDao.findById(request.getItemId(), 1);
            if (! ObjectUtils.isEmpty(item)){

                //查询外部品牌绑定信息
                List<OuterBrandBinding> outerBrandBindingList = outerBrandBindingDAO.findByProjectIdAndBrandId(null, item.getBrandId());
                if (CollectionUtil.isNotEmpty(outerBrandBindingList)){
                    info.setOutBrandId(outerBrandBindingList.get(0).getOutBrandId());
                    info.setOutBrandName(outerBrandBindingList.get(0).getOuterName());
                }
                //查询外部分类绑定信息
                List<OuterCategoryBinding> outerCategoryBindingList = outerCategoryBindingDAO.findByCategoryId(item.getCategoryId());
                if (CollectionUtil.isNotEmpty(outerCategoryBindingList)){
                    info.setOutCategoryId(outerCategoryBindingList.get(0).getOuterCategoryId());
                    OuterCategoryNew categoryNew = outerCategoryNewDAO.findByOutIdAndOperatorId(info.getOutCategoryId().toString(),Constant.ZQ_DEFAULT_OPERATOR_ID);
                    if (! ObjectUtils.isEmpty(categoryNew)){
                        info.setOutCategoryName(categoryNew.getName());
                    }
                }
                info.setVatrate(item.getVatrate());
                info.setTaxCode(item.getTaxcode());
                info.setTaxName(item.getTaxname());
                info.setUnit(item.getUnit());
                info.setUniversalName(item.getUniversalName());
            }
            List<ParanaSkuInfoResponse> skuInfoResponses = new ArrayList<>();
            List<Sku> skuList = skuDao.findByItemId(request.getItemId(),request.getTenantId());
            if (! CollectionUtils.isEmpty(skuList)){
                skuList.forEach(v->{
                    ParanaSkuInfoResponse skuInfoResponse = new ParanaSkuInfoResponse();
                    skuInfoResponse.setId(v.getId());
                    skuInfoResponse.setCommoditymodel(v.getCommoditymodel());
                    skuInfoResponse.setBarcode(v.getBarcode());
                    skuInfoResponse.setPmodel(v.getPmodel());
                    skuInfoResponse.setColour(v.getColour());
                    skuInfoResponses.add(skuInfoResponse);
                });
            }
            info.setParanaSkuInfoResponses(skuInfoResponses);
        } else {
            info.setId(areaSkuExtend.getId());
            info.setAreaSkuId(areaSkuExtend.getAreaSkuId());
            info.setItemId(areaSkuExtend.getItemId());
            info.setSkuId(areaSkuExtend.getSkuId());
            info.setOutCategoryId(areaSkuExtend.getOutCategoryId());
            if (null != areaSkuExtend.getOutCategoryId()){
                OuterCategoryNew categoryNew = outerCategoryNewDAO.findByOutIdAndOperatorId(areaSkuExtend.getOutCategoryId().toString(),Constant.ZQ_DEFAULT_OPERATOR_ID);
                if (! ObjectUtils.isEmpty(categoryNew)){
                    info.setOutCategoryName(categoryNew.getName());
                }
            }
            info.setOutProjectCategoryId(areaSkuExtend.getOutProjectCategoryId());
            if (null != areaSkuExtend.getOutProjectCategoryId()){
                OuterCategoryNew outerShopModel = outerCategoryNewDAO.findByOutIdAndOperatorId(areaSkuExtend.getOutProjectCategoryId().toString(),projectId);
                if (! ObjectUtils.isEmpty(outerShopModel)){
                    info.setOutProjectCategoryName(outerShopModel.getName());
                }
            }
            info.setOutBrandId(areaSkuExtend.getOutBrandId());
            if (null != areaSkuExtend.getOutBrandId()){
                OuterBrand outerBrand = outerBrandDAO.queryByOutIdAndOperatorId(areaSkuExtend.getOutBrandId().toString(),Constant.ZQ_DEFAULT_OPERATOR_ID);
                if (! ObjectUtils.isEmpty(outerBrand)){
                    info.setOutBrandName(outerBrand.getBrandName());
                }
            }
            info.setPmodel(areaSkuExtend.getPmodel());
            info.setTaxCode(areaSkuExtend.getTaxCode());
            info.setTaxName(areaSkuExtend.getTaxName());
            info.setVatrate(areaSkuExtend.getVatrate());
            info.setMarketPrice(areaSkuExtend.getMarketPrice());;
            info.setExtendJson(areaSkuExtend.getExtendJson());
            info.setStatus(areaSkuExtend.getStatus());
            info.setUnit(areaSkuExtend.getUnit());
            info.setUniversalName(areaSkuExtend.getUniversalName());
            if (StringUtils.isNotEmpty(areaSkuExtend.getExtendJson())){

                List<ParanaSkuInfoResponse> paranaSkuInfoResponses = JSON.parseArray(areaSkuExtend.getExtendJson(), ParanaSkuInfoResponse.class);
                List<Sku> skuList = skuDao.findByItemId(request.getItemId(),request.getTenantId());

                Map<Long, ParanaSkuInfoResponse> skuMap = paranaSkuInfoResponses.stream()
                        .collect(Collectors.toMap(ParanaSkuInfoResponse::getId, sku -> sku));

                skuList.forEach(v->{
                    if (! skuMap.containsKey(v.getId())){
                        ParanaSkuInfoResponse response = new ParanaSkuInfoResponse();
                        response.setId(v.getId());
                        response.setCommoditymodel(v.getCommoditymodel());
                        response.setBarcode(v.getBarcode());
                        response.setPmodel(v.getPmodel());
                        response.setColour(v.getColour());
                        paranaSkuInfoResponses.add(response);
                    }
                });

                info.setParanaSkuInfoResponses(paranaSkuInfoResponses);
            }else{
                List<ParanaSkuInfoResponse> skuInfoResponses = new ArrayList<>();
                List<Sku> skuList = skuDao.findByItemId(request.getItemId(),request.getTenantId());
                if (! CollectionUtils.isEmpty(skuList)){
                    skuList.forEach(v->{
                        ParanaSkuInfoResponse skuInfoResponse = new ParanaSkuInfoResponse();
                        skuInfoResponse.setId(v.getId());
                        skuInfoResponse.setCommoditymodel(v.getCommoditymodel());
                        skuInfoResponse.setBarcode(v.getBarcode());
                        skuInfoResponse.setPmodel(v.getPmodel());
                        skuInfoResponse.setColour(v.getColour());
                        skuInfoResponses.add(skuInfoResponse);
                    });
                }
                info.setParanaSkuInfoResponses(skuInfoResponses);
            }
        }

        List<AreaSkuExtendsAttributeParam> attInfo = new ArrayList<>();
        for (AreaSkuExtendsAttribute att : attList){
            AreaSkuExtendsAttributeParam param = new AreaSkuExtendsAttributeParam();
            param.setIsCustAttr(att.getIsCustAttr());
            param.setAttributeId(att.getAttributeId());
            if (attributeMap != null && attributeMap.containsKey(att.getAttributeId())) {
                OuterCategoryAttribute outerCategoryAttribute = attributeMap.get(att.getAttributeId());
                if (outerCategoryAttribute != null && StringUtils.isNotBlank(outerCategoryAttribute.getName())) {
                    param.setAttributeName(outerCategoryAttribute.getName());
                } else {
                    param.setAttributeName(att.getAttributeName());
                }
            } else {
                param.setAttributeName(att.getAttributeName());
            }
            param.setIsCustVal(att.getIsCustVal());
            param.setValueId(att.getValueId());
            param.setValueCode(att.getValueCode());
            param.setValueLabel(att.getValueLabel());
            attInfo.add(param);
        }
        info.setAttInfos(attInfo);
        info.setStockQty(0L);

        ItemDetail itemDetail = itemDetailDao.findByItemId(request.getItemId(),request.getTenantId());
        if (! ObjectUtils.isEmpty(itemDetail)){
            ParanaItemDetailInfoResponse detailInfoResponse = new ParanaItemDetailInfoResponse();
            BeanUtils.copyProperties(itemDetail,detailInfoResponse);
            info.setItemDetail(detailInfoResponse);
        }

        Item item = itemDao.findById(request.getItemId(),request.getTenantId());
        if (! ObjectUtils.isEmpty(item)){
            info.setMainImage(item.getMainImage());
        }

        return info;
    }

    private List<ZqClientConfigInfoResponse> getZqClientConfigInfoResponses(AreaSkuExtendsfindBySkuIdRequest request) {
        //获取政企对接的渠道信息
        ZqClientConfigQueryRequest queryChannel = new ZqClientConfigQueryRequest();
        queryChannel.setStatus(1);
        queryChannel.setAuthId(request.getReqHeardDistributorIds());
        List<ZqClientConfigInfoResponse> channels = Assert.take(zqClientConfigReadFacade.list(queryChannel));
        return channels;
    }


    private Map<Long,AreaSkuExtendsAllInfo> querySkuOtherAttrs(Set<Long> skuIdSet,Set<Long> itemIdSet,Integer tenantId){

        Map<Long,AreaSkuExtendsAllInfo> result = new HashMap<>();
        if(null == skuIdSet || skuIdSet.isEmpty()){
            return result;
        }

        List<Sku> skuInfoList = skuReadDomainService.findByIdSet(skuIdSet,itemIdSet,tenantId,null,null);
        Map<Long, List<Sku>> skuMap = skuInfoList.stream().collect(Collectors.groupingBy(Sku::getItemId));

        Map<Long,Long> itemSkuIdMap = new HashMap<>();
        Map<Long,Long> jyPriceMap = new HashMap<>();
        if(null != skuInfoList){
            for(Sku sku : skuInfoList){
                itemIdSet.add(sku.getItemId());
                itemSkuIdMap.put(sku.getId(),sku.getItemId());
                //无外部拓展 默认取基准价
                if(null != sku.getPrice()){
                    jyPriceMap.put(sku.getId(),sku.getPrice());
                }
            }
        }

        if(itemIdSet.isEmpty()){
            return result;
        }

        List<Item> itemInfoList = itemReadDomainService.findByIdSet(itemIdSet,tenantId,null,null);

        Map<Long,AreaSkuExtendsAllInfo> itemResult = new HashMap<>();

        for(Item itemInfo : itemInfoList){
            AreaSkuExtendsAllInfo extendsInfo = new AreaSkuExtendsAllInfo();

            BigDecimal vatrate = null;
            String pmodel = null;
            String taxCode = null;
            String taxName = null;

            if(null != itemInfo){
//                List<GroupedOtherAttribute> otherAttributes = itemInfo.getOtherAttributes();
//                if(null != otherAttributes){
//                    for(GroupedOtherAttribute otherAttr : otherAttributes){
//                        if(null != otherAttr && StringUtils.equals("基本属性",otherAttr.getGroup())){
//                            if(null != otherAttr.getOtherAttributes()){
//                                Map<String,BigDecimal> vatrateMap = new HashMap<>();
//                                vatrateMap.put("13%",new BigDecimal("0.13"));
//                                vatrateMap.put("9%",new BigDecimal("0.09"));
//                                vatrateMap.put("0",new BigDecimal("0"));
//                                for(OtherAttribute attr : otherAttr.getOtherAttributes()){
//                                    if(StringUtils.equals("税率",attr.getAttrKey()) && null != attr.getAttrVal()){
//                                        vatrate = vatrateMap.get(attr.getAttrVal());
//                                    }
//                                    if(StringUtils.equals("规格型号",attr.getAttrKey()) && null != attr.getAttrVal()){
//                                        pmodel = attr.getAttrVal();
//                                    }
//                                    if(StringUtils.equals("税收分类编码",attr.getAttrKey()) && null != attr.getAttrVal()){
//                                        taxCode = attr.getAttrVal();
//                                    }
//                                    if(StringUtils.equals("税收分类名称",attr.getAttrKey()) && null != attr.getAttrVal()){
//                                        taxName = attr.getAttrVal();
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }

                vatrate = itemInfo.getVatrate();
                taxCode = itemInfo.getTaxcode();
                taxName = itemInfo.getTaxname();
                if(!CollectionUtil.isEmpty(skuMap) && skuMap.containsKey(itemInfo.getId())){
                    extendsInfo.setPmodel(skuMap.get(itemInfo.getId()).get(0).getPmodel());
                }
                extendsInfo.setItemId(itemInfo.getId());
                extendsInfo.setVatrate(vatrate);
                extendsInfo.setTaxCode(taxCode);
                extendsInfo.setTaxName(taxName);
                itemResult.put(itemInfo.getId(),extendsInfo);
            }
        }

        for(Long skuId : itemSkuIdMap.keySet()) {

            Long itemId = itemSkuIdMap.get(skuId);
            AreaSkuExtendsAllInfo itemExtendInfo = itemResult.get(itemId);
            if(null != itemExtendInfo) {

                Long marketPrice = jyPriceMap.get(skuId);
                if(null != marketPrice && marketPrice.compareTo(Long.valueOf("0")) > 0) {
                    itemExtendInfo.setMarketPrice(new BigDecimal(marketPrice.toString()));
                }

                result.put(skuId,itemExtendInfo);
            }
        }

        return result;
    }


    private AreaSkuExtendsAllInfo findBySKuIdToInfo(AreaSkuExtends areaSkuExtends,
                                                    List<AreaSkuExtendsAttribute> attList,
                                                    Map<Long, OuterCategoryAttribute> attributeMap) {
        AreaSkuExtendsAllInfo item = new AreaSkuExtendsAllInfo();
        if(null==areaSkuExtends){
            return item;
        }

        item.setId(areaSkuExtends.getId());
        item.setAreaSkuId(areaSkuExtends.getAreaSkuId());
        item.setItemId(areaSkuExtends.getItemId());
        item.setSkuId(areaSkuExtends.getSkuId());
        item.setOutCategoryId(areaSkuExtends.getOutCategoryId());
        item.setOutBrandId(areaSkuExtends.getOutBrandId());
        item.setPmodel(areaSkuExtends.getPmodel());
        item.setTaxCode(areaSkuExtends.getTaxCode());
        item.setTaxName(areaSkuExtends.getTaxName());
        item.setVatrate(areaSkuExtends.getVatrate());
        item.setMarketPrice(areaSkuExtends.getMarketPrice());;
        item.setExtendJson(areaSkuExtends.getExtendJson());
        item.setStatus(areaSkuExtends.getStatus());

        List<AreaSkuExtendsAttributeParam> attInfo = new ArrayList<>();
        for (AreaSkuExtendsAttribute att : attList){
            AreaSkuExtendsAttributeParam param = new AreaSkuExtendsAttributeParam();
            param.setIsCustAttr(att.getIsCustAttr());
            param.setAttributeId(att.getAttributeId());
            if (attributeMap != null && attributeMap.containsKey(att.getAttributeId())) {
                OuterCategoryAttribute outerCategoryAttribute = attributeMap.get(att.getAttributeId());
                if (outerCategoryAttribute != null && StringUtils.isNotBlank(outerCategoryAttribute.getName())) {
                    param.setAttributeName(outerCategoryAttribute.getName());
                } else {
                    param.setAttributeName(att.getAttributeName());
                }
            } else {
                param.setAttributeName(att.getAttributeName());
            }
            param.setIsCustVal(att.getIsCustVal());
            param.setValueId(att.getValueId());
            param.setValueCode(att.getValueCode());
            param.setValueLabel(att.getValueLabel());
            attInfo.add(param);
        }
        item.setAttInfos(attInfo);
        item.setStockQty(0L);
        return item;
    }

    public List<AreaSkuExtendsAttInfo> findAttribute(List<AdminAreaSkuExcelParam> params) {

        List<AreaSkuExtendsAttInfo> resultList = new ArrayList<>();
        List<AreaSkuExtendsAttribute> baseList= areaSkuExtendsAttributeDAO.findAttribute(params);
        for(AreaSkuExtendsAttribute item : baseList){
            AreaSkuExtendsAttInfo attInfo = attPojoToInfo(item);
            resultList.add(attInfo);
        }
        return resultList;
    }

    public List<AreaSkuExtendsInfo> findExtends(List<AdminAreaSkuExcelParam> params,boolean isFetchItem) {
        List<AreaSkuExtendsInfo> resultList = new ArrayList<>();
        Set<Long> skuIdsSet = new HashSet<>();
        Set<Long> itemIdsSet = new HashSet<>();
        Integer tenantId = null;
        for(AdminAreaSkuExcelParam param : params){
            skuIdsSet.add(param.getSkuId());
            itemIdsSet.add(param.getItemId());
            if(null == tenantId){
                tenantId = param.getTenantId();
            }
        }
        List<AreaSkuExtends> baseList = areaSkuExtendsDAO.findForExcel(params);
        for(AreaSkuExtends item : baseList){
            AreaSkuExtendsInfo info = this.extPojoToInfo(item);
            resultList.add(info);
            if(skuIdsSet.contains(item.getSkuId())){
                skuIdsSet.remove(item.getSkuId());
            }
        }

        if(isFetchItem){
            if(!skuIdsSet.isEmpty()){
                Map<Long,AreaSkuExtendsAllInfo> extendsAllInfoMap = querySkuOtherAttrs(skuIdsSet,itemIdsSet,tenantId);
                if(!extendsAllInfoMap.isEmpty()){
                    for(Long skuId : extendsAllInfoMap.keySet()){
                        AreaSkuExtendsAllInfo extendInfo = extendsAllInfoMap.get(skuId);
                        if(null != extendInfo){
                            AreaSkuExtendsInfo info = new AreaSkuExtendsInfo();
                            BeanUtils.copyProperties(extendInfo,info);
                            info.setSkuId(skuId);
                            resultList.add(info);
                        }
                    }
                }
            }
        }


        return resultList;
    }

    public List<AreaSkuExtendsAttInfo> findSkuAtt(List<AdminAreaSkuExcelParam> params){
        List<AreaSkuExtendsAttInfo> resultList = new ArrayList<>();
        List<AreaSkuExtendsAttribute> attributeList = areaSkuExtendsAttributeDAO.findAttribute(params);
        for(AreaSkuExtendsAttribute att : attributeList){
            AreaSkuExtendsAttInfo attInfo = attPojoToInfo(att);
            resultList.add(attInfo);
        }
        return resultList;
    }

    private AreaSkuExtendsInfo extPojoToInfo(AreaSkuExtends item){
        AreaSkuExtendsInfo info = new AreaSkuExtendsInfo();
        info.setId(item.getId());
        info.setOutCategoryId(item.getOutCategoryId());
        info.setOutBrandId(item.getOutBrandId());
        info.setPmodel(item.getPmodel());
        info.setTaxCode(item.getTaxCode());
        info.setTaxName(item.getTaxName());
        info.setVatrate(item.getVatrate());
        info.setMarketPrice(item.getMarketPrice());
        info.setAttributesJson(item.getAttributesJson());
        info.setOperatorId(item.getOperatorId());
        info.setAreaSkuId(item.getAreaSkuId());
        info.setStatus(item.getStatus());
        info.setTenantId(item.getTenantId());
        info.setItemId(item.getItemId());
        info.setSkuId(item.getSkuId());
        info.setUpdatedAt(item.getUpdatedAt());
        return info;
    }

    private AreaSkuExtendsAttInfo attPojoToInfo(AreaSkuExtendsAttribute item){
        AreaSkuExtendsAttInfo attInfo = new AreaSkuExtendsAttInfo();
        attInfo.setId(item.getId());
        attInfo.setTenantId(item.getTenantId());
        attInfo.setOperatorId(item.getOperatorId());
        attInfo.setAreaSkuId(item.getAreaSkuId());
        attInfo.setItemId(item.getItemId());
        attInfo.setSkuId(item.getSkuId());
        attInfo.setIsCustAttr(item.getIsCustAttr());
        attInfo.setAttributeId(item.getAttributeId());
        attInfo.setAttributeName(item.getAttributeName());
        attInfo.setIsCustVal(item.getIsCustVal());
        attInfo.setValueId(item.getValueId());
        attInfo.setValueCode(item.getValueCode());
        attInfo.setValueLabel(item.getValueLabel());
        attInfo.setExtendJson(item.getExtendJson());
        attInfo.setStatus(item.getStatus());
        return attInfo;
    }


    public AreaSkuExtendsInfo findBySkuIdOperatorId(Long skuId,Long operatorId,Integer tenantId){

        AreaSkuExtends areaSkuExtends = areaSkuExtendsDAO.findBySkuIdOperatorId(skuId,operatorId,tenantId);
        if(null != areaSkuExtends){
            return this.extPojoToInfo(areaSkuExtends);
        }
        return null;
    }

    public List<Long> findItemIdsByOperatorId(Long operatorId,Integer tenantId){

        List<Long> itemIds = areaSkuExtendsDAO.findItemIdsByOperatorId(operatorId,tenantId);
        if(null == itemIds){
            itemIds = new ArrayList<>();
        }
        return itemIds;
    }

    public List<AreaSkuExtendsInfo> findByItemIdsAndOperatorId(Set<Long> itemIds, Long operatorId, Integer tenantId){

        List<AreaSkuExtends> queryResult = areaSkuExtendsDAO.findByItemIdsAndOperatorId(itemIds,operatorId,tenantId);
        if(null == queryResult){
            queryResult = new ArrayList<>();
        }

        List<AreaSkuExtendsInfo> result = new ArrayList<>();

        for(AreaSkuExtends extendVO :queryResult){
            result.add(extPojoToInfo(extendVO));
        }
        return result;
    }

    public AreaSkuExtends queryByItemIdsAndOperatorId(Long itemId, Long operatorId, Integer tenantId){
        AreaSkuExtends areaSkuExtends = areaSkuExtendsDAO.queryByItemIdsAndOperatorId(itemId,operatorId,tenantId);
        if(ObjectUtils.isEmpty(areaSkuExtends)){
            return null;
        }
        return areaSkuExtends;
    }


}
