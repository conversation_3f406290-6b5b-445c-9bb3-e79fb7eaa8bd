package io.terminus.parana.item.search.handler;

import com.google.common.collect.Lists;
import io.terminus.api.request.AbstractPageRequest;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.item.enums.ItemStatus;
import io.terminus.parana.item.search.context.SearchContext;
import io.terminus.parana.item.search.docobject.ChoiceItemDO;
import io.terminus.parana.item.search.dto.ChoiceFrontItemDTO;
import io.terminus.parana.item.search.dto.SearchedItemWithAggs;
import io.terminus.parana.item.search.request.ChoiceItemSearchRequest;
import io.terminus.parana.item.search.request.ChoicePrimarySearchRequest;
import io.terminus.parana.item.search.request.PrimarySearchRequest;
import io.terminus.parana.item.search.request.handler.ChoiceItemSearchHandler;
import io.terminus.parana.search.client.builder.QueryBuilder;
import io.terminus.parana.search.client.result.Document;
import io.terminus.parana.search.client.result.SearchResult;
import io.terminus.parana.search.client.search.SearchRequest;
import io.terminus.parana.search.client.search.query.BoolQuery;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/20 17:38
 */
@Slf4j
public class BaseChoiceItemSearchHandler implements ChoiceItemSearchHandler {

    @Override
    public void handle(AbstractPageRequest abstractPageRequest, SearchRequest searchRequest) {
        ChoiceItemSearchRequest choiceItemRequest = (ChoiceItemSearchRequest) abstractPageRequest;

        // 获取查询对象
        BoolQuery boolQuery = (BoolQuery) searchRequest.getQuery();
        handle(choiceItemRequest, searchRequest, boolQuery);
        handleShowDistributorId(choiceItemRequest, boolQuery);
        List<Integer> status = Lists.newArrayList();
        status.add(1);
        status.add(-1);
        status.add(-5);
        boolQuery.filter(QueryBuilder.termsQuery().field("status").values(status));
    }

    protected void handle(ChoiceItemSearchRequest choiceItemRequest, SearchRequest searchRequest, BoolQuery boolQuery) {
        // 只查询加入渠道商商品库
        if (Objects.nonNull(choiceItemRequest.getFunnelDistributor()) && 1 == choiceItemRequest.getFunnelDistributor()) {
            boolQuery.filter(QueryBuilder.existsQuery().field("distributorIds"));
        }
    }

    protected void handleShowDistributorId(ChoiceItemSearchRequest choiceItemRequest, BoolQuery boolQuery) {
        if(Objects.nonNull(choiceItemRequest.getReqHeardDistributorIds())) {
            BoolQuery should = new BoolQuery().should(QueryBuilder.termQuery().field("source").value(BigDecimal.ZERO))
                    .should(QueryBuilder.termsQuery().field("showDistributorIds").value(choiceItemRequest.getReqHeardDistributorIds()));
            boolQuery.filter(should);
        }
    }

}
