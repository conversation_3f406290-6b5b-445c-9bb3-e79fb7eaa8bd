package io.terminus.parana.item.category.api.converter;

import io.terminus.parana.item.category.api.bean.response.*;
import io.terminus.parana.item.category.model.*;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 2018-07-16 下午5:15
 */
@Mapper(componentModel = "spring")
public interface CategoryConverter {
    BackCategoryInfo domain2dto(BackCategory backCategory);

    FrontCategoryInfo domain2dto(FrontCategory frontCategory);

    OuterCategoryInfo domain2dto(OuterCategory outerCategory);

    OuterCategoryNewInfo Category(OuterCategoryNew outerCategoryNew);

    OuterCategoryAttributeInfo Attribute(OuterCategoryAttribute categoryAttribute);

    BackCategoryAncestorsInfo toAncestorsModel(BackCategory backCategory);

    OuterCategoryBindingInfo do2model(OuterCategoryRelation relation);

    OuterCategoryAttributeInfo getInfo(OuterCategoryAttributeValue domain);
}
