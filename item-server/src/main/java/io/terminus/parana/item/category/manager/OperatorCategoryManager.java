/*
 * Copyright (c) 2018. 杭州端点网络科技有限公司.  All rights reserved.
 */

package io.terminus.parana.item.category.manager;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.category.model.BackCategory;
import io.terminus.parana.item.category.model.OperatorCategory;
import io.terminus.parana.item.category.model.OperatorCategoryBinding;
import io.terminus.parana.item.category.multi.cache.CacheOperatorCategoryInfoById;
import io.terminus.parana.item.category.repository.OperatorCategoryBindingDao;
import io.terminus.parana.item.category.repository.OperatorCategoryDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperatorCategoryManager {

    private final OperatorCategoryDao operatorCategoryDao;
    private final OperatorCategoryBindingDao operatorCategoryBindingDao;
    private final CacheOperatorCategoryInfoById cacheOperatorCategoryById;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean move(OperatorCategory toMove, OperatorCategory moved) {
        Boolean ok = operatorCategoryDao.update(toMove);

        if (!ok) {
            throw new ServiceException("move.index.fail");
        }

        if (log.isDebugEnabled()) {
            log.debug("successfully to update 'toMove' shop category with: {}", toMove);
        }

        ok = operatorCategoryDao.update(moved);

        if (!ok) {
            throw new ServiceException("move.index.fail");
        }

        if (log.isDebugEnabled()) {
            log.debug("successfully to update 'moved' shop category with: {}", moved);
        }

        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean bindTargetWithCategoryUpdate(OperatorCategory shopCategory, OperatorCategoryBinding shopCategoryBinding) {
        boolean ok = operatorCategoryDao.update(shopCategory);

        if (!ok) {
            throw new ServiceException("bind.shop.category.fail");
        }

        if (log.isDebugEnabled()) {
            log.debug("successfully to update shop category for binding with: {}", shopCategory);
        }

        ok = operatorCategoryBindingDao.create(shopCategoryBinding);

        if (!ok) {
            throw new ServiceException("bind.create.fail");
        }

        if (log.isDebugEnabled()) {
            log.debug("successfully to create shop category binding with: {}", shopCategoryBinding);
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean batchBindAndUnbind(Long categoryId, List<OperatorCategoryBinding> bindingList, @Nullable List<Long> unbindingIdList) {
        if (!CollectionUtils.isEmpty(unbindingIdList)) {
            operatorCategoryBindingDao.deletes(unbindingIdList);
        }

        if (!CollectionUtils.isEmpty(bindingList)) {
            operatorCategoryBindingDao.creates(bindingList);
        }

        operatorCategoryDao.updateHasBind(categoryId, operatorCategoryBindingDao.checkIfOperatorCategoryIdHasBinding(categoryId));

        // 缓存失效
        OperatorCategory operatorCategory = new OperatorCategory();
        operatorCategory.setId(categoryId);
        cacheOperatorCategoryById.operatorCategoryByIdCacheEvict(operatorCategory);

        return true;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean unbindTargetWithCategoryUpdate(OperatorCategory shopCategory, Long bindId) {
        boolean ok = operatorCategoryDao.update(shopCategory);

        if (!ok) {
            throw new ServiceException("unbind.shop.category.fail");
        }

        if (log.isDebugEnabled()) {
            log.debug("successfully to update shop category for unbinding with: {}", shopCategory);
        }

        ok = operatorCategoryBindingDao.delete(bindId);

        if (!ok) {
            throw new ServiceException("unbind.fail");
        }

        if (log.isDebugEnabled()) {
            log.debug("successfully to delete shop category binding by id: {}", bindId);
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void create(OperatorCategory shopCategory, OperatorCategory parent) {
        operatorCategoryDao.create(shopCategory);
        operatorCategoryDao.update(parent);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteAndRemoveBinding(Long id, Integer tenantId, String updatedBy) {
        operatorCategoryDao.delete(id, tenantId, updatedBy);

        // 缓存失效
        OperatorCategory operatorCategory = new OperatorCategory();
        operatorCategory.setId(id);
        cacheOperatorCategoryById.operatorCategoryByIdCacheEvict(operatorCategory);

        operatorCategoryBindingDao.deleteByOperatorCategoryId(id);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void deleteAndRemoveBindingWithUpdateParent(OperatorCategory parent, Long id, Integer tenantId, String updatedBy) {
        operatorCategoryDao.update(parent);
        deleteAndRemoveBinding(id, tenantId, updatedBy);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateCategoryIsShow(Set<Long> categoryIsShowSet,Set<Long> categoryNoShowSet, Long operatorId) {

        if (!CollectionUtils.isEmpty(categoryIsShowSet)) {
            this.updateIsShow(categoryIsShowSet,operatorId,1);
        }
        if (!CollectionUtils.isEmpty(categoryNoShowSet)) {
            this.updateIsShow(categoryNoShowSet,operatorId,0);
        }
    }

    public void updateIsShow(Set<Long> categorySet,Long operatorId,Integer isShow){

        List<Long> categoryList = Lists.newArrayList(categorySet);
        int listSize = categoryList.size();
        int toIndex = 400;
        for(int i = 0; i < listSize; i += 400) {

            if (i + 400 > listSize) {
                toIndex = listSize - i;
            }
            List<Long> newList = categoryList.subList(i, i + toIndex);
            operatorCategoryDao.updateIsShow(newList, operatorId, isShow);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean updateCategoryIsShowCustom(Set<Long> categoryIsShowSet, Long operatorId, Integer isShow) {
        List<Long> categoryList = Lists.newArrayList(categoryIsShowSet);
        try {
            if (!CollectionUtils.isEmpty(categoryList)) {
                boolean b = operatorCategoryDao.updateIsShow(categoryList, operatorId, isShow);
                log.info("updateCategoryIsShowCustom:{}",b);
            }
        } catch (Exception e) {
            throw new ServiceException("update.operator.category.isShow.fail");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean initChannelCategory(List<BackCategory> backCategoryList, List<List<OperatorCategory>> createList) {
        //pid+名称分组
        Map<String, BackCategory> backCategoryMap = backCategoryList.stream().collect(Collectors.toMap(e -> e.getPid() + "_" + e.getName(), Function.identity(), (e1, e2) -> e1));
        //id分组
        Map<Long, BackCategory> backCategoryMapById = backCategoryList.stream().collect(Collectors.toMap(BackCategory::getId, Function.identity()));

        for (List<OperatorCategory> operatorCategoryCreateList : createList) {

            //创建渠道分类使ID字段有值
            operatorCategoryCreateList.forEach(operatorCategoryDao::create);

            Map<String, OperatorCategory> operatorCategoryMap = operatorCategoryCreateList.stream().collect(Collectors.toMap(e -> e.getPid() + "_" + e.getName(), Function.identity(), (e1, e2) -> e1));
            List<OperatorCategoryBinding> operatorCategoryBindingList = Lists.newArrayList();

            //遍历渠道分类，绑定父类目
            operatorCategoryCreateList.forEach(operatorCategory -> {
                BackCategory backCategory = backCategoryMap.get(operatorCategory.getPid() + "_" + operatorCategory.getName());
                if (backCategory != null) {
                    OperatorCategoryBinding operatorCategoryBinding = new OperatorCategoryBinding();
                    operatorCategoryBinding.setOperatorId(operatorCategory.getOperatorId());
                    operatorCategoryBinding.setOperatorCategoryId(operatorCategory.getId());
                    operatorCategoryBinding.setBackCategoryId(backCategory.getId());
                    operatorCategoryBinding.setUpdatedBy(operatorCategory.getUpdatedBy());
                    operatorCategoryBindingList.add(operatorCategoryBinding);
                    if (backCategory.getPid() < 1) {
                        operatorCategory.setPid(0L);
                    } else {
                        //获取当前渠道父类目
                        BackCategory parentBackCategory = backCategoryMapById.get(backCategory.getPid());
                        if (ObjectUtil.isEmpty(parentBackCategory)) {
                            operatorCategory.setPid(-2L);
                        } else {
                            OperatorCategory parentOperatorCategory = operatorCategoryMap.get(parentBackCategory.getPid() + "_" + parentBackCategory.getName());
                            if (parentOperatorCategory != null) {
                                operatorCategory.setPid(parentOperatorCategory.getId());
                            }
                        }
                    }
                }
            });
            operatorCategoryCreateList.forEach(operatorCategoryDao::update);
            operatorCategoryBindingList.forEach(operatorCategoryBindingDao::create);
        }
        return Boolean.TRUE;
    }
}
