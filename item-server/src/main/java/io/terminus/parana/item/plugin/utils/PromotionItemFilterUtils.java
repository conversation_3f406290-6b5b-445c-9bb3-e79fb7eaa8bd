package io.terminus.parana.item.plugin.utils;

import com.google.common.base.MoreObjects;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import io.terminus.common.utils.Splitters;
import io.terminus.parana.item.search.domain.ActivityDO;
import io.terminus.parana.item.search.domain.SkuPriceDO;
import io.terminus.parana.promotion.api.constants.PromotionProductCodes;
import io.terminus.parana.promotion.api.enums.DiscountType;
import io.terminus.parana.promotion.api.enums.FeeType;
import io.terminus.parana.promotion.api.enums.PartType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 搜商品活动透出逻辑
 *
 * <AUTHOR>
 * @Date 2020-05-15
 **/
@Component
public class PromotionItemFilterUtils {

    private final static Set<String> SIMPLE_ACTIVITY_CODES = ImmutableSet.<String>builder()
            .add(PromotionProductCodes.SIMPLE)
            .add(PromotionProductCodes.PRESELL)
            .add(PromotionProductCodes.SECKILL)
            .build();
    
    /**
     * 商品参加的活动排序
     * 活动选取过程:
     * 1.单品活动放在最前面,命中优惠价最低的一个活动,优惠价相同时,创建时间早的优先(只命中一个活动)
     * 2.其它类型的活动(运费优惠除外),扣减类型相同的取最优,扣减类型不同或优惠力度相同的取创建时间早的
     * 3.运费活动取优惠力度最大的,优惠力度相同时,创建时间早的优先(只命中一个活动)
     *
     * @param limitCount 限制每个商品做多参与的活动数量
     * @param extra      扩展字段  目前逻辑可设置的值为终端类型以及业务来源
     */
    public static List<ActivityDO> parseActivityForItem(List<ActivityDO> activities, Map<Long, SkuPriceDO> skuPrices, List<String> codes, Integer limitCount, Map<String, String> extra) {
        if (CollectionUtils.isEmpty(activities)) {
            return activities;
        }
        Map<String, String> map = MoreObjects.firstNonNull(extra, Collections.EMPTY_MAP);
    
    
        //最终命中的活动
        List<ActivityDO> newActivities = Lists.newArrayListWithCapacity(activities.size());
        //计算活动价格，以及活动显示过滤处理
        if (!CollectionUtils.isEmpty(activities)) {
            //单品sku费用类型活动
            List<ActivityDO> simpleSkuFeeActivities = Lists.newArrayListWithCapacity(activities.size());
            //其它类型sku费用活动
            List<ActivityDO> otherSkuFeeActivities = Lists.newArrayListWithCapacity(activities.size());
            //运费活动
            List<ActivityDO> shippingFeeActivities = Lists.newArrayListWithCapacity(activities.size());
            activities.stream().forEach(activityDO -> {
                if (!codes.contains(activityDO.getCode()) || !isDisplayable(activityDO, map.get("businessType"), map.get("deviceType"))) {
                    return;
                }
                if (SIMPLE_ACTIVITY_CODES.contains(activityDO.getCode()) && FeeType.SKU_FEE.resolve().equals(activityDO.getFeeType())) {
                    simpleSkuFeeActivities.add(activityDO);
                } else if (PromotionProductCodes.TRANSPORT_FEE.equals(activityDO.getCode())) {
                    shippingFeeActivities.add(activityDO);
                } else {
                    otherSkuFeeActivities.add(activityDO);
                }
            });
            //获取最优的单品活动
            //最终命中的单品活动
            ActivityDO simpleActivity = getBestOne(skuPrices, simpleSkuFeeActivities);
            if (simpleActivity != null) {
                newActivities.add(simpleActivity);
            }
            //其它活动排序
            otherSkuFeeActivities.sort((o1, o2) -> {
                if (o1.getDiscountType() != null && o1.getDiscountType().equals(o2.getDiscountType())) {
                    int compare;
                    if (o1.getDiscountType().equals(DiscountType.DEDUCTION.resolve())) {
                        compare = o2.getDiscountValue().compareTo(o1.getDiscountValue());
                    } else {
                        compare = o1.getDiscountValue().compareTo(o2.getDiscountValue());
                    }
                    return compare == 0 ? o1.getCreatedAt().compareTo(o2.getCreatedAt()) : compare;
                }
                return o1.getCreatedAt().compareTo(o2.getCreatedAt());
            });
            newActivities.addAll(otherSkuFeeActivities);
    
            //获取最优的运费活动
            shippingFeeActivities.sort((o1, o2) -> {
                int compare = o2.getDiscountValue().compareTo(o1.getDiscountValue());
                return compare == 0 ? o1.getCreatedAt().compareTo(o2.getCreatedAt()) : compare;
            });
            if (!CollectionUtils.isEmpty(shippingFeeActivities)) {
                newActivities.add(shippingFeeActivities.get(0));
            }
            if (limitCount != null && newActivities.size() > limitCount) {
                newActivities = newActivities.subList(0, limitCount);
            }
        }
        return newActivities;
    }
    
    /**
     * 【simple之间】【presell之间】按照创建时间取最优
     *
     * @param simpleSkuFeeActivities
     * @return
     */
    private static List<ActivityDO> sortSimpleActivities(List<ActivityDO> simpleSkuFeeActivities) {
        if (CollectionUtils.isEmpty(simpleSkuFeeActivities)) {
            return simpleSkuFeeActivities;
        }
        simpleSkuFeeActivities.sort(((o1, o2) -> {
            if (o1.getCreatedAt() > o2.getCreatedAt()) {
                return -1;
            } else if (o1.getCreatedAt() < o2.getCreatedAt()) {
                return 1;
            } else {
                return 0;
            }
        }));
        return simpleSkuFeeActivities;
    }
    
    private static ActivityDO getBestOne(Map<Long, SkuPriceDO> skuPrices, List<ActivityDO> simpleSkuFeeActivities) {
        if (!CollectionUtils.isEmpty(simpleSkuFeeActivities)) {
            List<ActivityDO> filterActivityDOs = Lists.newArrayList();
            Iterator<ActivityDO> iterator = simpleSkuFeeActivities.iterator();
            ActivityDO activityDO;
            List<ActivityDO> simpleActivityList = Lists.newArrayList();
            List<ActivityDO> presellActivityList = Lists.newArrayList();
            while (iterator.hasNext()) {
                activityDO = iterator.next();
                if (Objects.equals(activityDO.getCode(), PromotionProductCodes.SIMPLE)) {
                    simpleActivityList.add(activityDO);
                } else if (Objects.equals(activityDO.getCode(), PromotionProductCodes.PRESELL)) {
                    presellActivityList.add(activityDO);
                } else {
                    filterActivityDOs.add(activityDO);
                }
            }
            if (!CollectionUtils.isEmpty(simpleActivityList)) {
                simpleActivityList = sortSimpleActivities(simpleActivityList);
                filterActivityDOs.add(simpleActivityList.get(0));
            }
            if (!CollectionUtils.isEmpty(presellActivityList)) {
                presellActivityList = sortSimpleActivities(presellActivityList);
                filterActivityDOs.add(presellActivityList.get(0));
            }
            simpleSkuFeeActivities = filterActivityDOs;
        }
    
        //最终命中的单品活动
        ActivityDO simpleActivity = null;
        Long skuPrice = 0L;
        for (ActivityDO activity : simpleSkuFeeActivities) {
            SkuPriceDO lowSkuPrice;
            if (!CollectionUtils.isEmpty(skuPrices) && !PartType.SKU_ID.getKey().equals(activity.getEffectType())) {
                lowSkuPrice = getLowSkuPrice(skuPrices);
            } else {
                lowSkuPrice = skuPrices.get(activity.getEffectValue());
            }
            if (lowSkuPrice == null) {
                continue;
            }
            calculateSimpleActivity(lowSkuPrice, activity);
            if (simpleActivity == null) {
                skuPrice = lowSkuPrice.getPrice();
                simpleActivity = activity;
            }
            if (!isDuringWarm(simpleActivity) && isDuringWarm(activity)) {
                continue;
            }
            if (isDuringWarm(simpleActivity) && !isDuringWarm(activity)) {
                skuPrice = lowSkuPrice.getPrice();
                simpleActivity = activity;
            }
            if (simpleActivity.getDiscountPrice() > activity.getDiscountPrice()
                    || (simpleActivity.getDiscountPrice().equals(activity.getDiscountPrice()) && simpleActivity.getCreatedAt() > activity.getCreatedAt())) {
                skuPrice = lowSkuPrice.getPrice();
                simpleActivity = activity;
            }
        }
        if (simpleActivity != null && isDuringWarm(simpleActivity)) {
            simpleActivity.setDiscountPrice(skuPrice);
        }
        return simpleActivity;
    }
    
    /**
     * 单品活动计算优惠价
     *
     * @param lowSkuPrice
     * @param activity
     */
    private static void calculateSimpleActivity(SkuPriceDO lowSkuPrice, ActivityDO activity) {
        if (activity.getDiscountType() == null) {
            activity.setDiscountPrice(lowSkuPrice.getPrice());
        } else {
            switch (DiscountType.resolve(activity.getDiscountType())) {
                case DEDUCTION:
                    activity.setDiscountPrice(lowSkuPrice.getPrice() - activity.getDiscountValue());
                    break;
                case DISCOUNT:
                    activity.setDiscountPrice(lowSkuPrice.getPrice() * activity.getDiscountValue() / 100L);
                    break;
                case FIXED_PRICE:
                    activity.setDiscountPrice(activity.getDiscountValue());
                    break;
                default:
                    activity.setDiscountPrice(lowSkuPrice.getPrice());
            }
        }
        activity.setOriginalPrice(lowSkuPrice.getOriginalPrice());
    }
    
    private static SkuPriceDO getLowSkuPrice(Map<Long, SkuPriceDO> skuPrices) {
        SkuPriceDO lowSkuPrice = null;
        for (Map.Entry<Long, SkuPriceDO> entry : skuPrices.entrySet()) {
            SkuPriceDO skuPrice = entry.getValue();
            if (null == skuPrice) {
                continue;
            }
            if (null == lowSkuPrice || lowSkuPrice.getPrice().compareTo(skuPrice.getPrice()) > 0) {
                lowSkuPrice = skuPrice;
            }
        }
        return lowSkuPrice;
    }
    
    
    public static List<ActivityDO> filterActivities(List<ActivityDO> activities, ActivityDO newActivity) {
        if (activities == null) {
            activities = Lists.newArrayList();
        }
        List<ActivityDO> result = activities.stream().filter(activity -> !PromotionItemFilterUtils.isExpired(activity)).collect(Collectors.toList());
        if (newActivity != null) {
            Map<Long, ActivityDO> id2ActivityDo = result.stream().collect(Collectors.toMap(ActivityDO::getId, activityDO -> activityDO));
            ActivityDO activityDO = id2ActivityDo.get(newActivity.getId());
            if (activityDO != null) {
                result.remove(activityDO);
            }
            if (!isExpired(newActivity)) {
                result.add(newActivity);
            }
        }
        return result;
    }
    
    private static Boolean isValid(ActivityDO activityDO) {
        if (activityDO.getStatus() != 1) {
            return Boolean.FALSE;
        }
        long time = System.currentTimeMillis();
        if (activityDO.getWarmStartAt() != null) {
            return time > activityDO.getWarmStartAt() && time < activityDO.getExpiredAt();
        } else {
            return time > activityDO.getStartAt() && time < activityDO.getExpiredAt();
        }
    }
    
    private static Boolean isExpired(ActivityDO activityDO) {
        if (activityDO.getStatus() != 1) {
            return Boolean.TRUE;
        }
        long time = System.currentTimeMillis();
        return time > activityDO.getExpiredAt();
    
    }
    
    private static Boolean isMeetDeviceType(ActivityDO activityDO, String deviceType) {
        Map<String, String> extra = activityDO.getExtra();
        if (StringUtils.isEmpty(deviceType) || CollectionUtils.isEmpty(extra)) {
            return Boolean.TRUE;
        }
        List<String> deviceTypes = Splitters.COMMA.splitToList(MoreObjects.firstNonNull(extra.get("deviceType"), ""));
        return deviceTypes.contains(deviceType);
    }
    
    private static Boolean isMeetBusinessType(ActivityDO activityDO, String businessType) {
        Map<String, String> extra = activityDO.getExtra();
    
        if (StringUtils.isEmpty(businessType) || CollectionUtils.isEmpty(extra)) {
            return Boolean.TRUE;
        }
        List<String> businessTypes = Splitters.COMMA.splitToList(MoreObjects.firstNonNull(extra.get("businessType"), ""));
        return businessTypes.contains(businessType);
    }
    
    private static Boolean isDuringWarm(ActivityDO activityDO) {
        if (activityDO.getWarmStartAt() == null) {
            return false;
        }
        Date now = new Date();
        return now.getTime() > activityDO.getWarmStartAt() && now.getTime() < activityDO.getStartAt();
    
    }
    
    private static Boolean isDisplayable(ActivityDO activityDO, String businessType, String deviceType) {
        return isValid(activityDO) && isMeetBusinessType(activityDO, businessType) && isMeetDeviceType(activityDO, deviceType);
    }

}
