package io.terminus.parana.item.enhance.component.promotion.deal;

import io.terminus.parana.item.enhance.api.bean.response.DynamicRenderInfo;
import io.terminus.parana.item.enhance.api.bean.response.param.ActivityResultVO;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.third.info.ActivityMatchedLine;
import io.terminus.parana.item.third.info.ActivityResult;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2018-12-18
 */
@Component
public class GiftDeal extends AbstractPromotionDeal {

    private static final String ACTIVITY_FREE_GIFT = "freeGift";

    @Override
    public String getCode() {
        return ACTIVITY_FREE_GIFT;
    }

    @Override
    public boolean handle(DynamicRenderInfo vo,
                          SkuInfo selectedSku,
                          List<ActivityResult> resultInfoList,
                          List<ActivityMatchedLine> lineInfoList,
                          List<ActivityMatchedLine> shopInfoList) {
        if (shopInfoList == null) {
            return false;
        }

        List<ActivityResultVO> voList = convertActivity(shopInfoList);
        voList.forEach(it -> vo.addActivity(ACTIVITY_FREE_GIFT, it));

        return true;
    }
}
