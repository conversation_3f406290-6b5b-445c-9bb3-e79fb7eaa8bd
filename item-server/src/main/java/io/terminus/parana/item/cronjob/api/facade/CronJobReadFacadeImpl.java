package io.terminus.parana.item.cronjob.api.facade;

import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.common.converter.GeneralConverter;
import io.terminus.parana.item.cronjob.api.bean.dto.CronJobDTO;
import io.terminus.parana.item.cronjob.api.bean.request.*;
import io.terminus.parana.item.cronjob.api.convert.CronJobConverter;
import io.terminus.parana.item.cronjob.model.CronJob;
import io.terminus.parana.item.cronjob.service.CronJobReadDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CronJobReadFacadeImpl implements CronJobReadFacade {

    @Autowired
    private CronJobReadDomainService cronJobReadDomainService;
    @Autowired
    private CronJobConverter cronJobConverter;

    @Override
    public Response<List<CronJobDTO>> scanJobList(CronJobScanRequest request) {
        try {
            List<CronJob> cronJobs = cronJobReadDomainService.scanJobList(
                    request.getTenantId(), request.getStatus(),
                    request.getDelay(), request.getDelayUnit(), request.getTypeList());
            return Response.ok(GeneralConverter.batchConvert(cronJobs, cronJobConverter::domain2dto));
        } catch (Exception e) {
            log.error("cron job scan failed, param:{}, cause:{}", request, e);
            return Response.fail("cron.job.scan.failed", "定时任务扫描失败");
        }
    }

    @Override
    public Response<Paging<CronJobDTO>> scanJobPaging(CronJobScanPagingRequest request) {
        try {
            Paging<CronJob> cronJobs = cronJobReadDomainService.scanJobPaging(
                    request.getTenantId(), request.getStatus(),
                    request.getDelay(), request.getDelayUnit(), request.getTypeList(),
                    request.getPageNo(), request.getPageSize());
            return Response.ok(GeneralConverter.batchConvert(cronJobs, cronJobConverter::domain2dto));
        } catch (Exception e) {
            log.error("cron job scan paging failed, param:{}, cause:{}", request, e);
            return Response.fail("cron.job.scan.paging.failed", "定时任务分页扫描失败");
        }
    }

    @Override
    public Response<CronJobDTO> queryById(CronJobQueryByIdRequest request) {
        try {
            CronJob cronJob =
                    cronJobReadDomainService.findById(request.getTenantId(), request.getId());
            return Response.ok(cronJobConverter.domain2dto(cronJob));
        } catch (Exception e) {
            log.error("cron job query failed, param:{}, cause:{}", request, e);
            return Response.fail("cron.job.query.failed", "定时任务查询失败");
        }
    }

    @Override
    public Response<List<CronJobDTO>> queryByIds(CronJobQueryByIdsRequest request) {
        try {
            List<CronJob> cronJobs =
                    cronJobReadDomainService.findByIds(request.getTenantId(), Lists.newArrayList(request.getIds()));
            return Response.ok(GeneralConverter.batchConvert(cronJobs, cronJobConverter::domain2dto));
        } catch (Exception e) {
            log.error("cron job query failed, param:{}, cause:{}", request, e);
            return Response.fail("cron.job.query.failed", "定时任务查询失败");
        }
    }

    @Override
    public Response<List<CronJobDTO>> queryByTargetCodes(CronJobQueryByTargetCodeRequest request) {
        try {
            List<CronJob> cronJobs = cronJobReadDomainService.findByTargetCodes(
                    request.getTenantId(),
                    request.getTargetCodeList(),
                    request.getTypeList(),
                    request.getStatus());
            return Response.ok(GeneralConverter.batchConvert(cronJobs, cronJobConverter::domain2dto));
        } catch (Exception e) {
            log.error("cron job query failed, param:{}, cause:{}", request, e);
            return Response.fail("cron.job.query.failed", "定时任务查询失败");
        }
    }
}
