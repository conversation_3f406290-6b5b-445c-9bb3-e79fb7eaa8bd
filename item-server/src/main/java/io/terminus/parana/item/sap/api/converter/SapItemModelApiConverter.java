package io.terminus.parana.item.sap.api.converter;

import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.sap.api.bean.response.FullSapItemInfo;
import io.terminus.parana.item.sap.api.bean.response.info.*;
import io.terminus.parana.item.sap.model.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class SapItemModelApiConverter {

    public FullSapItemInfo domain2info(FullSapItemsModelBO fullSapItemsModelBO){

        Item item=fullSapItemsModelBO.getItem();
        if(item==null)item=new Item();
        Sku sku=fullSapItemsModelBO.getSku();
        if(sku==null)sku=new Sku();
        SapSku sapSku=fullSapItemsModelBO.getSapSku();
        if(sapSku==null)sapSku=new SapSku();
        SkuYL skuYL=fullSapItemsModelBO.getSkuYL();
        if(skuYL==null)skuYL=new SkuYL();
        List<SapSkuM> sapSkuMList=fullSapItemsModelBO.getSapSkuMList();
        if(sapSkuMList==null)sapSkuMList=new ArrayList<SapSkuM>();
        List<SapSkuS> sapSkuSList=fullSapItemsModelBO.getSapSkuSList();
        if(sapSkuSList==null)sapSkuSList=new ArrayList<SapSkuS>();
        List<SapSkuC> sapSkuCList=fullSapItemsModelBO.getSapSkuCList();
        if(sapSkuCList==null)sapSkuCList=new ArrayList<SapSkuC>();
        List<ItemUnit> itemUnitList=fullSapItemsModelBO.getItemUnitList();
        if(itemUnitList==null)itemUnitList=new ArrayList<ItemUnit>();

        FullSapItemInfo fullSapItemInfo=new FullSapItemInfo();

        //sku
        SkuSapInfo skuSapInfo =new SkuSapInfo();
        skuSapInfo.setId( sku.getId() );
        skuSapInfo.setItemId( sku.getItemId() );
        skuSapInfo.setSapId( sku.getSapId() );
        skuSapInfo.setType( sku.getType() );
        skuSapInfo.setName( sku.getName() );
        skuSapInfo.setSdPartno( sku.getSdPartno() );
        skuSapInfo.setStatus( sku.getStatus() );
        skuSapInfo.setNw( sku.getNw() );
        skuSapInfo.setGw( sku.getGw() );
        skuSapInfo.setIsbarcode( sku.getIsbarcode() );
        skuSapInfo.setBarcode( sku.getBarcode() );
        skuSapInfo.setSkuCode( sku.getSkuCode() );
        skuSapInfo.setBarcodem( sku.getBarcodem() );
        skuSapInfo.setBarcodev( sku.getBarcodev() );
        skuSapInfo.setShortcut( sku.getShortcut() );
        skuSapInfo.setPnamekey( sku.getPnamekey() );
        skuSapInfo.setPtype( sku.getPtype() );
        skuSapInfo.setPmodel( sku.getPmodel() );
        skuSapInfo.setTaste( sku.getTaste() );
        skuSapInfo.setColour( sku.getColour() );
        skuSapInfo.setGoodssize( sku.getGoodssize() );
        skuSapInfo.setCommoditymodel( sku.getCommoditymodel() );
        skuSapInfo.setClassify( sku.getClassify() );
        skuSapInfo.setMadein( sku.getMadein() );
        skuSapInfo.setSeason( sku.getSeason() );
        skuSapInfo.setZcsfl( sku.getZcsfl() );
        skuSapInfo.setOriginalPrice( sku.getOriginalPrice() );
        skuSapInfo.setCbm( sku.getCbm() );
        skuSapInfo.setOverday( sku.getOverday() );
        skuSapInfo.setFreshday( sku.getFreshday() );
        skuSapInfo.setIslogno( sku.getIslogno() );
        skuSapInfo.setIssn( sku.getIssn() );
        skuSapInfo.setIsmedicalgoods( sku.getIsmedicalgoods() );
        skuSapInfo.setSourcetype( sku.getSourcetype() );
        skuSapInfo.setSourceid( sku.getSourceid());

        skuSapInfo.setWunit(sku.getWunit());

        skuSapInfo.setUnit(item.getUnit());
        skuSapInfo.setBrandId(item.getBrandId());
        skuSapInfo.setBrandName(item.getBrandName());
        skuSapInfo.setIndusty(item.getIndusty());
        skuSapInfo.setIndustyLevel2(item.getIndustyLevel2());
        skuSapInfo.setManufacturer(item.getManufacturer());
        skuSapInfo.setManufacturerName(item.getManufacturerName());
        skuSapInfo.setTaxcode(item.getTaxcode());
        skuSapInfo.setVatrate(item.getVatrate());
        skuSapInfo.setCategoryCode(item.getCategoryCode());

        skuSapInfo.setMstae(sku.getOutstatus());
        skuSapInfo.setRemarks(sku.getRemarks());
        skuSapInfo.setSdptype(sku.getSdptype());
        skuSapInfo.setPrate(sku.getPrate());
        skuSapInfo.setPunitm( sku.getPunitm() );

        skuSapInfo.setLtd(sapSku.getLtd());
        skuSapInfo.setLtdcode(sapSku.getLtdcode());

        //skuYL
        SkuYLSapInfo skuYLSapInfo=new SkuYLSapInfo();
        skuYLSapInfo.setId(skuYL.getId());
        skuYLSapInfo.setTenantId(skuYL.getTenantId());
        skuYLSapInfo.setItemId(skuYL.getItemId());
        skuYLSapInfo.setSkuId(skuYL.getSkuId());
        skuYLSapInfo.setFirstOperation(skuYL.getFirstOperation());
        skuYLSapInfo.setGeneralName(skuYL.getGeneralName());
        skuYLSapInfo.setSecurityclassify(skuYL.getSecurityclassify());
        skuYLSapInfo.setPdosagefrom(skuYL.getPdosagefrom());
        skuYLSapInfo.setUsageclassify(skuYL.getUsageclassify());
        skuYLSapInfo.setPenterprisecno(skuYL.getPenterprisecno());
        skuYLSapInfo.setPenterprisecname(skuYL.getPenterprisecname());
        skuYLSapInfo.setWtpenterprisecno(skuYL.getWtpenterprisecno());
        skuYLSapInfo.setWtpenterprisecname(skuYL.getWtpenterprisecname());
        skuYLSapInfo.setOnallowholder(skuYL.getOnallowholder());
        skuYLSapInfo.setOnallowholderaddr(skuYL.getOnallowholderaddr());
        skuYLSapInfo.setApprovaltype(skuYL.getApprovaltype());
        skuYLSapInfo.setApprovalno(skuYL.getApprovalno());
        skuYLSapInfo.setValiditydays(skuYL.getValiditydays());
        skuYLSapInfo.setStoragecondition(skuYL.getStoragecondition());
        skuYLSapInfo.setIstemperacontrol(skuYL.getIstemperacontrol());
        skuYLSapInfo.setPrinttype(skuYL.getPrinttype());
        skuYLSapInfo.setDrugquality(skuYL.getDrugquality());
        skuYLSapInfo.setFilearchive(skuYL.getFilearchive());

        //skuMInfos
        List<SkuMSapInfo> skuMInfos=new ArrayList<SkuMSapInfo>();
        for(SapSkuM  sapSkuM :sapSkuMList){
            SkuMSapInfo skuMSapInfo=new SkuMSapInfo();
            skuMSapInfo.setId( sapSkuM.getId() );
            skuMSapInfo.setSapId( sapSkuM.getSapId() );
            skuMSapInfo.setSkuId( sapSkuM.getSkuId() );
            skuMSapInfo.setWerksid( sapSkuM.getWerksid() );
            skuMSapInfo.setWerkscode( sapSkuM.getWerkscode() );
            skuMSapInfo.setEkgrpid( sapSkuM.getEkgrpid() );
            skuMSapInfo.setEkgrpcode( sapSkuM.getEkgrpcode() );
            skuMSapInfo.setBstme( sapSkuM.getBstme() );
            skuMSapInfo.setUmren( sapSkuM.getUmren() );
            skuMSapInfo.setUmrez( sapSkuM.getUmrez() );
            skuMSapInfo.setVabme( sapSkuM.getVabme() );
            skuMSapInfo.setMmsta( sapSkuM.getMmsta() );
            skuMSapInfo.setBstma( sapSkuM.getBstma() );
            skuMSapInfo.setXchpf( sapSkuM.getXchpf() );
            skuMSapInfo.setSernp( sapSkuM.getSernp() );
            skuMSapInfo.setBstmi( sapSkuM.getBstmi() );
            skuMSapInfo.setZdzcts( sapSkuM.getZdzcts() );
            skuMSapInfo.setMaabc( sapSkuM.getMaabc() );
            skuMSapInfo.setZgjz( sapSkuM.getZgjz() );
            skuMSapInfo.setZglbm( sapSkuM.getZglbm() );
            skuMSapInfo.setLgort( sapSkuM.getLgort() );
            skuMSapInfo.setZmrcw( sapSkuM.getZmrcw() );
            skuMSapInfo.setZmdm001( sapSkuM.getZmdm001() );
            skuMSapInfo.setApprovalenddate( sapSkuM.getApprovalenddate() );
            skuMSapInfo.setMaintain( sapSkuM.getMaintain() );
            skuMSapInfo.setMaintainperiod( sapSkuM.getMaintainperiod() );
            skuMSapInfo.setMaturitywarnday( sapSkuM.getMaturitywarnday() );
            skuMSapInfo.setNeareffectlockday( sapSkuM.getNeareffectlockday() );
            skuMSapInfo.setBigcode( sapSkuM.getBigcode() );
            skuMSapInfo.setBwtar( sapSkuM.getBwtar() );
            skuMSapInfo.setBklas( sapSkuM.getBklas() );
            skuMSapInfo.setMlmaa( sapSkuM.getMlmaa() );
            skuMSapInfo.setMlast( sapSkuM.getMlast() );
            skuMSapInfo.setVprsv( sapSkuM.getVprsv() );
            skuMSapInfo.setPeinh( sapSkuM.getPeinh() );
            skuMSapInfo.setNcost( sapSkuM.getNcost() );
            skuMSapInfo.setEkalr( sapSkuM.getEkalr() );
            skuMSapInfo.setHkmat( sapSkuM.getHkmat() );
            skuMSapInfo.setZccqy( sapSkuM.getZccqy() );
            skuMSapInfo.setAwsls( sapSkuM.getAwsls() );
            skuMSapInfo.setLosgr( sapSkuM.getLosgr() );
            skuMSapInfo.setPrice(sapSkuM.getPrice());
            skuMSapInfo.setRemarks(sapSkuM.getRemarks());
            skuMSapInfo.setStatus(sapSkuM.getStatus());
            skuMInfos.add(skuMSapInfo);
        }

        //skuSInfos
        List<SkuSSapInfo> skuSInfos=new ArrayList<SkuSSapInfo>();
        for(SapSkuS  sapSkuS: sapSkuSList){
            SkuSSapInfo skuSSapInfo=new SkuSSapInfo();
            skuSSapInfo.setId( sapSkuS.getId() );
            skuSSapInfo.setSapId( sapSkuS.getSapId() );
            skuSSapInfo.setItemId( sapSkuS.getItemId() );
            skuSSapInfo.setSkuId( sapSkuS.getSkuId() );
            skuSSapInfo.setVkorgid( sapSkuS.getVkorgid() );
            skuSSapInfo.setVkorgcode( sapSkuS.getVkorgcode() );
            skuSSapInfo.setVtwegid( sapSkuS.getVtwegid() );
            skuSSapInfo.setVtwegcode( sapSkuS.getVtwegcode() );
            skuSSapInfo.setVrkme( sapSkuS.getVrkme() );
            skuSSapInfo.setUmrenvrkme( sapSkuS.getUmrenvrkme() );
            skuSSapInfo.setUmrezvrkme( sapSkuS.getUmrezvrkme() );
            skuSSapInfo.setVavme( sapSkuS.getVavme() );
            skuSSapInfo.setVmsta( sapSkuS.getVmsta() );
            skuSSapInfo.setDwerk( sapSkuS.getDwerk() );
            skuSSapInfo.setSktof( sapSkuS.getSktof() );
            skuSSapInfo.setTatyp( sapSkuS.getTatyp() );
            skuSSapInfo.setTaxkm( sapSkuS.getTaxkm() );
            skuSSapInfo.setVersg( sapSkuS.getVersg() );
            skuSSapInfo.setKondm( sapSkuS.getKondm() );
            skuSSapInfo.setMtposnal( sapSkuS.getMtposnal() );
            skuSSapInfo.setMtpos( sapSkuS.getMtpos() );
            skuSSapInfo.setKtgrm( sapSkuS.getKtgrm() );
            skuSSapInfo.setTragr( sapSkuS.getTragr() );
            skuSSapInfo.setLadgr( sapSkuS.getLadgr() );
            skuSSapInfo.setPtype2(sapSkuS.getPtype2());
            skuSSapInfo.setPtype3(sapSkuS.getPtype3());
            skuSSapInfo.setStatus(sapSkuS.getStatus());
            skuSInfos.add(skuSSapInfo);
        }

        //skuSInfos
        List<SkuCSapInfo> skuCInfos=new ArrayList<SkuCSapInfo>();
        for(SapSkuC  sapSkuC: sapSkuCList){
            SkuCSapInfo skuCSapInfo=new SkuCSapInfo();
            skuCSapInfo.setBarcode(sapSkuC.getBarcode());
            skuCSapInfo.setName(sapSkuC.getName());
            skuCSapInfo.setSapId(sapSkuC.getSapId());
            skuCSapInfo.setCcode(sapSkuC.getCcode());
            skuCSapInfo.setId(sapSkuC.getId());
            skuCSapInfo.setItemId(sapSkuC.getItemId());
            skuCSapInfo.setSapItemId(sapSkuC.getSapItemId());
            skuCSapInfo.setSkuId(sapSkuC.getSkuId());
            skuCSapInfo.setStatus(sapSkuC.getStatus());
            skuCSapInfo.setSkucode(sapSkuC.getSkucode());
            skuCSapInfo.setCreatedBy(sapSkuC.getCreatedBy());
            skuCSapInfo.setSynsapsts(sapSkuC.getSynsapsts());
            skuCSapInfo.setSynsapresult(sapSkuC.getSynsapresult());
            skuCSapInfo.setSynwmssts(sapSkuC.getSynwmssts());
            skuCSapInfo.setSynwmsresult(sapSkuC.getSynwmsresult());
            skuCInfos.add(skuCSapInfo);
        }

        //skuUnitInfos
        List<SkuUnitInfo> skuUnitInfos=new ArrayList<SkuUnitInfo>();
        for (ItemUnit itemUnit:itemUnitList){
            SkuUnitInfo skuUnitInfo = new SkuUnitInfo();
            skuUnitInfo.setId(itemUnit.getId());
            skuUnitInfo.setMatnr( itemUnit.getMatnr() );
            skuUnitInfo.setMeinh( itemUnit.getMeinh() );
            skuUnitInfo.setUmren( itemUnit.getUmren() );
            skuUnitInfo.setUmrez( itemUnit.getUmrez() );
            skuUnitInfos.add(skuUnitInfo);
        }

        fullSapItemInfo.setSkuInfo(skuSapInfo);
        fullSapItemInfo.setSkuYLInfo(skuYLSapInfo);
        fullSapItemInfo.setSkuMInfos(skuMInfos);
        fullSapItemInfo.setSkuSInfos(skuSInfos);
        fullSapItemInfo.setSkuUnitInfos(skuUnitInfos);
        fullSapItemInfo.setSkuCInfos(skuCInfos);

        return fullSapItemInfo;
    }

}