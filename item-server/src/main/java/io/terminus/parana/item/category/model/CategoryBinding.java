package io.terminus.parana.item.category.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2018-07-28 11:39:28
 */
@Data
public class CategoryBinding implements Serializable {

    private static final long serialVersionUID = 160090798806822581L;
    /**
     * id
     */
    private Long id;
    /**
     * 前台叶子类目id
     */
    private Long frontCategoryId;
    /**
     * 后台叶子类目id
     */
    private Long backCategoryId;
    /**
     * createdAt
     */
    private Date createdAt;
    /**
     * updatedAt
     */
    private Date updatedAt;
}