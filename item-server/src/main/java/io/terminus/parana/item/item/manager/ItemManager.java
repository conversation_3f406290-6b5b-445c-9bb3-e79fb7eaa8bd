package io.terminus.parana.item.item.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.AssertUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.area.bo.AreaItemOperateBO;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.model.ItemAudit;
import io.terminus.parana.item.area.model.ItemStockInit;
import io.terminus.parana.item.area.repository.AreaItemDao;
import io.terminus.parana.item.area.repository.AreaSkuDao;
import io.terminus.parana.item.area.repository.ItemAuditDao;
import io.terminus.parana.item.attribute.manager.CategoryAttributeManager;
import io.terminus.parana.item.attribute.model.CategoryAttributeBinding;
import io.terminus.parana.item.brand.model.Brand;
import io.terminus.parana.item.brand.repository.BrandDAO;
import io.terminus.parana.item.category.api.converter.SaleAttributeApprovalApiConverter;
import io.terminus.parana.item.category.api.facade.SaleAttributeApprovalWriteFacade;
import io.terminus.parana.item.category.model.SaleAttributeApprovalModel;
import io.terminus.parana.item.category.repository.SaleAttributeApprovalDao;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibItemDao;
import io.terminus.parana.item.common.base.IdVersionPairBO;
import io.terminus.parana.item.common.business.tag.core.BitTagProcessLogic;
import io.terminus.parana.item.common.extension.AbstractTransactionalExtensionDeal;
import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.api.bean.request.item.param.ItemImportImage;
import io.terminus.parana.item.item.api.bean.request.item.param.ItemImportImageFail;
import io.terminus.parana.item.item.bo.ItemImportImageBO;
import io.terminus.parana.item.item.canal.ESMQInjection;
import io.terminus.parana.item.item.common.ItemEventLogHelper;
import io.terminus.parana.item.item.enums.ItemDimensionSchemaType;
import io.terminus.parana.item.item.enums.ItemStatus;
import io.terminus.parana.item.item.enums.ItemZqPushlStatusEnum;
import io.terminus.parana.item.item.extension.ItemTransactionalExtension;
import io.terminus.parana.item.item.model.*;
import io.terminus.parana.item.item.repository.*;
import io.terminus.parana.item.partnership.bo.VendorPartnershipQueryBO;
import io.terminus.parana.item.partnership.enums.VendorPartnershipStatus;
import io.terminus.parana.item.partnership.model.VendorPartnership;
import io.terminus.parana.item.partnership.repository.VendorPartnershipDao;
import io.terminus.parana.item.plugin.third.api.inventory.api.InventoryWriteApi;
import io.terminus.parana.item.plugin.third.api.misc.api.ParanaThirdMessageWriteApi;
import io.terminus.parana.item.price.repository.ItemQuotedPriceDao;
import io.terminus.parana.item.relation.model.BaseSku;
import io.terminus.parana.item.relation.utils.GenerateHelper;
import io.terminus.parana.item.sap.model.SkuYL;
import io.terminus.parana.item.sap.repository.SkuYLDao;
import io.terminus.parana.item.third.param.ThirdInventoryAdjustParam;
import io.terminus.parana.item.third.param.ThirdInventoryAdjustRequest;
import io.terminus.parana.item.third.param.ThirdParanaThirdMessageBean;
import io.terminus.parana.item.third.param.ThirdParanaThirdMessageCreateRequest;
import io.terminus.parana.item.transcript.model.FullItemImportCreateBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-05-14
 */
@Slf4j
@Component
public class ItemManager extends AbstractTransactionalExtensionDeal {

    private final ItemDao itemDao;
    private final SkuDao skuDao;
    private final SkuYLDao skuYLDao;
    private final ItemDetailDao itemDetailDao;
    private final ItemDimensionDao itemDimensionDao;
    private final BitTagProcessLogic bitTagProcessLogic;
    private final GenerateHelper generateHelper;
    private final ItemUpdateLogDao itemUpdateLogDao;
    private final AreaItemDao areaItemDao;
    private final AreaSkuDao areaSkuDao;
    private final ItemEventLogHelper itemEventLogHelper;
    private final ItemAuditDao itemAuditDao;
    private final ItemQuotedPriceDao itemQuotedPriceDao;
    private final CategoryAttributeManager categoryAttributeManager;
    private final SaleAttributeApprovalApiConverter saleAttributeApprovalApiConverter;
    private final SaleAttributeApprovalDao saleAttributeApprovalDao;
    private final ChoiceLotLibItemDao choiceLotLibItemDao;

    private final ParanaThirdMessageWriteApi paranaThirdMessageWriteApi;

    private final SaleAttributeApprovalWriteFacade saleAttributeApprovalWriteFacade;

    @Autowired
    private BrandDAO brandDAO;

    @Autowired
    private ItemBrandDao itemBrandDao;

    @Autowired
    private VendorPartnershipDao vendorPartnershipDao;

    @Autowired
    private InventoryWriteApi inventoryWriteApi;

    @Autowired
    private VendorItemChannlRelationDao vendorItemChannlRelationDao;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private VendorItemChannlSkuRelationDao vendorItemChannlSkuRelationDao;

    @Autowired
    private ItemEventLogDao itemEventLogDao;

    @Autowired
    private VendorItemShipConfigDao vendorItemShipConfigDao;

    @Autowired(required = false)
    private ItemTransactionalExtension itemTransactionalExtension;

    public ItemManager(ItemDao itemDao, SkuDao skuDao, SkuYLDao skuYLDao, ItemDetailDao itemDetailDao,
                       ItemDimensionDao itemDimensionDao, BitTagProcessLogic bitTagProcessLogic,
                       GenerateHelper generateHelper, ItemUpdateLogDao itemUpdateLogDao, AreaItemDao areaItemDao, AreaSkuDao areaSkuDao,
                       ItemEventLogHelper itemEventLogHelper, ItemAuditDao itemAuditDao,
                       ItemQuotedPriceDao itemQuotedPriceDao, CategoryAttributeManager categoryAttributeManager,
                       SaleAttributeApprovalApiConverter saleAttributeApprovalApiConverter, SaleAttributeApprovalDao saleAttributeApprovalDao, ChoiceLotLibItemDao choiceLotLibItemDao, ParanaThirdMessageWriteApi paranaThirdMessageWriteApi, SaleAttributeApprovalWriteFacade saleAttributeApprovalWriteFacade) {
        this.itemDao = itemDao;
        this.skuDao = skuDao;
        this.skuYLDao = skuYLDao;
        this.itemDetailDao = itemDetailDao;
        this.itemDimensionDao = itemDimensionDao;
        this.bitTagProcessLogic = bitTagProcessLogic;
        this.generateHelper = generateHelper;
        this.itemUpdateLogDao = itemUpdateLogDao;
        this.areaItemDao = areaItemDao;
        this.areaSkuDao = areaSkuDao;
        this.itemEventLogHelper = itemEventLogHelper;
        this.itemAuditDao = itemAuditDao;
        this.itemQuotedPriceDao = itemQuotedPriceDao;
        this.categoryAttributeManager = categoryAttributeManager;
        this.saleAttributeApprovalApiConverter = saleAttributeApprovalApiConverter;
        this.saleAttributeApprovalDao = saleAttributeApprovalDao;
        this.choiceLotLibItemDao = choiceLotLibItemDao;
        this.paranaThirdMessageWriteApi = paranaThirdMessageWriteApi;
        this.saleAttributeApprovalWriteFacade = saleAttributeApprovalWriteFacade;
    }

    /**
     *导入修改提交db
     * @param bo
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchImportUpdateItem(FullItemOperateBO bo){
        boolean isOk = false;
        Item item = bo.getItem();
        List<Sku> toUpdateSkuList = bo.getToUpdateSkuList();
        List<AreaItem> updateAreaItemList = bo.getUpdateAreaItemList();
        List<AreaSku> updateAreaSkuList = bo.getUpdateAreaSkuList();
        ItemUpdateLog itemUpdateLog = bo.getItemUpdateLog();
        List<ItemAudit> baseAuditAdoptList = bo.getBaseAuditAdoptList();
        if(item != null){
            isOk = itemDao.update(item);
        }
        if(CollectionUtil.isNotEmpty(toUpdateSkuList)){
            for (Sku sku : toUpdateSkuList) {
                isOk = skuDao.update(sku);
            }
        }
        if(CollectionUtil.isNotEmpty(updateAreaItemList)){
            for (AreaItem areaItem : updateAreaItemList) {
                isOk = areaItemDao.update(areaItem);
            }
        }
        if(CollectionUtil.isNotEmpty(updateAreaSkuList)){
            for (AreaSku areaSku : updateAreaSkuList) {
                isOk = areaSkuDao.update(areaSku);
            }
        }
        if(itemUpdateLog != null){
            isOk = itemUpdateLogDao.create(itemUpdateLog);
        }
        if(CollectionUtil.isNotEmpty(baseAuditAdoptList)){
            for (ItemAudit itemAudit : baseAuditAdoptList) {
                isOk = itemAuditDao.create(itemAudit);
                ItemUpdateLog auditLog = BeanUtil.toBean(itemUpdateLog, ItemUpdateLog.class);
                auditLog.setAuditId(itemAudit.getId());
                isOk = itemUpdateLogDao.create(auditLog);
            }
        }
        return isOk;
    }

    /**
     *批量保存商品信息
     * @param fullItemImportCreateBO
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchImportCreateItem(FullItemImportCreateBO fullItemImportCreateBO,String createBy){
        log.info("batchImportCreateItem req::{}", JSONUtil.toJsonStr(fullItemImportCreateBO));
        List<Item> items = fullItemImportCreateBO.getAddItemList();
        List<Sku> skus = fullItemImportCreateBO.getSkuList();
        List<ItemDetail> details = fullItemImportCreateBO.getAddItemDetailList();
        List<ItemEventLog> itemEventLogs = fullItemImportCreateBO.getAddItemEventLogList();
        // PART 1: 创建item
        boolean isOk = false;
        if(CollectionUtil.isNotEmpty(items)){
            isOk= itemDao.creates(items,createBy) == items.size();
            Assert.isTrue(isOk, "item.create.fail");
        }
        // PART 2: 创建sku
        if(CollectionUtil.isNotEmpty(skus)){
            isOk= skuDao.creates(skus,createBy) == skus.size();
            Assert.isTrue(isOk, "sku.create.fail");
        }
        // PART 3: 创建itemDetail
        if(CollectionUtil.isNotEmpty(details)){
            isOk= itemDetailDao.creates(details) == details.size();
            Assert.isTrue(isOk, "itemDetail.create.fail");
        }
        // PART 4: 创建itemEventLog
        if(CollectionUtil.isNotEmpty(itemEventLogs)){
            isOk= itemEventLogDao.creates(itemEventLogs) == itemEventLogs.size();
            Assert.isTrue(isOk, "itemEventLog.create.fail");
        }

        log.info("batchImportCreateItem res::{}",isOk);
        return isOk;

    }

    /**
     * 保存商品信息 （整购B 导入商品需求）
     *
     * @param fullItemImportCreateBO
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateItem(FullItemImportCreateBO fullItemImportCreateBO) {

        // 前置扩展
        //beforeCreateExtension(bo);

        Item item = fullItemImportCreateBO.getItem();
        List<Sku> skuList = fullItemImportCreateBO.getSkuList();
        ItemDetail itemDetail = fullItemImportCreateBO.getItemDetail();

        Boolean isOk;

        // PART 1: 创建item对象
        isOk = itemDao.create(item);
        Assert.isTrue(isOk, "item.create.fail");

        if (log.isDebugEnabled()) {
            log.debug("[ITEM CREATE] Successfully(TRA) create item with: {}", item);
        }

        // PART 2: 创建sku对象集合
        for (Sku sku : skuList) {
            isOk = skuDao.create(sku);
            Assert.isTrue(isOk, "sku.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM CREATE] Successfully(TRA) create sku with: {}", sku);
            }
        }

        // PART 3: 创建itemDetail对象
        isOk = itemDetailDao.create(itemDetail);
        Assert.isTrue(isOk, "item.detail.create.fail");

        if (log.isDebugEnabled()) {
            log.debug("[ITEM CREATE] Successfully(TRA) create item detail with: {}", itemDetail);
        }

        // 后置扩展
        //onCreateExtension(bo);

        //记录商品创建动作
        isOk = itemEventLogHelper.itemCreateEventLog(item, item.getTenantId());
        Assert.isTrue(isOk, "商品创建日志记录失败！");

        return isOk;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Long create(FullItemOperateBO bo, Map<Integer, Object> paramMap) {
        // 前置扩展
        beforeCreateExtension(bo);

        Item item = bo.getItem();
        List<Sku> skuList = bo.getToCreateSkuList();
        SkuYL skuYL = bo.getSkuYL();
        ItemDetail itemDetail = bo.getItemDetail();

        Boolean isOk;

        // PART 1: 创建item对象
        isOk = itemDao.create(item);
        Assert.isTrue(isOk, "item.create.fail");

        if (log.isDebugEnabled()) {
            log.debug("[ITEM CREATE] Successfully(TRA) create item with: {}", item);
        }

        // PART 2: 创建sku对象集合
        for (Sku sku : skuList) {
            isOk = skuDao.create(sku);
            Assert.isTrue(isOk, "sku.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM CREATE] Successfully(TRA) create sku with: {}", sku);
            }
        }

        // PART 2.1: 创建医疗属性
        if (item.getIsmedicalgoods() != null
                && item.getIsmedicalgoods().equals("Y")) {
            isOk = skuYLDao.create(skuYL);
            Assert.isTrue(isOk, "skuYL.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM CREATE] Successfully(TRA) create skuYL with: {}", skuYL);
            }
        }

        // PART 3: 创建itemDetail对象
        isOk = itemDetailDao.create(itemDetail);
        Assert.isTrue(isOk, "item.detail.create.fail");

        if (log.isDebugEnabled()) {
            log.debug("[ITEM CREATE] Successfully(TRA) create item detail with: {}", itemDetail);
        }

        // PART 4: 写入维度扩展信息
        if (!CollectionUtils.isEmpty(bo.getItemDimensionList())) {
            Integer creates = itemDimensionDao.creates(bo.getItemDimensionList());

            Assert.isTrue(creates != null, "item.dimension.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM CREATE] Successfully(TRA) create item dimension with: {}", bo.getItemDimensionList());
            }
        }

        // 后置扩展
        onCreateExtension(bo);

        // 位标事务处理
        isOk = bitTagProcessLogic.transactional(paramMap);
        Assert.isTrue(isOk, "bit.tag.process.fail");

        //记录商品创建动作
        isOk = itemEventLogHelper.itemCreateEventLog(item, item.getTenantId());
        Assert.isTrue(isOk, "商品创建日志记录失败！");

        return item.getId();
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Long createPlus(FullItemOperateBO bo, Map<Integer, Object> paramMap, AreaItemOperateBO areaItemOperateBO) {
        // 前置扩展
        beforeCreateExtension(bo);

        Item item = bo.getItem();
        List<Sku> skuList = bo.getToCreateSkuList();
        SkuYL skuYL = bo.getSkuYL();
        ItemDetail itemDetail = bo.getItemDetail();

        Boolean isOk;

        // PART 1: 创建item对象
        isOk = itemDao.create(item);
        Assert.isTrue(isOk, "item.create.fail");

        // PART 2: 创建sku对象集合
        for (Sku sku : skuList) {
            isOk = skuDao.create(sku);
            Assert.isTrue(isOk, "sku.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM CREATE] Successfully(TRA) create sku with: {}", sku);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("[ITEM CREATE] Successfully(TRA) create item with: {}", item);
        }

        if (item.getIsSaleAttributes() == 1) {
            if (CollUtil.isEmpty(bo.getVendorItemChannlRelationModels())) {
                throw new ServiceException("vendorItemChannlRelationCreateRequests.is.null");
            }
            // 提炼渠道
            Set<Long> channelSet = bo.getVendorItemChannlRelationModels().stream().map(VendorItemChannlRelationModel::getChannelId).collect(Collectors.toSet());
            Date date = new Date();
            List<VendorItemChannlRelationModel> modelList = new ArrayList<>();
            for (Long channelId : channelSet) {
                VendorItemChannlRelationModel vendorItemChannlRelationModel = new VendorItemChannlRelationModel();
                vendorItemChannlRelationModel.setVendorId(item.getShopId());
                vendorItemChannlRelationModel.setItemId(item.getId());
                vendorItemChannlRelationModel.setChannelId(channelId);
                vendorItemChannlRelationModel.setIsDelete(0);
                vendorItemChannlRelationModel.setId(idGenerator.nextValue(VendorItemChannlRelationModel.class));
                vendorItemChannlRelationModel.setCreatedAt(date);
                vendorItemChannlRelationModel.setUpdatedAt(date);
                modelList.add(vendorItemChannlRelationModel);
            }

            boolean b = vendorItemChannlRelationDao.creates(modelList) == modelList.size();
            if (!b) {
                throw new ServiceException("vendorItemChannlRelationDao.create.fail");
            }

            // 商品转map
            Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getKey, Function.identity()));
            Map<Long, VendorItemChannlRelationModel> modelMap = modelList.stream().collect(Collectors.toMap(VendorItemChannlRelationModel::getChannelId, Function.identity()));
            List<VendorItemChannlSkuRelationModel> vendorItemChannlSkuRelationModels = new ArrayList<>();
            // 商品渠道供应商关系表
            for (VendorItemChannlRelationModel vendorItemChannlRelationModel : bo.getVendorItemChannlRelationModels()) {
                for (VendorItemChannlSkuRelationModel x : vendorItemChannlRelationModel.getVendorItemChannlSkuRelationModelList()) {
                    VendorItemChannlSkuRelationModel model = new VendorItemChannlSkuRelationModel();
                    model.setVendorItemChannlRelationId(modelMap.get(x.getChannelId()).getId());
                    model.setChannelId(x.getChannelId());
                    model.setChannelPrice(x.getChannelPrice());
                    model.setIsDelete(0);
                    model.setCreatedAt(date);
                    model.setUpdatedAt(date);
                    model.setId(idGenerator.nextValue(VendorItemChannlSkuRelationModel.class));
                    Sku sku = skuMap.get(x.getKey());
                    model.setSkuId(sku.getId());
                    vendorItemChannlSkuRelationModels.add(model);
                }
            }


            boolean b1 = vendorItemChannlSkuRelationDao.creates(vendorItemChannlSkuRelationModels) == vendorItemChannlSkuRelationModels.size();
            if (!b1) {
                throw new ServiceException("vendorItemChannlSkuRelationDao.create.fail");
            }
        }

        // PART 2.1: 创建医疗属性
        if (item.getIsmedicalgoods() != null
                && item.getIsmedicalgoods().equals("Y")) {
            isOk = skuYLDao.create(skuYL);
            Assert.isTrue(isOk, "skuYL.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM CREATE] Successfully(TRA) create skuYL with: {}", skuYL);
            }
        }

        // PART 3: 创建itemDetail对象
        isOk = itemDetailDao.create(itemDetail);
        Assert.isTrue(isOk, "item.detail.create.fail");

        if (log.isDebugEnabled()) {
            log.debug("[ITEM CREATE] Successfully(TRA) create item detail with: {}", itemDetail);
        }

        // PART 4: 写入维度扩展信息
        if (!CollectionUtils.isEmpty(bo.getItemDimensionList())) {
            Integer creates = itemDimensionDao.creates(bo.getItemDimensionList());

            Assert.isTrue(creates != null, "item.dimension.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM CREATE] Successfully(TRA) create item dimension with: {}", bo.getItemDimensionList());
            }
        }

        // 后置扩展
        onCreateExtension(bo);

        // 位标事务处理
        isOk = bitTagProcessLogic.transactional(paramMap);
        Assert.isTrue(isOk, "bit.tag.process.fail");

        //记录商品创建动作
        isOk = itemEventLogHelper.itemCreateEventLog(item, 1);
        Assert.isTrue(isOk, "商品创建日志记录失败！");

        if(areaItemOperateBO != null){
            publish(areaItemOperateBO);
        }

        return item.getId();
    }

    private void beforeCreateExtension(FullItemOperateBO bo) {
        if (itemTransactionalExtension == null) {
            return;
        }

        StopWatch stopWatch = prepare();
        ExtensionResult extensionResult = itemTransactionalExtension.beforeCreate(bo);
        finish(stopWatch, extensionResult);
    }

    private void onCreateExtension(FullItemOperateBO bo) {
        if (itemTransactionalExtension == null) {
            return;
        }

        StopWatch stopWatch = prepare();
        ExtensionResult extensionResult = itemTransactionalExtension.onCreate(bo);
        finish(stopWatch, extensionResult);
    }

    private void clearDimension(Item item, Set<Long> skuIdList, String type, String code, Integer tenantId) {
        boolean isOk = itemDimensionDao.delete(ItemDimensionSchemaType.SCHEMA_ITEM.name(), item.getId().toString(),
                type, code, tenantId);
        Assert.isTrue(isOk, "item.dimension.delete.fail");

        isOk = itemDimensionDao.batchDelete(ItemDimensionSchemaType.SCHEMA_SKU.name(),
                AssembleDataUtils.set2set(skuIdList, String::valueOf), type, code, tenantId);
        Assert.isTrue(isOk, "sku.dimension.delete.fail");
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Set<Long> update(FullItemOperateBO bo, Map<Integer, Object> paramMap,AreaItemOperateBO areaItemOperateBO) {

        // 前置扩展
        beforeUpdateExtension(bo);

        Set<Long> originalSkuIdSet = new HashSet<>();

        Boolean isOk;
        ItemUpdateLog zqItemUpdateLog = bo.getZqItemUpdateLog();
        boolean isZqUpdate = (null != zqItemUpdateLog);

        if (bo.getVendorItemChannlRelationModels() != null && bo.getVendorItemChannlRelationModels().size() > 0) {
            List<VendorItemChannlRelationModel> vendorItemChannlRelationModels = bo.getVendorItemChannlRelationModels();
            Set<Long> itemChannlSkuRelationIds = new HashSet<>();
            for (VendorItemChannlRelationModel vendorItemChannlRelationModel : vendorItemChannlRelationModels) {
                List<VendorItemChannlSkuRelationModel> vendorItemChannlSkuRelationModelList = vendorItemChannlRelationModel.getVendorItemChannlSkuRelationModelList();
                if (!CollectionUtils.isEmpty(vendorItemChannlSkuRelationModelList)) {
                    Set<Long> vendorItemChannlSkuRelationIds = vendorItemChannlSkuRelationModelList.stream().map(VendorItemChannlSkuRelationModel::getId).collect(Collectors.toSet());
                    itemChannlSkuRelationIds.addAll(vendorItemChannlSkuRelationIds);
                }
            }
            if(!CollectionUtils.isEmpty(itemChannlSkuRelationIds)) {
                vendorItemChannlSkuRelationDao.deletes(new ArrayList<>(itemChannlSkuRelationIds));
            }
            Set<Long> itemChannlRelations = vendorItemChannlRelationModels.stream().map(VendorItemChannlRelationModel::getId).collect(Collectors.toSet());
            if(!CollectionUtils.isEmpty(itemChannlRelations)) {
                vendorItemChannlRelationDao.deletes(new ArrayList<>(itemChannlRelations));
            }
            createVendorItemChannlAndSku(bo);
        }
//        if(null != bo.getModels() && bo.getModels().size()>0){
//            VendorItemChannlSkuRelationModel models = bo.getModels().get(0);
//            if(models.getIsDelete().equals(1)){
//                //删除
//                Set<Long> itemChannlRelationIds = bo.getModels().stream().map(VendorItemChannlSkuRelationModel::getVendorItemChannlRelationId).collect(Collectors.toSet());
//                List<Long> idByItemChannlRelationId = vendorItemChannlSkuRelationDao.findIdByItemChannlRelationId(new ArrayList<>(itemChannlRelationIds));
//                vendorItemChannlSkuRelationDao.deletes(idByItemChannlRelationId);
//                vendorItemChannlRelationDao.deletes(new ArrayList<>(itemChannlRelationIds));
//            }else {
//                vendorItemChannlSkuRelationDao.mulUpdate(bo.getModels());
//            }
//        }
        // PART 1: 更新item
        Item item = bo.getItem();
        if (!isZqUpdate) {
            //原整购商品修改逻辑
            isOk = itemDao.update(item);
            Assert.isTrue(isOk, "item.update.fail");
            if (log.isDebugEnabled()) {
                log.debug("[ITEM UPDATE] Successfully(TRA) update item with: {}", item);
            }
        } else {
            //政企商品修改逻辑
            Integer itemVersion = item.getVersion();
            if (ItemZqPushlStatusEnum.ZQ_PUSH_STATUS_NEW.getValue() == zqItemUpdateLog.getZqItemStatusEnum().getValue()) {
                isOk = itemDao.update(item);
                Assert.isTrue(isOk, "item.update.fail");
                if (log.isDebugEnabled()) {
                    log.debug("[ITEM UPDATE] Successfully(TRA) update item with: {}", item);
                }
                itemVersion = itemVersion + 1;
            } else {
                // PART 1.1: 更新政企item记log
                if (zqItemUpdateLog.isDataSaveFlag()) {
                    isOk = itemUpdateLogDao.create(zqItemUpdateLog);
                    Assert.isTrue(isOk, "item.zq.update.log.save.fail,info：{}");
                } else {
                    isOk = itemUpdateLogDao.update(zqItemUpdateLog);
                    Assert.isTrue(isOk, "item.zq.update.log.update.fail");
                }
                if (log.isDebugEnabled()) {
                    log.debug("[ITEM ZQ UPDATE LOG] Successfully(TRA) save item log with: {}", zqItemUpdateLog);
                }
            }
            // 变更商品已修改状态
            isOk = itemDao.updateZqPushStatus(item.getId(), zqItemUpdateLog.getZqItemStatusEnum().getValue(),
                    null, itemVersion, item.getTenantId(), item.getUpdatedBy());
            Assert.isTrue(isOk, "item.zq.update.fail");
        }

        // PART 1.2: 更新item记log
        if (bo.getItemUpdateLog() != null) {
            List<ItemUpdateLog> itemUpdateLogs = Lists.newArrayList();
            if (areaItemOperateBO != null) {
                ItemUpdateLog itemUpdateLog = bo.getItemUpdateLog();
                for (ItemAudit itemAudit : areaItemOperateBO.getToCreateItemAuditList()) {
                    ItemUpdateLog updateLog = new ItemUpdateLog();
                    BeanUtils.copyProperties(itemUpdateLog, updateLog);
                    generateHelper.generateId(updateLog);
                    updateLog.setAuditId(itemAudit.getId());
                    updateLog.setTimeStamp(new Date().getTime());
                    itemUpdateLogs.add(updateLog);
                }
            }
            if (!CollectionUtils.isEmpty(itemUpdateLogs)) {
                isOk = itemUpdateLogDao.creates(itemUpdateLogs) > 0;
                Assert.isTrue(isOk, "item.update.log.fail");
            }
        }

        // PART 2: 更新sku
        for (Sku sku : bo.getToUpdateSkuList()) {
            isOk = skuDao.update(sku);
            Assert.isTrue(isOk, "sku.update.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM UPDATE] Successfully(TRA) update sku with: {}", sku);
            }

            originalSkuIdSet.add(sku.getId());
        }

        // PART 2.1: 创建医疗属性
        if (item.getIsmedicalgoods() != null
                && item.getIsmedicalgoods().equals("Y")) {

            if (bo.getSkuYL().getId() == null) {
                generateHelper.generateId(bo.getSkuYL());
                isOk = skuYLDao.create(bo.getSkuYL());
                Assert.isTrue(isOk, "skuYL.create.fail");

                if (log.isDebugEnabled()) {
                    log.debug("[ITEM CREATE] Successfully(TRA) create skuYL with: {}", bo.getSkuYL());
                }
            } else {
                isOk = skuYLDao.update(bo.getSkuYL());
                Assert.isTrue(isOk, "skuYL.update.fail");

                if (log.isDebugEnabled()) {
                    log.debug("[ITEM CREATE] Successfully(TRA) update skuYL with: {}", bo.getSkuYL());
                }
            }

        }

        // PART 3: 删除sku
        for (Sku sku : bo.getToDeleteSkuList()) {
            isOk = skuDao.updateStatus(sku.getId(), sku.getVersion(), ItemStatus.DELETED.getValue(),
                    sku.getTenantId(), sku.getUpdatedBy());

            Assert.isTrue(isOk, "sku.delete.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM UPDATE] Successfully(TRA) delete sku with: {}", sku);
            }

            originalSkuIdSet.add(sku.getId());
        }

        // PART 4: 创建sku
        for (Sku sku : bo.getToCreateSkuList()) {
            isOk = skuDao.create(sku);
            Assert.isTrue(isOk, "sku.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM UPDATE] Successfully(TRA) create sku with: {}", sku);
            }
        }

        if (!isZqUpdate) {
            // PART 5: 更新itemDetail
            isOk = itemDetailDao.update(bo.getItemDetail());
            Assert.isTrue(isOk, "item.detail.update.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM UPDATE] Successfully(TRA) update itemDetail with: {}", bo.getItemDetail());
            }
        }

        // PART 6: 维度信息重写
        if (!CollectionUtils.isEmpty(bo.getItemDimensionList())) {
            ItemDimension dimension = AssembleDataUtils.takeFirst(bo.getItemDimensionList());
            clearDimension(item, originalSkuIdSet, dimension.getType(), dimension.getCode(), item.getTenantId());

            Integer creates = itemDimensionDao.creates(bo.getItemDimensionList());
            Assert.isTrue(creates != 0, "item.dimension.create.fail");
        }

        // 需求159需求点2：合规性审核通过，供应商再次修改信息并提交时，所有区域本商品自动下架
        if (bo.getBaseAuditAdoptList().size() > 0 && areaItemOperateBO == null) {

            log.info("合规审核通过，更新商品，区域本商品自动下架开始....");

            // 修改所有区域运营存在本商品的上架状态为已下架
            // 校验装修
            List<Long> itemIdList = Lists.newArrayList(item.getId());
            log.info("checkRenovation::::::::::");
//            Response<Set<Long>> response = designItemService.checkHomePageRenovationItem(bo.getPublishedOperatorIdList(), itemIdList);
//            if (!response.isSuccess()) {
//                throw new ServiceException("查询装修失败");
//            }
//            log.info("checkRenovation::::::::::" + response.getResult().toString());
//            if (!response.getResult().isEmpty()) {
//                throw new ServiceException("商品：" + response.getResult().toString() + "，存在装修");
//            }
            log.info("operatorIds:" + bo.getPublishedOperatorIdList() + "itemIdList:" + itemIdList);

            // 批量删除item_quoted_price商品数据
            itemQuotedPriceDao.deleteItemByOperatorIdsBatch(bo.getPublishedOperatorIdList(), item.getId());

            for (Long id : bo.getPublishedOperatorIdList()) {
                log.info("商品下架--------------");
                areaItemDao.operatorOffShelf(id, item.getId());

                areaSkuDao.operatorOffShelf(id, item.getId());

                //记录区域商品下架动作信息
                itemEventLogHelper.areaItemOffShelfEventLogById(item.getId(), id, RequestContext.getTenantId());
            }
            log.info("update.shop.auto.shelf.end");
        }

        // 159需求点3
        if (!CollectionUtils.isEmpty(bo.getPriceAuditList())) {
            // 更新价格审核状态
            boolean auditUpdateFlag = itemAuditDao.updateItemAuditStatusByBatch(bo.getPriceAuditList());
            AssertUtil.isTrue(auditUpdateFlag, "更新商品信息失败！");
        }

        // 后置扩展
        onUpdateExtension(bo);

        Assert.isTrue(isOk, "bit.tag.transactional.fail");
        isOk = bitTagProcessLogic.transactional(paramMap);

//        //记录商品更新动作
//        isOk = itemEventLogHelper.itemUpdateEventLog(item,item.getTenantId());
//        Assert.isTrue(isOk, "商品更新日志记录失败！");
        //如果areaItemOperateBO不为空 则是修改并发布  需要调用发布商品
        if(areaItemOperateBO != null){
            publish(areaItemOperateBO);
        }

        return originalSkuIdSet;
    }

    public boolean publish(AreaItemOperateBO operateBO) {
        boolean isOk = true;

        List<AreaItem> toCreateAreaItemList = operateBO.getToCreateAreaItemList();
        for (AreaItem areaItem:
                toCreateAreaItemList) {
            if (StringUtils.isBlank(areaItem.getMainImage())){
                Assert.isTrue(false, "商品信息未完善，请完善信息后发布");
            }
        }

        List<ItemAudit> itemAuditCreateList = operateBO.getToCreateItemAuditList();
        if (!CollectionUtils.isEmpty(itemAuditCreateList)) {
            isOk = itemAuditDao.creates(itemAuditCreateList) == itemAuditCreateList.size();
            Assert.isTrue(isOk, "area.item.audit.create.fail");
        }

        List<ItemAudit> itemAuditUpdateList = operateBO.getToUpdateItemAuditList();
        if (!CollectionUtils.isEmpty(itemAuditUpdateList)) {
            itemAuditUpdateList.forEach(x -> {
                Assert.isTrue(itemAuditDao.update(x), "area.item.audit.update.fail");
            });
        }

        List<AreaItem> itemCreateList = operateBO.getToCreateAreaItemList();
        if (!CollectionUtils.isEmpty(itemCreateList)) {
            isOk = areaItemDao.creates(itemCreateList) == itemCreateList.size();
            Assert.isTrue(isOk, "area.item.create.fail");
        }

        List<Long> toDeleteAreaSkuIdList = operateBO.getToDeleteAreaSkuIdList();
        log.info("ItemManager.publish() toDeleteAreaSkuIdList:{}", toDeleteAreaSkuIdList);
        if (!CollectionUtils.isEmpty(toDeleteAreaSkuIdList)) {
            isOk = areaSkuDao.deleteBySkuIds(toDeleteAreaSkuIdList);
            Assert.isTrue(isOk, "area.sku.delete.fail");
            log.info("ItemManager.publish() toDeleteAreaSkuIdList end");
        }
        // 添加新建的关联areaSku
        if (!CollectionUtils.isEmpty(operateBO.getToCreateLinkAreaSkuList())) {
            operateBO.getToCreateAreaSkuList().addAll(operateBO.getToCreateLinkAreaSkuList());
        }
        List<AreaSku> areaSkuList = operateBO.getToCreateAreaSkuList();
        if (!CollectionUtils.isEmpty(areaSkuList)) {
            isOk = areaSkuDao.creates(areaSkuList) == areaSkuList.size();
            Assert.isTrue(isOk, "area.sku.create.fail");
        }
        // 添加关联areaItem
        if (!CollectionUtils.isEmpty(operateBO.getToUpdateLinkAreaItemList())) {
            operateBO.getToUpdateAreaItemList().addAll(operateBO.getToUpdateLinkAreaItemList());
        }
        for (AreaItem areaItem : operateBO.getToUpdateAreaItemList()) {
            isOk = areaItemDao.update(areaItem);
            if (isOk){
                try {
                    ThirdParanaThirdMessageCreateRequest messageCreateRequest = buildUpdateAreaItemMessageReq(areaItem);
                    paranaThirdMessageWriteApi.create(messageCreateRequest);
                } catch (Exception e) {
                    log.error("发送消息失败::{}", Throwables.getStackTraceAsString(e));
                }
            }
            Assert.isTrue(isOk, "area.item.update.fail");
        }
        // 添加更新的关联areaSku
        if (!CollectionUtils.isEmpty(operateBO.getToUpdateLinkAreaSkuList())) {
            operateBO.getToUpdateAreaSkuList().addAll(operateBO.getToUpdateLinkAreaSkuList());
        }
        for (AreaSku areaSku : operateBO.getToUpdateAreaSkuList()) {
            isOk = areaSkuDao.update(areaSku);
            Assert.isTrue(isOk, "area.sku.update.fail");
        }
        //处理没有审核记录的商品发送第三方消息
        for (AreaItem areaItem : operateBO.getToMessageForNotAuditAreaItem()) {
            isOk = areaItemDao.update(areaItem);
            if (isOk){
                try {
                    ThirdParanaThirdMessageCreateRequest messageCreateRequest = buildUpdateAreaItemMessageReq(areaItem);
                    paranaThirdMessageWriteApi.create(messageCreateRequest);
                } catch (Exception e) {
                    log.error("发送消息失败::{}", Throwables.getStackTraceAsString(e));
                }
            }
            Assert.isTrue(isOk, "area.item.not.audit.update.fail");
        }
        for (AreaSku areaSku : operateBO.getToUpdateNotAuditAreaSkuList()) {
            isOk = areaSkuDao.update(areaSku);
            Assert.isTrue(isOk, "area.sku.not.audit.update.fail");
        }
        log.info("operateBO:{}", operateBO);
        for (ItemStockInit itemStockInit : operateBO.getToCreateItemStockInit()) {
            log.info("选中区域:{}", itemStockInit.getOperatorIds());
            isOk = itemStockInit(itemStockInit.getVendorId(), itemStockInit.getOperatorIds(), itemStockInit.getSkuId(), itemStockInit.getStock());
            Assert.isTrue(isOk, "itemStockInit.is.fail");
        }

        if (ObjectUtil.isNotEmpty(operateBO.getToCreateSaleAttributeApproval())) {
            isOk = saleAttributeApprovalWriteFacade.creates(operateBO.getToCreateSaleAttributeApproval()) == operateBO.getToCreateSaleAttributeApproval().size();
            Assert.isTrue(isOk, "itemStockInit.is.fail");
        }

        if (ObjectUtil.isNotEmpty(operateBO.getToCreateCategoryAttributeBinding())) {
            for (CategoryAttributeBinding categoryAttributeBinding : operateBO.getToCreateCategoryAttributeBinding()) {
                isOk = categoryAttributeManager.createBinding(categoryAttributeBinding);
                Assert.isTrue(isOk, "itemStockInit.is.fail");
            }
        }
        ESMQInjection.entryAreaSkuChange(operateBO);
        return isOk;
    }

    private ThirdParanaThirdMessageCreateRequest buildUpdateAreaItemMessageReq(AreaItem areaItem) {
        ThirdParanaThirdMessageCreateRequest messageCreateRequest = new ThirdParanaThirdMessageCreateRequest();
        messageCreateRequest.setCreatedBy(areaItem.getUpdatedBy());
        messageCreateRequest.setUpdatedBy(areaItem.getUpdatedBy());
        messageCreateRequest.setRemark("商品信息更新");
        messageCreateRequest.setOperatorId(areaItem.getOperatorId());
        messageCreateRequest.setCreatedAt(new Date());
        messageCreateRequest.setUpdatedAt(new Date());
        messageCreateRequest.setMessageType(4);
        ThirdParanaThirdMessageBean thirdParanaThirdMessageBean = new ThirdParanaThirdMessageBean();
        thirdParanaThirdMessageBean.setId(areaItem.getItemId());
        messageCreateRequest.setBean(thirdParanaThirdMessageBean);
        return messageCreateRequest;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Set<Long> openItemUpdate(FullItemOperateBO bo, Map<Integer, Object> paramMap) {

        // 前置扩展
        beforeUpdateExtension(bo);
        Set<Long> originalSkuIdSet = new HashSet<>();
        Boolean isOk;
        // 商品信息
        Item item = bo.getItem();
        // 处理销售渠道
        if (!CollectionUtils.isEmpty(bo.getVendorItemChannlRelationModels())) {
            // 查询商品绑定的销售渠道
            VendorItemChannlRelationModel queryItemChannlRelationModel = new VendorItemChannlRelationModel();
            queryItemChannlRelationModel.setItemId(item.getId());
            queryItemChannlRelationModel.setVendorId(item.getShopId());
            queryItemChannlRelationModel.setIsDelete(0);
            List<VendorItemChannlRelationModel> itemChannlList = vendorItemChannlRelationDao.findItemChannl(queryItemChannlRelationModel);
            Set<Long> itemChannlRelations = itemChannlList.stream().map(VendorItemChannlRelationModel::getId).collect(Collectors.toSet());
            // 删除原有绑定关系
            if(!CollectionUtils.isEmpty(itemChannlRelations)) {
                vendorItemChannlRelationDao.deletes(new ArrayList<>(itemChannlRelations));
            }
            Set<Long> itemChannlSkuRelationIds = new HashSet<>();
            for (VendorItemChannlRelationModel vendorItemChannlRelationModel : itemChannlList) {
                List<VendorItemChannlSkuRelationModel> vendorItemChannlSkuRelationModelList = vendorItemChannlRelationModel.getVendorItemChannlSkuRelationModelList();
                if (!CollectionUtils.isEmpty(vendorItemChannlSkuRelationModelList)) {
                    Set<Long> vendorItemChannlSkuRelationIds = vendorItemChannlSkuRelationModelList.stream().map(VendorItemChannlSkuRelationModel::getId).collect(Collectors.toSet());
                    itemChannlSkuRelationIds.addAll(vendorItemChannlSkuRelationIds);
                }
            }
            if(!CollectionUtils.isEmpty(itemChannlSkuRelationIds)) {
                vendorItemChannlSkuRelationDao.deletes(new ArrayList<>(itemChannlSkuRelationIds));
            }

            // 新建销售渠道数据
            Set<Long> channelSet = bo.getVendorItemChannlRelationModels().stream().map(VendorItemChannlRelationModel::getChannelId).collect(Collectors.toSet());
            List<Sku> skuList = bo.getToUpdateSkuList();
            List<VendorItemChannlRelationModel> modelList = new ArrayList<>();
            for (Long channelId : channelSet) {
                VendorItemChannlRelationModel vendorItemChannlRelationModel = new VendorItemChannlRelationModel();
                vendorItemChannlRelationModel.setVendorId(item.getShopId());
                vendorItemChannlRelationModel.setItemId(item.getId());
                vendorItemChannlRelationModel.setChannelId(channelId);
                vendorItemChannlRelationModel.setIsDelete(0);
                vendorItemChannlRelationModel.setId(idGenerator.nextValue(VendorItemChannlRelationModel.class));
                vendorItemChannlRelationModel.setCreatedAt(new Date());
                vendorItemChannlRelationModel.setUpdatedAt(new Date());
                modelList.add(vendorItemChannlRelationModel);
            }

            boolean b = vendorItemChannlRelationDao.creates(modelList) == modelList.size();
            Assert.isTrue(b, "vendorItemChannlRelationDao.create.fail");

            Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getKey, Function.identity()));
            Map<Long, VendorItemChannlRelationModel> modelMap = modelList.stream().collect(Collectors.toMap(VendorItemChannlRelationModel::getChannelId, Function.identity()));
            List<VendorItemChannlSkuRelationModel> vendorItemChannlSkuRelationModels = new ArrayList<>();
            for (VendorItemChannlRelationModel vendorItemChannlRelationModel : bo.getVendorItemChannlRelationModels()) {
                for (VendorItemChannlSkuRelationModel x : vendorItemChannlRelationModel.getVendorItemChannlSkuRelationModelList()) {
                    VendorItemChannlSkuRelationModel model = new VendorItemChannlSkuRelationModel();
                    model.setVendorItemChannlRelationId(modelMap.get(x.getChannelId()).getId());
                    model.setChannelId(x.getChannelId());
                    model.setChannelPrice(x.getChannelPrice());
                    model.setIsDelete(0);
                    model.setCreatedAt(new Date());
                    model.setUpdatedAt(new Date());
                    model.setId(idGenerator.nextValue(VendorItemChannlSkuRelationModel.class));
                    Sku sku = skuMap.get(x.getKey());
                    model.setSkuId(sku.getId());
                    vendorItemChannlSkuRelationModels.add(model);
                }
            }

            boolean b1 = vendorItemChannlSkuRelationDao.creates(vendorItemChannlSkuRelationModels) == vendorItemChannlSkuRelationModels.size();
            Assert.isTrue(b1, "vendorItemChannlRelationDao.create.fail");
        }

        // PART 1: 更新item
        isOk = itemDao.update(item);
        Assert.isTrue(isOk, "item.update.fail");
        if (log.isDebugEnabled()) {
            log.debug("[ITEM UPDATE] Successfully(TRA) update item with: {}", item);
        }

        // PART 1.2: 更新item记log
        if (bo.getItemUpdateLog() != null) {
            generateHelper.generateId(bo.getItemUpdateLog());
            bo.getItemUpdateLog().setTimeStamp(new Date().getTime());
            isOk = itemUpdateLogDao.create(bo.getItemUpdateLog());
            Assert.isTrue(isOk, "item.update.log.fail");
        }

        // PART 2: 更新sku
        for (Sku sku : bo.getToUpdateSkuList()) {
            isOk = skuDao.update(sku);
            Assert.isTrue(isOk, "sku.update.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM UPDATE] Successfully(TRA) update sku with: {}", sku);
            }

            originalSkuIdSet.add(sku.getId());
        }

        for (Sku sku : bo.getToCreateSkuList()) {
            isOk = skuDao.create(sku);
            Assert.isTrue(isOk, "sku.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM UPDATE] Successfully(TRA) create sku with: {}", sku);
            }
        }

        // PART 5: 更新itemDetail
        isOk = itemDetailDao.update(bo.getItemDetail());
        Assert.isTrue(isOk, "item.detail.update.fail");

        if (log.isDebugEnabled()) {
            log.debug("[ITEM UPDATE] Successfully(TRA) update itemDetail with: {}", bo.getItemDetail());
        }

        if(!CollectionUtils.isEmpty(bo.getUpdateAreaItemList())){
            for (AreaItem areaItem : bo.getUpdateAreaItemList()) {
                isOk = areaItemDao.update(areaItem);
                Assert.isTrue(isOk, "areaItem.update.fail");
            }
        }

        if(!CollectionUtils.isEmpty(bo.getUpdateAreaSkuList())){
            for (AreaSku areaSku : bo.getUpdateAreaSkuList()) {
                isOk = areaSkuDao.update(areaSku);
                Assert.isTrue(isOk, "areaSku.update.fail");
            }
        }

        if(!CollectionUtils.isEmpty(bo.getCreateAreaSkuList())){
            for (AreaSku areaSku : bo.getCreateAreaSkuList()) {
                isOk = areaSkuDao.create(areaSku);
                Assert.isTrue(isOk, "areaSku.create.fail");
            }
        }

        // PART 6: 维度信息重写
        if (!CollectionUtils.isEmpty(bo.getItemDimensionList())) {
            ItemDimension dimension = AssembleDataUtils.takeFirst(bo.getItemDimensionList());
            clearDimension(item, originalSkuIdSet, dimension.getType(), dimension.getCode(), item.getTenantId());

            Integer creates = itemDimensionDao.creates(bo.getItemDimensionList());
            Assert.isTrue(creates != 0, "item.dimension.create.fail");
        }

        // 后置扩展
        onUpdateExtension(bo);

        isOk = bitTagProcessLogic.transactional(paramMap);
        Assert.isTrue(isOk, "bit.tag.transactional.fail");

        return originalSkuIdSet;
    }

    private void beforeUpdateExtension(FullItemOperateBO bo) {
        if (itemTransactionalExtension == null) {
            return;
        }

        StopWatch stopWatch = prepare();
        ExtensionResult extensionResult = itemTransactionalExtension.beforeUpdate(bo);
        finish(stopWatch, extensionResult);
    }

    private void onUpdateExtension(FullItemOperateBO bo) {
        if (itemTransactionalExtension == null) {
            return;
        }

        StopWatch stopWatch = prepare();
        ExtensionResult extensionResult = itemTransactionalExtension.onUpdate(bo);
        finish(stopWatch, extensionResult);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Set<Long> sellerCancel(Set<Long> operatorIdSet, Long itemId, Long shopId, Integer version, Integer tenantId, String updatedBy) {

        boolean isOk;

        if (!CollectionUtils.isEmpty(operatorIdSet)) {
            areaItemDao.operatorDeleteByItemId(operatorIdSet, shopId, itemId);
            areaSkuDao.operatorDeleteByItemId(operatorIdSet, shopId, itemId);
        }

        log.info("删除商品id:{}",itemId);
        // PART 1: 删除Item
        isOk = itemDao.updateStatusCheckShopId(itemId, version, ItemStatus.DELETED.getValue(), shopId, tenantId, updatedBy);
        if (!isOk) {
            throw new ServiceException("item.delete.fail");
        }
        try{

            itemAuditDao.logicDeleteScce(itemId);
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
        }

        if (log.isDebugEnabled()) {
            log.debug("[ITEM DELETE] Successfully(TRA) delete item by id: {}", itemId);
        }

        // PART 2: 删除sku
        List<Sku> skuList = skuDao.findByItemId(itemId, tenantId);

        if (CollectionUtils.isEmpty(skuList)) {
            throw new ServiceException("data.error.for.missing.sku");
        }

        Set<Long> deletedSkuIdSet = Sets.newLinkedHashSetWithExpectedSize(skuList.size());

        for (Sku sku : skuList) {
            isOk = skuDao.updateStatusCheckShopId(sku.getId(), sku.getVersion(), ItemStatus.DELETED.getValue(), shopId, tenantId, updatedBy);

            if (!isOk) {
                throw new ServiceException("sku.delete.fail");
            }

            if (log.isDebugEnabled()) {
                log.debug("[ITEM DELETE] Successfully(TRA) delete sku by id: {}", sku.getId());
            }

            deletedSkuIdSet.add(sku.getId());
        }

        return deletedSkuIdSet;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Set<Long> sellerDelete(Long itemId, Long shopId, Integer version, Integer tenantId, String updatedBy) {

        boolean isOk;

        // PART 1: 删除Item
        isOk = itemDao.updateStatusCheckShopId(itemId, version, ItemStatus.DELETED.getValue(), shopId, tenantId, updatedBy);

        if (!isOk) {
            throw new ServiceException("item.delete.fail");
        }

        if (log.isDebugEnabled()) {
            log.debug("[ITEM DELETE] Successfully(TRA) delete item by id: {}", itemId);
        }

        // PART 2: 删除sku
        List<Sku> skuList = skuDao.findByItemId(itemId, tenantId);

        if (CollectionUtils.isEmpty(skuList)) {
            throw new ServiceException("data.error.for.missing.sku");
        }

        Set<Long> deletedSkuIdSet = Sets.newLinkedHashSetWithExpectedSize(skuList.size());

        for (Sku sku : skuList) {
            isOk = skuDao.updateStatusCheckShopId(sku.getId(), sku.getVersion(), ItemStatus.DELETED.getValue(), shopId, tenantId, updatedBy);

            if (!isOk) {
                throw new ServiceException("sku.delete.fail");
            }

            if (log.isDebugEnabled()) {
                log.debug("[ITEM DELETE] Successfully(TRA) delete sku by id: {}", sku.getId());
            }

            deletedSkuIdSet.add(sku.getId());
        }

        return deletedSkuIdSet;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Set<Long> sellerDelete(List<IdVersionPairBO> targetList, Long shopId, Integer tenantId, String updatedBy) {
        Set<Long> deletedSkuIdSet = new HashSet<>();

        for (IdVersionPairBO pair : targetList) {
            Set<Long> partial = this.sellerDelete(pair.getId(), shopId, pair.getVersion(), tenantId, updatedBy);
            deletedSkuIdSet.addAll(partial);
        }

        return deletedSkuIdSet;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Set<Long> sellerUpdateStatus(Long id, Integer version, Long shopId, Integer status, Integer tenantId, String updatedBy) {

        Boolean isOk;

        // 更新item
        isOk = itemDao.updateStatusCheckShopId(id, version, status, shopId, tenantId, updatedBy);

        if (!isOk) {
            throw new ServiceException("item.update.fail");
        }

        if (log.isDebugEnabled()) {
            log.debug("[ITEM UPDATE] Successfully(TRA) update item status by id: {}", id);
        }

        // PART 2: 更新sku
        List<Sku> skuList = skuDao.findByItemId(id, tenantId);

        if (CollectionUtils.isEmpty(skuList)) {
            throw new ServiceException("data.error.for.missing.sku");
        }

        Set<Long> updatedSkuIdSet = Sets.newLinkedHashSetWithExpectedSize(skuList.size());

        for (Sku sku : skuList) {
            // 不启用的Sku不更改状态
            if (sku.getStatus() == ItemStatus.DISABLE.getValue()) {
                continue;
            }

            isOk = skuDao.updateStatusCheckShopId(sku.getId(), sku.getVersion(), status, shopId, tenantId, updatedBy);

            if (!isOk) {
                throw new ServiceException("sku.update.status.fail");
            }

            if (log.isDebugEnabled()) {
                log.debug("[ITEM UPDATE] Successfully(TRA) update sku status by id: {}", sku.getId());
            }

            updatedSkuIdSet.add(sku.getId());
        }

        return updatedSkuIdSet;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Set<Long> sellerUpdateStatus(List<IdVersionPairBO> targetList, Long shopId, Integer status, Integer tenantId, String updatedBy) {
        Set<Long> updatedSkuIdSet = new HashSet<>();

        for (IdVersionPairBO pair : targetList) {
            Set<Long> partial = this.sellerUpdateStatus(pair.getId(), pair.getVersion(), shopId, status, tenantId, updatedBy);
            updatedSkuIdSet.addAll(partial);
        }

        return updatedSkuIdSet;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Set<Long> adminUpdateStatus(List<IdVersionPairBO> targetList, Integer status, Integer tenantId, String updatedBy) {
        Set<Long> updatedSkuIdSet = new HashSet<>();

        Boolean isOk;

        for (IdVersionPairBO pair : targetList) {
            isOk = itemDao.updateStatus(pair.getId(), pair.getVersion(), status, tenantId, updatedBy);

            if (!isOk) {
                throw new ServiceException("item.update.status.fail");
            }

            if (log.isDebugEnabled()) {
                log.debug("[ITEM UPDATE]  Successfully(TRA) update item status by id: {}", pair.getId());
            }

            List<Sku> skuList = skuDao.findByItemId(pair.getId(), tenantId);

            if (CollectionUtils.isEmpty(skuList)) {
                log.error("[ITEM UPDATE] Data error for item missing sku by id: {}!", pair.getId());
                throw new ServiceException("data.error.for.missing.sku");
            }

            for (Sku sku : skuList) {
                // 不启用的Sku不更改状态
                if (sku.getStatus() == ItemStatus.DISABLE.getValue()) {
                    continue;
                }
                isOk = skuDao.updateStatus(sku.getId(), sku.getVersion(), status, tenantId, updatedBy);

                if (!isOk) {
                    throw new ServiceException("sku.update.status.fail");
                }

                if (log.isDebugEnabled()) {
                    log.debug("[ITEM UPDATE]  Successfully(TRA) update sku status by id: {}", sku.getId());
                }

                updatedSkuIdSet.add(sku.getId());
            }
        }

        return updatedSkuIdSet;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void adminUpdateItemByShopId(Boolean frozen, Long shopId, Integer tenantId, String updatedBy) {
        StopWatch stopWatch = new StopWatch("FROZEN/UNFROZEN shop items");
        stopWatch.start("FROZEN/UNFROZEN shop items");

        Integer status = frozen
                ? ItemStatus.FROZEN.getValue()
                : ItemStatus.OFF_SHELF.getValue();

        try {
            itemDao.adminUpdateStatusCheckShopId(shopId, tenantId, status, updatedBy);
            skuDao.adminUpdateStatusCheckShopId(shopId, tenantId, status, updatedBy);
        } finally {
            stopWatch.stop();
            if (log.isInfoEnabled()) {
                log.info(stopWatch.prettyPrint());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Set<Long> updateItemSapId(Long itemId, String sapId, Integer tenantId, String updatedBy) {

        //TODO  代码实现于之前方法冲突 先注释掉
//        Set<Long> skuIds = new HashSet<>();
//
//        Boolean isOk;
//
//        Item vo = itemDao.findById(itemId);
//        isOk = itemDao.updateSapId(itemId,vo.getVersion(),sapId,tenantId,updatedBy);
//        if (!isOk) {
//            throw new ServiceException("item.sapId.status.fail");
//        }
//
//        if (log.isDebugEnabled()) {
//            log.debug("[ITEM UPDATE]  Successfully(TRA) update item sapId by id: {}", vo.getId());
//        }
//
//        List<Sku> skuList = skuDao.findByItemId(itemId,tenantId);
//
//        if (CollectionUtils.isEmpty(skuList)) {
//            log.error("[ITEM UPDATE] Data error for item missing sku by id: {}!", vo.getId());
//            throw new ServiceException("data.error.for.missing.sku");
//        }
//
//        for(Sku sku : skuList){
//            isOk = skuDao.updateSapId(sku.getId(),sku.getVersion(),sapId,tenantId,updatedBy);
//            if (!isOk) {
//                throw new ServiceException("sku.update.sapId.fail");
//            }
//            skuIds.add(sku.getId());
//        }
//
//        return skuIds;
        return null;
    }

    @Transactional
    public Boolean updateAreaAndChannelByVendor(Long itemId, Set<Long> operatorIdSet, Integer version, String salesArea, String salesChannel, Integer tenantId, String updatedBy) {

        Boolean isOk = Boolean.TRUE;

        if (operatorIdSet != null && operatorIdSet.size() > 0) {
            isOk = areaItemDao.updateAreaAndChannel(itemId, operatorIdSet, salesArea, salesChannel, tenantId, updatedBy);
            Assert.isTrue(isOk, "area.item.update.fail");
        }

        isOk = itemDao.updateAreaAndChannel(itemId, version, salesArea, salesChannel, tenantId, updatedBy);
        Assert.isTrue(isOk, "item.update.fail");

        return isOk;
    }

    public void createVendorItemChannlAndSku(FullItemOperateBO bo) {
        Item item = bo.getItem();
        // 提炼渠道
        Set<Long> channelSet = bo.getVendorItemChannlRelationModels().stream().map(VendorItemChannlRelationModel::getChannelId).collect(Collectors.toSet());
//        List<Sku> skuList = bo.getToCreateSkuList();
        List<VendorItemChannlRelationModel> modelList = new ArrayList<>();
        for (Long channelId : channelSet) {
            VendorItemChannlRelationModel vendorItemChannlRelationModel = new VendorItemChannlRelationModel();
            vendorItemChannlRelationModel.setVendorId(item.getShopId());
            vendorItemChannlRelationModel.setItemId(item.getId());
            vendorItemChannlRelationModel.setChannelId(channelId);
            vendorItemChannlRelationModel.setIsDelete(0);
            vendorItemChannlRelationModel.setId(idGenerator.nextValue(VendorItemChannlRelationModel.class));
            vendorItemChannlRelationModel.setCreatedAt(new Date());
            vendorItemChannlRelationModel.setUpdatedAt(new Date());
            modelList.add(vendorItemChannlRelationModel);
        }

        boolean b = vendorItemChannlRelationDao.creates(modelList) == modelList.size();
        Assert.isTrue(b, "vendorItemChannlRelationDao.create.fail");

        // 商品转map
//        Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getKey, Function.identity()));
        Map<Long, VendorItemChannlRelationModel> modelMap = modelList.stream().collect(Collectors.toMap(VendorItemChannlRelationModel::getChannelId, Function.identity()));
        List<VendorItemChannlSkuRelationModel> vendorItemChannlSkuRelationModels = new ArrayList<>();
        // 商品渠道供应商关系表
        for (VendorItemChannlRelationModel vendorItemChannlRelationModel : bo.getVendorItemChannlRelationModels()) {
            for (VendorItemChannlSkuRelationModel x : vendorItemChannlRelationModel.getVendorItemChannlSkuRelationModelList()) {
                VendorItemChannlSkuRelationModel model = new VendorItemChannlSkuRelationModel();
                model.setVendorItemChannlRelationId(modelMap.get(x.getChannelId()).getId());
                model.setChannelId(x.getChannelId());
                model.setChannelPrice(x.getChannelPrice());
                model.setIsDelete(0);
                model.setCreatedAt(new Date());
                model.setUpdatedAt(new Date());
                model.setId(idGenerator.nextValue(VendorItemChannlSkuRelationModel.class));
//                Sku sku = skuMap.get(x.getKey());
                model.setSkuId(x.getSkuId());
                vendorItemChannlSkuRelationModels.add(model);
            }
        }


        boolean b1 = vendorItemChannlSkuRelationDao.creates(vendorItemChannlSkuRelationModels) == vendorItemChannlSkuRelationModels.size();
        Assert.isTrue(b1, "vendorItemChannlRelationDao.create.fail");
    }

    @Transactional
    public Boolean updateZqPushStatus(Long itemId, Integer zqPushStatus, String remarks, Integer version, Integer tenantId, String updatedBy,
                                      Item itemVO, ItemDetail itemDetailVo, boolean isRevoker) {

        Boolean isOk = false;

        if (null == zqPushStatus) {
            return isOk;
        }

        if (null != zqPushStatus && ItemZqPushlStatusEnum.ZQ_PUSH_STATUS_RUN.getValue() == zqPushStatus.intValue() && !isRevoker) {

            if (null != itemVO) {
                isOk = itemDao.update(itemVO);
                Assert.isTrue(isOk, "item.update.fail");
                if (log.isDebugEnabled()) {
                    log.debug("[ITEM updateZqPushStatus] Successfully(TRA) update item with: {}", itemVO);
                }
                version = version + 1;
            }

            if (null != itemDetailVo) {
                isOk = itemDetailDao.update(itemDetailVo);
                Assert.isTrue(isOk, "item.detail.update.fail");

                if (log.isDebugEnabled()) {
                    log.debug("[ITEM updateZqPushStatus] Successfully(TRA) update itemDetail with: {}", itemDetailVo);
                }
            }

            itemUpdateLogDao.closeZqItemUpdateLog(itemId);

            if (null != itemVO) {
                //记录商品更新动作
                isOk = itemEventLogHelper.itemUpdateEventLog(itemVO, itemVO.getTenantId());
                Assert.isTrue(isOk, "商品更新日志记录失败！");
            }
        }

        if (isRevoker) {
            itemUpdateLogDao.closeZqItemUpdateLog(itemId);
        }

        isOk = itemDao.updateZqPushStatus(itemId, zqPushStatus, remarks, version, tenantId, updatedBy);
        Assert.isTrue(isOk, "item.update.zqPushStatus.update.fail");

        return isOk;
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Boolean openItemDelete(Set<Long> operatorIdSet, String outerId, Long shopId, Integer tenantId, String updatedBy) {

        boolean isOk;
        Item itemInfo = itemDao.openFindByOutId(outerId, tenantId);

        if(ObjectUtils.isEmpty(itemInfo)){
            log.error("商品信息不存在!");
            throw new ServiceException("item.delete.fail");
        }

        if (!CollectionUtils.isEmpty(operatorIdSet)) {
            areaItemDao.operatorDeleteByItemId(operatorIdSet, shopId, itemInfo.getId());
            areaSkuDao.operatorDeleteByItemId(operatorIdSet, shopId, itemInfo.getId());
        }

        // PART 1: 删除Item
        isOk = itemDao.updateStatusCheckShopId(itemInfo.getId(), itemInfo.getVersion(), ItemStatus.DELETED.getValue(), shopId, tenantId, updatedBy);

        if (!isOk) {
            throw new ServiceException("item.delete.fail");
        }

        if (log.isDebugEnabled()) {
            log.debug("[ITEM DELETE] Successfully(TRA) delete item by id: {}", itemInfo.getId());
        }

        // PART 2: 删除sku
        List<Sku> skuList = skuDao.findByItemId(itemInfo.getId(), tenantId);

        if (CollectionUtils.isEmpty(skuList)) {
            throw new ServiceException("data.error.for.missing.sku");
        }
        for (Sku sku : skuList) {
            isOk = skuDao.updateStatusCheckShopId(sku.getId(), sku.getVersion(), ItemStatus.DELETED.getValue(), shopId, tenantId, updatedBy);

            if (!isOk) {
                throw new ServiceException("sku.delete.fail");
            }

            if (log.isDebugEnabled()) {
                log.debug("[ITEM DELETE] Successfully(TRA) delete sku by id: {}", sku.getId());
            }
        }

        return isOk;
    }


    @Transactional
    public Boolean batchCreateItem(FullItemOpenCreateBO fullItemOpenCreateBO){
        Item item = fullItemOpenCreateBO.getItem();
        ItemDetail itemDetail = fullItemOpenCreateBO.getItemDetail();
        List<Sku> skus = fullItemOpenCreateBO.getSkus();

        String logoUrl = "https://eabbc-test.oss-cn-beijing.aliyuncs.com/images/8834e6d4-0a31-44c3-ad25-b50ed9234051.png";
        String name = item.getBrandName();
        Brand brand = brandDAO.findByOnlyName(name.toLowerCase());
        if(Objects.isNull(brand)){
            brand = new Brand();
            brand.setId(idGenerator.nextValue(Brand.class));
            brand.setStatus(1);
            brand.setExtensionType(0);
            brand.setName(name.trim());
            brand.setUniqueName(name.toLowerCase());
            brand.setLogo(logoUrl);
            brand.setCreatedAt(new Date());
            brand.setUpdatedAt(new Date());
            Boolean flag = brandDAO.create(brand);
            if (!flag) {
                Assert.isTrue(flag, "brand.create.fail");
            }
            item.setBrandId(brand.getId());
        }

        // PART 1: 创建item对象
        Boolean isOk = itemDao.create(item);
        Assert.isTrue(isOk, "item.create.fail");
        // PART 2: 创建itemDetail对象
        isOk = itemDetailDao.create(itemDetail);
        Assert.isTrue(isOk, "item.detail.create.fail");
        // PART 3: 创建sku对象集合
        if(CollectionUtil.isNotEmpty(skus)){
            for (Sku sku : skus) {
                isOk = skuDao.create(sku);
                Assert.isTrue(isOk, "sku.create.fail");

                if (log.isDebugEnabled()) {
                    log.debug("[ITEM CREATE] Successfully(TRA) create sku with: {}", sku);
                }
            }
        }
        // PART 4: 区域商品信息与渠道价创建
        List<AreaItem> areaItemList = fullItemOpenCreateBO.getAreaItemList();
        List<AreaSku> areaSkuList = fullItemOpenCreateBO.getAreaSkuList();
        // PART 4.1: 区域商品信息创建
        if (CollectionUtil.isNotEmpty(areaItemList)) {
            isOk = areaItemDao.creates(areaItemList) == areaItemList.size();
            Assert.isTrue(isOk, "area.item.create.fail");

            for (AreaItem areaItem : areaItemList) {
                if(Objects.isNull(brand)){
                    ItemBrand model = new ItemBrand();
                    model.setId(idGenerator.nextValue(ItemBrand.class));
                    model.setIsExisting(0);
                    model.setBrandCode(brand.getId());
                    model.setBrandName(name);
                    model.setImageUrl(logoUrl);
                    model.setAuditStatus(2);
                    model.setDelFlag(0);
                    model.setOperatorId(areaItem.getOperatorId());
                    model.setTenantId(Long.valueOf(item.getTenantId().toString()));
                    model.setUpdatedAt(new Date());
                    model.setCreatedAt(new Date());
                    model.setUpdatedBy("admin");
                    model.setCreatedBy("admin");

                    Boolean isSuccess = itemBrandDao.create(model);
                    if (!isSuccess) {
                        Assert.isTrue(isOk, "brand.audit.create.fail");
                    }
                }
            }
        }
        // PART 4.2: 区域商品sku信息创建
        if (CollectionUtil.isNotEmpty(areaSkuList)) {
            isOk = areaSkuDao.creates(areaSkuList) == areaSkuList.size();
            Assert.isTrue(isOk, "area.sku.create.fail");
        }

        for (ItemStockInit itemStockInit : fullItemOpenCreateBO.getToCreateItemStockInit()) {
            isOk = itemStockInit(itemStockInit.getVendorId(), itemStockInit.getOperatorIds(), itemStockInit.getSkuId(), itemStockInit.getStock());
            Assert.isTrue(isOk, "itemStockInit.is.fail");
        }

        if (ObjectUtil.isNotEmpty(fullItemOpenCreateBO.getToCreateSaleAttributeApproval())) {
            List<SaleAttributeApprovalModel> model = saleAttributeApprovalApiConverter.getlist(fullItemOpenCreateBO.getToCreateSaleAttributeApproval());
            Date date = new Date();
            for (SaleAttributeApprovalModel info : model) {
                info.setAuditStatus(1);
                info.setDelFlag(0);
                info.setId(idGenerator.nextValue(SaleAttributeApprovalModel.class));
                info.setCreateTime(date);
                info.setUpdateTime(date);
            }
            isOk = saleAttributeApprovalDao.creates(model) == fullItemOpenCreateBO.getToCreateSaleAttributeApproval().size();
            Assert.isTrue(isOk, "itemStockInit.is.fail");
        }

        if (ObjectUtil.isNotEmpty(fullItemOpenCreateBO.getToCreateCategoryAttributeBinding())) {
            for (CategoryAttributeBinding categoryAttributeBinding : fullItemOpenCreateBO.getToCreateCategoryAttributeBinding()) {
                isOk = categoryAttributeManager.createBinding(categoryAttributeBinding);
                Assert.isTrue(isOk, "itemStockInit.is.fail");
            }
        }

        if (!CollectionUtils.isEmpty(fullItemOpenCreateBO.getVendorItemChannlRelationModels())) {
            // 提炼渠道
            Set<Long> channelSet = fullItemOpenCreateBO.getVendorItemChannlRelationModels().stream().map(VendorItemChannlRelationModel::getChannelId).collect(Collectors.toSet());
            Date date = new Date();
            List<VendorItemChannlRelationModel> modelList = new ArrayList<>();
            for (Long channelId : channelSet) {
                VendorItemChannlRelationModel vendorItemChannlRelationModel = new VendorItemChannlRelationModel();
                vendorItemChannlRelationModel.setVendorId(item.getShopId());
                vendorItemChannlRelationModel.setItemId(item.getId());
                vendorItemChannlRelationModel.setChannelId(channelId);
                vendorItemChannlRelationModel.setIsDelete(0);
                vendorItemChannlRelationModel.setId(idGenerator.nextValue(VendorItemChannlRelationModel.class));
                vendorItemChannlRelationModel.setCreatedAt(date);
                vendorItemChannlRelationModel.setUpdatedAt(date);
                modelList.add(vendorItemChannlRelationModel);
            }

            isOk = vendorItemChannlRelationDao.creates(modelList) == modelList.size();
            if (!isOk) {
                throw new ServiceException("vendorItemChannlRelationDao.create.fail");
            }

            // 商品转map
            Map<Long, Sku> skuMap = skus.stream().collect(Collectors.toMap(Sku::getKey, Function.identity()));
            Map<Long, VendorItemChannlRelationModel> modelMap = modelList.stream().collect(Collectors.toMap(VendorItemChannlRelationModel::getChannelId, Function.identity()));
            List<VendorItemChannlSkuRelationModel> vendorItemChannlSkuRelationModels = new ArrayList<>();
            // 商品渠道供应商关系表
            for (VendorItemChannlRelationModel vendorItemChannlRelationModel : fullItemOpenCreateBO.getVendorItemChannlRelationModels()) {
                for (VendorItemChannlSkuRelationModel x : vendorItemChannlRelationModel.getVendorItemChannlSkuRelationModelList()) {
                    VendorItemChannlSkuRelationModel model = new VendorItemChannlSkuRelationModel();
                    model.setVendorItemChannlRelationId(modelMap.get(x.getChannelId()).getId());
                    model.setChannelId(x.getChannelId());
                    model.setChannelPrice(x.getChannelPrice());
                    model.setIsDelete(0);
                    model.setCreatedAt(date);
                    model.setUpdatedAt(date);
                    model.setId(idGenerator.nextValue(VendorItemChannlSkuRelationModel.class));
                    Sku sku = skuMap.get(x.getKey());
                    model.setSkuId(sku.getId());
                    vendorItemChannlSkuRelationModels.add(model);
                }
            }

            isOk = vendorItemChannlSkuRelationDao.creates(vendorItemChannlSkuRelationModels) == vendorItemChannlSkuRelationModels.size();
            if (!isOk) {
                throw new ServiceException("vendorItemChannlSkuRelationDao.create.fail");
            }
        }

        return isOk;
    }

    /**
     * 商品库存初始化
     *
     * @param vendorId    供应商id
     * @param operatorIds 当前商品发布的区运营id集合
     */
    public boolean itemStockInit(Long vendorId, Set<Long> operatorIds, String skuId, Long stock) {
        VendorPartnershipQueryBO queryBO = VendorPartnershipQueryBO.builder()
                .vendorId(vendorId)
                .operatorIdList(operatorIds)
                .status(VendorPartnershipStatus.AVAILABLE.getStatus())
                .build();
        List<VendorPartnership> vendorPartnershipList =  vendorPartnershipDao.findByConditions(queryBO.toMap());

        //过滤出当前商品的绑定关系信息
        List<VendorPartnership> list = vendorPartnershipList.stream().filter(f -> operatorIds.contains(f.getOperatorId())).collect(Collectors.toList());
        List<ThirdInventoryAdjustParam> params = list.stream().map(li -> {
            ThirdInventoryAdjustParam setParam = new ThirdInventoryAdjustParam();
            setParam.setEntityId(skuId);
            setParam.setEntityType(1);
            setParam.setRealQuantity(stock);
            setParam.setSafeQuantity(0L);
            setParam.setWarehouseType(100);
            setParam.setWarehouseCode(li.getWarehouseCode());
            setParam.setOperatorId(li.getOperatorId());
            setParam.setVendorId(li.getVendorId());
            return setParam;
        }).collect(Collectors.toList());
        log.info("params.size():::::::::::::::::" + params.size());
        ThirdInventoryAdjustRequest request = new ThirdInventoryAdjustRequest();
        request.setThirdInventoryAdjustParams(params);
        request.setTenantId(1);
        log.info("request.getThirdInventoryAdjustParams().size():::::::::::::::::" + request.getThirdInventoryAdjustParams().size());
        inventoryWriteApi.adjust(request);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateItemCategoryId(Item datum, ItemDetail itemDetail) {
        boolean isOk= true;
        isOk = itemDao.updateItem(datum);
        Assert.isTrue(isOk, "商品更新失败！");

        List<Sku> byItemId = skuDao.findByItemId(datum.getId(), datum.getTenantId());
        for (Sku sku: byItemId){
            sku.setName(datum.getName());
            sku.setImage(datum.getMainImage());
            isOk = skuDao.update(sku);
            Assert.isTrue(isOk, "SKU更新失败！");
        }

        if (itemDetail != null) {
            ItemDetail itemDetailDaoByItemId = itemDetailDao.findByItemId(datum.getId(), datum.getTenantId());
            itemDetailDaoByItemId.setImageJson(itemDetail.getImageJson());
            itemDetailDaoByItemId.setPcDetail(itemDetail.getPcDetail());
            itemDetailDaoByItemId.setWapDetail(itemDetail.getWapDetail());
            isOk = itemDetailDao.update(itemDetailDaoByItemId);
        }
        Assert.isTrue(isOk, "商品详情更新失败！");
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Long openCreatePlus(FullItemOperateBO bo, Map<Integer, Object> paramMap) {
        // 前置扩展
        beforeCreateExtension(bo);

        Item item = bo.getItem();
        List<Sku> skuList = bo.getToCreateSkuList();
        ItemDetail itemDetail = bo.getItemDetail();

        Boolean isOk;

        // PART 1: 创建item对象
        isOk = itemDao.create(item);
        Assert.isTrue(isOk, "item.create.fail");

        // PART 2: 创建sku对象集合
        for (Sku sku : skuList) {
            isOk = skuDao.create(sku);
            Assert.isTrue(isOk, "sku.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM CREATE] Successfully(TRA) create sku with: {}", sku);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("[ITEM CREATE] Successfully(TRA) create item with: {}", item);
        }

        if (!CollectionUtils.isEmpty(bo.getVendorItemChannlRelationModels())) {
            // 提炼渠道
            Set<Long> channelSet = bo.getVendorItemChannlRelationModels().stream().map(VendorItemChannlRelationModel::getChannelId).collect(Collectors.toSet());
            Date date = new Date();
            List<VendorItemChannlRelationModel> modelList = new ArrayList<>();
            for (Long channelId : channelSet) {
                VendorItemChannlRelationModel vendorItemChannlRelationModel = new VendorItemChannlRelationModel();
                vendorItemChannlRelationModel.setVendorId(item.getShopId());
                vendorItemChannlRelationModel.setItemId(item.getId());
                vendorItemChannlRelationModel.setChannelId(channelId);
                vendorItemChannlRelationModel.setIsDelete(0);
                vendorItemChannlRelationModel.setId(idGenerator.nextValue(VendorItemChannlRelationModel.class));
                vendorItemChannlRelationModel.setCreatedAt(date);
                vendorItemChannlRelationModel.setUpdatedAt(date);
                modelList.add(vendorItemChannlRelationModel);
            }

            boolean b = vendorItemChannlRelationDao.creates(modelList) == modelList.size();
            if (!b) {
                throw new ServiceException("vendorItemChannlRelationDao.create.fail");
            }

            // 商品转map
            Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getKey, Function.identity()));
            Map<Long, VendorItemChannlRelationModel> modelMap = modelList.stream().collect(Collectors.toMap(VendorItemChannlRelationModel::getChannelId, Function.identity()));
            List<VendorItemChannlSkuRelationModel> vendorItemChannlSkuRelationModels = new ArrayList<>();
            // 商品渠道供应商关系表
            for (VendorItemChannlRelationModel vendorItemChannlRelationModel : bo.getVendorItemChannlRelationModels()) {
                for (VendorItemChannlSkuRelationModel x : vendorItemChannlRelationModel.getVendorItemChannlSkuRelationModelList()) {
                    VendorItemChannlSkuRelationModel model = new VendorItemChannlSkuRelationModel();
                    model.setVendorItemChannlRelationId(modelMap.get(x.getChannelId()).getId());
                    model.setChannelId(x.getChannelId());
                    model.setChannelPrice(x.getChannelPrice());
                    model.setIsDelete(0);
                    model.setCreatedAt(date);
                    model.setUpdatedAt(date);
                    model.setId(idGenerator.nextValue(VendorItemChannlSkuRelationModel.class));
                    Sku sku = skuMap.get(x.getKey());
                    model.setSkuId(sku.getId());
                    vendorItemChannlSkuRelationModels.add(model);
                }
            }

            boolean b1 = vendorItemChannlSkuRelationDao.creates(vendorItemChannlSkuRelationModels) == vendorItemChannlSkuRelationModels.size();
            if (!b1) {
                throw new ServiceException("vendorItemChannlSkuRelationDao.create.fail");
            }
        }

        // PART 3: 创建itemDetail对象
        isOk = itemDetailDao.create(itemDetail);
        Assert.isTrue(isOk, "item.detail.create.fail");

        if (log.isDebugEnabled()) {
            log.debug("[ITEM CREATE] Successfully(TRA) create item detail with: {}", itemDetail);
        }

        // PART 4: 写入维度扩展信息
        if (!CollectionUtils.isEmpty(bo.getItemDimensionList())) {
            Integer creates = itemDimensionDao.creates(bo.getItemDimensionList());

            Assert.isTrue(creates != null, "item.dimension.create.fail");

            if (log.isDebugEnabled()) {
                log.debug("[ITEM CREATE] Successfully(TRA) create item dimension with: {}", bo.getItemDimensionList());
            }
        }

        // 后置扩展
        onCreateExtension(bo);

        // 位标事务处理
        isOk = bitTagProcessLogic.transactional(paramMap);
        Assert.isTrue(isOk, "bit.tag.process.fail");

        //记录商品创建动作
        isOk = itemEventLogHelper.itemCreateEventLog(item, item.getTenantId());
        Assert.isTrue(isOk, "商品创建日志记录失败！");

        return item.getId();
    }

    @Transactional
    public void updateItemStatus(Map<Integer,List<Long>> itemMap, Set<Long> operatorIds){
        if(CollectionUtil.isEmpty(itemMap)){
            log.error("无修改状态商品信息！");
        }
        for (Integer status : itemMap.keySet()) {
            List<Long> itemIds = itemMap.get(status);
            if(status == -3){
                itemDao.batchUpdateStatus(itemIds,status);
                List<Sku> skuList = skuDao.findByItemIdSet(new HashSet<>(itemIds));
                if(CollectionUtil.isNotEmpty(skuList)){
                    skuDao.batchUpdateStatus(skuList.stream().map(BaseSku::getId).collect(Collectors.toList()),status);
                }
                choiceLotLibItemDao.batchUpdateStatus(itemIds,1);
            }
            for (Long operatorId : operatorIds) {
                areaItemDao.batchUpdateAreaItemStatus(operatorId, status.toString(), itemIds, "system");
                areaSkuDao.batchUpdateAreaItemStatus(operatorId, status.toString(), itemIds, "system");
            }
        }
    }

    /**
     * 通过压缩文件更新商品图片
     *
     * @param itemImportImageBO
     * @return
     */
    public Boolean updateImageForZip(ItemImportImageBO itemImportImageBO) {
        Date currentDate = new Date();
        List<ItemImportImage> itemImportImageList = itemImportImageBO.getItemImportImageList();
        List<ItemImportImageFail> itemImportImageFailList = itemImportImageBO.getItemImportImageFailList();
        List<ItemImportImage> successList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemImportImageList)) {
            for (ItemImportImage itemImportImage : itemImportImageList) {
                Item item = itemDao.findById(itemImportImage.getItemId(), itemImportImageBO.getTenantId());
                if (Objects.isNull(item)) {
                    ItemImportImageFail fail = new ItemImportImageFail();
                    fail.setItemId(itemImportImage.getItemId());
                    fail.setSkuCode(String.valueOf(itemImportImage.getSkuId()));
                    fail.setRemark("商品信息不存在");
                    itemImportImageFailList.add(fail);
                    continue;
                }

                ItemDetail itemDetail = itemDetailDao.findByItemId(itemImportImage.getItemId(), itemImportImageBO.getTenantId());
                if (Objects.isNull(itemDetail)) {
                    ItemImportImageFail fail = new ItemImportImageFail();
                    fail.setItemId(itemImportImage.getItemId());
                    fail.setSkuCode(String.valueOf(itemImportImage.getSkuId()));
                    fail.setRemark("商品详细信息不存在");
                    itemImportImageFailList.add(fail);
                    continue;
                }

                successList.add(itemImportImage);
                item.setMainImage(itemImportImage.getMainImage());
                item.setUpdatedAt(currentDate);
                item.setUpdatedBy(itemImportImageBO.getUpdatedBy());
                itemDetail.setImageJson(CollectionUtils.isEmpty(itemImportImage.getImages()) ? "[]" : JSON.toJSONString(itemImportImage.getImages()));
                itemDetail.setPcDetail(itemImportImage.getPcDetail());
                itemDetail.setWapDetail(itemImportImage.getWapDetail());
                itemDetail.setUpdatedAt(currentDate);
//                itemDetail.setUpdatedBy(itemImportImageBO.getUpdatedBy());
//                itemDetail.setUpdatedName(itemImportImageBO.getUpdatedName());
                itemDao.update(item);
                itemDetailDao.update(itemDetail);
            }

            itemImportImageBO.setItemImportImageList(successList);
        }
        return true;
    }

    public Boolean freezeItemStatus(Long shopId,Integer status){
        Boolean flag = Boolean.TRUE;
        List<Item> list = itemDao.findByShopIdItems(shopId);
        List<Long> ids = new ArrayList<>();
        for (Item item : list) {
            ids.add(item.getId());
        }
        if (!CollectionUtils.isEmpty(ids)) {
            flag = itemDao.batchUpdateStatus(ids,status);
            if (flag){
                skuDao.batchUpdateStatus(ids,status);
                areaItemDao.updateItemStatus(ids,status);
                areaSkuDao.updateItemStatus(ids,status);
            }
        }
        if (flag) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 旺店通推送平台货品标识
     *
     * @param itemId
     * @return
     */
    public Boolean updateItemForWdt(Long  itemId ,Integer  tenantId,String isWdtCreate) {

        Item item = itemDao.findById(itemId, tenantId);
        if(ObjectUtils.isEmpty(item)){
            log.error("商品信息不存在!");
            throw new ServiceException("item.updateItemForWdt.fail");
        }
        Map<String, String> extra = item.getExtra();
        if(extra == null){
            extra = new HashMap<>();
        }
        extra.put("isWdtPush",isWdtCreate);
        Item itemParam = new Item();
        itemParam.setId(item.getId());
        itemParam.setTenantId(item.getTenantId());
        itemParam.setVersion(item.getVersion());
        itemParam.setExtra(extra);
        Boolean isOk =  itemDao.update(itemParam);
        Assert.isTrue(isOk, "商品创建日志记录失败！");
        return isOk;
    }
}
