package io.terminus.parana.item.cronjob.api.convert;

import io.terminus.parana.item.cronjob.api.bean.dto.CronJobDTO;
import io.terminus.parana.item.cronjob.model.CronJob;
import org.mapstruct.Mapper;

/**
 * dto到domain的转换
 *
 * <AUTHOR> xlt
 * @since : 2019-3-26
 */
@Mapper(componentModel = "spring")
public interface CronJobConverter {

    CronJobDTO domain2dto(CronJob cronJob);

    CronJob dto2domain(CronJobDTO cronJobDTO);
}
