package io.terminus.parana.item.footprint.util;

import com.google.common.collect.Lists;
import io.terminus.parana.item.footprint.enums.FootprintStatus;
import io.terminus.parana.item.footprint.model.Footprint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Tuple;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 足迹工具类
 *
 * <AUTHOR>
 * @since 2019-07-02
 */
@Slf4j
public class FootprintUtils {

    public static final String FOOTPRINT_NAMESPACE = "FootPrint:";
    public static final Double REDIS_SCORE_MIN = 1000000000d;
    public static final Double REDIS_SCORE_MAX = 10000000000d;

    /**
     * redis key处理用
     */
    public static String getRedisKeyWithNamespace(Integer tenantId, Long userId, Integer targetType, Long operatorId) {
        return String.format("%s%d_%d_%d_%d", FOOTPRINT_NAMESPACE, tenantId, userId, targetType, operatorId);
    }

//    public static String getRedisKeyWithNamespace(String key) {
//        if (StringUtils.isEmpty(key)) {
//            throw new ServiceException("illegal.redis.key");
//        }
//        return FOOTPRINT_NAMESPACE + key;
//    }

    /**
     * redis足迹用
     * <p>
     * 输出时间戳 格式hhmmss
     */
    public static Long getTimeStamp() {
        return LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 时间戳转DATE
     * <p>
     * dateStamp:yyyymmdd
     * timeStamp:hhmmss
     */
    public static Date getDateFromTimeStamp(Long timeStamp) {
        return Date.from(LocalDateTime.ofInstant(Instant.ofEpochSecond(timeStamp), ZoneId.systemDefault())
                .atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * redis数据结构转标准model
     */
    public static List<Footprint> redisDataToModel(Integer tenantId, Long userId, Integer targetType,Long currentOperatorId,
                                                   Set<Tuple> membersWithScore) {
        List<Footprint> footprintList = Lists.newArrayList();

        if (CollectionUtils.isEmpty(membersWithScore)) {
            return footprintList;
        }

        /**
         * redis数据结构
         * key: namespace + tenantId + userId + type
         * member: itemId + operatorId
         * score: datestamp.timestamp [yyyymmdd.hhmmss]
         */
        membersWithScore.forEach(member -> {
            String footprintValue = new String(member.getBinaryElement());
            String[] valueList = footprintValue.split("_");

            if(valueList.length != 2) {
                log.warn("footprint member fail to convert, footprintValue = {}", footprintList);
                return;
            }

            Long score = new Double(member.getScore()).longValue();
            String keyValue = valueList[0];
            String targetId = null;
            Long choiceLotLibId = null;
            if(keyValue.indexOf("@")>0){
                String[] libIdList = keyValue.split("@");
                targetId = libIdList[0];
                choiceLotLibId = Long.valueOf(libIdList[1]);
            }else{
                targetId =  keyValue;
            }
            Footprint footprint = new Footprint();
            footprint.setTenantId(tenantId);
            footprint.setTargetId(Long.valueOf(targetId));
            footprint.setTargetType(targetType);
            footprint.setOperatorId(Long.valueOf(valueList[1]));
            footprint.setCurrentOperatorId(currentOperatorId);
            footprint.setUserId(userId);
            footprint.setVisitAt(getDateFromTimeStamp(score));
            footprint.setStatus(FootprintStatus.NORMAL.getValue());
            footprint.setChoiceLotLibId(choiceLotLibId);
            footprintList.add(footprint);
        });

        return footprintList;

    }


    /**
     * String左侧或右侧填充0
     */
    public static String addZeroForNum(String str, int strLength, boolean left) {
        int strLen = str.length();
        StringBuffer sb;
        while (strLen < strLength) {
            sb = new StringBuffer();
            if (left) {
                // 左补0
                sb.append("0").append(str);
            } else {
                // 右补0
                sb.append(str).append("0");
            }
            str = sb.toString();
            strLen = str.length();
        }
        return str;
    }
}
