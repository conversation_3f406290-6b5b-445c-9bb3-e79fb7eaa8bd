package io.terminus.parana.item.category.api.converter;

import io.terminus.parana.item.category.api.bean.response.CategoryAttributeInfo;
import io.terminus.parana.item.category.model.CategoryAttribute;
import io.terminus.parana.item.category.model.CategoryWithAttribute;
import io.terminus.parana.item.common.converter.BaseConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @date 2019-03-05
 */
@Mapper(componentModel = "spring")
public interface AttributeConverter extends BaseConverter {

    @Mappings({
            @Mapping(source = "attrMetasJson", target = "attrMetas"),
            @Mapping(source = "attrValsJson", target = "attrVals")
    })
    CategoryAttributeInfo domain2dto(CategoryAttribute domain);

    default CategoryAttributeInfo domain2dto(CategoryWithAttribute domain) {
        if (null == domain) {
            return null;
        }
        CategoryAttributeInfo model = new CategoryAttributeInfo();
        model.setId(domain.getAttributeId());
        model.setCategoryId(domain.getCategoryId());
        model.setAttrKey(domain.getAttribute().getAttrKey());
        model.setGroup(domain.getGroup());
        model.setIndex(domain.getIndex());
        model.setExtraJson(domain.getAttribute().getExtraJson());
        model.setStatus(domain.getStatus());
        model.setIsExtended(false);
        model.setAttrMetas(json2mapOfString(domain.getAttribute().getAttrMetasJson()));
        model.setAttrVals(json2listOfString(domain.getAttribute().getAttrValsJson()));
        return model;
    }
}
