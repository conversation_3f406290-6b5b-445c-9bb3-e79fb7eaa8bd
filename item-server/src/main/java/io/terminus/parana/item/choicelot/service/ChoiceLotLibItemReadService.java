package io.terminus.parana.item.choicelot.service;

import com.google.common.collect.Maps;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibItemModel;
import io.terminus.parana.item.choicelot.model.bo.ChoiceLotLibItemTotalBO;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibItemDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ChoiceLotLibItemReadService {

    private final ChoiceLotLibItemDao choiceLotLibItemDao;

    public ChoiceLotLibItemModel findChoiceItemByChoiceId(Long operatorId, Long itemId, Long choiceLotLibId, Long delBatchNo) {
        return choiceLotLibItemDao.findChoiceItemByChoiceId(operatorId, itemId, choiceLotLibId, delBatchNo);
    }

    public List<ChoiceLotLibItemTotalBO> findItemNumTotalByChoiceId(Set<Long> choiceLotLibId) {
        return choiceLotLibItemDao.findItemNumTotalByChoiceId(choiceLotLibId);
    }

    public List<ChoiceLotLibItemModel> findCanalChoiceItemByChoiceId(Long operatorId, Long itemId, Long choiceLotLibId, Long delBatchNo) {
        return choiceLotLibItemDao.findCanalChoiceItemByChoiceId(operatorId, itemId, choiceLotLibId, delBatchNo);
    }


    public List<ChoiceLotLibItemModel> pageFindByCriteria(Long choiceLotLibId, Integer offset, Integer limit, Set<Long> itemIds, Long delBatchNo) {
        Map<String, Object> criteriaMap = Maps.newHashMap();
        criteriaMap.put("choiceLotLibId", choiceLotLibId);
        criteriaMap.put("itemIds", itemIds);
        criteriaMap.put("delBatchNo", delBatchNo);
        criteriaMap.put("offset", offset);
        criteriaMap.put("limit", limit);
        return choiceLotLibItemDao.pageFindByCriteria(criteriaMap);
    }

    public List<ChoiceLotLibItemModel> listFindByCriteria(Long choiceLotLibId, List<Long> itemIds, Long delBatchNo) {
        Map<String, Object> criteriaMap = Maps.newHashMap();
        criteriaMap.put("choiceLotLibId", choiceLotLibId);
        criteriaMap.put("delBatchNo", delBatchNo);
        criteriaMap.put("itemIds", CollectionUtils.isEmpty(itemIds) ? null : new HashSet<>(itemIds));
        return choiceLotLibItemDao.listFindByCriteria(criteriaMap);
    }

    public List<ChoiceLotLibItemModel> list(Map<String, Object> criteriaMap) {
        return choiceLotLibItemDao.listFindByCriteria(criteriaMap);
    }

    public List<ChoiceLotLibItemModel> listAll() {
        return choiceLotLibItemDao.listAll();
    }


    public List<ChoiceLotLibItemModel> listFindByCriteriaList(Long choiceLotLibId, List<Long> itemIds, Integer source) {
        Map<String, Object> criteriaMap = Maps.newHashMap();
        criteriaMap.put("choiceLotLibId", choiceLotLibId);
        criteriaMap.put("itemIds", itemIds);
        criteriaMap.put("source", source);
        return  choiceLotLibItemDao.listFindByCriteria(criteriaMap);
    }

    public List<ChoiceLotLibItemModel> getCreateTimeByChoiceLotLibId(Long choiceLotLibId, List<Long> ids) {
        Map<String, Object> criteriaMap = Maps.newHashMap();
        criteriaMap.put("choiceLotLibId", choiceLotLibId);
        criteriaMap.put("ids", ids);
        return  choiceLotLibItemDao.getCreateTimeByChoiceLotLibId(criteriaMap);
    }

    public List<ChoiceLotLibItemModel> findChoiceLotLibItemListByIds(List<Long> itemIds, List<Long> choiceLotLibId) {
        return choiceLotLibItemDao.findCanalChoiceItemByChoiceIdsAndItemIds(itemIds, choiceLotLibId);
    }

    public List<ChoiceLotLibItemModel> findChoiceItemByChoiceIdsAndItemIds(List<Long> itemIds, List<Long> choiceIds) {
        return choiceLotLibItemDao.findChoiceItemByChoiceIdsAndItemIds(itemIds, choiceIds);
    }

    public Long countForDataReport(Map<String, Object> criteria) {
        return choiceLotLibItemDao.countForDataReport(criteria);
    }
}
