package io.terminus.parana.item.category.util;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 领域模型接口兼容旧版Response转换
 *
 * <AUTHOR>
 * @version 2018-08-20 上午9:45
 */
@Deprecated
public final class CompatWrapper {

    public static <T, E> Response<T> wrap(Response<E> resp, Function<E, T> convert) {
        if (!resp.isSuccess()) {
            return Response.fail(resp.getError());
        }
        if (resp.getResult() == null) {
            return Response.ok(null);
        }
        return Response.ok(convert.apply(resp.getResult()));
    }

    public static <T, E> Response<List<T>> wrapList(Response<List<E>> resp, Function<E, T> convert) {
        if (!resp.isSuccess()) {
            return Response.fail(resp.getError());
        }
        List<E> list = resp.getResult();
        if (null == list || list.isEmpty()) {
            return Response.ok(Collections.emptyList());
        }
        return Response.ok(list.stream().map(convert).collect(Collectors.toList()));
    }

    public static <T, E> Response<Optional<T>> wrapOptional(Response<E> resp, Function<E, T> convert) {
        if (!resp.isSuccess()) {
            return Response.fail(resp.getError());
        }
        if (resp.getResult() == null) {
            return Response.ok(Optional.empty());
        }
        return Response.ok(Optional.of(convert.apply(resp.getResult())));
    }

    public static <T, E> Response<Paging<T>> wrapPaging(Response<Paging<E>> resp, Function<E, T> convert) {
        if (!resp.isSuccess()) {
            return Response.fail(resp.getError());
        }
        Paging<E> paging = resp.getResult();
        if (null == paging || paging.isEmpty()) {
            return Response.ok(Paging.empty());
        }
        return Response.ok(new Paging<>(paging.getTotal(),
                paging.getData().stream().map(convert).collect(Collectors.toList()))
        );
    }
}
