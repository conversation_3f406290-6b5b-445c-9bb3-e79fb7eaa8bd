package io.terminus.parana.item.favorites.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.favorites.enums.UserCommonItemListSource;
import io.terminus.parana.item.favorites.model.UserCommonItemList;
import io.terminus.parana.item.favorites.repository.UserCommonItemListDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserCommonItemListWriteService extends AbsServiceBase {

    private final UserCommonItemListDao userCommonItemListDao;

    /**
     * 新增用户常用商品清单
     *
     * @param userCommonItemList
     * @return
     */
    public Boolean add(UserCommonItemList userCommonItemList) {
        try {
            return userCommonItemListDao.create(userCommonItemList);
        } catch (Exception e) {
            log.error("[UserCommonItemList]add failed, param:{} cause:{}", userCommonItemList, e);
            throw new ServiceException("userCommonItemList.add.failed");
        }
    }

    /**
     * 取消清单
     */
    public Boolean deleteByItems(Long userId, List<Long> ids, Long operatorId, Integer source) {
        Map<String, Object> params = new HashMap<>();
        try {
            params.put("userId", userId);
            params.put("itemIds", ids);
            params.put("operatorId", operatorId);
            params.put("source", source);
            return userCommonItemListDao.deleteByItems(params);
        } catch (Exception e) {
            log.error("[deleteByItems] delete failed, param:{} cause:{}", params, e);
            throw new ServiceException("deleteByItems.delete.failed");
        }
    }

    /**
     * 批量添加
     */
    @Transactional(rollbackFor = Exception.class)
    public int addBatch(List<UserCommonItemList> addList) {
        try {
            if(null == addList || addList.isEmpty()) {
                return -1;
            }
            UserCommonItemList itemList = addList.get(0);
            Map<String, Object> params = new HashMap<>();
            params.put("userId", itemList.getUserId());
            params.put("operatorId", itemList.getOperatorId());
            params.put("itemIds", addList.stream().map(UserCommonItemList::getItemId).collect(Collectors.toList()));

            if(!UserCommonItemListSource.BUY_ITEM.getValue().equals(itemList.getSource())) {//非购买的话，添加来源
                params.put("source", itemList.getSource());
            }
            userCommonItemListDao.deleteByItems(params);
            return userCommonItemListDao.creates(addList);
        } catch (Exception e) {
            log.error("[addBatch] addBatch failed, param:{} cause:{}", addList, e);
            throw new ServiceException("addBatch.add.failed");
        }
    }
}
