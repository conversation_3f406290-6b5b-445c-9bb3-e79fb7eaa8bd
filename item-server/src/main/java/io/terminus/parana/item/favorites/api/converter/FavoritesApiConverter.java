package io.terminus.parana.item.favorites.api.converter;

import io.terminus.parana.item.favorites.api.bean.request.param.FavoritesParam;
import io.terminus.parana.item.favorites.model.Favorites;
import org.mapstruct.Mapper;

/**
 * dto到domain的转换
 *
 * <AUTHOR> xlt
 * @since : 2018-12-17
 */
@Mapper(componentModel = "spring")
public interface FavoritesApiConverter {

    Favorites param2domain(FavoritesParam favoritesParam);

}
