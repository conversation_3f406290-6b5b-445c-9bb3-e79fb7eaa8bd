package io.terminus.parana.item.favorites.cache;

import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.favorites.enums.FavoritesStatus;
import io.terminus.parana.item.favorites.model.Favorites;
import io.terminus.parana.item.favorites.repository.FavoritesDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.util.Pool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static io.terminus.parana.item.favorites.util.FavoritesUtils.favoritesIdAssemble;

@Slf4j
@Component
public class FavoritesCache {

    private static final String NAMESPACE = "Favorites:";
    private static final String TRUE = "_1";
    private static final String FALSE = "_0";
    private static final Integer EXPIRE_SECONDS = 5 * 60;

    @Autowired
    private FavoritesDao favoritesDao;
    @Autowired
    private Pool<Jedis> jedisPool;

    public Boolean checkFavorite(Integer tenantId, Long userId,
                                 Long targetId, Integer targetType, Long operatorId) {
        HashMap<String, Object> params = Maps.newHashMap();
        params.put("tenantId", tenantId);
        params.put("userId", userId);
        params.put("operatorId", operatorId);
        params.put("targetId", targetId);
        params.put("targetType", targetType);
        params.put("status", FavoritesStatus.NORMAL.getValue());
        return checkFavorite(params);
    }

    private Boolean checkFavorite(Map<String, Object> params) {
        String key = getRedisKey(params);
        String member = getRedisMember(params);

        Set<String> s;
        try (Jedis jedis = jedisPool.getResource()) {
            s = jedis.smembers(key);
        } catch (Exception e) {
            throw new ServiceException("[FavoritesCache]redis error:{}", e);
        }

        Boolean r = getResult(s, member);
        if (r != null) {
            return r;
        }

        Favorites favorites = favoritesDao.checkFavorite(params);
        if (!ObjectUtils.isEmpty(favorites)
                && FavoritesStatus.NORMAL.getValue().equals(favorites.getStatus())) {
            r = true;
        } else {
            r = false;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            jedis.sadd(key, setResult(r, member));
            jedis.expire(key, EXPIRE_SECONDS);
        }

        return r;
    }

    public void cacheInvalidate(Long userId, String uniqueKey) {
        try {
            String key = getRedisKey(userId.toString());
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.srem(key, uniqueKey + TRUE, uniqueKey + FALSE);
            }
        } catch (Exception e) {
            log.error("[FavoritesCache]cache invalidate failed", e);
            throw new ServiceException("favorites.redis.error");
        }
    }

    public void cacheInvalidate(Long userId, List<String> uniqueKeys) {
        try {
            String key = getRedisKey(userId.toString());
            try (Jedis jedis = jedisPool.getResource()) {
                try (Pipeline pipeline = jedis.pipelined()) {
                    uniqueKeys.forEach(uniqueKey -> pipeline.srem(key, uniqueKey + TRUE, uniqueKey + FALSE));
                    pipeline.sync();
                }
            }
        } catch (Exception e) {
            log.error("[FavoritesCache]cache invalidate failed", e);
            throw new ServiceException("favorites.redis.error");
        }
    }

    public void cacheInvalidate(Integer tenantId, Long userId,
                                Long targetId, Integer targetType, Long operatorId) {
        HashMap<String, Object> params = Maps.newHashMap();
        params.put("tenantId", tenantId);
        params.put("userId", userId);
        params.put("targetId", targetId);
        params.put("targetType", targetType);
        params.put("status", FavoritesStatus.NORMAL.getValue());
        params.put("operatorId", operatorId);
        cacheInvalidate(params);
    }

    private void cacheInvalidate(Map<String, Object> params) {
        try {
            String key = getRedisKey(params.get("userId").toString());
            String member = getRedisMember(params);
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.srem(key, member + TRUE, member + FALSE);
            }
        } catch (Exception e) {
            log.error("[FavoritesCache]cache invalidate failed", e);
            throw new ServiceException("favorites.redis.error");
        }
    }

    private String setResult(Boolean r, String member) {
        return r ? member + TRUE : member + FALSE;
    }

    private Boolean getResult(Set<String> s, String member) {
        if (s.contains(member + TRUE)) {
            return true;
        }
        if (s.contains(member + FALSE)) {
            return false;
        }
        return null;
    }


    private String getRedisKey(Map<String, Object> params) {
        try {
            return getRedisKey(params.get("userId").toString());
        } catch (Exception e) {
            log.error("[FavoritesCache]get redis key failed", e);
            throw new ServiceException("favorites.redis.error");
        }
    }

    private String getRedisMember(Map<String, Object> params) {
        try {
            return favoritesIdAssemble(
                    params.get("tenantId").toString(),
                    params.get("userId").toString(),
                    params.get("targetId").toString(),
                    params.get("targetType").toString(),
                    params.get("operatorId").toString());
        } catch (Exception e) {
            log.error("[FavoritesCache]get redis member failed:{}, params:{}", e, params);
            throw new ServiceException("favorites.redis.error");
        }
    }

    private String getRedisKey(String userId) {
        try {
            return new StringBuilder()
                    .append(NAMESPACE)
                    .append(userId).toString();
        } catch (Exception e) {
            log.error("[FavoritesCache]get redis key failed", e);
            throw new ServiceException("favorites.redis.error");
        }
    }

}
