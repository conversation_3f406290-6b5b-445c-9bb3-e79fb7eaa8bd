package io.terminus.parana.item.search.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.eascs.user.enterprise.api.bean.request.param.EnterpriseAuthenticationQueryParam;
import com.eascs.user.enterprise.api.bean.response.EnterpriseAuthenticationInfo;
import com.eascs.user.enterprise.api.facade.EnterpriseAuthenticationReadFacade;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.api.utils.StringUtil;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.item.area.api.bean.request.AreaSkuByOperatorIdsAndItemIdsRequest;
import io.terminus.parana.item.area.api.bean.request.AreaSkuQueryByItemRequest;
import io.terminus.parana.item.area.api.bean.response.AreaItemInfo;
import io.terminus.parana.item.area.api.bean.response.AreaSkuInfo;
import io.terminus.parana.item.area.api.facade.AreaSkuReadFacade;
import io.terminus.parana.item.category.manager.BackCategoryService;
import io.terminus.parana.item.category.model.BackCategory;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceLotLibQueryRequest;
import io.terminus.parana.item.choicelot.api.bean.response.ChoiceLotLibInfo;
import io.terminus.parana.item.choicelot.api.facade.ChoiceLotLibReadFacade;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.choicelot.model.DistributorItemLibModel;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibSkuReadService;
import io.terminus.parana.item.choicelot.service.DistributorItemLibReadService;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.common.utils.CollectionObjectUtil;
import io.terminus.parana.item.distributorskulib.model.DistributorSkuLibModel;
import io.terminus.parana.item.distributorskulib.service.DistributorSkuLibReadService;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuAttributeInfo;
import io.terminus.parana.item.item.constant.ItemExtraConstantKey;
import io.terminus.parana.item.item.enums.ItemType;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.model.SkuAttribute;
import io.terminus.parana.item.item.repository.SkuDao;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.plugin.third.api.inventory.api.InventoryReadApi;
import io.terminus.parana.item.plugin.third.api.trade.api.OrderReadApi;
import io.terminus.parana.item.plugin.third.impl.tongka.service.util.TongkaItemUtil;
import io.terminus.parana.item.search.adaptor.ItemComponentAdaptor;
import io.terminus.parana.item.search.client.EASearchClient;
import io.terminus.parana.item.search.context.SearchContext;
import io.terminus.parana.item.search.docobject.ChoiceItemDO;
import io.terminus.parana.item.search.docobject.ChoiceSkuDO;
import io.terminus.parana.item.search.dto.ChoiceFrontItemDTO;
import io.terminus.parana.item.search.dto.ChoiceFrontSkuDTO;
import io.terminus.parana.item.search.dto.SearchedItemWithAggs;
import io.terminus.parana.item.search.enums.SearchIndexQueryEnum;
import io.terminus.parana.item.search.handler.BaseAggregationHandler;
import io.terminus.parana.item.search.handler.BaseChoicePrimarySearchHandler;
import io.terminus.parana.item.search.request.ChoiceItemSearchRequest;
import io.terminus.parana.item.search.request.ChoicePrimarySearchRequest;
import io.terminus.parana.item.search.request.ChoiceSkuMessageRequest;
import io.terminus.parana.item.search.request.ChoiceSkuOrderRequest;
import io.terminus.parana.item.shop.api.bean.request.ShopQueryByIdRequest;
import io.terminus.parana.item.shop.api.bean.request.ShopQueryByNameRequest;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.shop.api.facade.ShopReadFacade;
import io.terminus.parana.item.third.info.InventoryEntityResponse;
import io.terminus.parana.item.third.param.ThirdInventoryQueryByOperatorRequest;
import io.terminus.parana.search.client.SearchClient;
import io.terminus.parana.search.client.result.SearchResult;
import io.terminus.parana.search.client.search.Aggregate;
import io.terminus.parana.search.client.search.SearchRequest;
import io.terminus.parana.search.retrieval.RetrievalQueryService;
import io.terminus.parana.search.retrieval.builder.RetrievalQueryRequestBuilder;
import io.terminus.parana.trade.buy.api.request.ServiceOrderQueryRequest;
import io.terminus.parana.trade.buy.api.request.param.FirmMealParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/16 20:07
 */
@Slf4j
@Service
@AllArgsConstructor
public class ChoiceItemSearchFacadeImpl implements ChoiceItemSearchFacade {
    private final RetrievalQueryService retrievalQueryService;
    private final ItemComponentAdaptor itemComponentAdaptor;
    private final BaseChoicePrimarySearchHandler baseChoicePrimarySearchHandler;
    private final SearchClient searchClient;
    private final EASearchClient eaSearchClient;
    private final RetrievalQueryRequestBuilder requestBuilder;
    private final BaseAggregationHandler aggregationHandler;
    private final OrderReadApi orderReadApi;
    private final ChoiceLotLibReadFacade choiceLotLibReadFacade;
    private final BackCategoryService backCategoryService;
    private final InventoryReadApi inventoryReadApi;
    private final AreaSkuReadFacade areaSkuReadFacade;
    private final SkuReadDomainService skuReadDomainService;
    private final ChoiceLotLibSkuReadService choiceLotLibSkuReadService;
    private final DistributorItemLibReadService distributorItemLibReadService;
    private final DistributorSkuLibReadService distributorSkuLibReadService;

    private final EnterpriseAuthenticationReadFacade enterpriseAuthenticationReadFacade;
    private final ShopReadFacade shopReadFacade;
    private final SkuDao skuDao;

    private final static Response<SearchedItemWithAggs<ChoiceFrontItemDTO>> EMPTY_DATA_RESPONSE = new Response<>();

    @Override
    public <Request extends ChoiceItemSearchRequest, DocObject extends ChoiceItemDO> Response<Paging<DocObject>> search(Request request) {
        log.info("ChoiceItemSearchFacadeImpl.search. request::::::{}", request);
        if (StringUtils.isNotBlank(request.getDistributorName())){
            if(request.getOperatorId() == 1L){
                ShopQueryByNameRequest param = new ShopQueryByNameRequest();
                param.setName(request.getDistributorName());
                param.setType(2);
                param.setTenantId(request.getTenantId());
                ShopInfo shopInfo = Assert.take(shopReadFacade.queryByName(param));
                if (shopInfo == null){
                    Paging<DocObject> docObjectPaging = new Paging<>();
                    docObjectPaging.setTotal(0L);
                    return Response.ok(new Paging<>());
                }
                request.setDistributorIds(shopInfo.getId() + "");
            }else{
                EnterpriseAuthenticationQueryParam param = new EnterpriseAuthenticationQueryParam();
                param.setEnterpriseName(request.getDistributorName());
                param.setTenantId(request.getTenantId());
                List<EnterpriseAuthenticationInfo> enterpriseAuthenticationInfos = Lists.newArrayList();
                ///////////////////////////////////
                Response<List<EnterpriseAuthenticationInfo>> response = enterpriseAuthenticationReadFacade.choiceEnterpriseAuthentication(param);
                if (response.isSuccess()) {
                    enterpriseAuthenticationInfos.addAll(response.getResult());
                }
                ///////////////////////////////////
                if (CollectionUtils.isEmpty(enterpriseAuthenticationInfos)){
                    Paging<DocObject> docObjectPaging = new Paging<>();
                    docObjectPaging.setTotal(0L);
                    return Response.ok(new Paging<>());
                }

                List<Long> enterpriseIds = enterpriseAuthenticationInfos.stream().map(EnterpriseAuthenticationInfo::getId).collect(Collectors.toList());
                String distributorIds = StringUtils.join(enterpriseIds,"_");
                request.setDistributorIds(distributorIds);
            }
        }

        Paging<DocObject> paging = retrievalQueryService.paging(request);
        if (paging.isEmpty()) {
            return Response.ok(paging);
        }
        // 获取分类名称
        Set<Long> categoryIdSet = new HashSet<>();
        paging.getData().forEach(v -> categoryIdSet.addAll(v.getCategoryIds()));
        Map<Long, String> categoryNameMap = itemComponentAdaptor.getCategoryNameMap(categoryIdSet, request.getTenantId());

        Map<Long, ShopInfo> shopMap = Maps.newHashMap();
        Set<Long> sourceOperatorIds = paging.getData().stream().filter(f -> f.getSourceOperatorId() != null).map(DocObject::getSourceOperatorId).collect(Collectors.toSet());
        if(CollectionUtil.isNotEmpty(sourceOperatorIds)){
            ShopQueryByIdRequest param = new ShopQueryByIdRequest();
            param.setIdSet(sourceOperatorIds);
            List<ShopInfo> shopList = Assert.take(shopReadFacade.queryById(param));
            if(CollectionUtil.isNotEmpty(shopList)){
                shopMap = shopList.stream().collect(Collectors.toMap(ShopInfo::getId, Function.identity(), (k1, k2) -> k1));
            }
        }
        List<EnterpriseAuthenticationInfo> enterpriseAuthenticationInfoList = Lists.newArrayList();
        List<ShopInfo> shopInfos = Lists.newArrayList();
        Set<Long> ids = Sets.newHashSet();
        for (DocObject itemDO : paging.getData()) {
            if(CollectionUtil.isNotEmpty(itemDO.getDistributorIds())){
                ids.addAll(itemDO.getDistributorIds());
            }
        }
        if(ids.size() > 0){
            if(request.getOperatorId()!=null && request.getOperatorId() == 1){
                ShopQueryByIdRequest param = new ShopQueryByIdRequest();
                param.setIdSet(ids);
                shopInfos = Assert.take(shopReadFacade.queryById(param));
            }else{
                enterpriseAuthenticationInfoList = Assert.take(enterpriseAuthenticationReadFacade.getEnterpriseAuthenticationInfoByIds(new ArrayList<>(ids)));
            }
        }

        for (DocObject itemDO : paging.getData()) {
            itemDO.setCategoryName(categoryNameMap.get(itemDO.getCategoryId()));
            if(!CollectionUtils.isEmpty(itemDO.getCategoryIds())){
                List<String> categoryNames = itemDO.getCategoryIds().stream().map(categoryNameMap::get).collect(Collectors.toList());
                itemDO.setCategoryNames(categoryNames);
            }
            if (!CollectionUtils.isEmpty(itemDO.getDistributorIds())) {
                if(request.getOperatorId()!=null && request.getOperatorId() == 1){
                    itemDO.setDistributorNames(shopInfos.stream().filter(f -> itemDO.getDistributorIds().contains(f.getId())).map(ShopInfo::getName).distinct().collect(Collectors.toList()));
                }else{
                    List<String> nameList = enterpriseAuthenticationInfoList.stream().filter(f -> itemDO.getDistributorIds().contains(f.getId())).map(EnterpriseAuthenticationInfo::getCompanyFullName).collect(Collectors.toList());
                    itemDO.setDistributorNames(nameList);
                }
                if(itemDO.getDistributorIds().contains(request.getReqHeardDistributorIds())){
                    itemDO.setInMyLibrary(Boolean.TRUE);
                }
            }
            if(itemDO.getSourceOperatorId() != null){
                ShopInfo shopInfo = shopMap.get(itemDO.getSourceOperatorId());
                if(shopInfo != null){
                    itemDO.setSourceOperatorName(shopInfo.getName());
                }
            }
        }
        //查询库存
        inventoryPackage(paging);
        //设置运营商名称
        buildOperatorName(paging);
        //设置吊牌价
        setCentralizedPurchasePrice(paging);
        return Response.ok(paging);
    }

    @Override
    public <Request extends ChoiceItemSearchRequest, DocObject extends ChoiceItemDO> Response<Paging<DocObject>> distributorSearch(Request request) {
        //如果是平台查看选品库
        List<Long> expireChoiceIds = Lists.newArrayList();
        if(request.getReqHeardDistributorIds() != null && request.getReqHeardDistributorIds() == 1){
            //获取系统所有运营新建的 给到平台的选品库数据
            List<Long> choiceLotLibIds = Assert.take(choiceLotLibReadFacade.queryAllOperatorChoiceLotLib());
            if(CollectionUtil.isEmpty(choiceLotLibIds)){
                choiceLotLibIds = Lists.newArrayList();
                choiceLotLibIds.add(-1L);
            }
            request.setChoiceLotLibIds(Joiner.on("_").join(choiceLotLibIds));
        }else{
            log.info("ChoiceItemSearchFacadeImpl.distributorSearch. request::::::{}", request);
            expireChoiceIds = getDistributorSearchParamRich(request);
            log.info("ChoiceItemSearchFacadeImpl.distributorSearch. getDistributorSearchParamCheck result::::::{}, paramCheck:{}", request, expireChoiceIds);
            if (null == expireChoiceIds || null == request.getReqHeardDistributorIds()) {
                return Response.ok(Paging.empty());
            }
        }
        if (null != request.getDistributorItemJoin()) {
            if (1 == request.getDistributorItemJoin()) {
                request.setDistributorIds(request.getReqHeardDistributorIds().toString());
            } else if (2 == request.getDistributorItemJoin()) {
                request.setNotDistributorIds(request.getReqHeardDistributorIds().toString());
            }
        }
        //这里是因为 商品可能存在审核中  页面显示的是下架 所以如果通过下架查询 把审核中的也查询出来
        if(StringUtils.isNotEmpty(request.getStatus()) && request.getStatus().equals("-1")){
            request.setStatus("-1_-5");
        }

        log.info("ChoiceItemSearchFacadeImpl.distributorSearch. request::::::{}", JSON.toJSONString(request));

        // 构造搜索请求
        SearchRequest searchRequest = requestBuilder.build(request);
        // 搜索主文档与主聚合
        SearchIndexQueryEnum group = SearchIndexQueryEnum.INDEX_CHOICE_SEARCH_ITEM_GROUP;
        //如果是平台 不根据itemId分组
        if(request.getReqHeardDistributorIds() == 1){
            group = SearchIndexQueryEnum.INDEX_CHOICE_SEARCH_CHOICE_ITEM_GROUP;
        }
        SearchResult<DocObject> searchResult = eaSearchClient.search(searchRequest, group);
        // 从搜索结果解析文档
        Paging<DocObject> documents = baseChoicePrimarySearchHandler.parseSearchResult(searchResult);
        if (documents.isEmpty()) {
            return Response.ok(documents);
        }

        for (DocObject datum : documents.getData()) {
            if(datum.getCategoryId() <= 10000000){
                log.info("未刷分类id:{}",datum.getItemId());
            }

        }
        // 获取分类名称
        List<Long> categoryIdList = documents.getData().stream().filter(x->x.getStatus() != -3).map(DocObject::getCategoryId).collect(Collectors.toList());
        Map<Long, List<BackCategory>> categoryMap = backCategoryService.batchFindAncestorsOf(categoryIdList);

        for (DocObject itemDO : documents.getData()) {
            List<BackCategory> backCategoryList = categoryMap.get(itemDO.getCategoryId());
            if(!CollectionObjectUtil.isEmpty(backCategoryList)){
                String categoryName = "";
                for (int i = 0; i < backCategoryList.size(); i++) {
                    if (i == 0) {
                        categoryName = backCategoryList.get(i).getName();
                    } else {
                        categoryName = categoryName + ">" + backCategoryList.get(i).getName();
                    }
                }
                itemDO.setCategoryName(categoryName);
            }

            if (!CollectionUtils.isEmpty(itemDO.getDistributorIds())) {
                if (itemDO.getDistributorIds().contains(request.getReqHeardDistributorIds())) {
                    itemDO.setInMyLibrary(Boolean.TRUE);
                }
            }
            itemDO.setLoseStatus(0);
            // 到期数据处理
            if(expireChoiceIds.contains(itemDO.getChoiceLotLibId())){
                itemDO.setLoseStatus(1);
                itemDO.setMaxDistributorPrice(null);
                itemDO.setMinDistributorPrice(null);
                itemDO.setMaxPreResellerGrossRate(null);
                itemDO.setMinPreResellerGrossRate(null);
            }
            //平台可以看到全部数据
            if(request.getReqHeardDistributorIds() != 1){
                itemDO.setMaxMarkup(null);
                itemDO.setMinMarkup(null);
                itemDO.setMaxBasePrice(null);
                itemDO.setMinBasePrice(null);
            }
        }
        //查询库存
        inventoryPackage(documents);
        //设置运营商名称
        buildOperatorName(documents);
        //设置吊牌价
        setCentralizedPurchasePrice(documents);
        return Response.ok(documents);
    }

    private List<Long> getDistributorSearchParamRich(ChoiceItemSearchRequest request) {
        if (null == request.getOperatorId()) {
            log.error("ChoiceItemSearchFacadeImpl.distributorSearch. get operator id is null");
            return null;
        }
        // 获取渠道商购买的选品库服务
        ServiceOrderQueryRequest serviceOrderQueryRequest = new ServiceOrderQueryRequest();
        serviceOrderQueryRequest.setChannelId(request.getReqHeardDistributorIds());
        serviceOrderQueryRequest.setServiceTypes("1,3");
        serviceOrderQueryRequest.setOperatorId(request.getOperatorId());
        List<FirmMealParam> firmMealParamList = orderReadApi.queryFirmMealList(serviceOrderQueryRequest);
        if (CollectionUtils.isEmpty(firmMealParamList)) {
            log.info("ChoiceItemSearchFacadeImpl.serviceOrderQueryRequest. Service Order is empty. request::::::{}", serviceOrderQueryRequest);
//            return null;
            firmMealParamList = Lists.newArrayList();
        }
        // 获取渠道商已过期选品库Id  not null 选品库Id -> map[最大到日期] -> 选品库Id(到期日期)
        List<Long> expireChoiceIds = firmMealParamList.stream().filter(v -> Objects.nonNull(v.getProjectId())).collect(
                Collectors.toMap(FirmMealParam::getProjectId, Function.identity(),
                        BinaryOperator.maxBy(Comparator.comparing(FirmMealParam::getExpiringDate))))
                .entrySet().stream().filter(v -> v.getValue().getExpiringDate().before(new Date())).map(Map.Entry::getKey).collect(Collectors.toList());
        if (null == request.getChoiceLotLibId() && StringUtils.isBlank(request.getChoiceLotLibIds())) {
            // 得到的选品库服务
            String choiceLotLibIds = firmMealParamList.stream().filter( v -> null != v.getProjectId()).map(v -> String.valueOf(v.getProjectId())).collect(Collectors.joining("_"));
            log.info("ChoiceItemSearchFacadeImpl.serviceOrderQueryRequest. buy choice. choiceLotLibIds::{}", choiceLotLibIds);
            if(StringUtil.isBlank(choiceLotLibIds)){
                log.error("ChoiceItemSearchFacadeImpl.serviceOrderQueryRequest. get choiceLotLibIds isBlank. choiceLotLibIds::::::{}", choiceLotLibIds);
                return null;
            }
            request.setChoiceLotLibIds(choiceLotLibIds);
        }

        if(StringUtil.isNotBlank(request.getStatus()) && "-7".equals(request.getStatus())){
            if(CollectionUtils.isEmpty(expireChoiceIds)){
                return null;
            }
            request.setStatus(null);
            if(null != request.getChoiceLotLibId() && !expireChoiceIds.contains(request.getChoiceLotLibId())) {
                log.info("ChoiceItemSearchFacadeImpl.serviceOrderQueryRequest. expireChoiceIds and expireChoiceIds. {}::::::{}", expireChoiceIds, request.getChoiceLotLibId());
                return null;
            }else if(StringUtil.isNotBlank(request.getChoiceLotLibIds())){
                String reqExpireChoiceIds = Arrays.stream(request.getChoiceLotLibIds().split("_"))
                        .filter(v -> StringUtil.isNotBlank(v) && expireChoiceIds.contains(Long.valueOf(v))).collect(Collectors.joining("_"));
                request.setChoiceLotLibIds(StringUtil.isNotBlank(reqExpireChoiceIds) ? reqExpireChoiceIds : null);
            }else if(expireChoiceIds.size() > 0){
                request.setChoiceLotLibIds(StringUtils.join(expireChoiceIds,"_"));
            }
        }

        if (null != request.getDistributorItemJoin()) {
            if (1 == request.getDistributorItemJoin()) {
                request.setDistributorIds(request.getReqHeardDistributorIds().toString());
            } else if (2 == request.getDistributorItemJoin()) {
                request.setNotDistributorIds(request.getReqHeardDistributorIds().toString());
            }
        }
        return expireChoiceIds;
    }

    @Override
    public <Request extends ChoicePrimarySearchRequest> Response<SearchedItemWithAggs<ChoiceFrontItemDTO>> searchWithAgg(Request request) {
        log.info("ChoiceItemSearchFacadeImpl searchWithAgg request:::: {} ", JSON.toJSONString(request));
        if(null == request.getDistributorId()){
            return Response.fail("您还未认证企业, 请先认证企业信息.");
        }
            Boolean richParam = richChoiceLotLibIds(request);
            if(!richParam){
                return Response.fail("未获取服务信息, 运营商还未发布商品, 请稍后再查试....");
            }
        log.info("ChoiceItemSearchFacadeImpl searchWithAgg richParam after request:::: {}", request);
        // 搜索关键字处理
        if (StringUtils.isBlank(request.getKeyword())) {
            return searchWithAggHandler(request);
        }
        Response<SearchedItemWithAggs<ChoiceFrontItemDTO>> searchResult = searchWithAggHandler(request);
        Paging<ChoiceFrontItemDTO> entities = searchResult.getResult().getEntities();
        log.info("Choice Item ES Search result=>{}, total=>{}", entities.getData().toString(), entities.getTotal());
        // 第一页如果没有查询到数据则直接返回
        if (entities.isEmpty() && request.getPageNo() <= 1) {
            return searchResult;
        }
        // 筛选出符合关键字的商品
        List<ChoiceFrontItemDTO> filterResult = entities.getData().stream()
                .filter(frontItemDTO -> frontItemDTO.getName().contains(request.getKeyword()))
                .collect(Collectors.toList());
        // 如果当前页没有数据了，需要置空属性、品牌等信息
        log.info("ChoiceItemSearchFacadeImpl.searchWithAgg.isEmpty_1:{}",filterResult);
        if (CollectionUtils.isEmpty(filterResult)) {
//            return EMPTY_DATA_RESPONSE;
            searchResult.getResult().getEntities().setData(Lists.newArrayList());
            return searchResult;
        }
        log.info("ChoiceItemSearchFacadeImpl.searchWithAgg.isEmpty_2");
        // 如果当前页数据量小于要查询的数据量就直接返回，表示后面没有完全匹配到的商品
        long currentTotal = request.getPageNo() <= 1 ? request.getPageSize() : request.getPageNo() * request.getPageSize();
        if (filterResult.size() < request.getPageSize()) {
            entities.setData(filterResult);
            entities.setTotal(currentTotal - (request.getPageSize() - filterResult.size()));
            return searchResult;
        }
        // 如果当前页数据量等于要查询的数据量，那么再查询查询下一页的数据，用于修改总条数方便前端判断是否还有下一页数据
        request.setPageNo(request.getPageNo() + 1);
        Response<SearchedItemWithAggs<ChoiceFrontItemDTO>> nextResult = searchWithAggHandler(request);
        Paging<ChoiceFrontItemDTO> nextEntities = nextResult.getResult().getEntities();
        long nextCount = getNextCount(nextEntities.getData(), request.getKeyword());
        log.info("next page Choice Item ES Search result count:{}", nextCount);
        if (nextCount < 1) {
            entities.setTotal(currentTotal);
        }
        return searchResult;
    }

    @Override
    public Response<Paging<ChoiceItemDO>>  distributorMySearch(ChoiceItemSearchRequest request) {
        log.info("进来了");
        Response<Paging<ChoiceItemDO>> paging = distributorSearch(request);
        if(null != paging ){
            log.info("paging不为空");
            if(null != paging.getResult()){
                if(null != paging.getResult().getData()){
                    log.info("getData不为空");
                    List<ChoiceItemDO> data = paging.getResult().getData();
                    //供应商ID
                    Set<Long> vendorSet = data.stream().map(ChoiceItemDO::getVendorId).collect(Collectors.toSet());
                    Set<Long> itemSet = data.stream().map(ChoiceItemDO::getItemId).collect(Collectors.toSet());

                    AreaSkuQueryByItemRequest queryByItemRequest = new AreaSkuQueryByItemRequest();
                    queryByItemRequest.setOperatorId(request.getOperatorId());
                    queryByItemRequest.setItemIdSet(itemSet);

                    List<AreaSkuInfo> areaSkuInfos = Assert.take(areaSkuReadFacade.queryByItem(queryByItemRequest));
                    Set<Long> skuIdSet = areaSkuInfos.stream().map(AreaSkuInfo::getSkuId).collect(Collectors.toSet());
                    Map<Long, AreaSkuInfo> areaSkuMapBySkuId = areaSkuInfos.stream().collect(Collectors.toMap(AreaSkuInfo::getSkuId, Function.identity()));

                    ThirdInventoryQueryByOperatorRequest req = new ThirdInventoryQueryByOperatorRequest();
                    req.setVendorIdSet(vendorSet);
                    req.setSkuIdSet(skuIdSet);
                    req.setOperatorId(request.getOperatorId());

                    List<InventoryEntityResponse> inventoryEntityList = inventoryReadApi.queryByOperator(req);
                    for (InventoryEntityResponse inventoryEntityRespons : inventoryEntityList) {
                        AreaSkuInfo areaSkuInfo = areaSkuMapBySkuId.get(Long.valueOf(inventoryEntityRespons.getEntityId()));
                        if(!ObjectUtils.isEmpty(areaSkuInfo)){
                            log.info("区域查出来的商品id"+areaSkuInfo.getItemId());
                            for (ChoiceItemDO itemDO :data ) {
                                if(areaSkuInfo.getItemId().equals(itemDO.getItemId())){
                                    log.info("我的商品库的商品id"+areaSkuInfo.getItemId());
                                    itemDO.setRealQuantity(inventoryEntityRespons.getRealQuantity());
                                    break;
                                }
                            }
                            areaSkuInfo.setRealQuantity(inventoryEntityRespons.getRealQuantity());
                        }
                        areaSkuMapBySkuId.put(Long.valueOf(inventoryEntityRespons.getEntityId()), areaSkuInfo);
                    }
                    for (ChoiceItemDO itemDO :data ) {
                        if(!itemDO.getInMyLibrary()){
                            data.remove(itemDO);
                        }
                    }
                }
            }
        }
        return paging;
    }

    @Override
    public <Request extends ChoiceItemSearchRequest, DocObject extends ChoiceItemDO> Response<List<ChoiceFrontSkuDTO>> orderSearch(Request request) {
        ChoiceItemSearchRequest itemSearchRequest = new ChoiceItemSearchRequest();
        request.setTenantId(RequestContext.getTenantId());
        // 获取渠道商Id
        request.setReqHeardDistributorIds(request.getReqHeardDistributorIds());
        request.setDistributorIds(String.valueOf(request.getDistributorIds()));
        request.setOperatorId(request.getOperatorId());
        log.info("ChoiceItemSearchFacadeImpl.distributorSearch. request::::::{}", request);
        List<Long> expireChoiceIds = getDistributorSearchParamRich(request);
        log.info("ChoiceItemSearchFacadeImpl.distributorSearch. getDistributorSearchParamCheck result::::::{}, paramCheck:{}", request, expireChoiceIds);
        if (null == expireChoiceIds || null == itemSearchRequest.getReqHeardDistributorIds()) {
            Paging<DocObject> docObjectPaging = new Paging<>();
            docObjectPaging.setTotal(0L);

        }
        request.setDistributorIds(String.valueOf(request.getReqHeardDistributorIds()));
        // 构造搜索请求
        SearchRequest searchRequest = requestBuilder.build(request);
        // 搜索主文档与主聚合
        SearchResult<DocObject> searchResult = eaSearchClient.search(searchRequest, SearchIndexQueryEnum.INDEX_CHOICE_SEARCH_ITEM_GROUP);
        // 从搜索结果解析文档
        Paging<DocObject> documents = baseChoicePrimarySearchHandler.parseSearchResult(searchResult);
        if (documents.isEmpty()) {
            return Response.ok(new ArrayList<>());
        }
        Set<Long> itemIds = documents.getData().stream().map(DocObject::getItemId).collect(Collectors.toSet());
        List<ChoiceFrontSkuDTO> choiceFrontSkuDTOS = new ArrayList<>();

        Map<String, InventoryEntityResponse> inventoryMap = Maps.newHashMap();
        AreaSkuByOperatorIdsAndItemIdsRequest param = new AreaSkuByOperatorIdsAndItemIdsRequest();
        param.setOperatorIds(Collections.singletonList(request.getOperatorId()));
        param.setItemIds(new ArrayList<>(itemIds));
        List<AreaSkuInfo> areaSkuInfos = Assert.take(areaSkuReadFacade.findByOperatorIdsAndItemIds(param));
        Map<Long, List<AreaSkuInfo>> areaSkuMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(areaSkuInfos)){
            areaSkuMap = areaSkuInfos.stream().collect(Collectors.groupingBy(AreaSkuInfo::getItemId));

            //查询库存
            ThirdInventoryQueryByOperatorRequest req = new ThirdInventoryQueryByOperatorRequest();
            req.setVendorIdSet(areaSkuInfos.stream().map(AreaSkuInfo::getVendorId).collect(Collectors.toSet()));
            req.setSkuIdSet(areaSkuInfos.stream().map(AreaSkuInfo::getSkuId).collect(Collectors.toSet()));
            List<Long> operatorIds = documents.getData().stream().map(DocObject::getSourceOperatorId).filter(Objects::nonNull).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(operatorIds)){
                operatorIds = Lists.newArrayList();
            }
            operatorIds.add(request.getOperatorId());
            operatorIds.add(1L);
            req.setOperatorIds(operatorIds.stream().distinct().collect(Collectors.toList()));
            inventoryMap = inventoryReadApi.groupInventoryQueryByOperator(req);
        }
        //查询sku
        List<Sku> skus = skuDao.findByItemIdSet(itemIds);
        Map<Long, Sku> skuMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(skus)) {
            skuMap = skus.stream().collect(Collectors.toMap(Sku::getId, Function.identity()));
        }
        for (DocObject itemDO : documents.getData()) {
            if (!CollectionUtils.isEmpty(itemDO.getDistributorIds())) {
                if (itemDO.getDistributorIds().contains(itemSearchRequest.getReqHeardDistributorIds())) {
                    itemDO.setInMyLibrary(Boolean.TRUE);
                }
            }
            // 到期数据处理
            if(expireChoiceIds.contains(itemDO.getChoiceLotLibId())){
                itemDO.setStatus(-7);
                itemDO.setMaxDistributorPrice(null);
                itemDO.setMinDistributorPrice(null);
                itemDO.setMaxPreResellerGrossRate(null);
                itemDO.setMinPreResellerGrossRate(null);
            }
            itemDO.setMaxMarkup(null);
            itemDO.setMinMarkup(null);
            itemDO.setMaxBasePrice(null);
            itemDO.setMinBasePrice(null);
            List<ChoiceLotLibSkuModel> choiceLotLibSkuBySkuIds = choiceLotLibSkuReadService.listByChoiceLotLibId(request.getOperatorId(), itemDO.getItemId(), itemDO.getChoiceLotLibId());
            if (CollectionObjectUtil.isEmpty(choiceLotLibSkuBySkuIds)){
                continue;
            }
            List<AreaSkuInfo> skuInfos = areaSkuMap.get(itemDO.getItemId());
            Map<Long, AreaSkuInfo> skuInfoMap = CollectionObjectUtil.toMap(skuInfos, AreaSkuInfo::getSkuId);
            for (ChoiceLotLibSkuModel choiceLotLibSkuModel : choiceLotLibSkuBySkuIds) {
                // sku不存在或者被禁用 sku不展示（渠道）
                if (!skuMap.containsKey(choiceLotLibSkuModel.getSkuId()) || 0 == skuMap.get(choiceLotLibSkuModel.getSkuId()).getStatus()) {
                    continue;
                }
                ChoiceFrontSkuDTO choiceFrontSkuDTO = new ChoiceFrontSkuDTO();
                BeanUtils.copyProperties(itemDO,choiceFrontSkuDTO);
                choiceFrontSkuDTO.setDistributorPrice(choiceLotLibSkuModel.getDistributorPrice());
                choiceFrontSkuDTO.setSkuImage(itemDO.getMainImage());
                choiceFrontSkuDTO.setSkuId(choiceLotLibSkuModel.getSkuId());
                AreaSkuInfo areaSkuInfo = skuInfoMap.get(choiceLotLibSkuModel.getSkuId());
                if(areaSkuInfo != null){
                    choiceFrontSkuDTO.setAttributes(areaSkuInfo.getAttributes());
                    choiceFrontSkuDTO.setSkuImage(areaSkuInfo.getImage());
                    InventoryEntityResponse inventory = inventoryMap.get(itemDO.getInventoryKey(areaSkuInfo.getSkuId()));
                    if (inventory != null) {
                        choiceFrontSkuDTO.setSaleQuantity(inventory.getSellableQuantity());

                    }
                }
                choiceFrontSkuDTOS.add(choiceFrontSkuDTO);
            }
        }
        return Response.ok(choiceFrontSkuDTOS);
    }

    @Override
    public Response<List<ChoiceSkuDO>> choiceSkuSearch(ChoiceSkuOrderRequest request) {
        List<ChoiceSkuMessageRequest> choiceSkuMessages = request.getChoiceSkuMessages();
        if(CollectionObjectUtil.isEmpty(choiceSkuMessages)){
            return Response.ok(new ArrayList<>());
        }
        List<Sku> skuInfoList = null;
        List<Long> itemId = choiceSkuMessages.stream().map(ChoiceSkuMessageRequest::getItemId).filter(Objects::nonNull).collect(Collectors.toList());
        log.info("传参itemId:{}",itemId);
        //查询sku信息
        if(CollectionObjectUtil.isEmpty(itemId)){
            Set<Long> skuIds = choiceSkuMessages.stream().map(ChoiceSkuMessageRequest::getSkuId).collect(Collectors.toSet());
            skuInfoList = skuReadDomainService.findByIdSet(skuIds, 1, null, null);

        }else{
            skuInfoList = skuReadDomainService.findByItemIdSet(choiceSkuMessages.stream().map(ChoiceSkuMessageRequest::getItemId).collect(Collectors.toSet()), 1,
                    null, null);
        }
        if(CollectionUtil.isEmpty(skuInfoList)){
            return Response.ok(new ArrayList<>());
        }
        itemId = skuInfoList.stream().map(Sku::getItemId).collect(Collectors.toList());

        Map<Long, ItemInfo> itemInfoMap = itemComponentAdaptor.getItemInfoMap(new HashSet<>(itemId), 1);
        Map<Long, AreaItemInfo> areaItemInfoMap = itemComponentAdaptor.getAreaItemInfoMap(new HashSet<>(itemId), 1, request.getOperatorId());
        String itemIds = StringUtils.join(itemId, "_");
        ChoiceItemSearchRequest choicePrimarySearchRequest = new ChoiceItemSearchRequest();
        choicePrimarySearchRequest.setDistributorIds(String.valueOf(request.getReqHeardDistributorIds()));
        choicePrimarySearchRequest.setOperatorId(request.getOperatorId());
        choicePrimarySearchRequest.setPageSize(2000);
        choicePrimarySearchRequest.setReqHeardDistributorIds(request.getReqHeardDistributorIds());
        ChoiceItemSearchRequest itemSearchRequest = new ChoiceItemSearchRequest();
        choicePrimarySearchRequest.setTenantId(RequestContext.getTenantId());
        choicePrimarySearchRequest.setItemIds(itemIds);
        // 获取渠道商Id
        choicePrimarySearchRequest.setReqHeardDistributorIds(request.getReqHeardDistributorIds());
        choicePrimarySearchRequest.setDistributorIds(String.valueOf(request.getReqHeardDistributorIds()));
        choicePrimarySearchRequest.setOperatorId(request.getOperatorId());
        log.info("ChoiceItemSearchFacadeImpl.distributorSearch. request::::::{}", request);
        List<Long> expireChoiceIds = getDistributorSearchParamRich(choicePrimarySearchRequest);
        log.info("ChoiceItemSearchFacadeImpl.distributorSearch. getDistributorSearchParamCheck result::::::{}, paramCheck:{}", request, expireChoiceIds);
        if (null == expireChoiceIds || null == choicePrimarySearchRequest.getReqHeardDistributorIds()) {
            Paging<ChoiceItemDO> docObjectPaging = new Paging<>();
            docObjectPaging.setTotal(0L);

        }
        // 构造搜索请求
        SearchRequest searchRequest = requestBuilder.build(choicePrimarySearchRequest);
        // 搜索主文档与主聚合
        SearchResult<ChoiceItemDO> searchResult = eaSearchClient.search(searchRequest, SearchIndexQueryEnum.INDEX_CHOICE_SEARCH_ITEM_GROUP);
        // 从搜索结果解析文档
        Paging<ChoiceItemDO> documents = baseChoicePrimarySearchHandler.parseSearchResult(searchResult);
        if (!documents.isEmpty()) {
            for (ChoiceItemDO itemDO : documents.getData()) {
                if (!CollectionUtils.isEmpty(itemDO.getDistributorIds())) {
                    if (itemDO.getDistributorIds().contains(itemSearchRequest.getReqHeardDistributorIds())) {
                        itemDO.setInMyLibrary(Boolean.TRUE);
                    }
                }
                // 到期数据处理
                if(expireChoiceIds.contains(itemDO.getChoiceLotLibId())){
                    itemDO.setStatus(-7);
                    itemDO.setMaxDistributorPrice(null);
                    itemDO.setMinDistributorPrice(null);
                    itemDO.setMaxPreResellerGrossRate(null);
                    itemDO.setMinPreResellerGrossRate(null);
                }
                itemDO.setMaxMarkup(null);
                itemDO.setMinMarkup(null);
                itemDO.setMaxBasePrice(null);
                itemDO.setMinBasePrice(null);
            }
        }
        List<ChoiceItemDO> data = documents.getData();
        Map<Long, List<ChoiceItemDO>> longChoiceItemDOMap = CollectionObjectUtil.toMapList(data, ChoiceItemDO::getItemId);
        List<ChoiceSkuDO> choiceSkuDOS = new ArrayList<>();
        for (Sku sku : skuInfoList) {
            if (0 == sku.getStatus()) {
                // 如果是禁用小程序就不返回
                continue;
            }
            List<ChoiceItemDO> choiceItemDOS = longChoiceItemDOMap.get(sku.getItemId());
            if(choiceItemDOS == null){
                ChoiceSkuDO choiceSkuDO = new ChoiceSkuDO();
                BeanUtils.copyProperties(sku,choiceSkuDO);
                choiceSkuDO.setSkuStatus(-7);
                choiceSkuDO.setErrorMessage("商品已失效");
                choiceSkuDOS.add(choiceSkuDO);
                continue;
            }
            for (ChoiceItemDO choiceItemDO : choiceItemDOS) {
                ChoiceSkuDO choiceSkuDO = new ChoiceSkuDO();
                BeanUtils.copyProperties(sku,choiceSkuDO);
                List<ChoiceLotLibSkuModel> choiceLotLibSkuBySkuIds = choiceLotLibSkuReadService.findChoiceLotLibSkuBySkuIds(request.getOperatorId(), choiceItemDO.getItemId(), Collections.singleton(sku.getId()), choiceItemDO.getChoiceLotLibId());
                choiceSkuDO.setSkuStatus(choiceItemDO.getStatus());
                choiceSkuDO.setChoiceLotLibId(choiceItemDO.getChoiceLotLibId());
                choiceSkuDO.setChoiceLotLibName(choiceItemDO.getChoiceLotLibName());
                choiceSkuDO.setBasePrice(choiceLotLibSkuBySkuIds.get(0).getDistributorPrice());
                ItemInfo itemInfo = itemInfoMap.get(sku.getItemId());
                AreaItemInfo areaItemInfo = areaItemInfoMap.get(sku.getItemId());
                choiceSkuDO.setSaleArea(itemInfo.getSalesArea());
                choiceSkuDO.setErrorMessage(choiceSkuDO.getSkuStatus() == -1?"商品已下架":choiceSkuDO.getSkuStatus()==-7?"商品已失效":null);
                //查询库存
                ThirdInventoryQueryByOperatorRequest req = new ThirdInventoryQueryByOperatorRequest();
                req.setVendorIdSet(Collections.singleton(sku.getShopId()));
                req.setSkuIdSet(Collections.singleton(sku.getId()));
                Set<Long> operatorIds = Sets.newHashSet();
                if(!ObjectUtils.isEmpty(areaItemInfo.getSourceOperatorId())){
                    operatorIds.add(areaItemInfo.getSourceOperatorId());
                }
                operatorIds.add(request.getOperatorId());
                operatorIds.add(1L);
                req.setOperatorIds(operatorIds.stream().distinct().collect(Collectors.toList()));
                Map<String, InventoryEntityResponse> inventoryMap = inventoryReadApi.groupInventoryQueryByOperator(req);
                InventoryEntityResponse inventory = inventoryMap.get(choiceItemDO.getInventoryKey(sku.getId()));
                if (inventory != null) {
                    choiceSkuDO.setSellableQuantity(inventory.getSellableQuantity());
                }
                if(StringUtils.isBlank(choiceSkuDO.getImage())){
                    choiceSkuDO.setImage(choiceItemDO.getMainImage());
                }
                if (!ObjectUtils.isEmpty(request.getType()) && 1 == request.getType()) {
                    //小程序商品
                    DistributorItemLibModel distributorItemLibReq = new DistributorItemLibModel();
                    distributorItemLibReq.setItemId(choiceItemDO.getItemId());
                    distributorItemLibReq.setDistributorId(request.getReqHeardDistributorIds());
                    distributorItemLibReq.setChoiceLotLibId(choiceItemDO.getChoiceLotLibId());
                    distributorItemLibReq.setOperatorId(request.getOperatorId());
                    distributorItemLibReq.setStateDeleted(0);
                    DistributorItemLibModel distributorItemLibModel = distributorItemLibReadService.view(distributorItemLibReq);
                    if (!ObjectUtils.isEmpty(distributorItemLibModel)) {
                        //渠道商商品售卖状态
                        choiceSkuDO.setSaleStatus(distributorItemLibModel.getSaleStatus());
                        if (!ObjectUtils.isEmpty(choiceSkuDO.getSaleStatus()) && choiceSkuDO.getSaleStatus() == 0) {
                            // 停止售卖 商品状态为下架
                            choiceSkuDO.setSkuStatus(-1);
                        }
                    }
                    DistributorSkuLibModel distributorSkuLibReq = new DistributorSkuLibModel();
                    distributorSkuLibReq.setItemId(choiceItemDO.getItemId());
                    distributorSkuLibReq.setChoiceLotLibId(distributorItemLibModel.getChoiceLotLibId());
                    distributorSkuLibReq.setDistributorId(request.getReqHeardDistributorIds());
                    distributorSkuLibReq.setSkuId(sku.getId());
                    distributorSkuLibReq.setStateDeleted(0);
                    DistributorSkuLibModel distributorSkuLibModel = distributorSkuLibReadService.view(distributorSkuLibReq);
                    if (!ObjectUtils.isEmpty(distributorSkuLibModel)) {
                        //渠道商商品售价
                        choiceSkuDO.setBasePrice(distributorSkuLibModel.getSalePrice());
                    }
                }

                // 填充通卡数科商品信息
                TongkaItemUtil.fillChoiceSku(choiceSkuDO, itemInfo);
                choiceSkuDOS.add(choiceSkuDO);
            }
        }
        return Response.ok(choiceSkuDOS);
    }

    private <Request extends ChoicePrimarySearchRequest> Response<SearchedItemWithAggs<ChoiceFrontItemDTO>> searchWithAggHandler(Request request) {
        try {
            // 构造搜索请求
            SearchRequest searchRequest = requestBuilder.build(request);
            // 构造主聚合条件
            searchRequest.setAggregates(aggregationHandler.handlePreAggregateCondition(request));
            // 搜索主文档与主聚合
            SearchResult<ChoiceFrontItemDTO> searchResult = eaSearchClient.search(searchRequest, SearchIndexQueryEnum.INDEX_CHOICE_SEARCH_CHOICE_ITEM_GROUP);
            // 处理主聚合结果
            aggregationHandler.handlePreAggregateResult(request, searchResult.getAggregations());
            // 从搜索结果解析文档
            Paging documents = baseChoicePrimarySearchHandler.parseSearchResult(searchResult);
            if (!documents.isEmpty()) {
                // 构造次聚合条件
                Map<String, Aggregate> aggregateMap = aggregationHandler.handlePostAggregateCondition(request);
                if (!CollectionUtils.isEmpty(aggregateMap)) {
                    // 次聚合无需主文档
                    searchRequest.setAggregates(aggregationHandler.handlePostAggregateCondition(request));
                    searchRequest.fetchSource(false);
                    // 次聚合
                    searchResult = searchClient.search(searchRequest);
                    // 处理次聚合结果
                    aggregationHandler.handlePostAggregateResult(request, searchResult.getAggregations());
                }

            }
            // 模型转化处理
            return Response.ok(baseChoicePrimarySearchHandler.convertToSearchResult(documents, request));
        } finally {
            SearchContext.clear();
        }
    }

    /**
     * 下一页商品符合条件的商品数量
     * @return
     */
    private long getNextCount(List<ChoiceFrontItemDTO> choiceItemList, String keyword) {
        return CollectionUtils.isEmpty(choiceItemList) ? 0L :
                choiceItemList.stream().filter(frontItemDTO -> frontItemDTO.getName().contains(keyword)).count();
    }

    /**
     * 获取选品库信息
     * @param request 请求参数
     * @return
     */
    private Boolean richChoiceLotLibIds(ChoicePrimarySearchRequest request){
        // 获取渠道商所在区域运营信息

        if (org.springframework.util.StringUtils.isEmpty(request.getChoiceLotLibIds())) {
            ChoiceLotLibQueryRequest choiceRequest = new ChoiceLotLibQueryRequest();
            choiceRequest.setOperatorId(request.getOperatorId());
            choiceRequest.setDistributorId(request.getDistributorId());
            Response<List<ChoiceLotLibInfo>> listResponse = choiceLotLibReadFacade.listChoiceLotLibList(choiceRequest);
            List<ChoiceLotLibInfo> take = Assert.take(listResponse);
            if (CollectionUtils.isEmpty(take)) {
                log.error("ChoiceItemSearchFacadeImpl.richChoiceLotLibIds get Choice Lot lib fail. param:{}", choiceRequest);
                return Boolean.FALSE;
            }
            String choiceLotLibIds = take.stream().map(v -> String.valueOf(v.getId())).collect(Collectors.joining("_"));
            request.setChoiceLotLibIds(choiceLotLibIds);
            log.info("ChoiceItemSearchFacadeImpl.richChoiceLotLibIds get Choice Lot lib Ids, {}", choiceLotLibIds);
        }

        // 获取渠道商购买的选品库
        ServiceOrderQueryRequest serviceOrderQueryRequest = new ServiceOrderQueryRequest();
        serviceOrderQueryRequest.setChannelId(request.getDistributorId());
        serviceOrderQueryRequest.setServiceTypes("1,3");;
        serviceOrderQueryRequest.setOperatorId(request.getOperatorId());
        List<FirmMealParam> firmMealParamList = orderReadApi.queryFirmMealList(serviceOrderQueryRequest);
        if(!CollectionUtils.isEmpty(firmMealParamList)){
            Map<Long, FirmMealParam> firmMealParamMap = firmMealParamList.stream().filter(v -> Objects.nonNull(v.getProjectId())).collect(
                    Collectors.toMap(FirmMealParam::getProjectId, Function.identity(),
                            BinaryOperator.maxBy(Comparator.comparing(FirmMealParam::getExpiringDate))));
            Map map = JSON.parseObject(JSON.toJSONString(firmMealParamMap), Map.class);
            Map<Long, io.terminus.parana.item.third.param.FirmMealParam> ma = Maps.newHashMap();
            for (Object o : map.keySet()) {
                io.terminus.parana.item.third.param.FirmMealParam mealParam = JSON.parseObject(JSON.toJSONString(map.get(o)), io.terminus.parana.item.third.param.FirmMealParam.class);
                //如果是申通快速下单页面查询商品 已经失效选品库下面的商品不在展示
                if(request.getSearchType() != null && request.getSearchType() == 2){

                }
                ma.put(Long.parseLong(o.toString()), mealParam);
            }
            request.setFirmMealParamMap(ma);
        }
        // 仅显示已购选品库处理
        if(Objects.nonNull(request.getIsHaveChoiceLotLib()) && request.getIsHaveChoiceLotLib() == 1){
            if(MapUtils.isEmpty(request.getFirmMealParamMap())){
                return Boolean.FALSE;
            }
            request.setChoiceLotLibIds(Joiner.on("_").join(request.getFirmMealParamMap().keySet()));
        }
        //如果是申通快速下单页面查询商品 已经失效选品库下面的商品不在展示
        if(request.getSearchType() != null && request.getSearchType() == 2){
            Set<Long> libIds = request.getFirmMealParamMap().values().stream().
                    filter(f -> !f.getExpiringDate().before(new Date())).
                    map(io.terminus.parana.item.third.param.FirmMealParam::getProjectId).collect(Collectors.toSet());
            log.info("libeIds: {}",libIds);
            request.setChoiceLotLibIds(Joiner.on("_").join(libIds));
        }
        return Boolean.TRUE;
    }

    protected List<SkuAttributeInfo> skuAttributeListToSkuAttributeInfoList(List<SkuAttribute> list) {
        if ( list == null ) {
            return null;
        }

        List<SkuAttributeInfo> list1 = new ArrayList<SkuAttributeInfo>( list.size() );
        for ( SkuAttribute skuAttribute : list ) {
            list1.add( skuAttributeToSkuAttributeInfo( skuAttribute ) );
        }

        return list1;
    }

    protected SkuAttributeInfo skuAttributeToSkuAttributeInfo(SkuAttribute skuAttribute) {
        if ( skuAttribute == null ) {
            return null;
        }

        SkuAttributeInfo skuAttributeInfo = new SkuAttributeInfo();

        Map<String, String> map = skuAttribute.getExtra();
        if ( map != null ) {
            skuAttributeInfo.setExtra( new HashMap<String, String>( map ) );
        }
        else {
            skuAttributeInfo.setExtra( null );
        }
        skuAttributeInfo.setId( skuAttribute.getId() );
        skuAttributeInfo.setAttrKey( skuAttribute.getAttrKey() );
        skuAttributeInfo.setAttrVal( skuAttribute.getAttrVal() );
        skuAttributeInfo.setShowImage( skuAttribute.getShowImage() );
        skuAttributeInfo.setThumbnail( skuAttribute.getThumbnail() );
        skuAttributeInfo.setImage( skuAttribute.getImage() );

        return skuAttributeInfo;
    }


    private <DocObject extends ChoiceItemDO> void inventoryPackage(Paging<DocObject> paging){
        if(paging.isEmpty()){
            return;
        }
        Map<String, InventoryEntityResponse> inventoryMap = Maps.newHashMap();
        AreaSkuByOperatorIdsAndItemIdsRequest param = new AreaSkuByOperatorIdsAndItemIdsRequest();
        param.setOperatorIds(paging.getData().stream().map(ChoiceItemDO::getOperatorId).collect(Collectors.toList()));
        param.setItemIds(paging.getData().stream().map(ChoiceItemDO::getItemId).collect(Collectors.toList()));

        List<AreaSkuInfo> areaSkuInfos = Assert.take(areaSkuReadFacade.findByOperatorIdsAndItemIds(param));
        Map<Long, List<AreaSkuInfo>> areaSkuMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(areaSkuInfos)){
            areaSkuMap = areaSkuInfos.stream().collect(Collectors.groupingBy(AreaSkuInfo::getItemId));

            //查询库存
            ThirdInventoryQueryByOperatorRequest req = new ThirdInventoryQueryByOperatorRequest();
            req.setVendorIdSet(areaSkuInfos.stream().map(AreaSkuInfo::getVendorId).collect(Collectors.toSet()));
            req.setSkuIdSet(areaSkuInfos.stream().map(AreaSkuInfo::getSkuId).collect(Collectors.toSet()));
            List<Long> operatorIds = paging.getData().stream().map(ChoiceItemDO::getSourceOperatorId).filter(Objects::nonNull).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(operatorIds)){
                operatorIds = Lists.newArrayList();
            }
            operatorIds.add(1L);
            operatorIds.addAll(param.getOperatorIds());
            req.setOperatorIds(operatorIds.stream().distinct().collect(Collectors.toList()));
            inventoryMap = inventoryReadApi.groupInventoryQueryByOperator(req);

            for (ChoiceItemDO itemDO : paging.getData()) {
                List<AreaSkuInfo> skuInfos = Lists.newArrayList();
                if (areaSkuMap.containsKey(itemDO.getItemId())) {
                    skuInfos = areaSkuMap.get(itemDO.getItemId());
                }
                int saleQuantity = 0;
                for (AreaSkuInfo skuInfo : skuInfos) {
                    if(itemDO.getOperatorId().equals(skuInfo.getOperatorId())){
                        InventoryEntityResponse inventory = inventoryMap.get(itemDO.getInventoryKey(skuInfo.getSkuId()));
                        if (inventory != null) {
                            saleQuantity += inventory.getSellableQuantity();
                        }
                    }
                }
                itemDO.setSafeQuantity(saleQuantity);
            }
        }
    }

    private <DocObject extends ChoiceItemDO> void buildOperatorName(Paging<DocObject> paging) {
        if(paging.isEmpty()){
            return;
        }
        Set<Long> operatorIds = paging.getData().stream().map(ChoiceItemDO::getOperatorId).collect(Collectors.toSet());
        operatorIds.addAll(paging.getData().stream().map(ChoiceItemDO::getVendorId).collect(Collectors.toSet()));
        ShopQueryByIdRequest request = new ShopQueryByIdRequest();
        request.setIdSet(operatorIds);
        Response<List<ShopInfo>> shopResp = shopReadFacade.queryById(request);
        if (!shopResp.isSuccess() || CollectionUtils.isEmpty(shopResp.getResult())) {
            return;
        }
        Map<Long, ShopInfo> shopMap = shopResp.getResult().stream().collect(Collectors.toMap(ShopInfo::getId, Function.identity()));
        for (DocObject item : paging.getData()) {
            if (shopMap.containsKey(item.getOperatorId())) {
                item.setOperatorName(shopMap.get(item.getOperatorId()).getName());
            }
            if (shopMap.containsKey(item.getVendorId())) {
                item.setVendorName(shopMap.get(item.getVendorId()).getName());
            }
        }
    }

    //增加吊牌价
    private <DocObject extends ChoiceItemDO> void setCentralizedPurchasePrice(Paging<DocObject> paging){
        //增加吊牌价
        if(paging != null && !paging.isEmpty()){
            Set<Long> itemIdSet = AssembleDataUtils.list2set(paging.getData(), ChoiceItemDO::getItemId);
            List<Sku> skuInfoList = skuReadDomainService.findByItemIdSet(itemIdSet,1,null,null);
            log.info("setCentralizedPurchasePrice skuInfoList {}",skuInfoList);
            //使用stream将sku列表根据itemId转换为分组的map
            Map<Long, List<Sku>> skuInfoMap = skuInfoList.stream().collect(Collectors.groupingBy(Sku::getItemId));
            for(ChoiceItemDO info:paging.getData()){
                List<Sku> skuInfos = skuInfoMap.get(info.getItemId());
                if(CollectionUtil.isEmpty(skuInfos)){
                    log.error("Warning:paging2Plus,sku info not get,skuId:{}",info.getItemId());
                    continue;
                }
                List<Long>  centralizedPurchasePriceList = new ArrayList<>();//吊牌价
                for(Sku skuInfo:skuInfos){
                    if(skuInfo.getExtraPrice()!=null && skuInfo.getExtraPrice().containsKey("centralizedPurchasePrice")){
                        centralizedPurchasePriceList.add(skuInfo.getExtraPrice().get("centralizedPurchasePrice"));
                    }
                }
                if(centralizedPurchasePriceList.size()>0){
                    info.setLowCentralizedPurchasePrice(centralizedPurchasePriceList.stream().min(Comparator.comparing(Long::longValue)).get());
                    info.setHighCentralizedPurchasePrice(centralizedPurchasePriceList.stream().max(Comparator.comparing(Long::longValue)).get());
                }
                Sku sku = skuInfos.get(0);
                // 处理直充商品参数
                if (ItemType.TOP_UP.getValue() == sku.getType()) {
                    String accountType = sku.getExtra().getOrDefault(ItemExtraConstantKey.ACCOUNT_TYPE_KEY, "");
                    String faceValue = sku.getExtra().getOrDefault(ItemExtraConstantKey.FACE_VALUE_KEY, "0");
                    info.setAccountType(accountType);
                    info.setFaceValue(Long.valueOf(faceValue));
                }

                // 处理卡卷商品
                if (ItemType.COUPON.getValue() == sku.getType()) {
                    String couponType = sku.getExtra().getOrDefault(ItemExtraConstantKey.COUPON_TYPE_KEY, "");
                    String faceValue = sku.getExtra().getOrDefault(ItemExtraConstantKey.FACE_VALUE_KEY, "0");
                    String useCase = sku.getExtra().getOrDefault(ItemExtraConstantKey.USE_CASE_KEY, "");
                    String couponName = sku.getExtra().getOrDefault(ItemExtraConstantKey.COUPON_NAME_KEY, "");
                    String couponLogo = sku.getExtra().getOrDefault(ItemExtraConstantKey.COUPON_LOGO_KEY, "");
                    String couponUseInfo = sku.getExtra().getOrDefault(ItemExtraConstantKey.COUPON_USE_INFO_KEY, "");
                    String couponBg = sku.getExtra().getOrDefault(ItemExtraConstantKey.COUPON_BG_KEY, "");
                    info.setCouponType(couponType);
                    info.setFaceValue(Long.valueOf(faceValue));
                    info.setUseCase(useCase);
                    info.setCouponName(couponName);
                    info.setCouponLogo(couponLogo);
                    info.setCouponUseInfo(couponUseInfo);
                    info.setCouponBg(couponBg);
                }

            }
        }
    }
}
