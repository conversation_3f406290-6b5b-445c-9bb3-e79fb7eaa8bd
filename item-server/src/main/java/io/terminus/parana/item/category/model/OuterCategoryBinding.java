package io.terminus.parana.item.category.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2018-08-06 16:24:50
 */
@Data
public class OuterCategoryBinding implements Serializable {

    private static final long serialVersionUID = -4370727849880250437L;
    /**
     * id
     */
    private Long id;
    /**
     * outerCategoryId
     */
    private Long outerCategoryId;
    /**
     * backCategoryId
     */
    private Long backCategoryId;
    /**
     * operatorId
     */
    private Long operatorId;
    /**
     * createdAt
     */
    private Date createdAt;
    /**
     * updatedAt
     */
    private Date updatedAt;

    /**
     * 供应商id
     */
    private Long vendorId;
}
