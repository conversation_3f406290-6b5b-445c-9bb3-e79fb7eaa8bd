package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Throwables;
import io.terminus.api.utils.StringUtil;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.exception.RestException;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.choicelot.api.bean.param.ChoiceLotLibNameBean;
import io.terminus.parana.item.choicelot.api.bean.param.DistributorItemIdsLibBean;
import io.terminus.parana.item.choicelot.api.bean.param.DistributorNameBeans;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceLotLibItemBindRequest;
import io.terminus.parana.item.choicelot.api.bean.request.DistributorLibBatchHistoryQueryRequest;
import io.terminus.parana.item.choicelot.api.facade.ChoiceLotLibItemWriteFacade;
import io.terminus.parana.item.choicelot.api.facade.DistributorItemLibWriteFacade;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibItemModel;
import io.terminus.parana.item.choicelot.model.DistributorItemLibModel;
import io.terminus.parana.item.choicelot.model.DistributorLibBatchHistoryModel;
import io.terminus.parana.item.choicelot.service.*;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.dto.ChoiceItemJoinExcelTemplate;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.search.docobject.AreaItemDO;
import io.terminus.parana.item.search.facade.AreaItemSearchFacade;
import io.terminus.parana.item.search.request.AreaItemSearchRequest;
import io.terminus.parana.trade.buy.api.request.param.FirmMealParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/18 11:22
 */
@Service
@Slf4j
public class ChoiceItemJoinExcelExportOssStrategyImpl implements ExcelExportStrategy {

    @Autowired
    private DistributorLibBatchHistoryReadService distributorLibBatchHistoryReadService;

    @Autowired
    private DistributorLibBatchHistoryWriteService distributorLibBatchHistoryWriteService;

    @Autowired
    private ChoiceLotLibReadDomainService choiceLotLibReadDomainService;

    @Autowired
    private AreaItemSearchFacade areaItemSearchFacade;

    @Autowired
    private ChoiceLotLibItemWriteFacade choiceLotLibItemWriteFacade;

    @Autowired
    private ChoiceLotLibItemWriteService choiceLotLibItemWriteService;

    @Autowired
    private ChoiceLotLibItemReadService choiceLotLibItemReadService;

    @Autowired
    private DistributorItemLibWriteFacade distributorItemLibWriteFacade;

    @Autowired
    private DistributorItemLibReadService distributorItemLibReadService;

    @Autowired
    private ThirdPartyRegistry registry;

    @Autowired
    private ExcelExportHelper excelExportHelper;

    @Autowired
    private AreaItemReadDomainService areaItemReadDomainService;

    @Override
    public String getType() {
        return ExcelExportType.DISTRIBUTOR_BATCH_JOIN_OPERATOR_EXPORT.getReportType();
    }

    @Override
    public String execute(String requestJson, String name) {
        Long beginTime = System.currentTimeMillis();
        DistributorLibBatchHistoryQueryRequest requestModel = JSON.parseObject(requestJson, DistributorLibBatchHistoryQueryRequest.class);
        if(Objects.isNull(requestModel) || Objects.isNull(requestModel.getId()) || Objects.isNull(requestModel.getOperatorId())){
            throw new RestException("request param is null");
        }
        log.info("ChoiceItemJoinExcelExportOssStrategyImpl.execute-{}-{}, name:{}, beginTime:{}",requestModel.getOperatorId(), requestModel.getId(), name, beginTime);
        DistributorLibBatchHistoryModel model = null;
        String filePath = null;
        InputStream is = null;

        try {
            model = distributorLibBatchHistoryReadService.queryOne(requestModel);
            if (Objects.isNull(model)) {
                log.info("ChoiceItemJoinExcelExportOssStrategyImpl.execute-{}-{} query batch id is null", requestModel.getOperatorId(), requestModel.getId());
                throw new RestException("query batch join is null");
            }
            ByteArrayOutputStream outputStream;
            List<ChoiceItemJoinExcelTemplate> itemJoinExcelTemplateData = getItemJoinExcelTemplateData(model);
            if (itemJoinExcelTemplateData.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(ChoiceItemJoinExcelTemplate.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(itemJoinExcelTemplateData, ChoiceItemJoinExcelTemplate.class);
            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("ChoiceItemJoinExcelExportOssStrategyImpl.execute-{}-{}  filePath：{}", requestModel.getOperatorId(), requestModel.getId(), filePath);
            model.setJoinStatus(1);
        } catch (Exception e) {
            log.error("ChoiceItemJoinExcelExportOssStrategyImpl.execute-{}-{}, error {}", requestModel.getOperatorId(), requestModel.getId(), Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("ChoiceItemJoinExcelExportOssStrategyImpl.execute-{}-{}, et {}", requestModel.getOperatorId(), requestModel.getId(), Throwables.getStackTraceAsString(et));
                }
            }
            if (null != model) {
                model.setJoinStatus(2);
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("ChoiceItemJoinExcelExportOssStrategyImpl.execute-{}-{}, et {}", requestModel.getOperatorId(), requestModel.getId(), Throwables.getStackTraceAsString(et));
                }
            }
            if (null != model) {
                model.setFilePatch(filePath);
                distributorLibBatchHistoryWriteService.update(model);
            }
        }
        return filePath;
    }

    public List<ChoiceItemJoinExcelTemplate> getItemJoinExcelTemplateData(DistributorLibBatchHistoryModel model) {
        log.info("1.ChoiceItemJoinExcelExportOssStrategyImpl.getItemJoinExcelTemplateData.execute-{}-{}, model:{}", model.getOperatorId(), model.getId(), model);
        if (StringUtil.isBlank(model.getDistributorJson())) {
            throw new RestException("synchronous param distributorIds is null");
        }
        Set<Long> distributorIds = JSON.parseArray(model.getDistributorJson(), DistributorNameBeans.class)
                .stream().map(DistributorNameBeans::getDistributorId).collect(Collectors.toSet());
        // 获取渠道商购买的选品库
        List<FirmMealParam> firmMealParams = choiceLotLibReadDomainService.queryFirmMealList(new ArrayList<>(distributorIds), model.getOperatorId());
        if (CollectionUtils.isEmpty(firmMealParams)) {
            throw new RestException("synchronous param distributor not service");
        }
        // 相同渠道商+选品库去重
        firmMealParams = firmMealParams.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(esObject -> esObject.getChannelId()+"-"+esObject.getProjectId()))), ArrayList::new));
        distributorIds = firmMealParams.stream().map(FirmMealParam::getChannelId).collect(Collectors.toSet());
        Set<Long> choiceLotLibIds = firmMealParams.stream().filter(Objects::nonNull).map(FirmMealParam::getProjectId).collect(Collectors.toSet());
        if (0 == model.getJoinChoiceType() && StringUtil.isNotBlank(model.getChoiceJson())) {
            // 同步部分选品库时去除不存在的绑定关系
            List<Long> choiceIds = JSON.parseArray(model.getChoiceJson(), ChoiceLotLibNameBean.class)
                    .stream().map(ChoiceLotLibNameBean::getChoiceLotLibId).collect(Collectors.toList());
            choiceLotLibIds = choiceLotLibIds.stream().filter(choiceIds::contains).collect(Collectors.toSet());
            firmMealParams = firmMealParams.stream().filter(v -> choiceIds.contains(v.getProjectId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(choiceLotLibIds)) {
            throw new RestException("synchronous param choiceLotLibIds is null");
        }
        log.info("2.ChoiceItemJoinExcelExportOssStrategyImpl.getItemJoinExcelTemplateData.execute-{}-{}, distributorIds:{}, choiceLotLibIds:{}", model.getOperatorId(), model.getId(),  distributorIds, choiceLotLibIds);
        List<ChoiceItemJoinExcelTemplate> dataInfoListResp = new ArrayList<>();

        // 获取商品数据
        AreaItemSearchRequest request = new AreaItemSearchRequest();
        //随便填充一个值 为了不让修改时间排序生效 不然每次都会把刚改的数据查询出来。
        request.setTenantId(1);
        request.setOperatorId(model.getOperatorId());
        request.setVendorId(model.getVendorId());
        request.setStatus("1");
        if (0 == model.getItemJoinType()) {
            // 部分商品同步
            Set<Long> itemIdSet = Arrays.stream(model.getItemIds().split(",")).filter(StringUtil::isNotBlank).map(Long::valueOf).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(itemIdSet)) {
                log.info("3.ChoiceItemJoinExcelExportOssStrategyImpl.getItemJoinExcelTemplateData.execute-{}-{}. itemIdSetList:{}",  model.getOperatorId(), model.getId(), itemIdSet);
                throw new RestException("synchronous param itemIdSet is null");
            }
            request.setItemIds(Joiner.on("_").join(itemIdSet));
        }
        int offset = 0;
        while (true) {
            try {
                List<AreaItem> areaItemList = areaItemReadDomainService.queryAreaItemPage(model.getOperatorId(), model.getVendorId(), offset, 100);
                if(CollectionUtil.isEmpty(areaItemList)){
                    break;
                }
                Set<Long> itemIds = areaItemList.stream().map(AreaItem::getItemId).collect(Collectors.toSet());
                request.setItemIds(Joiner.on("_").join(itemIds));
                request.setPageSize(itemIds.size());
                // 获取商品信息
                Response<Paging<AreaItemDO>> search = areaItemSearchFacade.search(request);
                Paging<AreaItemDO> itemPaging = Assert.take(search);
                log.info("一键同步查询数据{}-----{}",JSON.toJSONString(request), CollectionUtil.isEmpty(itemPaging.getData()) ? 0 : itemPaging.getData().size());
                if (itemPaging.isEmpty() || CollectionUtils.isEmpty(itemPaging.getData())) {
                    break;
                }
                List<ChoiceItemJoinExcelTemplate> templates = disposeItemInfo(itemPaging.getData(), model, firmMealParams, choiceLotLibIds);
                dataInfoListResp.addAll(templates);
            }catch (Exception e){
                log.error("商品同步选品库失败：参数:{},{}",JSON.toJSONString(request),e.getMessage());
            }finally {
                offset+=100;
            }
        }
        return dataInfoListResp;
    }

    private List<ChoiceItemJoinExcelTemplate> disposeItemInfo(List<AreaItemDO> areaItemDOList, DistributorLibBatchHistoryModel model,
                                                              List<FirmMealParam> firmMealParams, Set<Long> choiceLotLibIds) {
        // 商品数据
        List<ChoiceItemJoinExcelTemplate> templateList = new ArrayList<>();

        Map<Long, AreaItemDO> itemIdMap = areaItemDOList.stream().collect(Collectors.toMap(AreaItemDO::getItemId, Function.identity()));
        log.info("1.ChoiceItemJoinExcelExportOssStrategyImpl.disposeItemInfo.execute-{}-{} add itemIds:{}", model.getOperatorId(), model.getId(), itemIdMap.keySet());
        // 选品库商品
        ChoiceLotLibItemBindRequest request = new ChoiceLotLibItemBindRequest();
        request.setOperatorId(model.getOperatorId());
        request.setUpdatedBy(model.getCreatedBy());
        for (Long choiceLotLibId : choiceLotLibIds) {
            // 匹配选品库是否被购买
            List<FirmMealParam> firmMealParamList = firmMealParams.stream().filter(v -> v.getProjectId().equals(choiceLotLibId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(firmMealParamList)) {
                continue;
            }
            // 同步选品库
            Set<Long> syncDistributorItemIds  = getAddChoiceLotLib(areaItemDOList, model, templateList, itemIdMap, request, choiceLotLibId, firmMealParamList);
            if(CollectionUtils.isEmpty(syncDistributorItemIds)){
                continue;
            }

            // 新增渠道商商品
            for(FirmMealParam param : firmMealParams) {
                if(!choiceLotLibId.equals(param.getProjectId())){
                    continue;
                }
                List<Long> itemIds = new ArrayList<>(syncDistributorItemIds);
                List<DistributorItemLibModel> distributorItemList = distributorItemLibReadService.getDistributorItemList(param.getChannelId(), syncDistributorItemIds, choiceLotLibId, null);
                if(!CollectionUtils.isEmpty(distributorItemList)){
                    List<Long> collect = distributorItemList.stream().map(DistributorItemLibModel::getItemId).collect(Collectors.toList());
                    itemIds = syncDistributorItemIds.stream().filter(v ->!collect.contains(v)).collect(Collectors.toList());
                }
                if(CollectionUtils.isEmpty(itemIds)){
                    continue;
                }
                log.info("2.ChoiceItemJoinExcelExportOssStrategyImpl.disposeItemInfo.execute-{}-{} add choice lot lib result. distributorId:{}, choiceLotLibId:{},  itemIds:{}", model.getOperatorId(), model.getId(), param.getChannelId(), param.getProjectId(), syncDistributorItemIds);
                DistributorItemIdsLibBean distributorItemIdsLibBean = new DistributorItemIdsLibBean();
                distributorItemIdsLibBean.setDistributorId(param.getChannelId());
                distributorItemIdsLibBean.setOperatorId(model.getOperatorId());
                distributorItemIdsLibBean.setChoiceLotLibId(param.getProjectId());
                distributorItemIdsLibBean.setServiceId(param.getServiceId());
                distributorItemIdsLibBean.setTenantId(model.getTenantId());
                distributorItemIdsLibBean.setUpdateBy(model.getCreatedBy());
                distributorItemIdsLibBean.setItemIds(itemIds);
                distributorItemIdsLibBean.setSource(1);
                Response<Boolean> booleanResponse = distributorItemLibWriteFacade.saveBatchItemsByIds(distributorItemIdsLibBean);
                List<ChoiceItemJoinExcelTemplate> distributorTemplateData = getChoiceTemplateData(itemIds, itemIdMap, model, param, Boolean.FALSE, booleanResponse);
                templateList.addAll(distributorTemplateData);
            }
        }
        return templateList;
    }

    private Set<Long>  getAddChoiceLotLib(List<AreaItemDO> areaItemDOList, DistributorLibBatchHistoryModel model,
                                          List<ChoiceItemJoinExcelTemplate> templateList, Map<Long, AreaItemDO> itemIdMap,
                                          ChoiceLotLibItemBindRequest request, Long choiceLotLibId, List<FirmMealParam> firmMealParamList) {
        FirmMealParam firmMealParam = firmMealParamList.get(0);
        Set<Long> syncDistributorItemIds = new TreeSet<>();
        Set<Long> distributorIds = firmMealParamList.stream().map(FirmMealParam::getChannelId).collect(Collectors.toSet());

        // 筛选出未绑定选品库的商品
        List<Long> notBindItemIds = areaItemDOList.stream().filter(v -> null == v.getChoiceLotLibIds() || (null != v.getChoiceLotLibIds() && !v.getChoiceLotLibIds().contains(choiceLotLibId)))
                .map(AreaItemDO::getItemId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(notBindItemIds)){
            log.info("3.ChoiceItemJoinExcelExportOssStrategyImpl.disposeItemInfo.execute-{}-{} add choice lot lib. choiceLotLibId:{},  notBindItemIds:{}", model.getOperatorId(), model.getId(), choiceLotLibId, notBindItemIds);
            // 新增选品库商品
            request.setItemIds(notBindItemIds);
            request.setChoiceLotLibId(choiceLotLibId);
            request.setShowDistributorIds(distributorIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            request.setSource(1);
            Response<Boolean> bind = choiceLotLibItemWriteFacade.createBind(request);
            if(bind.isSuccess() && bind.getResult()){
                syncDistributorItemIds.addAll(notBindItemIds);
            }
            List<ChoiceItemJoinExcelTemplate> choiceTemplateData = getChoiceTemplateData(notBindItemIds, itemIdMap, model, firmMealParam, Boolean.TRUE, bind);
            templateList.addAll(choiceTemplateData);
        }

        // 过滤出已绑定选品库商品
        List<Long> choiceBindItemIds = itemIdMap.keySet().stream().filter(v -> !notBindItemIds.contains(v)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(choiceBindItemIds)) {
            return syncDistributorItemIds;
        }
        // 已绑定的同步商品
        List<ChoiceLotLibItemModel> choiceLotLibItemModelList = choiceLotLibItemReadService.listFindByCriteriaList(choiceLotLibId, choiceBindItemIds, 1);
        String showDistributorIds;
        for(ChoiceLotLibItemModel itemModel : choiceLotLibItemModelList){
            itemModel.setUpdatedBy(request.getUpdatedBy());
            if(StringUtil.isNotBlank(itemModel.getShowDistributorIds())) {
                Set<Long> choiceDisIds = Arrays.stream(itemModel.getShowDistributorIds().split(",")).map(Long::valueOf).collect(Collectors.toSet());
                choiceDisIds.addAll(distributorIds);
                showDistributorIds = choiceDisIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            }else {
                showDistributorIds = distributorIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            }
            // 更新选品库商品
            Boolean aBoolean = choiceLotLibItemWriteService.updateShowDistributorIds(itemModel, showDistributorIds);
            if(aBoolean) {
                syncDistributorItemIds.add(itemModel.getItemId());
                choiceBindItemIds.remove(itemModel.getItemId());
            }
        }
        if(choiceBindItemIds.size() > 0) {
            List<ChoiceItemJoinExcelTemplate> distributorTemplateData = getChoiceTemplateData(choiceBindItemIds, itemIdMap, model, firmMealParam, Boolean.TRUE, Response.fail("该商品已存在当前选品库"));
            templateList.addAll(distributorTemplateData);

        }
        return syncDistributorItemIds;
    }



    public List<ChoiceItemJoinExcelTemplate> getChoiceTemplateData(List<Long> itemIds,
                                                                   Map<Long, AreaItemDO> itemIdMap,
                                                                   DistributorLibBatchHistoryModel model,
                                                                   FirmMealParam param, Boolean synchronousType,
                                                                   Response<Boolean> response) {
        return itemIds.stream().map(itemId -> {
            AreaItemDO itemDo = itemIdMap.get(itemId);
            ChoiceItemJoinExcelTemplate template = new ChoiceItemJoinExcelTemplate();
            template.setVendorId(model.getVendorId());
            template.setVendorName(model.getVendorName());
            template.setDistributorId(synchronousType ? "" : param.getChannelId().toString());
            template.setDistributorName(synchronousType ? "" : param.getChannelName());
            template.setChoiceLotLibId(param.getProjectId().toString());
            template.setChoiceLotLibName(param.getProjectName());
            if(synchronousType) {
                template.setSynchronousType("选品库");
            }else{
                template.setSynchronousType("商品库");
            }
            template.setItemId(itemDo.getItemId());
            template.setName(itemDo.getName());
            if (itemDo.getStatus() == 1) {
                template.setStatus("上架");
            } else if (itemDo.getStatus() == -1) {
                template.setStatus("下架");
            } else if (itemDo.getStatus() == -2) {
                template.setStatus("冻结");
            } else if (itemDo.getStatus() == -5) {
                template.setStatus("审核中");
            } else if (itemDo.getStatus() == -3) {
                template.setStatus("供应商删除");
            }
            template.setBrandName(itemDo.getBrandName());
            template.setCategoryName(itemDo.getCategoryName());
            template.setMinBasePrice(itemDo.getLowBasePrice());
            template.setMaxBasePrice(itemDo.getHighBasePrice());
            template.setMinOriginalPrice(itemDo.getLowOriginalPrice());
            template.setMaxOriginalPrice(itemDo.getHighOriginalPrice());
            template.setMainImage(itemDo.getMainImage());
            if (null != response) {
                if (response.isSuccess()) {
                    template.setJoinStatus("成功");
                    template.setFailCause("");
                } else {
                    template.setJoinStatus("失败");
                    template.setFailCause(response.getError());
                }
            } else {
                template.setJoinStatus("失败");
                template.setFailCause("未知错误");
            }
            return template;
        }).collect(Collectors.toList());
    }
}
