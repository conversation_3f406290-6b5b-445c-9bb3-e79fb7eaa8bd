package io.terminus.parana.item.common.constants;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-09-05
 */
public interface StoreItemFrozenFlag {

    /**
     * 店铺冻结解冻后，商品正在处理中的标志，后追加店铺id
     * <p>处理完成后，无论成功与否，结果写会原key中<br>若成功，值为:{@link #STORE_ITEM_OPERATE_OK}，
     * 否则为：{@link #STORE_ITEM_OPERATE_FAIL}</p>
     */
    String STORE_ITEM_OPERATING_MASK = "store_item_operating_mask:";

    String STORE_ITEM_OPERATE_EXECUTING = "EXECUTING";

    String STORE_ITEM_OPERATE_OK = "EXECUTE_SUCCESS";

    String STORE_ITEM_OPERATE_FAIL = "EXECUTE_FAIL";
}
