package io.terminus.parana.item.brand.api.converter;

import io.terminus.parana.item.brand.api.bean.request.BrandCreateRequest;
import io.terminus.parana.item.brand.api.bean.request.BrandUpdateRequest;
import io.terminus.parana.item.brand.api.bean.response.BrandInfo;
import io.terminus.parana.item.brand.model.Brand;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2018-07-12 下午5:44
 */
@Mapper(componentModel = "spring", implementationName = "BrandConverterImplementation")
public interface BrandConverter {
    Brand vo2domain(BrandCreateRequest request);

    Brand vo2domain(BrandUpdateRequest request);

    BrandInfo domain2info(Brand brand);

    List<BrandInfo> domain2info(List<Brand> list);
}
