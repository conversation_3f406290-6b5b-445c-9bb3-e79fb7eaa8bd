package io.terminus.parana.item.favorites.extension;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.distributorskulib.model.DistributorSkuLibModel;
import io.terminus.parana.item.distributorskulib.service.DistributorSkuLibReadService;
import io.terminus.parana.item.favorites.extension.base.AbstractFavoritesItemExtension;
import io.terminus.parana.item.favorites.model.Favorites;
import io.terminus.parana.item.item.api.bean.request.item.ItemQueryByIdRequest;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.item.api.facade.ItemReadFacade;
import io.terminus.parana.item.price.model.ItemReferrerPrice;
import io.terminus.parana.item.search.docobject.DistributorItemLibDO;
import io.terminus.parana.item.search.facade.AreaItemSearchFacade;
import io.terminus.parana.item.search.facade.DistributorItemLibSearchFacade;
import io.terminus.parana.item.search.request.DistributorItemLibSearchRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Sets;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 默认商品收藏扩展实现
 *
 * <AUTHOR>
 */
@Order
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultFavoritesItemExtension extends AbstractFavoritesItemExtension {

    private final AreaItemSearchFacade areaItemSearchFacade;
    private final ItemReadFacade itemReadFacade;
    private final AreaSkuReadDomainService areaSkuReadDomainService;
    private final DistributorItemLibSearchFacade distributorItemLibSearchFacade;
    private final DistributorSkuLibReadService distributorSkuLibReadService;

    private static final String ITEM_DEFAULT_UNIT = "件";

    @Override
    public ExtensionResult beforeWrite(List<Favorites> favoritesList) {
        return ExtensionResult.ok();
    }

    @Override
    public ExtensionResult afterQuery(List<Favorites> favoritesList, Long referrerId, Long authId) {
        try {
            log.info("afterQuery favoritesList {} referrerId {}", JSON.toJSONString(favoritesList), referrerId);
            if (CollectionUtils.isEmpty(favoritesList)) {
                return ExtensionResult.ok();
            }
            List<Long> itemIdList = AssembleDataUtils.list2list(favoritesList, Favorites::getTargetId);
            List<Long> operatorIds = Lists.newArrayList();
            for (Favorites favorites : favoritesList) {
                if(CollectionUtil.isNotEmpty(favorites.getExtra()) && !StringUtils.isEmpty(favorites.getExtra().get("operatorId"))){
                    operatorIds.add(Long.parseLong(favorites.getExtra().get("operatorId")));
                }
            }
            operatorIds = operatorIds.stream().distinct().collect(Collectors.toList());
            int size = Math.max(operatorIds.size() * itemIdList.size() * 5, 100);

            //查询渠道商的商品信息  只查询上架、未删除、开售的
            DistributorItemLibSearchRequest request = new DistributorItemLibSearchRequest();
            request.setPageSize(size);
            request.setDistributorId(favoritesList.get(0).getOperatorId() + "");
            request.setReqHeardDistributorIds(favoritesList.get(0).getOperatorId());
            request.setOperatorId(operatorIds.get(0));
            request.setItemIds(Joiner.on("_").join(itemIdList));
            request.setTenantId(RequestContext.getTenantId());
            request.setStatus("1_-1");
            request.setStateDeleted(0);
            List<DistributorItemLibDO> distributorItemLibDOList = Assert.take(distributorItemLibSearchFacade.search(request)).getData();


            Set<Long> vendorIdSet = Sets.newHashSet();
            Map<String, DistributorItemLibDO> distributorItemMapWithItemIdKey = Maps.newHashMap();
            if(CollectionUtil.isNotEmpty(distributorItemLibDOList)){
                distributorItemLibDOList.forEach(distributorItemDO -> {
                    distributorItemMapWithItemIdKey.put(getKey(distributorItemDO), distributorItemDO);
                    vendorIdSet.add(distributorItemDO.getVendorId());
                });
            }

            List<AreaSku> areaSkuList = areaSkuReadDomainService.findByOperatorIdsAndItemIds(operatorIds, itemIdList);
            Map<Long, Long> itemMinQuantityMap = Maps.newHashMap();
            Map<Long, AreaSku> areaSkuNameMap = Maps.newHashMap();
            if(CollectionUtil.isNotEmpty(areaSkuList)){
                areaSkuList.forEach(areaSku -> {
                    areaSkuNameMap.put(areaSku.getItemId(),areaSku);
                    Long minQuantity = areaSku.getMinQuantity();
                    if (minQuantity != null) {
                        itemMinQuantityMap.put(areaSku.getItemId(), itemMinQuantityMap.containsKey(areaSku.getItemId())
                                ? Math.min(itemMinQuantityMap.get(areaSku.getItemId()), minQuantity)
                                : minQuantity);
                    }
                });
            }
            //增加作废商品的查询
            Set<Long> operatorIdSets = new HashSet<Long>();
            operatorIdSets.addAll(operatorIds);
            Set<Long> itemIdSet = new HashSet<Long>();
            itemIdSet.addAll(itemIdList);
            List<AreaSku> areaSkuDelList = areaSkuReadDomainService.findDeleteByOperatorIdsAndItemIds(operatorIdSets, itemIdSet);
            if(CollectionUtil.isNotEmpty(areaSkuDelList)){
                areaSkuDelList.forEach(areaSku -> {
                    areaSkuNameMap.put(areaSku.getItemId(),areaSku);
                });
            }

            // 获取商品 unit
            ItemQueryByIdRequest itemQueryByIdRequest = new ItemQueryByIdRequest();
            itemQueryByIdRequest.setIdSet(Sets.newHashSet(itemIdList));
            itemQueryByIdRequest.setTenantId(RequestContext.getTenantId());
            List<ItemInfo> itemInfoList = Assert.take(itemReadFacade.queryById(itemQueryByIdRequest));

            Map<Long, String> itemMinQuantityUnitMap = getUnitMap(itemInfoList);
            // 组装DTO
            favoritesList.forEach(favorites -> {
                DistributorItemLibDO distributorItemLibDO = distributorItemMapWithItemIdKey.get(this.getKey(favorites));
                Map<String, String> extra = CollectionUtils.isEmpty(favorites.getExtra())
                        ? Maps.newHashMap() : favorites.getExtra();
                if (distributorItemLibDO != null && distributorItemLibDO.getLoseStatus() != null && distributorItemLibDO.getLoseStatus() == 0) {

                    extra.put("itemName", distributorItemLibDO.getName());
                    extra.put("itemImageUrl", distributorItemLibDO.getMainImage());
                    extra.put("isDelete", String.valueOf(Boolean.FALSE));
                    extra.put("status", String.valueOf(distributorItemLibDO.getStatus()));
                    extra.put("saleStatus", String.valueOf(distributorItemLibDO.getSaleStatus()));
                    extra.put("lowPrice", String.valueOf(distributorItemLibDO.getLowPrice()));
                    extra.put("highPrice", String.valueOf(distributorItemLibDO.getHighPrice()));
                    extra.put("minQuantity", itemMinQuantityMap.get(distributorItemLibDO.getItemId()) + itemMinQuantityUnitMap.get(distributorItemLibDO.getItemId()));
                    extra.put("vendorName", distributorItemLibDO.getVendorName());
                    extra.put("lowOriginalPrice", String.valueOf(distributorItemLibDO.getLowOriginalPrice()));
                    extra.put("highOriginalPrice", String.valueOf(distributorItemLibDO.getHighOriginalPrice()));
                    //价格比较
                    Map<String, String> extraFa = favorites.getExtra();
                    extra.put("attributes", extraFa.get("attributes")==null?"":extraFa.get("attributes"));
                    extra.put("comparePrice", "0");
                    if(extraFa !=null && extraFa.containsKey("originalPrice") && extraFa.get("originalPrice")!=null && extraFa.containsKey("skuId")
                            && extraFa.get("skuId") !=null && extraFa.containsKey("choiceLotLibId") && extraFa.get("choiceLotLibId")!= null ){
                        Long originalPrice = Long.valueOf(extraFa.get("originalPrice"));
                        String skuId = extraFa.get("skuId");
                        String choiceLotLibId = extraFa.get("choiceLotLibId");
                        extra.put("originalPrice", String.valueOf(originalPrice));
                        Long distributorId = favorites.getOperatorId();
                        log.info("afterQuery originalPrice {} skuId {} choiceLotLibId {}",originalPrice,skuId,choiceLotLibId);
                        DistributorSkuLibModel distributorSkuLibModel = distributorSkuLibReadService.findByDistributorIdAndSkuId(distributorId,Long.valueOf(skuId),Long.valueOf(choiceLotLibId));
                        log.info("afterQuery distributorSkuLibModel {}",distributorSkuLibModel);
                        if(distributorSkuLibModel != null && distributorSkuLibModel.getSalePrice()!=null){
                            Long salePrice = distributorSkuLibModel.getSalePrice();
                            Long comparePrice = originalPrice - salePrice;
                            extra.put("comparePrice", String.valueOf(comparePrice));
                            extra.put("originalPrice", String.valueOf(salePrice));
                        }
                    }
                } else {
                    AreaSku areaSku = areaSkuNameMap.get(favorites.getTargetId());
                    if(areaSku != null){
                        extra.put("itemImageUrl", areaSku.getImage());
                        extra.put("itemName", areaSku.getName());
                    }
                    extra.put("isDelete", String.valueOf(Boolean.TRUE));
                    //价格比较
                    Map<String, String> extraFa = favorites.getExtra();
                    if(extraFa !=null && extraFa.containsKey("originalPrice") && extraFa.get("originalPrice")!=null ){
                        Long originalPrice = Long.valueOf(extraFa.get("originalPrice"));
                        extra.put("originalPrice", String.valueOf(originalPrice));
                    }
                }
                favorites.setExtra(extra);
            });
            return ExtensionResult.ok();
        } catch (Exception e) {
            log.error("favorites set {} extra info error, cause:{}", getType(), e);
            throw new ServiceException(e);
        }
    }

    private Map<Long, String> getUnitMap(List<ItemInfo> itemInfoList) {
        Map<Long, String> result = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(itemInfoList)){
            itemInfoList.forEach(itemInfo -> {
                if (itemInfo != null) {
                    result.put(itemInfo.getId(), StringUtils.isEmpty(itemInfo.getUnit()) ? ITEM_DEFAULT_UNIT : itemInfo.getUnit());
                }
            });
        }
        return result;
    }

    private String getKey(DistributorItemLibDO distributorItemLibDO) {
        return distributorItemLibDO.getItemId() + "_" + distributorItemLibDO.getOperatorId();
    }

    private String getKey(Favorites favorites) {
        if (favorites.getExtra() == null || favorites.getExtra().get("operatorId") == null) {
            log.error("favorites target operatorId is null:{}", favorites);
            return "error";
        }
        return favorites.getTargetId() + "_" + favorites.getExtra().get("operatorId");
    }
}
