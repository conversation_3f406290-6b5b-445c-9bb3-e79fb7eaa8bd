package io.terminus.parana.item.category.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.request.UserOperatorCategoryBindRequest;
import io.terminus.parana.item.category.service.UserOperatorCategoryBindingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserOperatorCategoryBindWriteFacadeImpl implements UserOperatorCategoryBindWriteFacade {

    private final UserOperatorCategoryBindingService userOperatorCategoryBindingService;

    @Override
    public Response<Boolean> bindUserOperatorCategory(UserOperatorCategoryBindRequest request) {
        return Response.ok(userOperatorCategoryBindingService.bindUserOperatorCategory(request.getUserId(), request.getOperatorId(), request.getOperatorCategoryIds()));
    }

}
