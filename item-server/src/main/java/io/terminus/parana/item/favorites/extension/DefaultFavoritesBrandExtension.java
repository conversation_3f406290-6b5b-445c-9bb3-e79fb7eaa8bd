package io.terminus.parana.item.favorites.extension;

import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.item.brand.api.bean.request.BrandMultiGetRequest;
import io.terminus.parana.item.brand.api.bean.response.BrandInfo;
import io.terminus.parana.item.brand.api.facade.BrandReadFacade;
import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.favorites.extension.base.AbstractFavoritesBrandExtension;
import io.terminus.parana.item.favorites.model.Favorites;
import io.terminus.parana.item.item.api.bean.request.item.ItemQueryByIdRequest;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/29 17:12
 * @description:
 */
@Order
@Slf4j
//@Service
public class DefaultFavoritesBrandExtension extends AbstractFavoritesBrandExtension {

    @Autowired
    private BrandReadFacade brandReadFacade;
    /**
     * 写前扩展
     *
     * @param favoritesList
     */
    @Override
    public ExtensionResult beforeWrite(List<Favorites> favoritesList) {
        return null;
    }

    /**
     * 查询后扩展
     *
     * @param favoritesList
     */
    @Override
    public ExtensionResult afterQuery(List<Favorites> favoritesList, Long referrerId, Long authId) {
        try {
            if (CollectionUtils.isEmpty(favoritesList)) {
                return ExtensionResult.ok();
            }
            // 获取收藏品牌的数据
            List<Long> brandIds = favoritesList.stream().map(Favorites::getTargetId).collect(Collectors.toList());
            BrandMultiGetRequest request = new BrandMultiGetRequest();
            request.setIds(brandIds);
            request.setTenantId(RequestContext.getTenantId());

            Response<List<BrandInfo>> response = brandReadFacade.findByIds(request);
            List<BrandInfo> brandInfoList = Assert.take(response);
            if (CollectionUtils.isEmpty(brandInfoList)) {
                return ExtensionResult.ok();
            }

            Map<Long, BrandInfo> brandDTOMap = brandInfoList.stream().collect(Collectors.toMap(BrandInfo::getId, i -> i));
            // 组装DTO
            favoritesList.forEach(favorites -> {
                BrandInfo brandInfo = brandDTOMap.get(favorites.getTargetId());

                Map<String, String> extra = CollectionUtils.isEmpty(favorites.getExtra())
                        ? Maps.newHashMap() : favorites.getExtra();
                if (brandInfo != null) {
                    extra.put("brandName", brandInfo.getName());
                    extra.put("logo", brandInfo.getLogo());
                    extra.put("id", String.valueOf(brandInfo.getId()));
                }

                favorites.setExtra(extra);
            });
            return ExtensionResult.ok();
        } catch (ServiceException e) {
            return ExtensionResult.fail(false, e.getMessage());
        } catch (Exception e) {
            log.error("favorites set {} extra info error, cause:{}", getType(), e);
            return ExtensionResult.fail(true, "favorites.set.extra.info.error");
        }
    }
}
