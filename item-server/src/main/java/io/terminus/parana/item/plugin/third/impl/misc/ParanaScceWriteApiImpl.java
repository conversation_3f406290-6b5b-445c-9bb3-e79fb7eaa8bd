package io.terminus.parana.item.plugin.third.impl.misc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.eascs.user.outeraddress.api.bean.request.OuterAddressPageRequest;
import com.eascs.user.outeraddress.api.bean.response.OuterAddressInfoResponse;
import com.eascs.user.outeraddress.api.facade.OuterAddressReadFacade;
import com.eascs.user.outeraddressbinding.api.facade.OuterAddressBindingReadFacade;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.item.area.api.bean.request.AreaItemAuditUpdateStatusRequest;
import io.terminus.parana.item.area.api.bean.request.AreaItemOffShelfRequest;
import io.terminus.parana.item.area.api.bean.request.VendorItemPublishRequest;
import io.terminus.parana.item.area.api.bean.response.AreaItemInfo;
import io.terminus.parana.item.area.api.bean.response.ItemAuditInfo;
import io.terminus.parana.item.area.api.facade.*;
import io.terminus.parana.item.area.enums.ItemAuditType;
import io.terminus.parana.item.brand.api.bean.request.BrandGetRequest;
import io.terminus.parana.item.brand.api.bean.response.BrandInfo;
import io.terminus.parana.item.brand.api.facade.BrandReadFacade;
import io.terminus.parana.item.category.api.bean.request.BackCategoryCreateRequest;
import io.terminus.parana.item.category.api.bean.request.BackCategoryQueryByPidRequest;
import io.terminus.parana.item.category.api.bean.request.BackCategoryUpdateRequest;
import io.terminus.parana.item.category.api.bean.response.BackCategoryInfo;
import io.terminus.parana.item.category.api.facade.BackCategoryReadFacade;
import io.terminus.parana.item.category.api.facade.BackCategoryWriteFacade;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceItemUpdatePriceScceRequest;
import io.terminus.parana.item.choicelot.api.facade.ChoiceLotLibSkuWriteFacade;
import io.terminus.parana.item.common.base.IdVersionPair;
import io.terminus.parana.item.common.base.JsonSupport;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.common.utils.CollectionObjectUtil;
import io.terminus.parana.item.item.api.bean.request.item.*;
import io.terminus.parana.item.item.api.bean.request.item.param.*;
import io.terminus.parana.item.item.api.bean.request.sku.FullItemQueryBySkuRequest;
import io.terminus.parana.item.item.api.bean.request.sku.SkuDefaultPriceSaveRequest;
import io.terminus.parana.item.item.api.bean.request.sku.SkuQueryByOuterIdRequest;
import io.terminus.parana.item.item.api.bean.request.sku.param.SkuAttributeParam;
import io.terminus.parana.item.item.api.bean.request.sku.param.SkuParam;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.item.api.facade.ItemReadFacade;
import io.terminus.parana.item.item.api.facade.ItemWriteFacade;
import io.terminus.parana.item.item.api.facade.SkuReadFacade;
import io.terminus.parana.item.item.api.facade.SkuWriteFacade;
import io.terminus.parana.item.item.enums.ItemStatus;
import io.terminus.parana.item.outerbrand.api.facade.OuterBrandReadPptFacade;
import io.terminus.parana.item.outerbrandbinding.api.bean.request.OuterBrandBindingCreateRequest;
import io.terminus.parana.item.outerbrandbinding.api.bean.response.OuterBrandBindingInfoResponse;
import io.terminus.parana.item.outerbrandbinding.api.facade.OuterBrandBindingReadFacade;
import io.terminus.parana.item.partnership.api.bean.request.VendorPartnershipQueryByVendorIdRequest;
import io.terminus.parana.item.partnership.api.facade.VendorPartnershipReadFacade;
import io.terminus.parana.item.plugin.third.api.misc.api.ParanaScceWriteApi;
import io.terminus.parana.item.plugin.third.impl.misc.Response.ScceCategorysMessage;
import io.terminus.parana.item.plugin.third.impl.misc.Response.ScceItemMessage;
import io.terminus.parana.item.plugin.third.impl.misc.Response.ScceItemSkuMessage;
import io.terminus.parana.item.scce.api.facade.ScceCategoryBindingReadFacade;
import io.terminus.parana.log.api.facade.StarlinkNewsReadFacade;
import io.terminus.parana.log.api.facade.StarlinkNewsWriteFacade;
import io.terminus.parana.log.api.facade.StarlinkReqErrorLogWriteFacade;
import io.terminus.parana.log.api.starlinknews.api.bean.response.StarlinkNewsInfoResponse;
import io.terminus.parana.log.api.starlinkreqerrorLog.api.bean.request.StarlinkReqErrorLogCreateRequest;
import io.terminus.parana.misc.star.sdk.api.ProductApi;
import io.terminus.parana.misc.star.sdk.exception.RequestFailException;
import io.terminus.parana.misc.star.sdk.model.ResultInfo;
import io.terminus.parana.misc.star.sdk.params.DefaultHeader;
import io.terminus.parana.misc.star.sdk.params.SpuListRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ParanaScceWriteApiImpl implements ParanaScceWriteApi {

    @Value("${scce.appkey:1519132181206216705}")
    private String appkey;

    @Value("${scce.appsecret:93397b37f17e4ddeabb23cccd5b92a73}")
    private String appsecret;
    @Value("${scce.deliveryFeeTempId:217}")
    private Long deliveryFeeTempId;
    @Value("${scce.vendorId:1100617001}")
    private Long vendorId;

    @Autowired(required = true)
    private StarlinkReqErrorLogWriteFacade starlinkReqErrorLogWriteFacade;

    @Autowired(required = true)
    private OuterAddressReadFacade outerAddressReadFacade;

    @Autowired(required = true)
    private OuterAddressBindingReadFacade outerAddressBindingReadFacade;

    @Autowired
    private ItemWriteFacade itemWriteFacade;

    @Autowired
    private ItemReadFacade itemReadFacade;

    @Autowired
    private StarlinkNewsWriteFacade starlinkNewsWriteFacade;

    @Autowired
    private StarlinkNewsReadFacade starlinkNewsReadFacade;

    @Autowired
    private AreaItemReadFacade areaItemReadFacade;

    @Autowired
    private AreaItemWriteFacade areaItemWriteFacade;

    @Autowired
    private SkuReadFacade skuReadFacade;

    @Autowired
    private SkuWriteFacade skuWriteFacade;

    @Autowired
    private AreaSkuReadFacade areaSkuReadFacade;

    @Autowired
    private VendorPartnershipReadFacade vendorPartnershipReadFacade;

    @Autowired
    private AreaItemAuditWriteFacade areaItemAuditWriteFacade;

    @Autowired
    private AreaItemAuditReadFacade areaItemAuditReadFacade;

    @Autowired
    private ScceCategoryBindingReadFacade scceCategoryBindingReadFacade;

    @Autowired
    private BackCategoryWriteFacade backCategoryWriteFacade;

    @Autowired
    private BackCategoryReadFacade backCategoryReadFacade;

    @Autowired(required = true)
    private OuterBrandReadPptFacade outerBrandReadPptFacade;

    @Autowired
    private BrandReadFacade brandReadFacade;

    @Autowired
    private OuterBrandBindingReadFacade outerBrandBindingReadFacade;

    @Autowired
    private ChoiceLotLibSkuWriteFacade choiceLotLibSkuWriteFacade;

    @Override
    public void selectScceItem(Integer pageIndex) {
        DefaultHeader defaultHeader = new DefaultHeader();
        defaultHeader.setAppKey(appkey);
        SpuListRequest spuListRequest = new SpuListRequest();
        spuListRequest.setPageSize(1);
        spuListRequest.setPageIndex(pageIndex);
        log.info(defaultHeader+"---"+appsecret);
        //查出星链3级区域id
        OuterAddressPageRequest outerAddressPageRequest = new OuterAddressPageRequest();
        outerAddressPageRequest.setLevel(3);
        Response<List<OuterAddressInfoResponse>> outAddressList = outerAddressReadFacade.list(outerAddressPageRequest);
        while (true){
            ResultInfo list = null;
            //查询星链商品id
            try{
                list = ProductApi.getSpuList(spuListRequest,defaultHeader,appsecret);
            }catch (RequestFailException e){
                StarlinkReqErrorLogCreateRequest starlinkReqErrorLogCreateRequest = new StarlinkReqErrorLogCreateRequest();
                starlinkReqErrorLogCreateRequest.setApiName("查询商品库SPUID列表");
                starlinkReqErrorLogCreateRequest.setRequestUrl("/scce/cmc/cmc/spu/open/getSpuIdList");
                starlinkReqErrorLogCreateRequest.setRequestBody(JSONUtil.toJsonStr(spuListRequest));
                starlinkReqErrorLogWriteFacade.create(starlinkReqErrorLogCreateRequest);
                return;
            }
            JSONObject json = (JSONObject) list.getData();
            List<Long> data = (List<Long>) json.get("spuIdList");
            if(data == null || CollectionObjectUtil.isEmpty(data)){
                return;
            }
            List<Long> spuIdList = data;
            ResultInfo spuDetail = null;
            //获取星链商品详情
            try{
                spuDetail = ProductApi.getSpuDetail(spuIdList, defaultHeader, appsecret);
            }catch (RequestFailException e){
                StarlinkReqErrorLogCreateRequest starlinkReqErrorLogCreateRequest = new StarlinkReqErrorLogCreateRequest();
                starlinkReqErrorLogCreateRequest.setApiName("查询SPU商品详情");
                starlinkReqErrorLogCreateRequest.setRequestUrl("/scce/cmc/cmc/spu/open/getSpuBySpuIds");
                starlinkReqErrorLogCreateRequest.setRequestBody(spuIdList.toString());
                starlinkReqErrorLogWriteFacade.create(starlinkReqErrorLogCreateRequest);
                return;
            }
            List<ScceItemMessage> scceItemMessages = objToList(spuDetail.getData(), ScceItemMessage.class);
            //处理商品数据入库
            List<ItemCreateRequest> requests = new ArrayList<>();
            for (ScceItemMessage spuDetailDatum : scceItemMessages) {
                //构建供应商商品
                ItemCreateRequest request = new ItemCreateRequest();
                ItemParam itemParam = new ItemParam();
                itemParam.setOuterId(String.valueOf(spuDetailDatum.getSpuId()));
                itemParam.setIsCrossBorder(0);
                itemParam.setShopId(vendorId);
                itemParam.setTenantId(1);
                itemParam.setUpdatedBy("admin");
                itemParam.setIsSaleAttributes(0);
                itemParam.setExtensionType(0);
                itemParam.setMainImage(spuDetailDatum.getCarouselImgList().get(0));
                itemParam.setName(spuDetailDatum.getName());
                OuterBrandBindingCreateRequest queryRequest = new OuterBrandBindingCreateRequest();
                queryRequest.setOuterCategoryId(spuDetailDatum.getBrandId());
                OuterBrandBindingInfoResponse take = Assert.take(outerBrandBindingReadFacade.view(queryRequest));
                itemParam.setBrandId(Long.valueOf(take.getCategoryId()));
                BrandGetRequest getRequest = new BrandGetRequest();
                getRequest.setId(Long.valueOf(take.getCategoryId()));
                BrandInfo brandInfo = Assert.take(brandReadFacade.findById(getRequest));
                if(brandInfo != null){
                    itemParam.setBrandName(brandInfo.getName());
                }
                //获取星链地区code
                List<Integer> limitArea = spuDetailDatum.getLimitArea();
                //过滤星链地址
                List<OuterAddressInfoResponse> result = outAddressList.getResult();
                List<OuterAddressInfoResponse> outAddress = new ArrayList();
                itemParam.setSalesArea("110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000");
                if(!CollectionObjectUtil.isEmpty(limitArea)){
                    result.stream().forEach(x -> {
                        if (limitArea.contains(x.getRegionCode())) {
                            outAddress.add(x);
                        }
                    });
                    //查询星链对应系统id
                    List<Integer> addressIds = CollectionObjectUtil.map(outAddress, OuterAddressInfoResponse::getRegionCode);
                    itemParam.setSalesArea(addressIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                }
                itemParam.setDeliveryFeeTempId(0L);
                if(spuDetailDatum.getFreeExpress() == 0){
                    itemParam.setDeliveryFeeTempId(deliveryFeeTempId);
                }
                // 类目需要映射
                itemParam.setCategoryId(Long.valueOf(spuDetailDatum.getCategoryId3()!=null?spuDetailDatum.getCategoryId3():spuDetailDatum.getCategoryId2()!=null?spuDetailDatum.getCategoryId2():spuDetailDatum.getCategoryId1()));
                Map<String,String> map = new HashMap<>();
                List<Long> categoryList = new ArrayList<>();
                categoryList.add(Long.valueOf(spuDetailDatum.getCategoryId1()));
                categoryList.add(Long.valueOf(spuDetailDatum.getCategoryId2()));
                categoryList.add(Long.valueOf(spuDetailDatum.getCategoryId3()));
                map.put("categoryList",categoryList.toString());
                map.put("supportReturn","1");
                itemParam.setExtra(map);
                itemParam.setOtherAttributes(Lists.newArrayList());
                request.setItemParam(itemParam);

                ItemDetailParam itemDetailParam = new ItemDetailParam();
                ItemDetailContentParam itemDetailContentParam = new ItemDetailContentParam();
                itemDetailContentParam.setTitle("商品详情图");
                StringBuilder st = new StringBuilder("");
                if(CollectionUtil.isNotEmpty(spuDetailDatum.getDetailImgList())){
                    for (String s : spuDetailDatum.getDetailImgList()) {
                        st.append("<p></p><div class=\"media-wrap image-wrap\"><img src=\"").append(s).append("\"/></div><p></p>");
                    }
                }
                itemDetailContentParam.setContent(st.toString());
                itemDetailParam.setPcDetail(Collections.singletonList(itemDetailContentParam));
                List<ImageParam> arrayList = new ArrayList<>();
                if(!CollectionObjectUtil.isEmpty(spuDetailDatum.getCarouselImgList()) && spuDetailDatum.getCarouselImgList().size()>1){
                    for (int i = 1; i < spuDetailDatum.getCarouselImgList().size(); i++) {
                        ImageParam imageParam = new ImageParam();
                        imageParam.setUrl(spuDetailDatum.getCarouselImgList().get(i));
                        arrayList.add(imageParam);
                    }
                }
                itemDetailParam.setImages(arrayList);
                request.setItemDetailParam(itemDetailParam);
                ItemTypeAndAttributeParam itemTypeAndAttributeParam = new ItemTypeAndAttributeParam();
                itemTypeAndAttributeParam.setItemType(1);
                itemTypeAndAttributeParam.setBusinessType(1);
                request.setItemTypeAndAttributeParam(itemTypeAndAttributeParam);

                //处理商品sku数据入库
                ResultInfo spuDetail1 = null;
                try{
                    spuDetail1 = ProductApi.getSpuDetail(spuDetailDatum.getSpuId(), defaultHeader, appsecret);
                    List<ScceItemSkuMessage> scceItemSkuMessages = objToList(spuDetail1.getData(), ScceItemSkuMessage.class);
                    log.info(""+scceItemSkuMessages);
                    List<SkuParam> skuParams = new ArrayList<>();
                    Long key = 0L;
                    for (ScceItemSkuMessage scceItemSkuMessage : scceItemSkuMessages) {
                        SkuParam skuParam = new SkuParam();
                        skuParam.setEnable(true);
                        Map<String,String> extra = new HashMap<>();
                        extra.put("minQuantity",String.valueOf(scceItemSkuMessage.getBuyStartQty()));
                        extra.put("physicalInventory",scceItemSkuMessage.getStock());
                        skuParam.setExtra(extra);
                        Map<String,Long> extraPrice = new HashMap<>();
                        BigDecimal defaultBasePrice = new BigDecimal(scceItemSkuMessage.getBasePrice()).multiply(new BigDecimal(100)).setScale(0);
                        extraPrice.put("defaultBasePrice", Long.valueOf(String.valueOf(defaultBasePrice)));
                        skuParam.setExtraPrice(extraPrice);
                        skuParam.setKey(key);
                        skuParam.setName(spuDetailDatum.getName());
                        BigDecimal originalPrice = new BigDecimal(scceItemSkuMessage.getSuggestPrice()).multiply(new BigDecimal(100)).setScale(0);
                        skuParam.setOriginalPrice(Long.valueOf(String.valueOf(originalPrice)));
                        skuParam.setPrice(originalPrice.longValue());
                        skuParam.setSkuCode("-");
                        skuParam.setOuterId(String.valueOf(scceItemSkuMessage.getSkuId()));
                        List<SkuAttributeParam> skuAttribute = new ArrayList<>();
                        for (Map<String, String> stringStringMap : scceItemSkuMessage.getSkuPropertyList()) {
                            SkuAttributeParam skuAttributeParam = new SkuAttributeParam();
                            skuAttributeParam.setAttrVal(stringStringMap.get("specValueName"));
                            skuAttributeParam.setAttrKey(stringStringMap.get("specName"));
                            skuAttributeParam.setShowImage(false);
                            skuAttributeParam.setImage(scceItemSkuMessage.getSkuPicUrl());
                            skuAttributeParam.setId(new Random().nextLong());
                            skuAttribute.add(skuAttributeParam);
                        }
                        skuParam.setAttributes(skuAttribute);
                        skuParams.add(skuParam);
                        key ++;
                    }
                    request.setSkuParamList(skuParams);
                    request.setVendorItemChannlRelationCreateRequests(new ArrayList<>());
                }catch (RequestFailException e){
                    log.error(Throwables.getStackTraceAsString(e));
                    StarlinkReqErrorLogCreateRequest starlinkReqErrorLogCreateRequest = new StarlinkReqErrorLogCreateRequest();
                    starlinkReqErrorLogCreateRequest.setApiName("查询SKU商品详情");
                    starlinkReqErrorLogCreateRequest.setRequestUrl("/scce/cmc/cmc/spu/open/listSkuBySpuId");
                    starlinkReqErrorLogCreateRequest.setRequestBody(String.valueOf(spuDetailDatum.getSpuId()));
                    starlinkReqErrorLogWriteFacade.create(starlinkReqErrorLogCreateRequest);
                    return;
                }
                request.setShopId(vendorId);
                request.setTenantId(1);
                requests.add(request);
            }
            Response<List<Long>> scceItem = itemWriteFacade.createScceItem(requests);
            spuListRequest.setPageIndex(spuListRequest.getPageIndex()+1);
            for (Long aLong : scceItem.getResult()) {
                ItemInfo itemInfo = new ItemInfo();
                itemInfo.setId(aLong);
                push(itemInfo);
            }
        }
    }

    @Override
    public void scceMessagePool() throws JsonProcessingException {
        //查询商品星链信息
        List<Integer> types = Arrays.asList(5, 6, 7, 10,11,12);
        scceMessage(types);
    }

    @Override
    public void scceMessagePool2() throws JsonProcessingException {
        //查询商品星链信息
        List<Integer> types = Collections.singletonList(4);
        scceMessage(types);
    }

    private void scceMessage(List<Integer> types) throws JsonProcessingException {
        for (int i = 0; i < 20; i++) {
            Response<List<StarlinkNewsInfoResponse>> listResponse = starlinkNewsReadFacade.listByStatus(types);
            List<StarlinkNewsInfoResponse> result = listResponse.getResult();
            log.info("星链消息处理开始{}",result.size());
            long l = System.currentTimeMillis();
            Map<Integer, List<StarlinkNewsInfoResponse>> integerListMap = CollectionObjectUtil.toMapList(result, StarlinkNewsInfoResponse::getEnumNum);
            //10和11
            updateItem(integerListMap);
            long l2 = System.currentTimeMillis();
            log.info("星链消息处理开始 处理10和11耗时{}",l2-l);
            //4 商品基础信息或商品状态变更通知 关联的商品基础信息或商品状态变更
            updateScceItemMessage(integerListMap);
            long l3 = System.currentTimeMillis();
            log.info("星链消息处理开始 处理4耗时{}",l3-l2);
            //5 商品删除通知 关联的商品spu删除
            deleteScceItem(integerListMap);
            long l4 = System.currentTimeMillis();
            log.info("星链消息处理开始 处理5耗时{}",l4-l3);
            //6 商品规格删除通知 商品规格删除
            deleteScceSku(integerListMap);
            long l5 = System.currentTimeMillis();
            log.info("星链消息处理开始 处理6耗时{}",l5-l4);
            //7 商品规格变更通知 商品规格变更
            updateScceItem(integerListMap);
            long l6 = System.currentTimeMillis();
            log.info("星链消息处理开始 处理7耗时{}",l6-l5);
            //12 选品库商品价格变更通知	企业商品库的商品sku选品库分销价变更
            updatePrice(integerListMap);
            long l7 = System.currentTimeMillis();
            log.info("星链消息处理开始 处理12耗时{}",l7-l6);
            //补偿 调整商品状态
            updateItemStatus(result);
            long l8 = System.currentTimeMillis();
            log.info("星链消息处理开始 处理补偿状态耗时{}",l8-l7);
        }

    }


    public void updateItemStatus(List<StarlinkNewsInfoResponse> list){
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        //过滤 类型为 5商品删除 6规格删除  消息
        list = list.stream().filter(f -> f.getEnumNum() != 5 && f.getEnumNum() != 6).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(list)){
            return;
        }
        Map<Integer,List<Long>> itemMap = Maps.newHashMap();
        for (StarlinkNewsInfoResponse starlinkNews : list) {
            String content = starlinkNews.getContent();
            Map<String,Long> map = JSON.parseObject(content, Map.class);
            Long spuId = map.get("spuId");

            ItemQueryBySingleIdRequest itemQueryBySingleIdRequest = new ItemQueryBySingleIdRequest();
            itemQueryBySingleIdRequest.setId(spuId);
            itemQueryBySingleIdRequest.setTenantId(1);
            ItemInfo itemInfo = Assert.take(itemReadFacade.queryByOutId(itemQueryBySingleIdRequest));
            if(itemInfo == null){
                continue;
            }
            if(starlinkNews.getEnumNum() == 8 || starlinkNews.getEnumNum() == 11){
                List<Long> li = itemMap.get(ItemStatus.OFF_SHELF.getValue());
                if(CollectionUtil.isEmpty(li)){
                    li = Lists.newArrayList();
                }
                li.add(itemInfo.getId());
                itemMap.put(ItemStatus.OFF_SHELF.getValue(),li);
                continue;
            }
            //获取星链商品详情
            ResultInfo spuDetail = null;
            try{
                DefaultHeader defaultHeader = new DefaultHeader();
                defaultHeader.setAppKey(appkey);
                spuDetail = ProductApi.getSpuDetail(Arrays.asList(spuId), defaultHeader, appsecret);
            }catch (RequestFailException e){
                log.error(Throwables.getStackTraceAsString(e));
                log.error("修改商品状态获取星链商品详情失败： {}",e.getMessage());
                continue;
            }
            List<ScceItemMessage> scceItemMessages = objToList(spuDetail.getData(), ScceItemMessage.class);
            //查询不到详情 默认认为删除
            if(CollectionUtil.isEmpty(scceItemMessages)){
                List<Long> li = itemMap.get(ItemStatus.DELETED.getValue());
                if(CollectionUtil.isEmpty(li)){
                    li = Lists.newArrayList();
                }
                li.add(itemInfo.getId());
                itemMap.put(ItemStatus.DELETED.getValue(),li);
            }
            String status = scceItemMessages.get(0).getStatus();
            //	商品状态：0、已上架1、已下架
            int s;
            switch (status){
                case "0" :
                    s = ItemStatus.ON_SHELF.getValue();
                    break;
                case "1" :
                    s = ItemStatus.OFF_SHELF.getValue();
                    break;
                default:
                    s = ItemStatus.DELETED.getValue();
                    break;
            }
            List<Long> li = itemMap.get(s);
            if(CollectionUtil.isEmpty(li)){
                li = Lists.newArrayList();
            }
            li.add(itemInfo.getId());
            itemMap.put(s,li);
        }
        itemWriteFacade.updateItemStatus(itemMap, new LinkedHashSet<>());
    }

    private void updateScceItemMessage(Map<Integer, List<StarlinkNewsInfoResponse>> integerListMap) {
        List<StarlinkNewsInfoResponse> itemUpdateStarlink = integerListMap.get(4);
        if(CollectionObjectUtil.isEmpty(itemUpdateStarlink)){
            return;
        }
        log.info("商品更新开始  {}",itemUpdateStarlink.size());
        try {
            OuterAddressPageRequest outerAddressPageRequest = new OuterAddressPageRequest();
            outerAddressPageRequest.setLevel(3);
            Response<List<OuterAddressInfoResponse>> outAddressList = outerAddressReadFacade.list(outerAddressPageRequest);
            List<StarlinkNewsInfoResponse> deleteStarlinkNewsInfoResponses = new ArrayList<>();
            for (StarlinkNewsInfoResponse starlinkNewsInfoResponse : itemUpdateStarlink) {
                long ll = System.currentTimeMillis();
                String content = starlinkNewsInfoResponse.getContent();
                Map<String,Long> map = JSON.parseObject(content, Map.class);
                Long spuId = map.get("spuId");
                //获取星链商品详情
                ResultInfo spuDetail = null;
                try{
                    DefaultHeader defaultHeader = new DefaultHeader();
                    defaultHeader.setAppKey(appkey);
                    spuDetail = ProductApi.getSpuDetail(Arrays.asList(spuId), defaultHeader, appsecret);
                }catch (RequestFailException e){
                    deleteStarlinkNewsInfoResponses.add(starlinkNewsInfoResponse);
                    StarlinkReqErrorLogCreateRequest starlinkReqErrorLogCreateRequest = new StarlinkReqErrorLogCreateRequest();
                    starlinkReqErrorLogCreateRequest.setApiName("查询SPU商品详情");
                    starlinkReqErrorLogCreateRequest.setRequestUrl("/scce/cmc/cmc/spu/open/getSpuBySpuIds");
                    starlinkReqErrorLogCreateRequest.setRequestBody(spuId.toString());
                    starlinkReqErrorLogWriteFacade.create(starlinkReqErrorLogCreateRequest);
                    continue;
                }
                List<ScceItemMessage> scceItemMessages = objToList(spuDetail.getData(), ScceItemMessage.class);
                ItemQueryBySingleIdRequest itemQueryBySingleIdRequest = new ItemQueryBySingleIdRequest();
                itemQueryBySingleIdRequest.setId(spuId);
                itemQueryBySingleIdRequest.setTenantId(1);
                ItemInfo itemInfo = Assert.take(itemReadFacade.queryByOutId(itemQueryBySingleIdRequest));
                if(itemInfo == null){
                    break;
                }
                itemInfo.setName(scceItemMessages.get(0).getName());
//            OuterBrandQueryRequest queryRequest = new OuterBrandQueryRequest();
//            queryRequest.setId(Long.valueOf(scceItemMessages.get(0).getBrandId()));
//            OuterBrandInfoResponse take = Assert.take(outerBrandReadPptFacade.view(queryRequest));
//            itemInfo.setBrandId(Long.valueOf(take.getCategoryId()));
//            BrandGetRequest getRequest = new BrandGetRequest();
//            getRequest.setId(Long.valueOf(take.getCategoryId()));
//            BrandInfo brandInfo = Assert.take(brandReadFacade.findById(getRequest));
//            if(brandInfo != null){
//                itemInfo.setBrandName(brandInfo.getName());
//            }
                OuterBrandBindingCreateRequest queryRequest = new OuterBrandBindingCreateRequest();
                queryRequest.setOuterCategoryId(scceItemMessages.get(0).getBrandId());
                OuterBrandBindingInfoResponse take = Assert.take(outerBrandBindingReadFacade.view(queryRequest));
                itemInfo.setBrandId(Long.valueOf(take.getCategoryId()));
                BrandGetRequest getRequest = new BrandGetRequest();
                getRequest.setId(Long.valueOf(take.getCategoryId()));
                BrandInfo brandInfo = Assert.take(brandReadFacade.findById(getRequest));
                if(brandInfo != null){
                    itemInfo.setBrandName(brandInfo.getName());
                }
                itemInfo.setMainImage(scceItemMessages.get(0).getCarouselImgList().get(0));
                //获取星链地区code
                List<Integer> limitArea = scceItemMessages.get(0).getLimitArea();
                //过滤星链地址
                List<OuterAddressInfoResponse> result = outAddressList.getResult();
                List<OuterAddressInfoResponse> outAddress = new ArrayList();
                itemInfo.setSalesArea("110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000");
                if(!CollectionObjectUtil.isEmpty(limitArea)){
                    result.stream().forEach(x -> {
                        if (limitArea.contains(x.getRegionCode())) {
                            outAddress.add(x);
                        }
                    });
                    //查询星链对应系统id
                    List<Integer> addressIds = CollectionObjectUtil.map(outAddress, OuterAddressInfoResponse::getRegionCode);
                    itemInfo.setSalesArea(addressIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                }
                itemInfo.setDeliveryFeeTempId(0L);
                if(scceItemMessages.get(0).getFreeExpress() == 0){
                    itemInfo.setDeliveryFeeTempId(deliveryFeeTempId);
                }
                ItemScceUpdateRequest request = new ItemScceUpdateRequest();
                itemInfo.setStatus(Integer.valueOf(scceItemMessages.get(0).getStatus()));
                request.setItemInfo(itemInfo);
                try{
                    itemWriteFacade.updateItem(request);
                    push(itemInfo);
                }catch (Exception e){
                    deleteStarlinkNewsInfoResponses.add(starlinkNewsInfoResponse);
                    log.error("执行错误11， {}",e.getMessage());
                }
                log.info("商品规格变动处理耗时 {}",(System.currentTimeMillis() - ll)/1000);
            }
            if(!CollectionObjectUtil.isEmpty(deleteStarlinkNewsInfoResponses)){
                itemUpdateStarlink.removeAll(deleteStarlinkNewsInfoResponses);
            }
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
            log.error("商品基础信息或商品状态变更通知 关联的商品基础信息或商品状态变更 失败 {}",e.getMessage());
        }finally {
            log.info("更新消息长度：{}",itemUpdateStarlink.size());
            List<Long> ids = itemUpdateStarlink.stream().map(StarlinkNewsInfoResponse::getId).collect(Collectors.toList());
            if(!CollectionObjectUtil.isEmpty(ids)){
                starlinkNewsWriteFacade.updateById(ids,1);
            }
        }
    }

    @Override
    public void scceCategory() {
        DefaultHeader defaultHeader = new DefaultHeader();
        defaultHeader.setAppKey(appkey);
        ResultInfo categorys = ProductApi.getCategorys(0L, defaultHeader, appsecret);
        List<ScceCategorysMessage> scceCategorysMessageList = new ArrayList<>();
        List<ScceCategorysMessage> scceItemMessages = objToList(categorys.getData(), ScceCategorysMessage.class);
        scceCategorysMessageList.addAll(scceItemMessages);
        addList(defaultHeader, scceCategorysMessageList, scceItemMessages);
        List<BackCategoryCreateRequest> backCategoryCreateRequests = new ArrayList<>();
        Map<Long, ScceCategorysMessage> scceCategorysMessageMap = CollectionObjectUtil.toMap(scceCategorysMessageList, ScceCategorysMessage::getPid);
        for (ScceCategorysMessage scceCategorysMessage : scceCategorysMessageList) {
            if(scceCategorysMessage.getId().equals(1395308905109655554L)){
                log.info("未拉取分类:{}",scceCategorysMessage);
            }
            BackCategoryCreateRequest backCategoryCreateRequest = new BackCategoryCreateRequest();
            backCategoryCreateRequest.setId(scceCategorysMessage.getId().longValue());
            backCategoryCreateRequest.setLevel(scceCategorysMessage.getLevel());
            backCategoryCreateRequest.setName(scceCategorysMessage.getName());
            backCategoryCreateRequest.setPid(scceCategorysMessage.getPid().longValue());
            backCategoryCreateRequest.setUpdatedBy("admin");
            backCategoryCreateRequest.setTenantId(1);
            backCategoryCreateRequest.setExtensionType(0);
            backCategoryCreateRequest.setHasChildren(scceCategorysMessageMap.get(scceCategorysMessage.getId()) == null?false:true);
            backCategoryCreateRequests.add(backCategoryCreateRequest);
        }
        List<Long> collect = backCategoryCreateRequests.stream().map(BackCategoryCreateRequest::getId).collect(Collectors.toList());
        BackCategoryQueryByPidRequest request = new BackCategoryQueryByPidRequest();
        request.setVendorBackCategoryIds(collect);
        List<BackCategoryInfo> list = Assert.take(backCategoryReadFacade.selectAll(request));
        backCategoryCreateRequests = backCategoryCreateRequests.stream().sorted(Comparator.comparing(BackCategoryCreateRequest::getLevel)).collect(Collectors.toList());
        if(CollectionObjectUtil.isEmpty(list)){
            backCategoryWriteFacade.creates(backCategoryCreateRequests);
        }else{
            Map<Long, BackCategoryInfo> longBackCategoryInfoMap = CollectionObjectUtil.toMap(list, BackCategoryInfo::getId);
            for (BackCategoryCreateRequest backCategoryCreateRequest : backCategoryCreateRequests) {
                BackCategoryInfo backCategoryInfo = longBackCategoryInfoMap.get(backCategoryCreateRequest.getId());
                if(backCategoryInfo == null){
                    backCategoryWriteFacade.create(backCategoryCreateRequest);
                }else if(backCategoryCreateRequest.getName().equals(backCategoryInfo.getName()) && backCategoryCreateRequest.getLevel() == backCategoryInfo.getLevel()){
                    BackCategoryUpdateRequest updateRequest = new BackCategoryUpdateRequest();
                    updateRequest.setId(backCategoryCreateRequest.getId());
                    updateRequest.setName(backCategoryCreateRequest.getName());
                    updateRequest.setTenantId(1);
                    updateRequest.setUpdatedBy("admin");
                    backCategoryWriteFacade.updateName(updateRequest);
                }
            }
        }


    }

    private static void addList(DefaultHeader defaultHeader, List<ScceCategorysMessage> scceCategorysMessageList, List<ScceCategorysMessage> scceItemMessages) {
        for (ScceCategorysMessage scceItemMessage : scceItemMessages) {
            if(scceItemMessage.getLevel() == 3){
                continue;
            }
            ResultInfo resultInfo = ProductApi.getCategorys(scceItemMessage.getId(), defaultHeader, "93397b37f17e4ddeabb23cccd5b92a73");
            List<ScceCategorysMessage> toList = objToList(resultInfo.getData(), ScceCategorysMessage.class);
            if(CollectionObjectUtil.isEmpty(toList) ){
                continue;
            }
            scceCategorysMessageList.addAll(toList);
            addList(defaultHeader,scceCategorysMessageList,toList);
        }
    }

    private void updateItem(Map<Integer, List<StarlinkNewsInfoResponse>> integerListMap) {
        List<StarlinkNewsInfoResponse> starlinkNewsInfoResponses = new ArrayList<>();
        try {
            //10 商品库选品变动通知	选品库商品spu被加入企业商品库
            List<StarlinkNewsInfoResponse> addItemStarlink = integerListMap.get(10);
            //11 商品库选品变动通知 选品库商品spu被移出企业商品库
            List<StarlinkNewsInfoResponse> deleteItemStarLink = integerListMap.get(11);
            if(CollectionObjectUtil.isEmpty(addItemStarlink) && CollectionObjectUtil.isEmpty(deleteItemStarLink) ){
                return;
            }
            if(!CollectionObjectUtil.isEmpty(addItemStarlink)){
                starlinkNewsInfoResponses.addAll(addItemStarlink);
            }
            if(!CollectionObjectUtil.isEmpty(deleteItemStarLink)){
                starlinkNewsInfoResponses.addAll(deleteItemStarLink);
            }
            //查出星链3级区域id
            OuterAddressPageRequest outerAddressPageRequest = new OuterAddressPageRequest();
            outerAddressPageRequest.setLevel(3);
            Response<List<OuterAddressInfoResponse>> outAddressList = outerAddressReadFacade.list(outerAddressPageRequest);

            starlinkNewsInfoResponses.stream().sorted(Comparator.comparing(StarlinkNewsInfoResponse::getCreatedAt));
            List<StarlinkNewsInfoResponse> deleteStarlinkNewsInfoResponses = new ArrayList<>();
            for (StarlinkNewsInfoResponse starlinkNewsInfoResponse : starlinkNewsInfoResponses) {
                if(starlinkNewsInfoResponse.getEnumNum()==10){
                    String content = starlinkNewsInfoResponse.getContent();
                    Map<String,Long> map = JSON.parseObject(content, Map.class);
                    ItemQueryBySingleIdRequest itemQueryBySingleIdRequest = new ItemQueryBySingleIdRequest();
                    itemQueryBySingleIdRequest.setId(map.get("spuId"));
                    itemQueryBySingleIdRequest.setTenantId(1);

                    ItemInfo info = Assert.take(itemReadFacade.queryByOutId(itemQueryBySingleIdRequest));
                    if(info != null){
                        List<IdVersionPair> list = new ArrayList<>();
                        IdVersionPair pair = new IdVersionPair();
                        pair.setVersion(info.getVersion());
                        pair.setId(info.getId());
                        list.add(pair);
                        ItemUpdateStatusByAdminRequest request = new ItemUpdateStatusByAdminRequest();
                        request.setStatus(-1);
                        request.setTenantId(1);
                        request.setUpdatedBy("admin");
                        request.setTargetList(list);
                        try{
                            itemWriteFacade.adminUpdateStatus(request);
                            push(info);
                        }catch (Exception e){
                            log.error("更新商品信息失败:{}",e);
                            deleteStarlinkNewsInfoResponses.add(starlinkNewsInfoResponse);
                        }
                    }else{
                        DefaultHeader defaultHeader = new DefaultHeader();
                        defaultHeader.setAppKey(appkey);
                        ResultInfo list = null;
                        ResultInfo spuDetail = null;
                        //获取星链商品详情
                        try{
                            spuDetail = ProductApi.getSpuDetail(Arrays.asList(map.get("spuId")), defaultHeader, appsecret);
                        }catch (RequestFailException e){
                            deleteStarlinkNewsInfoResponses.add(starlinkNewsInfoResponse);
                            StarlinkReqErrorLogCreateRequest starlinkReqErrorLogCreateRequest = new StarlinkReqErrorLogCreateRequest();
                            starlinkReqErrorLogCreateRequest.setApiName("查询SPU商品详情");
                            starlinkReqErrorLogCreateRequest.setRequestUrl("/scce/cmc/cmc/spu/open/getSpuBySpuIds");
                            starlinkReqErrorLogCreateRequest.setRequestBody(Arrays.asList(map.get("spuId")).toString());
                            starlinkReqErrorLogWriteFacade.create(starlinkReqErrorLogCreateRequest);
                            continue;
                        }
                        List<ScceItemMessage> scceItemMessages = objToList(spuDetail.getData(), ScceItemMessage.class);
                        //处理商品数据入库
                        List<ItemCreateRequest> requests = new ArrayList<>();
                        for (ScceItemMessage spuDetailDatum : scceItemMessages) {
                            try{
                                //构建供应商商品
                                ItemCreateRequest request = new ItemCreateRequest();
                                ItemParam itemParam = new ItemParam();
                                itemParam.setOuterId(String.valueOf(spuDetailDatum.getSpuId()));
                                itemParam.setIsCrossBorder(0);
                                itemParam.setShopId(vendorId);
                                itemParam.setTenantId(1);
                                itemParam.setUpdatedBy("admin");
                                itemParam.setIsSaleAttributes(0);
                                itemParam.setExtensionType(0);
                                itemParam.setMainImage(spuDetailDatum.getCarouselImgList().get(0));
                                itemParam.setName(spuDetailDatum.getName());
                                OuterBrandBindingCreateRequest queryRequest = new OuterBrandBindingCreateRequest();
                                queryRequest.setOuterCategoryId(spuDetailDatum.getBrandId());
                                OuterBrandBindingInfoResponse take = Assert.take(outerBrandBindingReadFacade.view(queryRequest));
                                itemParam.setBrandId(take.getCategoryId());
                                BrandGetRequest getRequest = new BrandGetRequest();
                                getRequest.setId(take.getCategoryId());
                                BrandInfo brandInfo = Assert.take(brandReadFacade.findById(getRequest));
                                if(brandInfo != null){
                                    itemParam.setBrandName(brandInfo.getName());
                                }
                                //获取星链地区code
                                List<Integer> limitArea = spuDetailDatum.getLimitArea();
                                //过滤星链地址
                                List<OuterAddressInfoResponse> result = outAddressList.getResult();
                                List<OuterAddressInfoResponse> outAddress = new ArrayList();
                                itemParam.setSalesArea("110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000");
                                if(!CollectionObjectUtil.isEmpty(limitArea)){
                                    result.stream().forEach(x -> {
                                        if (limitArea.contains(x.getRegionCode())) {
                                            outAddress.add(x);
                                        }
                                    });
                                    //查询星链对应系统id
                                    List<Integer> addressIds = CollectionObjectUtil.map(outAddress, OuterAddressInfoResponse::getRegionCode);
                                    itemParam.setSalesArea(addressIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                                }
                                itemParam.setDeliveryFeeTempId(0L);
                                if(spuDetailDatum.getFreeExpress() == 0){
                                    itemParam.setDeliveryFeeTempId(deliveryFeeTempId);
                                }
                                // 类目需要映射
                                itemParam.setCategoryId(spuDetailDatum.getCategoryId3()!=null?spuDetailDatum.getCategoryId3():spuDetailDatum.getCategoryId2()!=null?spuDetailDatum.getCategoryId2():spuDetailDatum.getCategoryId1());
                                Map<String,String> maps = new HashMap<>();
                                List<Long> categoryList = new ArrayList<>();
                                categoryList.add(spuDetailDatum.getCategoryId1());
                                categoryList.add(spuDetailDatum.getCategoryId2());
                                categoryList.add(spuDetailDatum.getCategoryId3());
                                maps.put("categoryList",categoryList.toString());
                                maps.put("supportReturn","1");
                                itemParam.setExtra(maps);
                                itemParam.setOtherAttributes(Lists.newArrayList());
                                request.setItemParam(itemParam);

                                ItemDetailParam itemDetailParam = new ItemDetailParam();
                                ItemDetailContentParam itemDetailContentParam = new ItemDetailContentParam();
                                itemDetailContentParam.setTitle("商品详情图");
                                StringBuilder st = new StringBuilder("");
                                if(CollectionUtil.isNotEmpty(spuDetailDatum.getDetailImgList())){
                                    for (String s : spuDetailDatum.getDetailImgList()) {
                                        st.append("<p></p><div class=\"media-wrap image-wrap\"><img src=\"").append(s).append("\"/></div><p></p>");
                                    }
                                }
                                itemDetailContentParam.setContent(st.toString());
                                itemDetailParam.setPcDetail(Collections.singletonList(itemDetailContentParam));
                                List<ImageParam> arrayList = new ArrayList<>();
                                if(!CollectionObjectUtil.isEmpty(spuDetailDatum.getCarouselImgList()) && spuDetailDatum.getCarouselImgList().size()>1){
                                    for (int i = 1; i < spuDetailDatum.getCarouselImgList().size(); i++) {
                                        ImageParam imageParam = new ImageParam();
                                        imageParam.setUrl(spuDetailDatum.getCarouselImgList().get(i));
                                        arrayList.add(imageParam);
                                    }
                                }
                                itemDetailParam.setImages(arrayList);
                                request.setItemDetailParam(itemDetailParam);
                                ItemTypeAndAttributeParam itemTypeAndAttributeParam = new ItemTypeAndAttributeParam();
                                itemTypeAndAttributeParam.setItemType(1);
                                itemTypeAndAttributeParam.setBusinessType(1);
                                request.setItemTypeAndAttributeParam(itemTypeAndAttributeParam);

                                //处理商品sku数据入库
                                ResultInfo spuDetail1 = null;
                                spuDetail1 = ProductApi.getSpuDetail(spuDetailDatum.getSpuId(), defaultHeader, appsecret);
                                List<ScceItemSkuMessage> scceItemSkuMessages = objToList(spuDetail1.getData(), ScceItemSkuMessage.class);
                                log.info(JSONUtil.toJsonStr(scceItemSkuMessages));
                                List<SkuParam> skuParams = new ArrayList<>();
                                Long key = 0L;
                                for (ScceItemSkuMessage scceItemSkuMessage : scceItemSkuMessages) {
                                    SkuParam skuParam = new SkuParam();
                                    skuParam.setEnable(true);
                                    Map<String,String> extra = new HashMap<>();
                                    extra.put("minQuantity",String.valueOf(scceItemSkuMessage.getBuyStartQty()));
                                    extra.put("physicalInventory",scceItemSkuMessage.getStock());
                                    skuParam.setExtra(extra);
                                    Map<String,Long> extraPrice = new HashMap<>();
                                    BigDecimal defaultBasePrice = new BigDecimal(scceItemSkuMessage.getBasePrice()).multiply(new BigDecimal(100)).setScale(0);
                                    extraPrice.put("defaultBasePrice", Long.valueOf(String.valueOf(defaultBasePrice)));
                                    skuParam.setExtraPrice(extraPrice);
                                    skuParam.setKey(key);
                                    skuParam.setName(spuDetailDatum.getName());
                                    BigDecimal originalPrice = new BigDecimal(scceItemSkuMessage.getSuggestPrice()).multiply(new BigDecimal(100)).setScale(0);
                                    skuParam.setOriginalPrice(Long.valueOf(String.valueOf(originalPrice)));
                                    skuParam.setPrice(originalPrice.longValue());
                                    skuParam.setSkuCode("-");
                                    skuParam.setOuterId(String.valueOf(scceItemSkuMessage.getSkuId()));
                                    List<SkuAttributeParam> skuAttribute = new ArrayList<>();
                                    for (Map<String, String> stringStringMap : scceItemSkuMessage.getSkuPropertyList()) {
                                        SkuAttributeParam skuAttributeParam = new SkuAttributeParam();
                                        skuAttributeParam.setAttrVal(stringStringMap.get("specValueName"));
                                        skuAttributeParam.setAttrKey(stringStringMap.get("specName"));
                                        skuAttributeParam.setShowImage(false);
                                        skuAttributeParam.setImage(scceItemSkuMessage.getSkuPicUrl());
                                        skuAttributeParam.setId(new Random().nextLong());
                                        skuAttribute.add(skuAttributeParam);
                                    }
                                    skuParam.setAttributes(skuAttribute);
                                    skuParams.add(skuParam);
                                    key ++;
                                }
                                request.setSkuParamList(skuParams);
                                request.setVendorItemChannlRelationCreateRequests(new ArrayList<>());
                                request.setShopId(vendorId);
                                request.setTenantId(1);
                                requests.add(request);
                            }catch (RequestFailException e){
                                deleteStarlinkNewsInfoResponses.add(starlinkNewsInfoResponse);
                                log.error(Throwables.getStackTraceAsString(e));
                                StarlinkReqErrorLogCreateRequest starlinkReqErrorLogCreateRequest = new StarlinkReqErrorLogCreateRequest();
                                starlinkReqErrorLogCreateRequest.setApiName("查询SKU商品详情");
                                starlinkReqErrorLogCreateRequest.setRequestUrl("/scce/cmc/cmc/spu/open/listSkuBySpuId");
                                starlinkReqErrorLogCreateRequest.setRequestBody(String.valueOf(spuDetailDatum.getSpuId()));
                                starlinkReqErrorLogWriteFacade.create(starlinkReqErrorLogCreateRequest);
                                continue;
                            }catch (Exception e){
                                deleteStarlinkNewsInfoResponses.add(starlinkNewsInfoResponse);
                                log.error(Throwables.getStackTraceAsString(e));
                                continue;
                            }
                        }
                        try{
                            Response<List<Long>> scceItem = itemWriteFacade.createScceItem(requests);
                            for (Long aLong : scceItem.getResult()) {
                                ItemInfo itemInfo = new ItemInfo();
                                itemInfo.setId(aLong);
                                push(itemInfo);
                            }
                        }catch (Exception e){
                            log.error("创建商品失败:{}",e.getMessage());
                            deleteStarlinkNewsInfoResponses.add(starlinkNewsInfoResponse);
                        }
                    }
                }else if (starlinkNewsInfoResponse.getEnumNum()==11){
                    String content = starlinkNewsInfoResponse.getContent();
                    Map<String,Long> map = JSON.parseObject(content, Map.class);
                    ItemQueryBySingleIdRequest itemQueryBySingleIdRequest = new ItemQueryBySingleIdRequest();
                    itemQueryBySingleIdRequest.setId(map.get("spuId"));
                    itemQueryBySingleIdRequest.setTenantId(1);
                    ItemInfo info = Assert.take(itemReadFacade.queryByOutId(itemQueryBySingleIdRequest));
                    info.setStatus(-3);
                    ItemCancelRequest request = new ItemCancelRequest();
                    request.setItemIds(Collections.singleton(info.getId()));
                    request.setUpdatedBy("admin");
                    request.setVendorId(vendorId);
                    request.setTenantId(1);
                    itemWriteFacade.cancelAll(request);
                }
            }
            if(!CollectionObjectUtil.isEmpty(deleteStarlinkNewsInfoResponses)){
                starlinkNewsInfoResponses.removeAll(deleteStarlinkNewsInfoResponses);
            }
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
            log.error("10和11消息处理失败：{}",e.getMessage());
        }finally {
            List<Long> ids = starlinkNewsInfoResponses.stream().map(StarlinkNewsInfoResponse::getId).collect(Collectors.toList());
            if(!CollectionObjectUtil.isEmpty(ids)){
                starlinkNewsWriteFacade.updateById(ids,1);
            }
        }


    }

    private Set<Long> push(ItemInfo info) {
        VendorPartnershipQueryByVendorIdRequest vendorPartnershipQueryByVendorIdRequest = new VendorPartnershipQueryByVendorIdRequest();
        vendorPartnershipQueryByVendorIdRequest.setVendorId(vendorId);
        Set<Long> ids = Assert.take(vendorPartnershipReadFacade.findOperatorIdsByVendorId(vendorPartnershipQueryByVendorIdRequest));
        for (Long id : ids) {
            AreaItemOffShelfRequest areaItemOffShelfRequest = new AreaItemOffShelfRequest();
            areaItemOffShelfRequest.setUpdateBy("admin");
            areaItemOffShelfRequest.setTenantId(1);
            areaItemOffShelfRequest.setItemIdSet(Collections.singleton(info.getId()));
            areaItemOffShelfRequest.setOperatorId(id);
            try{
                areaItemWriteFacade.operatorOffShelf(areaItemOffShelfRequest);
            }catch (Exception e){
                log.error("下架商品失败:itemId:{}",id);
                log.error(Throwables.getStackTraceAsString(e));
            }
        }
        VendorItemPublishRequest vendorItemPublishRequest = new VendorItemPublishRequest();
        vendorItemPublishRequest.setItemIdSet(Collections.singleton(info.getId()));
        vendorItemPublishRequest.setOperatorIdSet(ids);
        vendorItemPublishRequest.setVendorId(vendorId);
        vendorItemPublishRequest.setUpdatedBy("admin");
        vendorItemPublishRequest.setTenantId(1);
        areaItemWriteFacade.publishByItems(vendorItemPublishRequest);
        ItemCancelRequest itemCancelRequest = new ItemCancelRequest();
        itemCancelRequest.setItemId(info.getId());
        itemCancelRequest.setTenantId(1);
        itemCancelRequest.setUpdatedBy("admin");
        Response<List<ItemAuditInfo>> itemAudits = areaItemAuditReadFacade.selectItemAudit(itemCancelRequest);
        for (ItemAuditInfo itemAuditInfo : itemAudits.getResult()) {
            AreaItemAuditUpdateStatusRequest areaItemAuditUpdateStatusRequest = new AreaItemAuditUpdateStatusRequest();
            areaItemAuditUpdateStatusRequest.setAuditBy("admin");
            areaItemAuditUpdateStatusRequest.setId(itemAuditInfo.getId());
            areaItemAuditUpdateStatusRequest.setItemId(info.getId());
            areaItemAuditUpdateStatusRequest.setStatus("1");
            areaItemAuditUpdateStatusRequest.setRemarks("自动审核");
            areaItemAuditUpdateStatusRequest.setTenantId(RequestContext.getTenantId());
            areaItemAuditUpdateStatusRequest.setOperatorId(itemAuditInfo.getOperatorId());
            areaItemAuditUpdateStatusRequest.setType(ItemAuditType.BASE.getValue());
            areaItemAuditUpdateStatusRequest.setAuditBy("admin");
            areaItemAuditWriteFacade.updateStatus(areaItemAuditUpdateStatusRequest);
        }
        return ids;
    }

    private void updateScceItem(Map<Integer, List<StarlinkNewsInfoResponse>> integerListMap) throws JsonProcessingException {
        List<StarlinkNewsInfoResponse> skuUpdateStarlink = integerListMap.get(7);
        if(CollectionObjectUtil.isEmpty(skuUpdateStarlink)){
            return;
        }
        try {
            for (StarlinkNewsInfoResponse starlinkNewsInfoResponse : skuUpdateStarlink) {
                String content = starlinkNewsInfoResponse.getContent();
                Map<String,Long> map = JSON.parseObject(content, Map.class);
                Long skuId = map.get("skuId");
                SkuQueryByOuterIdRequest skuQueryByOuterIdRequest = new SkuQueryByOuterIdRequest();
                skuQueryByOuterIdRequest.setOuterIdSet(Collections.singleton(skuId.toString()));
                skuQueryByOuterIdRequest.setTenantId(1);
                skuQueryByOuterIdRequest.setShopId(vendorId);
                Response<List<SkuInfo>> query = skuReadFacade.queryByOuterId(skuQueryByOuterIdRequest);
                List<SkuInfo> skuInfoList = query.getResult();
                //如果没有则是新增规格
                if(CollectionObjectUtil.isEmpty(skuInfoList)){
                    log.info("修改规格参数：{}",JSON.toJSONString(map));
                    Long spuId = map.get("spuId");
                    ItemQueryBySingleIdRequest itemQueryBySingleIdRequest = new ItemQueryBySingleIdRequest();
                    itemQueryBySingleIdRequest.setId(spuId);
                    itemQueryBySingleIdRequest.setTenantId(1);
                    Response<ItemInfo> response = itemReadFacade.queryByOutId(itemQueryBySingleIdRequest);
                    DefaultHeader defaultHeader = new DefaultHeader();
                    defaultHeader.setAppKey(appkey);
                    ResultInfo spuDetail = ProductApi.getSpuDetail(spuId, defaultHeader, appsecret);
                    List<ScceItemSkuMessage> scceItemSkuMessages = objToList(spuDetail.getData(), ScceItemSkuMessage.class);
                    Map<Long, ScceItemSkuMessage> longScceItemSkuMessageMap = CollectionObjectUtil.toMap(scceItemSkuMessages, ScceItemSkuMessage::getSkuId);
                    ItemInfo info = response.getResult();
                    SkuInfo skuInfo = new SkuInfo();
                    skuInfo.setStatus(-1);
                    skuInfo.setItemId(info.getId());
                    skuInfo.setOuterId(skuId.toString());
                    skuInfo.setShopId(vendorId);
                    skuInfo.setName(info.getName());
                    skuInfo.setImage(longScceItemSkuMessageMap.get(skuId).getSkuPicUrl());
                    BigDecimal originalPrice = new BigDecimal(longScceItemSkuMessageMap.get(skuId).getOfficialDistriPrice()).multiply(new BigDecimal(100)).setScale(0);
                    BigDecimal basePrice = new BigDecimal(longScceItemSkuMessageMap.get(skuId).getBasePrice()).multiply(new BigDecimal(100)).setScale(0);
                    skuInfo.setPrice(originalPrice.longValue());
                    skuInfo.setOriginalPrice(originalPrice.longValue());
                    Map<String, Long> priceJson = new HashMap<>();
                    priceJson.put("defaultPrice",originalPrice.longValue());
                    priceJson.put("defaultBasePrice",basePrice.longValue());
                    priceJson.put("defaultPCommission",0L);
                    priceJson.put("defaultCommission",0L);
                    skuInfo.setPriceJson(JsonSupport.JSON.objectMapper.writeValueAsString(priceJson));
                    Map<String, String> extra = new HashMap<>();
                    extra.put("minQuantity",longScceItemSkuMessageMap.get(skuId).getBuyStartQty().toString());
                    extra.put("physicalInventory",longScceItemSkuMessageMap.get(skuId).getStock());
                    skuInfo.setExtra(extra);
                    FullItemQueryBySkuRequest fullItemQueryBySkuRequest = new FullItemQueryBySkuRequest();
                    fullItemQueryBySkuRequest.setSku(skuInfo);
                    skuWriteFacade.createSku(fullItemQueryBySkuRequest);
                    ItemInfo itemInfo = new ItemInfo();
                    itemInfo.setId(spuId);
                    push(itemInfo);
                }else{
                    for (SkuInfo sku : skuInfoList) {
                        Long spuId = map.get("spuId");
                        ItemQueryBySingleIdRequest itemQueryBySingleIdRequest = new ItemQueryBySingleIdRequest();
                        itemQueryBySingleIdRequest.setId(spuId);
                        itemQueryBySingleIdRequest.setTenantId(1);
                        Response<ItemInfo> response = itemReadFacade.queryByOutId(itemQueryBySingleIdRequest);
                        DefaultHeader defaultHeader = new DefaultHeader();
                        defaultHeader.setAppKey(appkey);
                        ResultInfo spuDetail = ProductApi.getSpuDetail(spuId, defaultHeader, appsecret);
                        List<ScceItemSkuMessage> scceItemSkuMessages = objToList(spuDetail.getData(), ScceItemSkuMessage.class);
                        Map<Long, ScceItemSkuMessage> longScceItemSkuMessageMap = CollectionObjectUtil.toMap(scceItemSkuMessages, ScceItemSkuMessage::getSkuId);
                        ItemInfo info = response.getResult();
                        if(info == null){
                            continue;
                        }
                        SkuInfo skuInfo = new SkuInfo();
                        skuInfo.setId(sku.getId());
                        skuInfo.setStatus(sku.getStatus());
                        skuInfo.setItemId(info.getId());
                        skuInfo.setOuterId(skuId.toString());
                        skuInfo.setShopId(vendorId);
                        skuInfo.setName(info.getName());
                        skuInfo.setImage(longScceItemSkuMessageMap.get(skuId).getSkuPicUrl());
                        BigDecimal originalPrice = new BigDecimal(longScceItemSkuMessageMap.get(skuId).getOfficialDistriPrice()).multiply(new BigDecimal(100)).setScale(0);
                        BigDecimal basePrice = new BigDecimal(longScceItemSkuMessageMap.get(skuId).getBasePrice()).multiply(new BigDecimal(100)).setScale(0);
                        skuInfo.setPrice(originalPrice.longValue());
                        skuInfo.setOriginalPrice(originalPrice.longValue());
                        Map<String, Long> priceJson = new HashMap<>();
                        priceJson.put("defaultPrice",originalPrice.longValue());
                        priceJson.put("defaultBasePrice",basePrice.longValue());
                        priceJson.put("defaultPCommission",0L);
                        priceJson.put("defaultCommission",0L);
                        skuInfo.setPriceJson(JsonSupport.JSON.objectMapper.writeValueAsString(priceJson));
                        Map<String, String> extra = new HashMap<>();
                        extra.put("minQuantity",longScceItemSkuMessageMap.get(skuId).getBuyStartQty().toString());
                        extra.put("physicalInventory",longScceItemSkuMessageMap.get(skuId).getStock());
                        skuInfo.setExtra(extra);
                        FullItemQueryBySkuRequest fullItemQueryBySkuRequest = new FullItemQueryBySkuRequest();
                        fullItemQueryBySkuRequest.setSku(skuInfo);
                        skuWriteFacade.updateSku(fullItemQueryBySkuRequest);
                        ItemInfo itemInfo = new ItemInfo();
                        itemInfo.setId(info.getId());
                        push(itemInfo);
                    }

                }

            }
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
            log.error("商品规格变更通知 商品规格变更  失败：{}",e.getMessage());
        }finally {
            List<Long> ids = skuUpdateStarlink.stream().map(StarlinkNewsInfoResponse::getId).collect(Collectors.toList());
            starlinkNewsWriteFacade.updateById(ids,1);
        }
    }

    private void deleteScceSku(Map<Integer, List<StarlinkNewsInfoResponse>> integerListMap) {
        List<StarlinkNewsInfoResponse> skuDeleteStarlink = integerListMap.get(6);
        if(CollectionObjectUtil.isEmpty(skuDeleteStarlink)){
            return;
        }
        try {
            for (StarlinkNewsInfoResponse starlinkNewsInfoResponse : skuDeleteStarlink) {
                String content = starlinkNewsInfoResponse.getContent();
                Map<String,Long> map = JSON.parseObject(content, Map.class);
                Long skuId = map.get("skuId");
                SkuQueryByOuterIdRequest skuQueryByOuterIdRequest = new SkuQueryByOuterIdRequest();
                skuQueryByOuterIdRequest.setOuterIdSet(Collections.singleton(skuId.toString()));
                skuQueryByOuterIdRequest.setTenantId(1);
                skuQueryByOuterIdRequest.setShopId(vendorId);
                Response<List<SkuInfo>> query = skuReadFacade.queryByOuterId(skuQueryByOuterIdRequest);
                List<SkuInfo> skuInfoList = query.getResult();
                skuInfoList.stream().forEach(x->x.setStatus(-3));
                FullItemQueryBySkuRequest fullItemQueryBySkuRequest = new FullItemQueryBySkuRequest();
                fullItemQueryBySkuRequest.setSkuInfoList(skuInfoList);
                skuWriteFacade.updateSkuStatus(fullItemQueryBySkuRequest);
            }
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
            log.error("商品规格删除通知 商品规格删除 失败：{}",e.getMessage());
        }finally {
            List<Long> ids = skuDeleteStarlink.stream().map(StarlinkNewsInfoResponse::getId).collect(Collectors.toList());
            starlinkNewsWriteFacade.updateById(ids,1);
        }


    }

    private void updatePrice(Map<Integer, List<StarlinkNewsInfoResponse>> integerListMap) {
        List<StarlinkNewsInfoResponse> ItemPriceStarlink = integerListMap.get(12);
        if(CollectionObjectUtil.isEmpty(ItemPriceStarlink)){
            return;
        }
        try {
            for (StarlinkNewsInfoResponse starlinkNewsInfoResponse : ItemPriceStarlink) {
                String content = starlinkNewsInfoResponse.getContent();
                Map<String,Object> map = JSON.parseObject(content, Map.class);
                SkuQueryByOuterIdRequest skuQueryByOuterIdRequest = new SkuQueryByOuterIdRequest();
                skuQueryByOuterIdRequest.setOuterIdSet(Collections.singleton((String) map.get("skuId")));
                skuQueryByOuterIdRequest.setShopId(vendorId);
                Response<List<SkuInfo>> query = skuReadFacade.queryByOuterId(skuQueryByOuterIdRequest);
                List<SkuInfo> skuInfos = query.getResult();
                for (SkuInfo skuInfo : skuInfos) {
                    String priceJson = skuInfo.getPriceJson();
                    Map<String,Long> priceJsonMap = JSON.parseObject(content, Map.class);
                    BigDecimal price = new BigDecimal(String.valueOf(map.get("distriPrice"))).multiply(new BigDecimal(100)).setScale(0);
                    skuInfo.setPrice(price.longValue());
                    SkuDefaultPriceSaveRequest skuDefaultPriceSaveRequest = new SkuDefaultPriceSaveRequest();
                    skuDefaultPriceSaveRequest.setDefaultCommission(0L);
                    skuDefaultPriceSaveRequest.setDefaultPCommission(0L);
                    skuDefaultPriceSaveRequest.setDefaultBasePrice(price.longValue());
                    skuDefaultPriceSaveRequest.setDefaultOriginalPrice(priceJsonMap.get("defaultOriginalPrice"));
                    skuDefaultPriceSaveRequest.setDefaultPrice(priceJsonMap.get("defaultPrice"));
                    skuDefaultPriceSaveRequest.setSkuId(skuInfo.getId());
                    skuWriteFacade.setDefaultPrice(skuDefaultPriceSaveRequest);
                    ItemInfo itemInfo = new ItemInfo();
                    itemInfo.setId(skuInfo.getItemId());
                    Set<Long> push = push(itemInfo);
                    for (Long aLong : push) {
                        ChoiceItemUpdatePriceScceRequest choiceItemUpdatePriceScceRequest = new ChoiceItemUpdatePriceScceRequest();
                        choiceItemUpdatePriceScceRequest.setItemId(skuInfo.getItemId());
                        choiceItemUpdatePriceScceRequest.setOperatorId(aLong);
                        choiceItemUpdatePriceScceRequest.setSkuId(skuInfo.getId());
                        choiceLotLibSkuWriteFacade.updateSccePrice(choiceItemUpdatePriceScceRequest);
                    }
                }
            }
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
            log.error("选品库商品价格变更通知 企业商品库的商品sku选品库分销价变更 失败 {}",e.getMessage());
        }finally {
            List<Long> ids = ItemPriceStarlink.stream().map(StarlinkNewsInfoResponse::getId).collect(Collectors.toList());
            starlinkNewsWriteFacade.updateById(ids,1);
        }
    }


    private void deleteScceItem(Map<Integer, List<StarlinkNewsInfoResponse>> integerListMap) {
        //需要删除的供应商item数据
        List<ItemInfo> deleteItemInfos = new ArrayList<>();
        //需要删除的区域item数据
        List<AreaItemInfo> areaItemInfoList = new ArrayList<>();
        //需要删除的区域sku数据
        List<StarlinkNewsInfoResponse> itemDeleteStarlink = integerListMap.get(5);
        if(CollectionObjectUtil.isEmpty(itemDeleteStarlink)){
            return;
        }
        try {
            for (StarlinkNewsInfoResponse starlinkNewsInfoResponse : itemDeleteStarlink) {
                String content = starlinkNewsInfoResponse.getContent();
                Map<String,Long> map = JSON.parseObject(content, Map.class);
                ItemQueryBySingleIdRequest itemQueryBySingleIdRequest = new ItemQueryBySingleIdRequest();
                itemQueryBySingleIdRequest.setId(map.get("spuId"));
                itemQueryBySingleIdRequest.setTenantId(1);
                //查询供应商商品信息
                Response<ItemInfo> itemInfoResponse = itemReadFacade.queryByOutId(itemQueryBySingleIdRequest);
                ItemInfo itemInfo = itemInfoResponse.getResult();
                deleteItemInfos.add(itemInfo);
//            SkuQueryByItemRequest skuQueryByItemRequest = new SkuQueryByItemRequest();
//            Set<Long> objects = new HashSet<>();
//            objects.add(itemInfo.getId());
//            skuQueryByItemRequest.setItemIdSet(objects);
//            //查询区域商品信息
//            AreaItemQueryByOperatorIdAndItemIdRequest query = new AreaItemQueryByOperatorIdAndItemIdRequest();
//            query.setItemIds(Collections.singleton(itemInfo.getId()));
//            query.setOperatorId(vendorId);
//            Response<List<AreaItemInfo>> areaItemInfos = areaItemReadFacade.queryByItemIdAndVendorId(query);
//            areaItemInfoList.addAll(areaItemInfos.getResult());
            }
            if(!CollectionObjectUtil.isEmpty(deleteItemInfos)){
                ItemCancelRequest itemCancelRequest = new ItemCancelRequest();
                itemCancelRequest.setVendorId(vendorId);
                itemCancelRequest.setItemIds(deleteItemInfos.stream().map(ItemInfo::getId).collect(Collectors.toSet()));
                itemCancelRequest.setTenantId(1);
                itemCancelRequest.setUpdatedBy("admin");
                itemWriteFacade.cancelAll(itemCancelRequest);
            }
        }catch (Exception e){
            log.error(Throwables.getStackTraceAsString(e));
            log.error("商品删除通知 关联的商品spu删除 失败 {}",e.getMessage());
        }finally {
            List<Long> ids = itemDeleteStarlink.stream().map(StarlinkNewsInfoResponse::getId).collect(Collectors.toList());
            starlinkNewsWriteFacade.updateById(ids,1);
        }
    }

    public static void main(String[] args) {
        DefaultHeader defaultHeader = new DefaultHeader();
        defaultHeader.setAppKey("1519132181206216705");
//        ResultInfo categorys = ProductApi.getCategorys(0L, defaultHeader, "93397b37f17e4ddeabb23cccd5b92a73");
//        DefaultHeader defaultHeader = new DefaultHeader();
//        defaultHeader.setAppKey("1519132181206216705");
//        PageRequest pageRequest = new PageRequest();
//        pageRequest.setPageIndex(1);
//        pageRequest.setPageSize(20);
//        ResultInfo spuDetail = ProductApi.getSpuDetail(Arrays.asList(1517376300552032257L), defaultHeader, "93397b37f17e4ddeabb23cccd5b92a73");
////        ResultInfo brands = ProductApi.getBrands(pageRequest, defaultHeader, "93397b37f17e4ddeabb23cccd5b92a73");
//        ResultInfo categorys = ProductApi.getCategorys(1395308858980700161L, defaultHeader, "93397b37f17e4ddeabb23cccd5b92a73");
//        log.info(categorys);
        SpuListRequest spuListRequest = new SpuListRequest();
        spuListRequest.setPageIndex(1);
        spuListRequest.setPageSize(1);
        ResultInfo spuDetail1 = ProductApi.getSpuDetail(1471032904141246465L, defaultHeader, "93397b37f17e4ddeabb23cccd5b92a73");
//        List<ScceItemSkuMessage> scceItemSkuMessages = objToList(spuDetail1.getData(), ScceItemSkuMessage.class);
//        log.info(scceItemSkuMessages.get(0).getSkuPropertyList().get(0).get("specValueName"));
//        ResultInfo spuList = ProductApi.getSpuList(spuListRequest, defaultHeader, "93397b37f17e4ddeabb23cccd5b92a73");
        log.info(JSONUtil.toJsonStr(spuDetail1));
    }

//    public static void main(String[] args) {
//
//        CreateOrderRequest openApiCreateOrderDto = new CreateOrderRequest();
//
//        List<CreateOrderRequest.GoodsList> goodsDtos = Lists.newArrayList();
//
//
//        CreateOrderRequest.GoodsList openApiGoodsDto = new CreateOrderRequest.GoodsList();
//        openApiGoodsDto.setSkuId("1405346980521099265");
//        openApiGoodsDto.setGoodsQty("1");
//        goodsDtos.add(openApiGoodsDto);
//
//        openApiCreateOrderDto.setGoodsList(goodsDtos);
//        openApiCreateOrderDto.setOrderSource(1);
//        openApiCreateOrderDto.setShipArea("广东省,"+"深圳市,"+"福田区,"+"福田街道");
//        openApiCreateOrderDto.setShipName("谢卫龙1");
//        openApiCreateOrderDto.setShipAddress("上梅林");
//        openApiCreateOrderDto.setShipMobile("15874130301");
//        openApiCreateOrderDto.setOutOrderSn("131411111");
//        openApiCreateOrderDto.setShipAreaCode("1336938677202059265"+","+"1336942937021136906"+","+"1336944799103442951"+","+"1336954088522063900");
//
//        DefaultHeader defaultHeader = new DefaultHeader();
//        defaultHeader.setAppKey("1519132181206216705");
//        ResultInfo order = OrderApi.createOrder(openApiCreateOrderDto, defaultHeader, "93397b37f17e4ddeabb23cccd5b92a73");
//        log.info("返回========="+order);
//    }


    public static <T> List<T> objToList(Object obj, Class<T> cla) {
        List<T> list = new ArrayList<T>();
        if (obj instanceof JSONArray) {
            List<T> ts = JSONUtil.toList((JSONArray) obj, cla);
            for (Object o : ts) {
                list.add(cla.cast(o));
            }
        }
        return list;
    }
}
