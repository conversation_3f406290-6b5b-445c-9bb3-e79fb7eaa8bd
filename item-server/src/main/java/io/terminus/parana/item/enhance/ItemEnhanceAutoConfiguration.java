package io.terminus.parana.item.enhance;

import io.terminus.parana.item.channel.extension.ChannelReadExtension;
import io.terminus.parana.item.channel.extension.ChannelWriteExtension;
import io.terminus.parana.item.enhance.component.inventory.DefaultInventoryRender;
import io.terminus.parana.item.enhance.component.inventory.InventoryRender;
import io.terminus.parana.item.enhance.component.promotion.PromotionDealSorter;
import io.terminus.parana.item.enhance.component.promotion.PromotionRender;
import io.terminus.parana.item.enhance.component.promotion.PromotionTagCodeProvider;
import io.terminus.parana.item.enhance.component.promotion.invoke.DefaultPromotionDealSorter;
import io.terminus.parana.item.enhance.component.promotion.invoke.DefaultPromotionRender;
import io.terminus.parana.item.enhance.extension.channel.DefaultChannelReadExtension;
import io.terminus.parana.item.enhance.extension.channel.DefaultChannelWriteExtension;
import io.terminus.parana.item.enhance.extension.promotion.DefaultPromotionTagCodeProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-06-24
 */
@Configuration
public class ItemEnhanceAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public PromotionRender promotionRender() {
        return new DefaultPromotionRender();
    }

    @Bean
    @ConditionalOnMissingBean
    public InventoryRender inventoryRender() {
        return new DefaultInventoryRender();
    }

    @Bean
    @ConditionalOnMissingBean
    public ChannelWriteExtension channelWriteExtension() {
        return new DefaultChannelWriteExtension();
    }

    @Bean
    @ConditionalOnMissingBean
    public ChannelReadExtension channelReadExtension() {
        return new DefaultChannelReadExtension();
    }

    @Bean
    @ConditionalOnMissingBean
    public PromotionDealSorter promotionDealSorter() {
        return new DefaultPromotionDealSorter();
    }

    @Bean
    @ConditionalOnMissingBean
    public PromotionTagCodeProvider promotionTagCodeProvider() {
        return new DefaultPromotionTagCodeProvider();
    }
}
