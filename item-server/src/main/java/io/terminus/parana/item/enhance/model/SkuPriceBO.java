package io.terminus.parana.item.enhance.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SkuPriceBO {

    /**
     * 底价
     */
    private Long basicPrice;

    /**
     * 渠道价
     */
    private Long channelPrice;

    /**
     * 上级佣金比例
     */
    private Long pCommission;

    /**
     * 佣金比例
     */
    private Long commission;

    @ApiModelProperty("会员购买价")
    private Long registerBuyPrice;

    @ApiModelProperty("注册用户推广价")
    private Long registerSpreadPrice;

}
