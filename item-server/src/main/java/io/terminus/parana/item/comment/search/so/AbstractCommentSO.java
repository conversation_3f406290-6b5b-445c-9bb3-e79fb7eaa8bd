package io.terminus.parana.item.comment.search.so;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.request.AbstractPageRequest;
import io.terminus.parana.item.common.enums.SortType;
import io.terminus.parana.search.annotation.query.Equals;
import io.terminus.parana.search.annotation.query.Greater;
import io.terminus.parana.search.annotation.query.Less;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-18
 */
@Data
public abstract class AbstractCommentSO extends AbstractPageRequest {
    private static final long serialVersionUID = -8929501287475211268L;
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Equals
    @ApiModelProperty("用户id")
    private Long userId;

    @Greater(field = "createdAt")
    @ApiModelProperty("时间起始值")
    private Date startAt;

    @Less(field = "createdAt")
    @ApiModelProperty("时间结束值")
    private Date endAt;

    private Integer limit;

    private Integer offset;

    @Equals
    @ApiModelProperty("父节点")
    private Long parentId;

    @ApiModelProperty("排除根节点")
    private Boolean excludeRoot;

    @Equals
    @ApiModelProperty("根节点")
    private Long topId;

    @Equals(field = "topId")
    @ApiModelProperty("根节点id集合")
    private Set<Long> topIdSet;

    @Equals
    @ApiModelProperty("状态")
    private Integer status;

    @Equals(field = "status")
    @ApiModelProperty("状态集合")
    private Set<Integer> statusSet;

    @Equals
    @ApiModelProperty("评价类型")
    private Integer type;

    public int getLimit() {
        return limit == null
                ? 20
                : limit;
    }

    public int getOffset() {
        return offset == null
                ? 0
                : offset;
    }

    @ApiModelProperty("排序字段")
    private String orderBy;

    @ApiModelProperty("排序类型")
    private String sortType;

    public String getOrderBy() {
        return orderBy != null
                ? orderBy
                : "createdAt";
    }

    public String getSortType() {
        return sortType != null
                ? sortType
                : SortType.DESC.name();
    }
}
