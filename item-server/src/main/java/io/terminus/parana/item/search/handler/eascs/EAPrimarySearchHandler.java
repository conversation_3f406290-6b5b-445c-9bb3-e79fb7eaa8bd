package io.terminus.parana.item.search.handler.eascs;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Paging;
import io.terminus.common.utils.Splitters;
import io.terminus.parana.item.category.api.bean.response.OperatorCategoryInfo;
import io.terminus.parana.item.category.api.bean.response.OperatorCategoryOfSearchInfo;
import io.terminus.parana.item.item.enums.UserLevel;
import io.terminus.parana.item.search.adaptor.ItemComponentAdaptor;
import io.terminus.parana.item.search.consts.Constant;
import io.terminus.parana.item.search.context.SearchContext;
import io.terminus.parana.item.search.docobject.AreaItemDO;
import io.terminus.parana.item.search.domain.ActivityDO;
import io.terminus.parana.item.search.dto.*;
import io.terminus.parana.item.search.enums.ItemSortEnum;
import io.terminus.parana.item.search.handler.BasePrimarySearchHandler;
import io.terminus.parana.item.search.request.AreaPrimarySearchRequest;
import io.terminus.parana.item.search.request.PrimarySearchRequest;
import io.terminus.parana.search.client.builder.HighlightBuilder;
import io.terminus.parana.search.client.builder.QueryBuilder;
import io.terminus.parana.search.client.result.Document;
import io.terminus.parana.search.client.result.SearchResult;
import io.terminus.parana.search.client.search.Order;
import io.terminus.parana.search.client.search.Script;
import io.terminus.parana.search.client.search.SearchRequest;
import io.terminus.parana.search.client.search.Sort;
import io.terminus.parana.search.client.search.query.BoolQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2020-08-02 上午11:03
 */
@Slf4j
public class EAPrimarySearchHandler extends BasePrimarySearchHandler<AreaItemDO> {
    // 价格排序脚本
    private final static String SORT_SCRIPT = "def key='channelPrice'+params.channelId; def referrerStatus = params.referrerStatus; if (null != referrerStatus && '2000' == referrerStatus) { return doc['lowOriginalPrice'].value; } else if (null != referrerStatus && '2001' == referrerStatus) { return doc['lowregisterbuyprice1'].value; } else if (null != referrerStatus && '2002' == referrerStatus) { return doc['lowoneprice1'].value; } else if (null != referrerStatus && '2003' == referrerStatus) { return doc['lowtwoprice1'].value; } else if (null != referrerStatus && '2004' == referrerStatus) { return doc['lowPrice'].value; } else if(doc.containsKey(key)){def price = doc[key].value;if(0 != price){return price;}else{return doc['lowPrice'].value;}}else{return doc['lowPrice'].value;}";
    // 价格筛选脚本
    private final static String RANGE_SCRIPT = "def flag=true;def key='channelPrice'+params.channelId; def referrerStatus = params.referrerStatus; if (null != referrerStatus) { if(null!=params.yjrLowPrice) { if ('2000' == referrerStatus) { flag&=params.yjrLowPrice<=doc['lowOriginalPrice'].value; } else if ('2001' == referrerStatus) { flag&=params.yjrLowPrice<=doc['lowregisterbuyprice1'].value; } else if ('2002' == referrerStatus) { flag&=params.yjrLowPrice<=doc['lowoneprice1'].value; } else if ('2003' == referrerStatus) { flag&=params.yjrLowPrice<=doc['lowtwoprice1'].value; } else if ('2004' == referrerStatus) { flag&=params.yjrLowPrice<=doc['lowPrice'].value; }} if(null!=params.yjrHighPrice){if ('2000' == referrerStatus) { flag&=params.yjrHighPrice>=doc['lowOriginalPrice'].value; } else if ('2001' == referrerStatus) { flag&=params.yjrHighPrice>=doc['lowregisterbuyprice1'].value; } else if ('2002' == referrerStatus) { flag&=params.yjrHighPrice>=doc['lowoneprice1'].value; } else if ('2003' == referrerStatus) { flag&=params.yjrHighPrice>=doc['lowtwoprice1'].value; } else if ('2004' == referrerStatus) { flag&=params.yjrHighPrice>=doc['lowPrice'].value; } } } else if(doc.containsKey(key)){if(null!=params.lowPrice){flag&=params.lowPrice<=doc[key].value;}if(null!=params.highPrice){flag&=params.highPrice>=doc[key].value;}}else{if(null!=params.lowPrice){flag&=params.lowPrice<=doc['lowPrice'].value;}if(null!=params.highPrice){flag&=params.highPrice>=doc['lowPrice'].value;}}return flag;";
//    // 价格排序脚本
//    private final static String SORT_SCRIPT = "def key='channelPrice'+params.channelId;if(doc.containsKey(key)){def price = doc[key].value;if(0 != price){return price;}else{return doc['lowPrice'].value;}}else{return doc['lowPrice'].value;}";
//    // 价格筛选脚本
//    private final static String RANGE_SCRIPT = "def flag=true;def key='channelPrice'+params.channelId;if(doc.containsKey(key)){if(null!=params.lowPrice){flag&=params.lowPrice<=doc[key].value;}if(null!=params.highPrice){flag&=params.highPrice>=doc[key].value;}}else{if(null!=params.lowPrice){flag&=params.lowPrice<=doc['lowPrice'].value;}if(null!=params.highPrice){flag&=params.highPrice>=doc['lowPrice'].value;}}return flag;";

    @Autowired
    private ItemComponentAdaptor componentAdaptor;

    @Override
    protected void handle(PrimarySearchRequest primaryRequest, SearchRequest searchRequest, BoolQuery boolQuery) {
        super.handle(primaryRequest, searchRequest, boolQuery);
        log.info("EAPrimarySearchHandler_handle_2");
        handleHighlight(primaryRequest, searchRequest, boolQuery);
    }

    @Override
    protected void handlePrice(PrimarySearchRequest primaryRequest, SearchRequest searchRequest, BoolQuery boolQuery) {
        log.info("EAPrimarySearchHandler_handle_5");
        if (null != primaryRequest.getLowPrice() || null != primaryRequest.getHighPrice()
                || (primaryRequest.getReferrerStatus() != null
                && (primaryRequest.getYjrLowPrice() != null || primaryRequest.getYjrHighPrice() != null))) {
            boolQuery.filter(QueryBuilder.scriptQuery().source(RANGE_SCRIPT)
                    .addParam("channelId", primaryRequest.getDimension().getChannelId())
                    .addParam("lowPrice", primaryRequest.getLowPrice())
                    .addParam("highPrice", primaryRequest.getHighPrice())
                    .addParam("yjrLowPrice", primaryRequest.getYjrLowPrice())
                    .addParam("yjrHighPrice", primaryRequest.getYjrHighPrice())
                    .addParam("referrerStatus", primaryRequest.getReferrerStatus())
            );
        }
    }

    @Override
    protected void handleSort(PrimarySearchRequest primaryRequest, SearchRequest searchRequest, BoolQuery boolQuery) {
        log.info("EAPrimarySearchHandler_handleSort_7");
        ItemSortEnum sort = ItemSortEnum.fromValue(primaryRequest.getSort());
        if (null != sort) {
            switch (sort) {
                case PRICE_ASC:
                    searchRequest.sort(new Script().source(SORT_SCRIPT).addParam("channelId", primaryRequest.getDimension().getChannelId()).addParam("referrerStatus", primaryRequest.getReferrerStatus()), Order.ASC, "number");
                    break;
                case PRICE_DESC:
                    searchRequest.sort(new Script().source(SORT_SCRIPT).addParam("channelId", primaryRequest.getDimension().getChannelId()).addParam("referrerStatus", primaryRequest.getReferrerStatus()), Order.DESC, "number");
                    break;

                case COMMINSSION_ASC:
                    searchRequest.sort("comminssion", Order.ASC);
                    break;
                case COMMINSSION_DESC:
                    searchRequest.sort("comminssion", Order.DESC);
                    break;

                case NEW_ITEM_ASC:
                    searchRequest.sort("updatedAt", Order.ASC);
                    break;
                case NEW_ITEM_DESC:
                    searchRequest.sort("updatedAt", Order.DESC);
                    break;
                case SALE_QUANTITY_ASC:
                    searchRequest.sort("saleQuantity", Order.ASC);
                    break;
                case SALE_QUANTITY_DESC:
                    searchRequest.sort("saleQuantity", Order.DESC);
                    break;
                case SUGGEST_PRICE_ASC:
                    searchRequest.sort("lowOriginalPrice", Order.ASC);
                    break;
                case SUGGEST_PRICE_DESC:
                    searchRequest.sort("highOriginalPrice", Order.DESC);
                    break;
                default:
                    log.info("其他排序...");
                    break;
            }
        }else{
            log.info("进入综合排序...");
            // 综合排序，商品权重优先 (整购B端需求1.0.0)
            List<Sort> sortList = new ArrayList<>();

            Sort sort0 = new Sort();
            sort0.field("_score");
            sort0.order(Order.DESC);
            sortList.add(sort0);

            Sort sort1 = new Sort();
            sort1.field("weightNumber");
            sort1.order(Order.DESC);
            sortList.add(sort1);

            Sort sort2 = new Sort();
            sort2.field("brandWeightNumber");
            sort2.order(Order.DESC);
            sortList.add(sort2);

            Sort sort3 = new Sort();
            sort3.field("updatedAt");
            sort3.order(Order.DESC);
            sortList.add(sort3);
            searchRequest.setSort(sortList);
            log.info("排序顺序："+searchRequest.getSort().toString());
        }
    }

    @Override
    protected void handleCategory(PrimarySearchRequest primaryRequest, SearchRequest searchRequest, BoolQuery boolQuery) {
        Set<Long> front2backCatIdSet = new HashSet<>();
        if (null != primaryRequest.getFrontCategoryId()) {
            OperatorCategoryOfSearchInfo info = componentAdaptor.getOperatorCategory(primaryRequest.getFrontCategoryId(), primaryRequest.getTenantId(), null, null);
            if (null != info.getBackCategoryIds() && null!=info.getAncestors()) {
                if (!CollectionUtils.isEmpty(info.getBackCategoryIds())) {
                    front2backCatIdSet.addAll(info.getBackCategoryIds());
                }
                List<OperatorCategoryInfo> frontCategoryModels = info.getAncestors();
                if (!CollectionUtils.isEmpty(frontCategoryModels)) {
                    OperatorCategoryInfo current = frontCategoryModels.get(frontCategoryModels.size() - 1);
                    // 已选择前台类目处理
                    SearchContext.addChosen(new Chosen(Constant.CHOSE_TYPE_FRONT_CATEGORY, current.getId(), current.getName()));
                    // 面包屑处理
                    List<IdAndName> crumbList = new ArrayList<>(frontCategoryModels.size());
                    for (OperatorCategoryInfo model : frontCategoryModels) {
                        crumbList.add(new IdAndName(model.getId(), model.getName()));
                    }
                    SearchContext.setBreadCrumbs(crumbList);
                }
            }else {
                front2backCatIdSet.add(-1L);
            }
        }
        Set<Long> backCatIdSet = new HashSet<>();
        if (StringUtils.hasText(primaryRequest.getBackCategoryIds())) {
            List<Long> backCatIdList = Splitters.UNDERSCORE.splitToList(primaryRequest.getBackCategoryIds()).stream().map(Long::valueOf).collect(Collectors.toList());
            backCatIdSet.addAll(backCatIdList);
            // 已选择后台类目处理
            Map<Long, String> categoryNameMap = componentAdaptor.getCategoryNameMap(backCatIdSet, primaryRequest.getTenantId());
            for (Long id : backCatIdList) {
                String name = categoryNameMap.get(id);
                if (null != name) {
                    SearchContext.addChosen(new Chosen(Constant.CHOSE_TYPE_BACK_CATEGORY, id, name));
                }
            }
        }
        if (!front2backCatIdSet.isEmpty() && !backCatIdSet.isEmpty()) {
            backCatIdSet.retainAll(front2backCatIdSet);
        } else if (backCatIdSet.isEmpty()) {
            backCatIdSet = front2backCatIdSet;
        }
        if (!backCatIdSet.isEmpty()) {
            boolQuery.filter(QueryBuilder.termsQuery().field("categoryIds").values(backCatIdSet));
        }
    }

    private void handleHighlight(PrimarySearchRequest primaryRequest, SearchRequest searchRequest, BoolQuery boolQuery) {
        AreaPrimarySearchRequest request = (AreaPrimarySearchRequest) primaryRequest;
        if (StringUtils.hasText(request.getPreTag()) && StringUtils.hasText(request.getPostTag())) {
            // 仅商品名称高亮
            searchRequest.setHighlight(HighlightBuilder.build()
                    .field("name")
                    .preTag(request.getPreTag())
                    .postTag(request.getPostTag())
            );
        }
    }

    @Override
    public Paging<AreaItemDO> parseSearchResult(SearchResult<AreaItemDO> result) {
        if (result.getDocuments().isEmpty()) {
            return Paging.empty();
        }

        List<Document<AreaItemDO>> documents = result.getDocuments().getData();
        List<AreaItemDO> itemList = new ArrayList<>(documents.size());

        for (Document<AreaItemDO> doc : documents) {
            itemList.add(doc.getSource());
            Map<String, List<String>> map = doc.getHighlight();
            if (!CollectionUtils.isEmpty(map)) {
                // 仅商品名称高亮
                List<String> ls = map.get("name");
                if (!CollectionUtils.isEmpty(ls)) {
                    doc.getSource().setName(ls.get(0));
                }
            }
        }

        return new Paging<>(result.getDocuments().getTotal(), itemList);
    }

    @Override
    protected FrontItemDTO convertToThinItem(AreaItemDO item, PrimarySearchRequest searchRequest) {
        log.info("convertToThinItem  item  {} , " ,JSON.toJSONString(item));
        AreaFrontItemDTO thinItem = new AreaFrontItemDTO();
        thinItem.setId(item.getItemId());
        thinItem.setItemId(item.getItemId());
        thinItem.setName(item.getName());
        thinItem.setMainImage(item.getMainImage());
        thinItem.setStatus(item.getStatus());
        thinItem.setType(item.getType());
        thinItem.setBusinessType(item.getBusinessType());
        thinItem.setHighPrice(item.getHighPrice());
        thinItem.setLowPrice(item.getLowPrice());
        thinItem.setIspromotion(item.getIspromotion());
        thinItem.setOriginalPrice(item.getOriginalPrice());
        thinItem.setInStock(item.getInStock());
        if (!CollectionUtils.isEmpty(item.getActivities())) {
            List<ActivityDO> activities = item.getActivities().stream()
                    .sorted(Comparator.comparing(ActivityDO::getCreatedAt).reversed())
                    .collect(Collectors.toList());
            thinItem.setActivities(activities.stream().map(activity -> {
                Activity act = new Activity();
                act.setCode(activity.getCode());
                act.setTag(activity.getTag());
                return act;
            }).collect(Collectors.toList()));
        }
        thinItem.setOperatorId(item.getOperatorId());
        thinItem.setIsNationwideAgencyItem(item.getIsNationwideAgencyItem());
        thinItem.setMinQuantity(item.getMinQuantity());
        thinItem.setUnit(item.getUnit());
        thinItem.setVendorName(item.getShopName());
        thinItem.setVendorId(item.getVendorId());
        thinItem.setRealVendorId(item.getShopId());
        thinItem.setPenterprisecname(item.getPenterprisecname());
        thinItem.setLowOriginalPrice(item.getLowOriginalPrice());
        thinItem.setHighOriginalPrice(item.getHighOriginalPrice());
        thinItem.setLowBasePrice(item.getLowBasePrice());
        thinItem.setHighBasePrice(item.getHighBasePrice());
        thinItem.setLowSpreadPricePrice(item.getLowspreadpriceprice1());
        thinItem.setHighSpreadPricePrice(item.getHighspreadpriceprice1());
        thinItem.setSaleQuantity(null == item.getSaleQuantity()?0:item.getSaleQuantity());
        // 搜索 根据用户等级设置最低购买价和分享家
        if(UserLevel.MEMBER.getValue().equals(searchRequest.getReferrerStatus()) &&
                null != item.getLowregisterbuyprice1() ){
            // 未认证合伙人售价及分享价
            thinItem.setLowPrice(item.getLowregisterbuyprice1());
            thinItem.setLowSpreadPricePrice(item.getLowregisterbuyprice1());
        } else if(UserLevel.V1.getValue().equals(searchRequest.getReferrerStatus()) &&
                null != item.getLowoneprice1()){
            // 未认证合伙人售价及分享价
            thinItem.setLowPrice(item.getLowoneprice1());
            thinItem.setLowSpreadPricePrice(item.getLowspreadpriceprice1());
        } else if(UserLevel.V2.getValue().equals(searchRequest.getReferrerStatus()) &&
                null != item.getLowtwoprice1()){
            // 未认证合伙人售价及分享价
            thinItem.setLowPrice(item.getLowtwoprice1());
            thinItem.setLowSpreadPricePrice(item.getLowspreadpriceprice1());
        } else if(UserLevel.V3.getValue().equals(searchRequest.getReferrerStatus()) &&
                null != item.getLowPrice()){
            // 未认证合伙人售价及分享价
            thinItem.setLowPrice(item.getLowPrice());
            thinItem.setLowSpreadPricePrice(item.getLowspreadpriceprice1());
        }
        thinItem.setBrandId(item.getBrandId());
        thinItem.setBrandName(item.getBrandName());
        thinItem.setLowCentralizedPurchasePrice(item.getLowCentralizedPurchasePrice());
        thinItem.setHighCentralizedPurchasePrice(item.getHighCentralizedPurchasePrice());
        return thinItem;
    }

}
