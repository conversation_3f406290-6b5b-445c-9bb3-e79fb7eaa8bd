package io.terminus.parana.item.channel.api.converter;

import io.terminus.parana.item.channel.api.bean.response.ChannelInfo;
import io.terminus.parana.item.channel.model.Channel;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-27
 */
@Mapper(componentModel = "spring")
public interface ChannelApiInfoConverter {

    ChannelInfo get(Channel channel);

    List<ChannelInfo> get(List<Channel> channelList);
}
