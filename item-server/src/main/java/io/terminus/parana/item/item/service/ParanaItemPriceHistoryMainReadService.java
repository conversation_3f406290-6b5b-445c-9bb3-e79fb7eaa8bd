package io.terminus.parana.item.item.service;

import io.terminus.common.model.Paging;
import io.terminus.parana.item.item.model.ParanaItemPriceHistoryMainModel;
import io.terminus.parana.item.item.repository.ParanaItemPriceHistoryMainDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class ParanaItemPriceHistoryMainReadService {

    private final ParanaItemPriceHistoryMainDao paranaItemPriceHistoryMainDao;

    /**
     * 查询变价记录主表
     */
    public ParanaItemPriceHistoryMainModel view(ParanaItemPriceHistoryMainModel model) {
        return paranaItemPriceHistoryMainDao.queryOne(model);
    }

    /**
     * 查询变价记录主表列表
     */
    public List<ParanaItemPriceHistoryMainModel> list(ParanaItemPriceHistoryMainModel model) {
        return paranaItemPriceHistoryMainDao.listByModel(model);
    }

    /**
     * 分页查询变价记录主表列表
     */
    public Paging<ParanaItemPriceHistoryMainModel> page(Map<String, Object> params, Integer offset, Integer limit) {
        return paranaItemPriceHistoryMainDao.page(params, offset, limit);
    }

    public List<ParanaItemPriceHistoryMainModel> listByMap(Map<String, Object> param) {
        return paranaItemPriceHistoryMainDao.listByMap(param);
    }
}
