package io.terminus.parana.item.area.component;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.area.manager.ItemAuditManager;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.model.ItemAudit;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.choicelot.model.ChoiceLotLib;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibItemModel;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibItemReadService;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibReadDomainService;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibSkuReadService;
import io.terminus.parana.item.choicelot.utils.MarkupCalculateUtils;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.configuration.model.ParanaItemConfigurationModel;
import io.terminus.parana.item.configuration.service.ParanaItemConfigurationReadService;
import io.terminus.parana.item.item.enums.ItemAuditStatusEnum;
import io.terminus.parana.item.item.model.ItemPriceHistory;
import io.terminus.parana.item.item.model.ParanaItemPriceHistoryMainModel;
import io.terminus.parana.item.item.service.ItemPriceHistoryReadService;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageBean;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageCreateRequest;
import io.terminus.parana.misc.itemorder.api.api.facade.ParanaThirdMessageWriteFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class ItemAuditHelper {

    @Autowired
    private ParanaItemConfigurationReadService paranaItemConfigurationReadService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private AreaSkuReadDomainService areaSkuReadDomainService;
    @Autowired
    private AreaItemReadDomainService areaItemReadDomainService;
    @Autowired
    private ChoiceLotLibSkuReadService choiceLotLibSkuReadService;
    @Autowired
    private ChoiceLotLibItemReadService choiceLotLibItemReadService;
    @Autowired
    private ChoiceLotLibReadDomainService choiceLotLibReadDomainService;
    @Autowired
    private ParanaThirdMessageWriteFacade paranaThirdMessageWriteFacade;
    @Autowired
    private ItemAuditManager itemAuditManager;
    @Autowired
    private ItemPriceHistoryReadService itemPriceHistoryReadService;

    public Boolean buildData(String source, List<ItemAudit> auditList) {
        if (ObjectUtil.isEmpty(auditList)) {
            log.error("ItemAuditHelper.buildData.itemAuditList is empty");
            throw new ServiceException("itemAuditList is empty");
        }
        //解决数据量大的问题 分批处理
        List<List<ItemAudit>> lists = Lists.partition(auditList, 500);
        Boolean flag = false;
        for (List<ItemAudit> itemAuditList : lists) {
            List<ItemAudit> itemAuditCreateList = Lists.newArrayList();
            List<ItemAudit> itemAuditUpdateList = Lists.newArrayList();
            List<ItemPriceHistory> itemPriceHistoryCreateList = Lists.newArrayList();
            List<ParanaItemPriceHistoryMainModel> itemPriceHistoryMainCreateList = Lists.newArrayList();
            List<ChoiceLotLibSkuModel> choiceLotLibSkuUpdateList = Lists.newArrayList();
            List<AreaSku> areaSkuUpdateList = Lists.newArrayList();
            List<AreaItem> areaItemUpdateList = Lists.newArrayList();
            List<ParanaThirdMessageCreateRequest> thirdMessageCreateRequestList = Lists.newArrayList();
            List<ItemPriceHistory> itemPriceHistoryUpdateList = Lists.newArrayList();

            //前置数据查询
            List<Long> skuIds = itemAuditList.stream().map(ItemAudit::getSkuId).collect(Collectors.toList());
            List<Long> itemIds = itemAuditList.stream().map(ItemAudit::getItemId).collect(Collectors.toList());
            List<Long> itemAuditIds = itemAuditList.stream().map(ItemAudit::getId).collect(Collectors.toList());
            List<AreaSku> allAreaSku = areaSkuReadDomainService.findByOperatorIdAndSkuIds(null, skuIds);
            Map<String, AreaSku> areaSkuMap = allAreaSku.stream().collect(Collectors.toMap(e -> e.getOperatorId() + "-" + e.getSkuId(), Function.identity(), (e1, e2) -> e1));
            List<AreaItem> allAreaItem = areaItemReadDomainService.findByOperatorIdAndItemIds(null, new HashSet<>(itemIds));
            Map<String, AreaItem> areaItemMap = allAreaItem.stream().collect(Collectors.toMap(e -> e.getOperatorId() + "-" + e.getItemId(), Function.identity(), (e1, e2) -> e1));
            //查询当前这批sku在选品库中的所有数据
            Map<String, Object> param = new HashMap<>();
            param.put("skuIds", skuIds);
            List<ChoiceLotLibSkuModel> choiceLotLibSkuBySkuIds = choiceLotLibSkuReadService.findChoiceLotLibSkuBySkuIds(param);
            Map<Long, ChoiceLotLib> choiceLotLibMap = Maps.newHashMap();
            List<ChoiceLotLibItemModel> choiceLotLibItemModels = Lists.newArrayList();
            if (ObjectUtil.isNotEmpty(choiceLotLibSkuBySkuIds)) {
                List<Long> choiceLotLibIdList = choiceLotLibSkuBySkuIds.stream().map(ChoiceLotLibSkuModel::getChoiceLotLibId).collect(Collectors.toList());
                param.put("choiceIds", choiceLotLibIdList);
                List<ChoiceLotLib> choiceLotLibs = choiceLotLibReadDomainService.listInfoFilter(param);
                choiceLotLibMap = choiceLotLibs.stream().collect(Collectors.toMap(ChoiceLotLib::getId, Function.identity(), (e1, e2) -> e1));
                choiceLotLibItemModels = choiceLotLibItemReadService.findChoiceItemByChoiceIdsAndItemIds(itemIds, choiceLotLibIdList);
            }
            //查询所有配置
            Map<String, Object> operatorConfigParams = Maps.newHashMap();
            //审核配置  运营商供货配置
            operatorConfigParams.put("types", Lists.newArrayList(1, 4));
            List<ParanaItemConfigurationModel> allConfig = paranaItemConfigurationReadService.findItemConfigurations(operatorConfigParams);

            HashMap<String, Object> param1 = Maps.newHashMap();
            param1.put("auditIds", itemAuditIds);
            List<ItemPriceHistory> itemPriceHistoryList = itemPriceHistoryReadService.listByMap(param1);
            Map<Long, List<ItemPriceHistory>> itemPriceHistoryMap = itemPriceHistoryList.stream().collect(Collectors.groupingBy(ItemPriceHistory::getAuditId));

            log.info("ItemAuditHelper.buildData.source:{}", source);
            for (ItemAudit itemAudit : itemAuditList) {
                Long id = itemAudit.getId();
                if (ObjectUtil.isEmpty(id)) {
                    itemAudit.setId(idGenerator.nextValue(ItemAudit.class));
                }
                if ("vendor".equals(source)) {
                    //判断是否为供品给平台
                    if (itemAudit.getOperatorId() == 1L) {
                        //调用platform逻辑
                        platform(
                                source,
                                itemAudit,
                                itemPriceHistoryCreateList,
                                itemPriceHistoryMainCreateList,
                                choiceLotLibSkuUpdateList,
                                areaSkuUpdateList,
                                areaItemUpdateList,
                                thirdMessageCreateRequestList,
                                allAreaSku,
                                allAreaItem,
                                choiceLotLibSkuBySkuIds,
                                choiceLotLibMap,
                                choiceLotLibItemModels,
                                allConfig,
                                areaSkuMap,
                                areaItemMap,
                                itemPriceHistoryMap,
                                itemPriceHistoryUpdateList
                        );
                    } else {
                        //调用operator逻辑
                        operator(
                                source,
                                itemAudit,
                                itemPriceHistoryCreateList,
                                itemPriceHistoryMainCreateList,
                                choiceLotLibSkuUpdateList,
                                areaSkuUpdateList,
                                areaItemUpdateList,
                                thirdMessageCreateRequestList,
                                allAreaSku,
                                allAreaItem,
                                choiceLotLibSkuBySkuIds,
                                choiceLotLibMap,
                                choiceLotLibItemModels,
                                allConfig,
                                areaSkuMap,
                                areaItemMap,
                                itemPriceHistoryMap,
                                itemPriceHistoryUpdateList
                        );
                    }
                } else if ("operator".equals(source)) {
                    //运营供品平台
                    //调用operator逻辑
                    operator(
                            source,
                            itemAudit,
                            itemPriceHistoryCreateList,
                            itemPriceHistoryMainCreateList,
                            choiceLotLibSkuUpdateList,
                            areaSkuUpdateList,
                            areaItemUpdateList,
                            thirdMessageCreateRequestList,
                            allAreaSku,
                            allAreaItem,
                            choiceLotLibSkuBySkuIds,
                            choiceLotLibMap,
                            choiceLotLibItemModels,
                            allConfig,
                            areaSkuMap,
                            areaItemMap,
                            itemPriceHistoryMap,
                            itemPriceHistoryUpdateList
                    );
                } else if ("platform".equals(source)) {
                    //平台改价创建审核
                    //调用platform逻辑
                    platform(
                            source,
                            itemAudit,
                            itemPriceHistoryCreateList,
                            itemPriceHistoryMainCreateList,
                            choiceLotLibSkuUpdateList,
                            areaSkuUpdateList,
                            areaItemUpdateList,
                            thirdMessageCreateRequestList,
                            allAreaSku,
                            allAreaItem,
                            choiceLotLibSkuBySkuIds,
                            choiceLotLibMap,
                            choiceLotLibItemModels,
                            allConfig,
                            areaSkuMap,
                            areaItemMap,
                            itemPriceHistoryMap,
                            itemPriceHistoryUpdateList
                    );
                }
                if (itemAudit.getStatus().equals(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue())) {
                    buildPlatformHistory(source, itemAudit, itemPriceHistoryCreateList, itemPriceHistoryMainCreateList, choiceLotLibSkuUpdateList, areaSkuUpdateList, thirdMessageCreateRequestList, allAreaSku, choiceLotLibSkuBySkuIds, choiceLotLibMap, choiceLotLibItemModels, new Date(), areaItemUpdateList, allAreaItem,
                            areaSkuMap,
                            areaItemMap, itemPriceHistoryMap, itemPriceHistoryUpdateList);
                }
                if (ObjectUtil.isEmpty(id)) {
                    itemAuditCreateList.add(itemAudit);
                } else {
                    itemAuditUpdateList.add(itemAudit);
                }
            }
            flag = itemAuditManager.itemAuditHelperDB(itemAuditCreateList, itemAuditUpdateList, itemPriceHistoryCreateList, itemPriceHistoryMainCreateList, choiceLotLibSkuUpdateList, areaSkuUpdateList, areaItemUpdateList, itemPriceHistoryUpdateList);
            if (flag) {
                try {
                    if (ObjectUtil.isNotEmpty(thirdMessageCreateRequestList)) {
                        for (ParanaThirdMessageCreateRequest paranaThirdMessageCreateRequest : thirdMessageCreateRequestList) {
                            paranaThirdMessageWriteFacade.create(paranaThirdMessageCreateRequest);
                        }
                    }
                } catch (Exception e) {
                    log.error("变价审核-发送渠道商改价消息失败:{},request:{}", Throwables.getStackTraceAsString(e), JSON.toJSONString(thirdMessageCreateRequestList));
                }
            }
        }
        return flag;
    }

    void operator(
            String source,
            ItemAudit itemAudit,
            List<ItemPriceHistory> itemPriceHistoryCreateList,
            List<ParanaItemPriceHistoryMainModel> itemPriceHistoryMainCreateList,
            List<ChoiceLotLibSkuModel> choiceLotLibSkuUpdateList,
            List<AreaSku> areaSkuUpdateList,
            List<AreaItem> areaItemUpdateList,
            List<ParanaThirdMessageCreateRequest> thirdMessageCreateRequestList,
            List<AreaSku> allAreaSku,
            List<AreaItem> allAreaItem,
            List<ChoiceLotLibSkuModel> choiceLotLibSkuBySkuIds,
            Map<Long, ChoiceLotLib> choiceLotLibMap,
            List<ChoiceLotLibItemModel> choiceLotLibItemModels,
            List<ParanaItemConfigurationModel> allConfig,
            Map<String, AreaSku> areaSkuMap,
            Map<String, AreaItem> areaItemMap,
            Map<Long, List<ItemPriceHistory>> itemPriceHistoryMap,
            List<ItemPriceHistory> itemPriceHistoryUpdateList
    ) {
        Date now = new Date();
        //品牌商创建审核 判断运营是否有配置自动审核
        if ("vendor".equals(source)) {
            //如果是品牌商供品运营商  并且配置为自动审核通过  将审核状态修改待平台审核
            if (allConfig.stream().anyMatch(e -> e.getType() == 1 && e.getOperatorId().equals(itemAudit.getOperatorId()) && e.getVendorId().equals(itemAudit.getVendorId()))) {
                allConfig.stream().filter(e -> e.getType() == 1 && e.getOperatorId().equals(itemAudit.getOperatorId()) && e.getVendorId().equals(itemAudit.getVendorId())).findFirst().ifPresent(e -> {
                    if (ObjectUtil.isNotEmpty(e.getPriceAudit()) && e.getPriceAudit() > 0) {
                        if (e.getPriceAudit() == 1) {
                            itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                        } else if (e.getPriceAudit() == 2 && itemAudit.getBeforeBasePrice() < itemAudit.getBasePrice()) {
                            itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                        } else if (e.getPriceAudit() == 3 && itemAudit.getBeforeBasePrice() > itemAudit.getBasePrice()) {
                            itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                        } else {
                            itemAudit.setStatus(ItemAuditStatusEnum.UNDER_REVIEW.getValue());
                        }
                    } else {
                        itemAudit.setStatus(ItemAuditStatusEnum.UNDER_REVIEW.getValue());
                    }
                });
            } else {
                itemAudit.setStatus(ItemAuditStatusEnum.UNDER_REVIEW.getValue());
            }
            //如果自动审核通过则修改运营商价格、生成运营商改价记录、发送渠道消息
            if (itemAudit.getStatus().equals(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue())) {

                platform("operator",
                        itemAudit,
                        itemPriceHistoryCreateList,
                        itemPriceHistoryMainCreateList,
                        choiceLotLibSkuUpdateList,
                        areaSkuUpdateList,
                        areaItemUpdateList,
                        thirdMessageCreateRequestList,
                        allAreaSku,
                        allAreaItem,
                        choiceLotLibSkuBySkuIds,
                        choiceLotLibMap,
                        choiceLotLibItemModels,
                        allConfig,
                        areaSkuMap,
                        areaItemMap,
                        itemPriceHistoryMap,
                        itemPriceHistoryUpdateList
                );
                //如果平台不存在商品来源运营商为当前运营商的商品  那么审核状态改为已完成  并且平台不展示该条审核
                if (allAreaItem.stream().noneMatch(e -> e.getOperatorId().equals(1L) && ObjectUtil.isNotEmpty(e.getSourceOperatorId()) && e.getItemId().equals(itemAudit.getItemId()) && e.getSourceType().equals(2) && e.getSourceOperatorId().equals(itemAudit.getOperatorId()))) {
                    itemAudit.setStatus(ItemAuditStatusEnum.ADOPT.getValue());
                    itemAudit.setPlatformDisplay(0);
                } else {
                    itemAudit.setPlatformDisplay(1);
                }
                buildHistory(source, itemAudit, itemPriceHistoryCreateList, itemPriceHistoryMainCreateList, choiceLotLibSkuUpdateList, areaSkuUpdateList, thirdMessageCreateRequestList, allAreaSku, choiceLotLibSkuBySkuIds, choiceLotLibMap, choiceLotLibItemModels, now, areaItemUpdateList, allAreaItem);
            }
        } else if ("operator".equals(source)) {
            if (!itemAudit.getStatus().equals(ItemAuditStatusEnum.ADOPT.getValue())) {
                if (allConfig.stream().anyMatch(e -> e.getType() == 4 && e.getOperatorId().equals(itemAudit.getOperatorId()))) {
                    allConfig.stream().filter(e -> e.getType() == 4 && e.getOperatorId().equals(itemAudit.getOperatorId())).findFirst().ifPresent(e -> {
                        if (ObjectUtil.isNotEmpty(e.getPlatformPriceAudit()) && e.getPlatformPriceAudit() > 0) {
                            if (itemAudit.getAuditPriceType().equals(0)) {
                                if (e.getPlatformPriceAudit() == 1) {
                                    itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                                } else if (e.getPlatformPriceAudit() == 2 && itemAudit.getBeforeBasePrice() < itemAudit.getBasePrice()) {
                                    itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                                } else if (e.getPlatformPriceAudit() == 3 && itemAudit.getBeforeBasePrice() > itemAudit.getBasePrice()) {
                                    itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                                } else {
                                    itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                                }
                            } else {
                                if (e.getPlatformPriceAudit() == 1) {
                                    itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                                } else if (e.getPlatformPriceAudit() == 2 && itemAudit.getUpstreamOperatorBeforeBasePrice() < itemAudit.getUpstreamOperatorAfterBasePrice()) {
                                    itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                                } else if (e.getPlatformPriceAudit() == 3 && itemAudit.getUpstreamOperatorBeforeBasePrice() > itemAudit.getUpstreamOperatorAfterBasePrice()) {
                                    itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                                } else {
                                    itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                                }
                            }

                        } else {
                            itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                        }
                    });
                } else {
                    itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                }
                //如果平台不存在商品来源运营商为当前运营商的商品  那么审核状态改为已完成  并且平台不展示该条审核
                if (allAreaItem.stream().noneMatch(e -> e.getOperatorId().equals(1L) && ObjectUtil.isNotEmpty(e.getSourceOperatorId()) && e.getItemId().equals(itemAudit.getItemId()) && e.getSourceType().equals(2) && e.getSourceOperatorId().equals(itemAudit.getOperatorId()))) {
                    itemAudit.setStatus(ItemAuditStatusEnum.ADOPT.getValue());
                    itemAudit.setPlatformDisplay(0);
                } else {
                    itemAudit.setPlatformDisplay(1);
                }
            }
            if (itemAudit.getStatus().equals(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue())) {

                itemAudit.setDelayTime(now);
            }
            //如果自动审核通过则修改运营商价格、生成运营商改价记录、发送渠道消息
            buildHistory(source, itemAudit, itemPriceHistoryCreateList, itemPriceHistoryMainCreateList, choiceLotLibSkuUpdateList, areaSkuUpdateList, thirdMessageCreateRequestList, allAreaSku, choiceLotLibSkuBySkuIds, choiceLotLibMap, choiceLotLibItemModels, now, areaItemUpdateList, allAreaItem);
        }
    }


    void platform(
            String source,
            ItemAudit itemAudit,
            List<ItemPriceHistory> itemPriceHistoryCreateList,
            List<ParanaItemPriceHistoryMainModel> itemPriceHistoryMainCreateList,
            List<ChoiceLotLibSkuModel> choiceLotLibSkuUpdateList,
            List<AreaSku> areaSkuUpdateList,
            List<AreaItem> areaItemUpdateList,
            List<ParanaThirdMessageCreateRequest> thirdMessageCreateRequestList,
            List<AreaSku> allAreaSku,
            List<AreaItem> allAreaItem,
            List<ChoiceLotLibSkuModel> choiceLotLibSkuBySkuIds,
            Map<Long, ChoiceLotLib> choiceLotLibMap,
            List<ChoiceLotLibItemModel> choiceLotLibItemModels,
            List<ParanaItemConfigurationModel> allConfig,
            Map<String, AreaSku> areaSkuMap,
            Map<String, AreaItem> areaItemMap,
            Map<Long, List<ItemPriceHistory>> itemPriceHistoryMap,
            List<ItemPriceHistory> itemPriceHistoryUpdateList
    ) {
        Date now = new Date();
        if ("vendor".equals(source)) {
            //判断是否存在配置
            //如果是品牌商供品平台  并且配置为自动审核通过  那只需要将审核状态修改为待生效  平台的改价及改价记录将在job中执行
            if (allConfig.stream().anyMatch(e -> e.getType() == 1 && e.getOperatorId() == 1L && e.getVendorId().equals(itemAudit.getVendorId()))) {
                //品牌商配置
                allConfig.stream().filter(e -> e.getType() == 1 && e.getOperatorId() == 1L && e.getVendorId().equals(itemAudit.getVendorId())).findFirst().ifPresent(e -> {
                    if (ObjectUtil.isNotEmpty(e.getPriceAudit()) && e.getPriceAudit() > 0) {
                        if (e.getPriceAudit() == 1) {
                            itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                        } else if (e.getPriceAudit() == 2 && itemAudit.getBeforeBasePrice() < itemAudit.getBasePrice()) {
                            itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                        } else if (e.getPriceAudit() == 3 && itemAudit.getBeforeBasePrice() > itemAudit.getBasePrice()) {
                            itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                        } else {
                            itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                        }
                    } else {
                        itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                    }
                });
            } else {
                itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
            }
        } else if ("operator".equals(source)) {
            if (allConfig.stream().anyMatch(e -> e.getType() == 4 && e.getOperatorId().equals(itemAudit.getOperatorId()))) {
                allConfig.stream().filter(e -> e.getType() == 4 && e.getOperatorId().equals(itemAudit.getOperatorId())).findFirst().ifPresent(e -> {
                    if (ObjectUtil.isNotEmpty(e.getPlatformPriceAudit()) && e.getPlatformPriceAudit() > 0) {
                        if (itemAudit.getAuditPriceType().equals(0)) {
                            if (e.getPlatformPriceAudit() == 1) {
                                itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                            } else if (e.getPlatformPriceAudit() == 2 && itemAudit.getBeforeBasePrice() < itemAudit.getBasePrice()) {
                                itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                            } else if (e.getPlatformPriceAudit() == 3 && itemAudit.getBeforeBasePrice() > itemAudit.getBasePrice()) {
                                itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                            } else {
                                itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                            }
                        } else {
                            if (e.getPlatformPriceAudit() == 1) {
                                itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                            } else if (e.getPlatformPriceAudit() == 2 && itemAudit.getUpstreamOperatorBeforeBasePrice() < itemAudit.getUpstreamOperatorAfterBasePrice()) {
                                itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                            } else if (e.getPlatformPriceAudit() == 3 && itemAudit.getUpstreamOperatorBeforeBasePrice() > itemAudit.getUpstreamOperatorAfterBasePrice()) {
                                itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
                            } else {
                                itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                            }
                        }
                    } else {
                        itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
                    }
                });
            }
        } else if ("platform".equals(source)) {
            itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
        }

        if (itemAudit.getStatus().equals(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue())) {
            itemAudit.setAuditAt(now);
            itemAudit.setDelayTime(now);
            buildPlatformHistory(source, itemAudit, itemPriceHistoryCreateList, itemPriceHistoryMainCreateList, choiceLotLibSkuUpdateList, areaSkuUpdateList, thirdMessageCreateRequestList, allAreaSku, choiceLotLibSkuBySkuIds, choiceLotLibMap, choiceLotLibItemModels, now, areaItemUpdateList, allAreaItem,
                    areaSkuMap,
                    areaItemMap, itemPriceHistoryMap, itemPriceHistoryUpdateList);
        }
    }


    private void buildHistory(String source, ItemAudit itemAudit, List<ItemPriceHistory> itemPriceHistoryCreateList, List<ParanaItemPriceHistoryMainModel> itemPriceHistoryMainCreateList, List<ChoiceLotLibSkuModel> choiceLotLibSkuUpdateList, List<AreaSku> areaSkuUpdateList, List<ParanaThirdMessageCreateRequest> thirdMessageCreateRequestList, List<AreaSku> allAreaSku, List<ChoiceLotLibSkuModel> choiceLotLibSkuBySkuIds, Map<Long, ChoiceLotLib> choiceLotLibMap, List<ChoiceLotLibItemModel> choiceLotLibItemModels, Date now, List<AreaItem> areaItemUpdateList, List<AreaItem> allAreaItem) {

        itemAudit.setAuditAt(now);
        log.info("ItemAuditHelper.buildHistory.itemAudit:{}", itemAudit);
        //判断运营选品库是否存在这个商品  如果不存在的话  说明运营存在这个品  但是没有加到任何一个选品库
        if (choiceLotLibSkuBySkuIds.stream().anyMatch(e -> e.getOperatorId().equals(itemAudit.getOperatorId()) && e.getSkuId().equals(itemAudit.getSkuId()) && (ObjectUtil.isEmpty(itemAudit.getExpJson()) || (JSONObject.parseObject(itemAudit.getExpJson()).get("choiceLotLibId") != null &&  JSONObject.parseObject(itemAudit.getExpJson()).getLong("choiceLotLibId").equals(e.getChoiceLotLibId()))))) {
            choiceLotLibSkuBySkuIds.stream().filter(upstreamChoiceLotLibSkuModel -> upstreamChoiceLotLibSkuModel.getOperatorId().equals(itemAudit.getOperatorId()) && upstreamChoiceLotLibSkuModel.getSkuId().equals(itemAudit.getSkuId()) && (ObjectUtil.isEmpty(itemAudit.getExpJson()) || (JSONObject.parseObject(itemAudit.getExpJson()).get("choiceLotLibId") != null && JSONObject.parseObject(itemAudit.getExpJson()).getLong("choiceLotLibId").equals(upstreamChoiceLotLibSkuModel.getChoiceLotLibId())))).forEach(upstreamChoiceLotLibSkuModel -> {
                ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel = new ParanaItemPriceHistoryMainModel();
                ItemPriceHistory itemPriceHistory = new ItemPriceHistory();
                Long beforeDistriPrice = upstreamChoiceLotLibSkuModel.getDistributorPrice();

                if (ObjectUtil.isNotEmpty(itemAudit.getExpJson())) {
                    //用于修改选品库加价率的情况
                    JSONObject jsonObject = JSONObject.parseObject(itemAudit.getExpJson());
                    if (jsonObject.containsKey(upstreamChoiceLotLibSkuModel.getChoiceLotLibId() + "_" + upstreamChoiceLotLibSkuModel.getSkuId())) {
                        Long markup = jsonObject.getLong(upstreamChoiceLotLibSkuModel.getChoiceLotLibId() + "_" + upstreamChoiceLotLibSkuModel.getSkuId());
                        upstreamChoiceLotLibSkuModel.setMarkup(markup);
                    }
                }
                // 分销价=供货价*（1+加价率）
                Long distriPrice = null;
                if ("vendor".equals(source)) {
                    distriPrice = MarkupCalculateUtils.getDistriPrice(itemAudit.getBasePrice(), upstreamChoiceLotLibSkuModel.getMarkup());
                } else if ("operator".equals(source)) {

                    if (ObjectUtil.isNotEmpty(itemAudit.getUpstreamOperatorAfterBasePrice()) && itemAudit.getAuditPriceType().equals(3)) {
                        distriPrice = itemAudit.getUpstreamOperatorAfterBasePrice();
                    } else {
                        distriPrice = MarkupCalculateUtils.getDistriPrice(ObjectUtil.isNotEmpty(itemAudit.getBasePrice()) ? itemAudit.getBasePrice() : upstreamChoiceLotLibSkuModel.getBasePrice(), upstreamChoiceLotLibSkuModel.getMarkup());
                    }
                } else if ("platform".equals(source)) {
                    distriPrice = itemAudit.getUpstreamOperatorAfterBasePrice();
                }

                paranaItemPriceHistoryMainModel.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                paranaItemPriceHistoryMainModel.setTenantId(1);
                paranaItemPriceHistoryMainModel.setOperatorId(upstreamChoiceLotLibSkuModel.getOperatorId());
                paranaItemPriceHistoryMainModel.setSkuId(upstreamChoiceLotLibSkuModel.getSkuId());
                paranaItemPriceHistoryMainModel.setItemId(upstreamChoiceLotLibSkuModel.getItemId());
                paranaItemPriceHistoryMainModel.setChoiceLotLibId(upstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                paranaItemPriceHistoryMainModel.setItemName(itemAudit.getName());
                paranaItemPriceHistoryMainModel.setAuditId(itemAudit.getId());
                paranaItemPriceHistoryMainModel.setCreatedAt(now);
                paranaItemPriceHistoryMainModel.setUpdatedAt(now);
                paranaItemPriceHistoryMainModel.setCreatedBy(itemAudit.getCreatedBy());
                if (itemAudit.getAuditPriceType().equals(3)) {
                    paranaItemPriceHistoryMainModel.setType(3);
                } else {
                    //运营商调价
                    if (itemAudit.getOperatorId().equals(1L)) {
                        paranaItemPriceHistoryMainModel.setType(2);
                    } else {
                        if (itemAudit.getOperatorId().equals(itemAudit.getInitiateOperatorId())) {
                            paranaItemPriceHistoryMainModel.setType(1);
                        } else {
                            paranaItemPriceHistoryMainModel.setType(0);
                        }
                    }
                }

                //当前为品牌商改价时才修改运营areaSku基础价格
                if (itemAudit.getAuditPriceType().equals(0)) {
                    allAreaSku.stream().filter(y -> y.getOperatorId().equals(itemAudit.getOperatorId()) && y.getSkuId().equals(itemAudit.getSkuId())).forEach(y -> {
                        y.setBasePrice(itemAudit.getBasePrice());
                        y.setOriginalPrice(itemAudit.getOriginalPrice());
                        allAreaItem.stream().filter(areaItem -> areaItem.getOperatorId().equals(y.getOperatorId()) && areaItem.getItemId().equals(y.getItemId())).findFirst().ifPresent(areaItemUpdateList::add);
                        areaSkuUpdateList.add(y);
                    });
                }


                if (choiceLotLibMap.containsKey(upstreamChoiceLotLibSkuModel.getChoiceLotLibId())) {
                    ChoiceLotLib choiceLotLib = choiceLotLibMap.get(upstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                    //渠道选品库需要发送改价通知
                    if (choiceLotLib.getType() == 0) {
                        for (ChoiceLotLibItemModel x : choiceLotLibItemModels) {
                            if (x.getChoiceLotLibId().equals(upstreamChoiceLotLibSkuModel.getChoiceLotLibId()) && x.getItemId().equals(upstreamChoiceLotLibSkuModel.getItemId())) {
                                if (ObjectUtil.isNotEmpty(x.getDistributorIds())) {
                                    String[] split = x.getDistributorIds().replace("#", "").split(",");

                                    ParanaThirdMessageBean paranaThirdMessageBean = new ParanaThirdMessageBean();
                                    paranaThirdMessageBean.setId(x.getChoiceLotLibId());
                                    paranaThirdMessageBean.setItemMessage(JSON.toJSONString(upstreamChoiceLotLibSkuModel.getSkuId()));
                                    for (String authId : split) {
                                        ParanaThirdMessageCreateRequest paranaThirdMessageCreateRequest = new ParanaThirdMessageCreateRequest();
                                        paranaThirdMessageCreateRequest.setOperatorId(x.getOperatorId());
                                        paranaThirdMessageCreateRequest.setMessageType(7);
                                        //构建渠道改价记录
                                        paranaThirdMessageCreateRequest.setAuthId(Long.parseLong(authId));
                                        paranaThirdMessageCreateRequest.setBean(paranaThirdMessageBean);
                                        thirdMessageCreateRequestList.add(paranaThirdMessageCreateRequest);
                                    }
                                }
                            }
                        }
                        itemPriceHistory.setDownstreamOperatorBeforePrice(beforeDistriPrice);
                        itemPriceHistory.setDownstreamOperatorAfterPrice(distriPrice);
                        itemPriceHistory.setDownstreamOperatorChoiceLotLibId(upstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                        itemPriceHistory.setDownstreamOperatorId(upstreamChoiceLotLibSkuModel.getOperatorId());
                        itemPriceHistory.setType(itemAudit.getAuditPriceType());
                    } else {
                        //平台库
                        itemAudit.setUpstreamOperatorBeforeBasePrice(beforeDistriPrice);
                        itemAudit.setUpstreamOperatorAfterBasePrice(distriPrice);
                        itemPriceHistory.setUpstreamOperatorBeforeBasePrice(beforeDistriPrice);
                        itemPriceHistory.setUpstreamOperatorAfterBasePrice(distriPrice);
                        itemPriceHistory.setUpstreamOperatorChoiceLotLibId(upstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                        itemPriceHistory.setUpstreamOperatorId(upstreamChoiceLotLibSkuModel.getOperatorId());
                        itemPriceHistory.setType(itemAudit.getAuditPriceType());
                    }
                }

                itemPriceHistory.setId(idGenerator.nextValue(ItemPriceHistory.class));
                itemPriceHistory.setTenantId(1);
                itemPriceHistory.setOperatorId(itemAudit.getOperatorId());
                itemPriceHistory.setChoiceLotLibId(upstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                itemPriceHistory.setSkuId(itemAudit.getSkuId());
                itemPriceHistory.setItemId(itemAudit.getItemId());
                itemPriceHistory.setAuditId(itemAudit.getId());
                itemPriceHistory.setCreatedBy(itemAudit.getCreatedBy());
                itemPriceHistory.setVendorBeforeBasePrice(itemAudit.getBeforeBasePrice());
                itemPriceHistory.setVendorAfterBasePrice(itemAudit.getBasePrice());
                itemPriceHistory.setCreatedAt(now);
                itemPriceHistory.setUpdatedAt(now);

                itemPriceHistoryMainCreateList.add(paranaItemPriceHistoryMainModel);
                itemPriceHistoryCreateList.add(itemPriceHistory);
                //只有在品牌商修改供货价的情况下才修改选品库的供货价
                if (itemAudit.getAuditPriceType() == 0) {
                    upstreamChoiceLotLibSkuModel.setBasePrice(itemAudit.getBasePrice());
                }

                if (itemAudit.getAuditPriceType().equals(3)) {
                    // 加价率 = (分销价-供货价）/供货价*100%。
                    upstreamChoiceLotLibSkuModel.setMarkup(MarkupCalculateUtils.getResellerRate(distriPrice, upstreamChoiceLotLibSkuModel.getBasePrice()));
                }

                // 分销毛利=分销价-供货价
                upstreamChoiceLotLibSkuModel.setResellerGross(MarkupCalculateUtils.sub(distriPrice, upstreamChoiceLotLibSkuModel.getBasePrice()));
                // 分销毛利率=（分销价-供货价）/分销价*100%
                upstreamChoiceLotLibSkuModel.setResellerGrossRate(MarkupCalculateUtils.getProfitRate(distriPrice, upstreamChoiceLotLibSkuModel.getBasePrice()));
                // 预估毛利率=（建议零售价 - 采购价）/ 建议零售价 × 100%
                upstreamChoiceLotLibSkuModel.setPreResellerGrossRate(MarkupCalculateUtils.getProfitRate(upstreamChoiceLotLibSkuModel.getOriginalPrice(), distriPrice));
                upstreamChoiceLotLibSkuModel.setDistributorPrice(distriPrice);
                if (ObjectUtil.isNotEmpty(itemAudit.getOriginalPrice())) {
                    upstreamChoiceLotLibSkuModel.setOriginalPrice(itemAudit.getOriginalPrice());
                }
                upstreamChoiceLotLibSkuModel.setUpdatedBy(itemAudit.getCreatedBy());

                //更新选品库
                choiceLotLibSkuUpdateList.add(upstreamChoiceLotLibSkuModel);
            });
        } else {
            if (allAreaSku.stream().anyMatch(e -> e.getOperatorId().equals(itemAudit.getOperatorId()) && e.getSkuId().equals(itemAudit.getSkuId()))) {
                allAreaSku.stream().filter(e -> e.getOperatorId().equals(itemAudit.getOperatorId()) && e.getSkuId().equals(itemAudit.getSkuId())).forEach(e -> {
                    allAreaItem.stream().filter(areaItem -> areaItem.getOperatorId().equals(e.getOperatorId()) && areaItem.getItemId().equals(e.getItemId())).findFirst().ifPresent(areaItemUpdateList::add);
                    e.setBasePrice(itemAudit.getBasePrice());
                    e.setOriginalPrice(itemAudit.getOriginalPrice());
                    e.setUpdatedAt(now);
                    e.setUpdatedBy(itemAudit.getAuditBy());
                    ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel = new ParanaItemPriceHistoryMainModel();
                    ItemPriceHistory itemPriceHistory = new ItemPriceHistory();
                    paranaItemPriceHistoryMainModel.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                    paranaItemPriceHistoryMainModel.setTenantId(1);
                    paranaItemPriceHistoryMainModel.setOperatorId(itemAudit.getOperatorId());
                    paranaItemPriceHistoryMainModel.setSkuId(itemAudit.getSkuId());
                    paranaItemPriceHistoryMainModel.setItemId(itemAudit.getItemId());
                    paranaItemPriceHistoryMainModel.setAuditId(itemAudit.getId());
                    paranaItemPriceHistoryMainModel.setItemName(itemAudit.getName());
                    paranaItemPriceHistoryMainModel.setCreatedAt(now);
                    paranaItemPriceHistoryMainModel.setUpdatedAt(now);
                    paranaItemPriceHistoryMainModel.setCreatedBy(itemAudit.getCreatedBy());

                    if (itemAudit.getAuditPriceType().equals(3)) {
                        paranaItemPriceHistoryMainModel.setType(3);
                    } else {
                        //运营商调价
                        if (itemAudit.getOperatorId().equals(1L)) {
                            paranaItemPriceHistoryMainModel.setType(2);
                        } else {
                            if (itemAudit.getOperatorId().equals(itemAudit.getInitiateOperatorId())) {
                                paranaItemPriceHistoryMainModel.setType(1);
                            } else {
                                paranaItemPriceHistoryMainModel.setType(0);
                            }
                        }
                    }

                    itemPriceHistory.setId(idGenerator.nextValue(ItemPriceHistory.class));
                    itemPriceHistory.setChoiceLotLibId(0L);
                    itemPriceHistory.setTenantId(1);
                    itemPriceHistory.setOperatorId(itemAudit.getOperatorId());
                    itemPriceHistory.setSkuId(itemAudit.getSkuId());
                    itemPriceHistory.setItemId(itemAudit.getItemId());
                    itemPriceHistory.setAuditId(itemAudit.getId());
                    itemPriceHistory.setType(1);
                    itemPriceHistory.setVendorBeforeBasePrice(itemAudit.getBeforeBasePrice());
                    itemPriceHistory.setVendorAfterBasePrice(itemAudit.getBasePrice());
                    itemPriceHistory.setCreatedBy(itemAudit.getCreatedBy());
                    itemPriceHistory.setCreatedAt(now);
                    itemPriceHistory.setUpdatedAt(now);
                    itemPriceHistory.setUpstreamOperatorId(e.getOperatorId());
                    itemPriceHistory.setType(itemAudit.getAuditPriceType());

                    itemPriceHistoryMainCreateList.add(paranaItemPriceHistoryMainModel);
                    itemPriceHistoryCreateList.add(itemPriceHistory);
                    areaSkuUpdateList.add(e);
                });
            }
        }

    }


    private void buildPlatformHistory(
            String source,
            ItemAudit itemAudit,
            List<ItemPriceHistory> itemPriceHistoryCreateList,
            List<ParanaItemPriceHistoryMainModel> itemPriceHistoryMainCreateList,
            List<ChoiceLotLibSkuModel> choiceLotLibSkuUpdateList,
            List<AreaSku> areaSkuUpdateList,
            List<ParanaThirdMessageCreateRequest> thirdMessageCreateRequestList,
            List<AreaSku> allAreaSku,
            List<ChoiceLotLibSkuModel> choiceLotLibSkuBySkuIds,
            Map<Long, ChoiceLotLib> choiceLotLibMap,
            List<ChoiceLotLibItemModel> choiceLotLibItemModels,
            Date now,
            List<AreaItem> areaItemUpdateList,
            List<AreaItem> allAreaItem,
            Map<String, AreaSku> areaSkuMap,
            Map<String, AreaItem> areaItemMap,
            Map<Long, List<ItemPriceHistory>> itemPriceHistoryMap,
            List<ItemPriceHistory> itemPriceHistoryUpdateList
    ) {
        Map<Long, List<ChoiceLotLibSkuModel>> choiceLotLibSkuMap = choiceLotLibSkuBySkuIds.stream().collect(Collectors.groupingBy(ChoiceLotLibSkuModel::getSkuId));
        Map<String, ChoiceLotLibSkuModel> lotLibSkuModelMap = choiceLotLibSkuBySkuIds.stream().collect(Collectors.toMap(e -> e.getOperatorId() + "-" + e.getChoiceLotLibId() + "-" + e.getSkuId(), Function.identity(), (e1, e2) -> e1));
        Map<Long, List<ChoiceLotLibItemModel>> choiceLotLibItemModelMap = choiceLotLibItemModels.stream().collect(Collectors.groupingBy(ChoiceLotLibItemModel::getItemId));
        itemAudit.setStatus(ItemAuditStatusEnum.ADOPT.getValue());
        AreaItem platformAreaItem = areaItemMap.get("1-" + itemAudit.getItemId());
        //判断平台是否存在该商品
        if (ObjectUtil.isEmpty(platformAreaItem)) {
            return;
        }

        List<ItemPriceHistory> thisAuditHistory = itemPriceHistoryMap.get(itemAudit.getId());

        int platformCount = 1;
        //判断平台选品库是否存在这个商品        如果不存在的话  说明品台存在这个品  但是没有加到任何一个选品库
        if (ObjectUtil.isNotEmpty(choiceLotLibSkuMap) && choiceLotLibSkuMap.containsKey(itemAudit.getSkuId()) && choiceLotLibSkuMap.get(itemAudit.getSkuId()).stream().anyMatch(choiceLotLibSkuModel -> choiceLotLibSkuModel.getOperatorId().equals(1L))) {

            //修改平台选品库及商品池价格 并生成改价记录
            for (ChoiceLotLibSkuModel choiceLotLibSkuModel : choiceLotLibSkuMap.get(itemAudit.getSkuId())) {
                if (choiceLotLibSkuModel.getOperatorId().equals(1L)) {

                    ItemPriceHistory itemPriceHistory = null;
                    if (ObjectUtil.isNotEmpty(thisAuditHistory)) {
                        for (ItemPriceHistory priceHistory : thisAuditHistory) {
                            if (priceHistory.getSkuId().equals(choiceLotLibSkuModel.getSkuId()) && priceHistory.getAuditId().equals(itemAudit.getId()) && ObjectUtil.isNotEmpty(priceHistory.getUpstreamOperatorChoiceLotLibId()) && ObjectUtil.isNotEmpty(priceHistory.getUpstreamOperatorId())) {
                                log.info("priceHistory:{}", priceHistory);
                                log.info("platformAreaItem:{}", platformAreaItem);
                                if (ObjectUtil.isNotEmpty(platformAreaItem.getSourceChoiceLotLibId()) && ObjectUtil.isNotEmpty(platformAreaItem.getSourceOperatorId()) && priceHistory.getUpstreamOperatorId().equals(platformAreaItem.getSourceOperatorId()) && priceHistory.getUpstreamOperatorChoiceLotLibId().equals(platformAreaItem.getSourceChoiceLotLibId())) {
                                    itemPriceHistory = priceHistory;
                                    break;
                                }
                            }
                        }
                    } else {
                        for (ItemPriceHistory priceHistory : itemPriceHistoryCreateList) {
                            if (priceHistory.getSkuId().equals(choiceLotLibSkuModel.getSkuId()) && priceHistory.getAuditId().equals(itemAudit.getId()) && ObjectUtil.isNotEmpty(priceHistory.getUpstreamOperatorChoiceLotLibId()) && ObjectUtil.isNotEmpty(priceHistory.getUpstreamOperatorId())) {
                                log.info("priceHistory:{}", priceHistory);
                                log.info("platformAreaItem:{}", platformAreaItem);
                                if (ObjectUtil.isNotEmpty(platformAreaItem.getSourceChoiceLotLibId()) && ObjectUtil.isNotEmpty(platformAreaItem.getSourceOperatorId()) && priceHistory.getUpstreamOperatorId().equals(platformAreaItem.getSourceOperatorId()) && priceHistory.getUpstreamOperatorChoiceLotLibId().equals(platformAreaItem.getSourceChoiceLotLibId())) {
                                    itemPriceHistory = priceHistory;
                                    break;
                                }
                            }
                        }
                    }

                    ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel = new ParanaItemPriceHistoryMainModel();

                    if (ObjectUtil.isNotEmpty(itemAudit.getExpJson())) {
                        //用于修改选品库加价率的情况
                        JSONObject jsonObject = JSONObject.parseObject(itemAudit.getExpJson());
                        if (jsonObject.containsKey(choiceLotLibSkuModel.getChoiceLotLibId() + "_" + choiceLotLibSkuModel.getSkuId())) {
                            Long markup = jsonObject.getLong(choiceLotLibSkuModel.getChoiceLotLibId() + "_" + choiceLotLibSkuModel.getSkuId());
                            choiceLotLibSkuModel.setMarkup(markup);
                        }
                        if (itemAudit.getOperatorId().equals(1L)) {
                            if (jsonObject.containsKey("choiceLotLibId")) {
                                Long choiceLotLibId = jsonObject.getLong("choiceLotLibId");
                                if (!choiceLotLibSkuModel.getChoiceLotLibId().equals(choiceLotLibId)) {
                                    continue;
                                }
                            }
                        }
                    }

                    Long basePrice = null;
                    Long distriPrice = null;

                    //判断当前审核是否为平台自己创建
                    if (itemAudit.getInitiateOperatorId().equals(1L)) {
                        //当为平台自己创建的审核时只有两种情况   修改分销价  修改选品库加价率
                        //当为平台自己创建的审核时 无需更新AreaSku的basePrice
                        distriPrice = itemAudit.getPlatformAfterBasePrice();
                    } else {
                        //来自上游的审核单  只需要判断当前商品来源类型
                        //如果为品牌供品平台，取itemAudit的basePrice
                        //如果为运营供品平台则按照当前areaItem的来源operatorId和sourceChoiceLotLibId取出对应的选品库sku  获取分销价
                        //都要更新areaSku的basePrice
                        if (platformAreaItem.getSourceType().equals(1)) {
                            basePrice = itemAudit.getBasePrice();
                            distriPrice = MarkupCalculateUtils.getDistriPrice(itemAudit.getBasePrice(), choiceLotLibSkuModel.getMarkup());
                        } else if (platformAreaItem.getSourceType().equals(2)) {
                            ChoiceLotLibSkuModel choiceLotLibSkuModel1 = lotLibSkuModelMap.get(platformAreaItem.getSourceOperatorId() + "-" + platformAreaItem.getSourceChoiceLotLibId() + "-" + itemAudit.getSkuId());
                            if (ObjectUtil.isNotEmpty(choiceLotLibSkuModel1)) {
                                basePrice = choiceLotLibSkuModel1.getDistributorPrice();
                                distriPrice = MarkupCalculateUtils.getDistriPrice(basePrice, choiceLotLibSkuModel.getMarkup());
                            }
                        }
                        AreaSku areaSku = areaSkuMap.get(choiceLotLibSkuModel.getOperatorId() + "-" + choiceLotLibSkuModel.getSkuId());
                        areaSku.setBasePrice(basePrice);
                        areaSku.setOriginalPrice(itemAudit.getOriginalPrice());
                        areaItemUpdateList.add(platformAreaItem);
                        areaSkuUpdateList.add(areaSku);
                    }

                    paranaItemPriceHistoryMainModel.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                    paranaItemPriceHistoryMainModel.setTenantId(1);
                    paranaItemPriceHistoryMainModel.setOperatorId(choiceLotLibSkuModel.getOperatorId());
                    paranaItemPriceHistoryMainModel.setSkuId(itemAudit.getSkuId());
                    paranaItemPriceHistoryMainModel.setItemId(itemAudit.getItemId());
                    paranaItemPriceHistoryMainModel.setChoiceLotLibId(choiceLotLibSkuModel.getChoiceLotLibId());
                    paranaItemPriceHistoryMainModel.setAuditId(itemAudit.getId());
                    paranaItemPriceHistoryMainModel.setItemName(itemAudit.getName());
                    paranaItemPriceHistoryMainModel.setCreatedAt(now);
                    paranaItemPriceHistoryMainModel.setUpdatedAt(now);
                    paranaItemPriceHistoryMainModel.setCreatedBy(itemAudit.getCreatedBy());
                    if (itemAudit.getOperatorId().equals(1L)) {
                        if (itemAudit.getInitiateOperatorId().equals(1L)) {
                            paranaItemPriceHistoryMainModel.setType(2);
                        } else {
                            if (platformAreaItem.getSourceType() == 1) {
                                paranaItemPriceHistoryMainModel.setType(0);
                            } else {
                                paranaItemPriceHistoryMainModel.setType(1);
                            }
                        }
                    } else {
                        //运营商调价
                        paranaItemPriceHistoryMainModel.setType(1);
                    }

                    if (ObjectUtil.isEmpty(itemPriceHistory)) {
                        itemPriceHistory = new ItemPriceHistory();
                        itemPriceHistory.setPlatformBeforeBasePrice(choiceLotLibSkuModel.getDistributorPrice());
                        itemPriceHistory.setPlatformAfterBasePrice(distriPrice);

                        itemPriceHistory.setId(idGenerator.nextValue(ItemPriceHistory.class));
                        itemPriceHistory.setTenantId(1);
                        itemPriceHistory.setOperatorId(itemAudit.getOperatorId());
                        itemPriceHistory.setChoiceLotLibId(0L);
                        itemPriceHistory.setPlatformChoiceLotLibId(choiceLotLibSkuModel.getChoiceLotLibId());
                        itemPriceHistory.setSkuId(itemAudit.getSkuId());
                        itemPriceHistory.setItemId(itemAudit.getItemId());
                        itemPriceHistory.setAuditId(itemAudit.getId());
                        itemPriceHistory.setCreatedBy(itemAudit.getCreatedBy());
                        itemPriceHistory.setCreatedAt(now);
                        itemPriceHistory.setUpdatedAt(now);
                        if (itemAudit.getOperatorId().equals(1L)) {
                            if (itemAudit.getInitiateOperatorId().equals(1L)) {
                                itemPriceHistory.setType(2);
                            } else {
                                if (platformAreaItem.getSourceType() == 1) {
                                    itemPriceHistory.setType(0);
                                    itemPriceHistory.setVendorBeforeBasePrice(itemAudit.getBeforeBasePrice());
                                    itemPriceHistory.setVendorAfterBasePrice(itemAudit.getBasePrice());
                                } else {
                                    itemPriceHistory.setType(1);
                                }
                            }
                        } else {
                            //运营商调价
                            itemPriceHistory.setType(1);
                        }
                        itemPriceHistoryCreateList.add(itemPriceHistory);
                    } else {
                        if (platformCount > 1) {
                            //判断存在多个平台选品库的情况
                            itemPriceHistory = BeanUtil.toBean(itemPriceHistory, ItemPriceHistory.class);
                            itemPriceHistory.setDownstreamOperatorBeforePrice(null);
                            itemPriceHistory.setDownstreamOperatorAfterPrice(null);
                            itemPriceHistory.setDownstreamOperatorId(null);
                            itemPriceHistory.setDownstreamOperatorChoiceLotLibId(null);
                            itemPriceHistory.setPlatformBeforeBasePrice(choiceLotLibSkuModel.getDistributorPrice());
                            itemPriceHistory.setPlatformAfterBasePrice(distriPrice);
                            itemPriceHistory.setPlatformChoiceLotLibId(choiceLotLibSkuModel.getChoiceLotLibId());
                            itemPriceHistoryCreateList.add(itemPriceHistory);
                        } else {
                            itemPriceHistory.setPlatformBeforeBasePrice(choiceLotLibSkuModel.getDistributorPrice());
                            itemPriceHistory.setPlatformAfterBasePrice(distriPrice);
                            itemPriceHistory.setPlatformChoiceLotLibId(choiceLotLibSkuModel.getChoiceLotLibId());
                            itemPriceHistoryUpdateList.add(itemPriceHistory);
                        }

                    }

                    itemPriceHistoryMainCreateList.add(paranaItemPriceHistoryMainModel);


                    if (ObjectUtil.isNotEmpty(basePrice)) {
                        choiceLotLibSkuModel.setBasePrice(basePrice);
                    }

                    if (itemAudit.getAuditPriceType().equals(3) && itemAudit.getOperatorId().equals(choiceLotLibSkuModel.getOperatorId())) {
                        // 加价率 = (分销价-供货价）/供货价*100%。
                        choiceLotLibSkuModel.setMarkup(MarkupCalculateUtils.getResellerRate(distriPrice, choiceLotLibSkuModel.getBasePrice()));
                    }

                    // 分销毛利=分销价-供货价
                    choiceLotLibSkuModel.setResellerGross(MarkupCalculateUtils.sub(distriPrice, choiceLotLibSkuModel.getBasePrice()));
                    // 分销毛利率=（分销价-供货价）/分销价*100%
                    choiceLotLibSkuModel.setResellerGrossRate(MarkupCalculateUtils.getProfitRate(distriPrice, choiceLotLibSkuModel.getBasePrice()));
                    // 预估毛利率=（建议零售价 - 采购价）/ 建议零售价 × 100%
                    choiceLotLibSkuModel.setPreResellerGrossRate(MarkupCalculateUtils.getProfitRate(itemAudit.getOriginalPrice(), distriPrice));
                    choiceLotLibSkuModel.setDistributorPrice(distriPrice);
                    if (ObjectUtil.isNotEmpty(itemAudit.getOriginalPrice())) {
                        choiceLotLibSkuModel.setOriginalPrice(itemAudit.getOriginalPrice());
                    }
                    //更新选品库
                    choiceLotLibSkuUpdateList.add(choiceLotLibSkuModel);

                    int count = 1;
                    if (choiceLotLibSkuMap.get(itemAudit.getSkuId()).stream().anyMatch(e -> !e.getOperatorId().equals(1L) && !e.getOperatorId().equals(itemAudit.getOperatorId()))) {
                        //排除上游运营商及平台选品库数据
                        Long finalDistriPrice = distriPrice;

                        for (ChoiceLotLibSkuModel downstreamChoiceLotLibSkuModel : choiceLotLibSkuMap.get(itemAudit.getSkuId())) {
                            if (!downstreamChoiceLotLibSkuModel.getOperatorId().equals(1L) && !downstreamChoiceLotLibSkuModel.getOperatorId().equals(itemAudit.getOperatorId())) {
                                if (areaItemMap.containsKey(downstreamChoiceLotLibSkuModel.getOperatorId() + "-" + downstreamChoiceLotLibSkuModel.getItemId())) {
                                    AreaItem areaItem = areaItemMap.get(downstreamChoiceLotLibSkuModel.getOperatorId() + "-" + downstreamChoiceLotLibSkuModel.getItemId());
                                    if (ObjectUtil.isNotEmpty(areaItem.getSourceChoiceLotLibId()) && areaItem.getSourceChoiceLotLibId().equals(choiceLotLibSkuModel.getChoiceLotLibId())) {
                                        ChoiceLotLib choiceLotLib = choiceLotLibMap.get(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                        if (choiceLotLib == null) {
                                            throw new ServiceException("选品库不存在");
                                        }
                                        ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel2 = new ParanaItemPriceHistoryMainModel();


                                        Long beforeBasePrice2 = downstreamChoiceLotLibSkuModel.getDistributorPrice();

                                        // 分销价=供货价*（1+加价率）
                                        Long distriPrice1 = MarkupCalculateUtils.getDistriPrice(finalDistriPrice, downstreamChoiceLotLibSkuModel.getMarkup());


                                        if (count > 1) {
                                            //判断订购改商品的下游运营商数量  如果大于1的话则需要复制多条记录
                                            ItemPriceHistory itemPriceHistory2 = BeanUtil.toBean(itemPriceHistory, ItemPriceHistory.class);
                                            itemPriceHistory2.setId(idGenerator.nextValue(ItemPriceHistory.class));
                                            itemPriceHistory2.setDownstreamOperatorChoiceLotLibId(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                            itemPriceHistory2.setDownstreamOperatorId(downstreamChoiceLotLibSkuModel.getOperatorId());
                                            itemPriceHistory2.setDownstreamOperatorBeforePrice(beforeBasePrice2);
                                            itemPriceHistory2.setDownstreamOperatorAfterPrice(distriPrice1);
                                            itemPriceHistory2.setType(2);
                                            itemPriceHistoryCreateList.add(itemPriceHistory2);
                                        } else {
                                            itemPriceHistory.setDownstreamOperatorBeforePrice(beforeBasePrice2);
                                            itemPriceHistory.setDownstreamOperatorAfterPrice(distriPrice1);
                                            itemPriceHistory.setDownstreamOperatorId(downstreamChoiceLotLibSkuModel.getOperatorId());
                                            itemPriceHistory.setDownstreamOperatorChoiceLotLibId(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                        }

                                        paranaItemPriceHistoryMainModel2.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                                        paranaItemPriceHistoryMainModel2.setTenantId(1);
                                        paranaItemPriceHistoryMainModel2.setOperatorId(downstreamChoiceLotLibSkuModel.getOperatorId());
                                        paranaItemPriceHistoryMainModel2.setSkuId(itemAudit.getSkuId());
                                        paranaItemPriceHistoryMainModel2.setItemId(itemAudit.getItemId());
                                        paranaItemPriceHistoryMainModel2.setChoiceLotLibId(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                        paranaItemPriceHistoryMainModel2.setItemName(itemAudit.getName());
                                        paranaItemPriceHistoryMainModel2.setAuditId(itemAudit.getId());
                                        paranaItemPriceHistoryMainModel2.setCreatedAt(now);
                                        paranaItemPriceHistoryMainModel2.setUpdatedAt(now);
                                        paranaItemPriceHistoryMainModel2.setCreatedBy(itemAudit.getCreatedBy());


                                        //平台调价
                                        paranaItemPriceHistoryMainModel2.setType(2);


                                        //更新下游运营商AreaSku的basePrice
                                        AreaSku areaSku = areaSkuMap.get(downstreamChoiceLotLibSkuModel.getOperatorId() + "-" + itemAudit.getSkuId());
                                        if (areaSku.getSkuId().equals(itemAudit.getSkuId()) && !areaSku.getOperatorId().equals(itemAudit.getOperatorId())) {
                                            areaSku.setBasePrice(finalDistriPrice);
                                            areaSku.setOriginalPrice(itemAudit.getOriginalPrice());
                                            areaItemUpdateList.add(areaItem);
                                            areaSkuUpdateList.add(areaSku);
                                        }
                                        //发送渠道商改价消息
                                        //下游运营商选品库
                                        ChoiceLotLib downChoiceLotLib = choiceLotLibMap.get(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                        if (downChoiceLotLib.getType() == 0) {
                                            choiceLotLibItemModelMap.get(downstreamChoiceLotLibSkuModel.getItemId()).stream().filter(choiceLotLibItemModel -> choiceLotLibItemModel.getChoiceLotLibId().equals(downstreamChoiceLotLibSkuModel.getChoiceLotLibId()) && choiceLotLibItemModel.getItemId().equals(downstreamChoiceLotLibSkuModel.getItemId())).forEach(choiceLotLibItemModel -> {
                                                if (ObjectUtil.isNotEmpty(choiceLotLibItemModel.getDistributorIds())) {
                                                    String[] split = choiceLotLibItemModel.getDistributorIds().replace("#", "").split(",");
                                                    ParanaThirdMessageBean paranaThirdMessageBean = new ParanaThirdMessageBean();
                                                    paranaThirdMessageBean.setId(choiceLotLibItemModel.getChoiceLotLibId());
                                                    paranaThirdMessageBean.setItemMessage(JSON.toJSONString(downstreamChoiceLotLibSkuModel.getSkuId()));
                                                    for (String authId : split) {
                                                        ParanaThirdMessageCreateRequest paranaThirdMessageCreateRequest = new ParanaThirdMessageCreateRequest();
                                                        paranaThirdMessageCreateRequest.setOperatorId(choiceLotLibItemModel.getOperatorId());
                                                        paranaThirdMessageCreateRequest.setMessageType(7);
                                                        paranaThirdMessageCreateRequest.setAuthId(Long.parseLong(authId));
                                                        paranaThirdMessageCreateRequest.setBean(paranaThirdMessageBean);
                                                        thirdMessageCreateRequestList.add(paranaThirdMessageCreateRequest);
                                                    }
                                                }
                                            });
                                        }

                                        downstreamChoiceLotLibSkuModel.setBasePrice(finalDistriPrice);

                                        // 加价率 = (分销价-供货价）/供货价*100%。
                                        downstreamChoiceLotLibSkuModel.setMarkup(MarkupCalculateUtils.getResellerRate(distriPrice1, downstreamChoiceLotLibSkuModel.getBasePrice()));
                                        // 分销毛利=分销价-供货价
                                        downstreamChoiceLotLibSkuModel.setResellerGross(MarkupCalculateUtils.sub(distriPrice1, downstreamChoiceLotLibSkuModel.getBasePrice()));
                                        // 分销毛利率=（分销价-供货价）/分销价*100%
                                        downstreamChoiceLotLibSkuModel.setResellerGrossRate(MarkupCalculateUtils.getProfitRate(distriPrice1, downstreamChoiceLotLibSkuModel.getBasePrice()));
                                        // 预估毛利率=（建议零售价 - 采购价）/ 建议零售价 × 100%
                                        downstreamChoiceLotLibSkuModel.setPreResellerGrossRate(MarkupCalculateUtils.getProfitRate(downstreamChoiceLotLibSkuModel.getOriginalPrice(), distriPrice1));
                                        downstreamChoiceLotLibSkuModel.setDistributorPrice(distriPrice1);
                                        if (ObjectUtil.isNotEmpty(itemAudit.getOriginalPrice())) {
                                            downstreamChoiceLotLibSkuModel.setOriginalPrice(itemAudit.getOriginalPrice());
                                        }
                                        //更新选品库
                                        choiceLotLibSkuUpdateList.add(downstreamChoiceLotLibSkuModel);
                                        itemPriceHistoryMainCreateList.add(paranaItemPriceHistoryMainModel2);
                                        count++;
                                    }
                                }

                            }
                        }
                    }
                    for (AreaSku e : allAreaSku) {
                        if (!e.getOperatorId().equals(1L) && !e.getOperatorId().equals(itemAudit.getOperatorId()) && e.getSkuId().equals(choiceLotLibSkuModel.getSkuId())) {
                            AreaItem areaItem = areaItemMap.get(e.getOperatorId() + "-" + e.getItemId());
                            if (ObjectUtil.isNotEmpty(areaItem.getSourceChoiceLotLibId()) && areaItem.getSourceChoiceLotLibId().equals(choiceLotLibSkuModel.getChoiceLotLibId())) {
                                boolean falg = false;
                                for (AreaSku areaSkuUpdate : areaSkuUpdateList) {
                                    if (e.getOperatorId().equals(areaSkuUpdate.getOperatorId()) && e.getSkuId().equals(areaSkuUpdate.getSkuId())) {
                                        falg = true;
                                        break;
                                    }
                                }
                                if (falg) {
                                    continue;
                                }
                                ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel3 = new ParanaItemPriceHistoryMainModel();
                                paranaItemPriceHistoryMainModel3.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                                paranaItemPriceHistoryMainModel3.setTenantId(1);
                                paranaItemPriceHistoryMainModel3.setOperatorId(e.getOperatorId());
                                paranaItemPriceHistoryMainModel3.setSkuId(itemAudit.getSkuId());
                                paranaItemPriceHistoryMainModel3.setItemId(itemAudit.getItemId());
                                paranaItemPriceHistoryMainModel3.setItemName(itemAudit.getName());
                                paranaItemPriceHistoryMainModel3.setAuditId(itemAudit.getId());
                                paranaItemPriceHistoryMainModel3.setCreatedAt(now);
                                paranaItemPriceHistoryMainModel3.setUpdatedAt(now);
                                paranaItemPriceHistoryMainModel3.setType(2);
                                paranaItemPriceHistoryMainModel3.setCreatedBy(itemAudit.getCreatedBy());

                                if (count > 1) {
                                    //判断订购改商品的下游运营商数量  如果大于1的话则需要复制多条记录
                                    ItemPriceHistory itemPriceHistory2 = BeanUtil.toBean(itemPriceHistory, ItemPriceHistory.class);
                                    itemPriceHistory2.setDownstreamOperatorChoiceLotLibId(null);
                                    itemPriceHistory2.setId(idGenerator.nextValue(ItemPriceHistory.class));
                                    itemPriceHistory2.setDownstreamOperatorId(e.getOperatorId());
                                    itemPriceHistory2.setDownstreamOperatorBeforePrice(null);
                                    itemPriceHistory2.setDownstreamOperatorAfterPrice(null);
                                    if (itemAudit.getOperatorId().equals(1L)) {
                                        itemPriceHistory2.setType(2);
                                    } else {
                                        //运营商调价
                                        itemPriceHistory2.setType(1);
                                    }
                                    itemPriceHistoryCreateList.add(itemPriceHistory2);
                                } else {
                                    itemPriceHistory.setDownstreamOperatorBeforePrice(null);
                                    itemPriceHistory.setDownstreamOperatorAfterPrice(null);
                                    itemPriceHistory.setDownstreamOperatorId(e.getOperatorId());
                                }

                                e.setBasePrice(distriPrice);
                                e.setOriginalPrice(itemAudit.getOriginalPrice());
                                e.setUpdatedAt(now);
                                e.setUpdatedBy(itemAudit.getAuditBy());
                                itemPriceHistoryMainCreateList.add(paranaItemPriceHistoryMainModel3);
                                areaSkuUpdateList.add(e);
                                areaItemUpdateList.add(areaItem);
                                count++;
                            }
                        }
                    }
                    platformCount++;
                }
            }


        } else {
            if (areaSkuMap.containsKey("1-" + itemAudit.getSkuId())) {
                AreaSku areaSku = areaSkuMap.get("1-" + itemAudit.getSkuId());
                ItemPriceHistory itemPriceHistory = null;


                if (ObjectUtil.isNotEmpty(thisAuditHistory)) {
                    for (ItemPriceHistory priceHistory : thisAuditHistory) {
                        if (priceHistory.getSkuId().equals(areaSku.getSkuId()) && priceHistory.getAuditId().equals(itemAudit.getId()) && ObjectUtil.isNotEmpty(priceHistory.getUpstreamOperatorChoiceLotLibId()) && ObjectUtil.isNotEmpty(priceHistory.getUpstreamOperatorId())) {
                            log.info("priceHistory:{}", priceHistory);
                            log.info("platformAreaItem:{}", platformAreaItem);
                            if (ObjectUtil.isNotEmpty(platformAreaItem.getSourceChoiceLotLibId()) && ObjectUtil.isNotEmpty(platformAreaItem.getSourceOperatorId()) && priceHistory.getUpstreamOperatorId().equals(platformAreaItem.getSourceOperatorId()) && priceHistory.getUpstreamOperatorChoiceLotLibId().equals(platformAreaItem.getSourceChoiceLotLibId())) {
                                itemPriceHistory = priceHistory;
                                break;
                            }
                        }
                    }
                } else {
                    for (ItemPriceHistory priceHistory : itemPriceHistoryCreateList) {
                        if (priceHistory.getSkuId().equals(areaSku.getSkuId()) && priceHistory.getAuditId().equals(itemAudit.getId()) && ObjectUtil.isNotEmpty(priceHistory.getUpstreamOperatorChoiceLotLibId()) && ObjectUtil.isNotEmpty(priceHistory.getUpstreamOperatorId())) {
                            log.info("priceHistory:{}", priceHistory);
                            log.info("platformAreaItem:{}", platformAreaItem);
                            if (ObjectUtil.isNotEmpty(platformAreaItem.getSourceChoiceLotLibId()) && ObjectUtil.isNotEmpty(platformAreaItem.getSourceOperatorId()) && priceHistory.getUpstreamOperatorId().equals(platformAreaItem.getSourceOperatorId()) && priceHistory.getUpstreamOperatorChoiceLotLibId().equals(platformAreaItem.getSourceChoiceLotLibId())) {
                                itemPriceHistory = priceHistory;
                                break;
                            }
                        }
                    }
                }

                if (areaSku.getOperatorId().equals(1L)) {
                    ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel = new ParanaItemPriceHistoryMainModel();
                    if (ObjectUtil.isEmpty(itemPriceHistory)) {
                        itemPriceHistory = new ItemPriceHistory();
                        itemPriceHistory.setId(idGenerator.nextValue(ItemPriceHistory.class));
                        itemPriceHistory.setTenantId(1);
                        itemPriceHistory.setOperatorId(itemAudit.getOperatorId());
                        itemPriceHistory.setSkuId(itemAudit.getSkuId());
                        itemPriceHistory.setItemId(itemAudit.getItemId());
                        itemPriceHistory.setChoiceLotLibId(0L);
                        itemPriceHistory.setAuditId(itemAudit.getId());
                        itemPriceHistory.setCreatedBy(itemAudit.getCreatedBy());
                        itemPriceHistory.setCreatedAt(now);
                        itemPriceHistory.setUpdatedAt(now);

                        if (itemAudit.getOperatorId().equals(1L)) {
                            if (itemAudit.getInitiateOperatorId().equals(1L)) {
                                itemPriceHistory.setType(2);
                            } else {
                                if (platformAreaItem.getSourceType() == 1) {
                                    itemPriceHistory.setType(0);
                                    itemPriceHistory.setVendorBeforeBasePrice(itemAudit.getBeforeBasePrice());
                                    itemPriceHistory.setVendorAfterBasePrice(itemAudit.getBasePrice());
                                } else {
                                    itemPriceHistory.setType(1);
                                }
                            }
                        } else {
                            //运营商调价
                            itemPriceHistory.setType(1);
                        }

                        itemPriceHistoryCreateList.add(itemPriceHistory);
                    }

                    if (platformAreaItem.getSourceType().equals(1)) {
                        areaSku.setBasePrice(itemAudit.getBasePrice());
                        areaSku.setOriginalPrice(itemAudit.getOriginalPrice());
                    } else {
                        if (ObjectUtil.isNotEmpty(platformAreaItem.getSourceOperatorId()) && (platformAreaItem.getSourceOperatorId().equals(areaSku.getOperatorId()) || platformAreaItem.getSourceOperatorId().equals(itemAudit.getOperatorId()))) {
                            if (ObjectUtil.isNotEmpty(itemAudit.getExpJson()) && JSONObject.parseObject(itemAudit.getExpJson()).getLong("choiceLotLibId").equals(platformAreaItem.getSourceChoiceLotLibId())) {
                                areaSku.setBasePrice(itemAudit.getUpstreamOperatorAfterBasePrice());
                                areaSku.setOriginalPrice(itemAudit.getOriginalPrice());
                            }
                        }
                    }
                    paranaItemPriceHistoryMainModel.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                    paranaItemPriceHistoryMainModel.setTenantId(1);
                    paranaItemPriceHistoryMainModel.setOperatorId(areaSku.getOperatorId());
                    paranaItemPriceHistoryMainModel.setSkuId(itemAudit.getSkuId());
                    paranaItemPriceHistoryMainModel.setItemId(itemAudit.getItemId());
                    paranaItemPriceHistoryMainModel.setAuditId(itemAudit.getId());
                    paranaItemPriceHistoryMainModel.setCreatedAt(now);
                    paranaItemPriceHistoryMainModel.setUpdatedAt(now);
                    paranaItemPriceHistoryMainModel.setCreatedBy(itemAudit.getCreatedBy());

                    if (itemAudit.getOperatorId().equals(1L)) {
                        paranaItemPriceHistoryMainModel.setType(2);
                    } else {
                        //运营商调价
                        paranaItemPriceHistoryMainModel.setType(1);
                    }

                    areaSku.setUpdatedAt(now);
                    areaSku.setUpdatedBy(itemAudit.getAuditBy());

                    itemPriceHistoryMainCreateList.add(paranaItemPriceHistoryMainModel);
                    areaItemUpdateList.add(platformAreaItem);
                    areaSkuUpdateList.add(areaSku);
                }

            }
        }
    }
}
