package io.terminus.parana.item.attribute.repository;

import com.google.common.collect.ImmutableMap;
import io.terminus.parana.item.attribute.model.CategoryAttributeBinding;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import io.terminus.parana.item.common.utils.RepositoryMap;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-12-09
 */
@Repository
public class CategoryAttributeBindingDao extends AbstractMybatisDao<CategoryAttributeBinding> {


    public CategoryAttributeBinding findNearestUp(Long categoryId, Integer order) {
        return sqlSession.selectOne(sqlId("findNearestUp"), ImmutableMap.of(
                "categoryId", categoryId,
                "order", order
        ));
    }

    public CategoryAttributeBinding findNearestDown(Long categoryId, Integer order) {
        return sqlSession.selectOne(sqlId("findNearestDown"), ImmutableMap.of(
                "categoryId", categoryId,
                "order", order
        ));
    }

    public boolean updateOrder(Long id, Integer order) {
        return sqlSession.update(sqlId("updateOrder"), ImmutableMap.of(
                "id", id,
                "order", order
        )) == 1;
    }

    public boolean checkBindingByGroup(String group) {
        return sqlSession.selectOne(sqlId("checkBindingByGroup"), RepositoryMap.of(
                "group", group
        )) != null;
    }

    public List<CategoryAttributeBinding> findByCategoryId(Long categoryId) {
        return sqlSession.selectList(sqlId("findByCategoryId"), categoryId);
    }

    public List<CategoryAttributeBinding> findByCategoryIdSet(Set<Long> categoryIdSet) {
        return sqlSession.selectList(sqlId("findByCategoryIdSet"), RepositoryMap.of(
                "categoryIdSet", categoryIdSet
        ));
    }

    public CategoryAttributeBinding findByGroupAndName(Long categoryId, String group, String name) {
        return sqlSession.selectOne(sqlId("findByGroupAndName"), RepositoryMap.of(
                "categoryId", categoryId,
                "group", group,
                "name", name
        ));
    }

    public List<CategoryAttributeBinding> findByAttributeId(Long attributeId) {
        return sqlSession.selectList(sqlId("findByAttributeId"), attributeId);
    }

    public Integer getMaxOrder() {
        return sqlSession.selectOne(sqlId("getMaxOrder"), RepositoryMap.standard());
    }

    public boolean deleteByCategoryId(Long categoryId) {
        sqlSession.delete(sqlId("deleteByCategoryId"), categoryId);
        return Boolean.TRUE;
    }
}
