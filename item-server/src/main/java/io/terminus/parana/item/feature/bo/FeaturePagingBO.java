package io.terminus.parana.item.feature.bo;

import io.terminus.parana.item.common.base.AbstractModelPagingBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-04-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeaturePagingBO extends AbstractModelPagingBO {
    private static final long serialVersionUID = 5434261453008610060L;

    private String code;

    private String name;

    private Integer status;
}
