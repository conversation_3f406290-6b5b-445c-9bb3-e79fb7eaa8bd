package io.terminus.parana.item.comment.api.facade;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.comment.api.bean.request.*;
import io.terminus.parana.item.comment.api.bean.response.ItemCommentInfo;
import io.terminus.parana.item.comment.api.bean.response.ItemCommentRenderInfo;
import io.terminus.parana.item.comment.api.bean.response.ItemCommentStatisticsInfo;
import io.terminus.parana.item.comment.api.converter.ItemCommentApiConverter;
import io.terminus.parana.item.comment.api.converter.ItemCommentApiInfoConverter;
import io.terminus.parana.item.comment.app.ItemCommentReadApp;
import io.terminus.parana.item.comment.model.Comment;
import io.terminus.parana.item.comment.search.so.ItemCommentSO;
import io.terminus.parana.item.comment.service.CommentReadDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ItemCommentReadFacadeImpl implements ItemCommentReadFacade {

    private final ItemCommentReadApp itemCommentReadApp;
    private final CommentReadDomainService commentReadDomainService;
    private final ItemCommentApiConverter itemCommentApiConverter;
    private final ItemCommentApiInfoConverter itemCommentApiInfoConverter;

    @Override
    public Response<ItemCommentStatisticsInfo> statistic(ItemCommentStatisticsRequest request) {
        try {
            return Response.ok(itemCommentReadApp.statistics(request.getItemId(), request.getTenantId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Paging<ItemCommentRenderInfo>> render(ItemCommentRenderRequest request) {
        try {
            return Response.ok(itemCommentReadApp.renderItemComment(request.getItemId(), request.getLimit(),
                    request.getOffset(), request.getTenantId(), request.getWithImage(), request.getWithPursue()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<ItemCommentRenderInfo> renderFull(ItemCommentFullRenderRequest request) {
        try {
            return Response.ok(itemCommentReadApp.renderFullInfo(request.getId(), request.getTenantId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Paging<ItemCommentInfo>> simplePaging(ItemCommentSimplePagingRequest request) {
        log.info("ItemCommentSimplePagingRequest:::::::::::::::::"+request);
        ItemCommentSO so = itemCommentApiConverter.get(request);

        try {
            return Response.ok(itemCommentReadApp.paging(request.getSceneCode(), so));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Paging<ItemCommentRenderInfo>> simplePagingTree(ItemCommentSimplePagingRequest request) {
        ItemCommentSO so = itemCommentApiConverter.get(request);

        try {
            return Response.ok(itemCommentReadApp.pagingTree(request.getSceneCode(), so));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<ItemCommentInfo> randomWithStatus(ItemCommentRandomWithStatusRequest request) {
        try {
            Comment comment = commentReadDomainService.randomCommentWithStatus(request.getStatus(), request.getOperatorId());
            return Response.ok(itemCommentApiInfoConverter.get(comment));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<ItemCommentInfo> findById(ItemCommentQueryByIdRequest request) {
        try {
            Comment comment = commentReadDomainService.findById(request.getId(), request.getTenantId());
            return Response.ok(itemCommentApiInfoConverter.get(comment));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }
}
