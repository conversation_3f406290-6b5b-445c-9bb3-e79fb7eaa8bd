package io.terminus.parana.item.choicelot.repository;

import com.google.common.collect.Maps;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceLotLibCreateRequest;
import io.terminus.parana.item.choicelot.api.bean.response.ChoiceLotLibInfo;
import io.terminus.parana.item.choicelot.model.ChoiceLotLib;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import io.terminus.parana.item.item.canal.ESMQInjection;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: ChoiceLotDao
 * @projectName item-center
 * @description: 选品库数据操作 TODO
 * @date 2022/3/3 15:13
 */
@Repository
public class ChoiceLotLibDao extends AbstractMybatisDao<ChoiceLotLib> {


    public List<ChoiceLotLib> list(Map<String, Object> criteria) {
        return sqlSession.selectList(sqlId("list"), criteria);
    }


    /**
     * 根据Id查询选品库信息
     *
     * @param id         选品库ID(必传)
     * @param operatorId
     * @return
     */
    public ChoiceLotLib getById(Long id, Long operatorId) {
        Map<String, String> params = new HashMap<>();
        params.put("id", id.toString());
        params.put("operatorId", operatorId.toString());
        return getSqlSession().selectOne(sqlId("getById"), params);
    }

    /**
     * 根据ID修改选品库信息
     *
     * @param request
     * @return
     */
    public Boolean updateById(ChoiceLotLibCreateRequest request) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("id", request.getId());
        params.put("name", request.getName());
        params.put("descr", request.getDescr());
        params.put("spuMaxQty", request.getSpuMaxQty());
        params.put("distributorVisible", request.getDistributorVisible());
        params.put("updatedBy", request.getUpdatedBy());
        params.put("operatorId", request.getOperatorId());
        Boolean result = getSqlSession().update(sqlId("updateById"), params) > 0;
        if (result) {
           // ESMQInjection.entryChoiceByItemIds(request.getId(), request.getOperatorId(), null, null, request.getUpdatedBy());
        }
        return result;
    }


    /**
     * 根据ID删除选品库信息
     * @return
     */
    public Boolean deleteById(Map<String, Object> param) {
         return getSqlSession().update(sqlId("deleteById"), param) > 0;
    }

    /**
     * 多条件筛选选品库信息
     *
     * @param params
     * @return
     */
    public List<ChoiceLotLib> listInfoFilter(Map<String, Object> params) {
        return getSqlSession().selectList(sqlId("listInfoFilter"), params);
    }

}
