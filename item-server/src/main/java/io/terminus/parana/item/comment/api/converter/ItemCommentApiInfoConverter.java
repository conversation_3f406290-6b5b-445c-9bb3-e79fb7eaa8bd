package io.terminus.parana.item.comment.api.converter;

import io.terminus.parana.item.comment.api.bean.response.ItemCommentInfo;
import io.terminus.parana.item.comment.model.Comment;
import io.terminus.parana.item.comment.search.dataobject.ItemCommentDO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-18
 */
@Mapper
public interface ItemCommentApiInfoConverter {

    ItemCommentInfo get(ItemCommentDO itemCommentDO);

    ItemCommentInfo get(Comment comment);

    List<ItemCommentInfo> get(List<ItemCommentDO> list);
}
