package io.terminus.parana.item.channel.bo;

import io.terminus.parana.item.common.base.AbstractModelPagingBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChannelPagingCriteria extends AbstractModelPagingBO {

    private static final long serialVersionUID = 731958262230902541L;

    private Long id;

    private String name;
}
