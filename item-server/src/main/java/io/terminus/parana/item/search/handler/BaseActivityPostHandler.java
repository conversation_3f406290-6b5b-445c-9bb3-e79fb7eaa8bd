package io.terminus.parana.item.search.handler;

import com.google.common.collect.Lists;
import io.terminus.parana.item.common.dimension.DimensionContext;
import io.terminus.parana.item.item.enums.SdPriceEnum;
import io.terminus.parana.item.price.api.bean.response.ItemSDPriceInfo;
import io.terminus.parana.item.search.adaptor.ItemComponentAdaptor;
import io.terminus.parana.item.search.config.SearchProperties;
import io.terminus.parana.item.search.docobject.AreaItemDO;
import io.terminus.parana.item.search.domain.ActivityDO;
import io.terminus.parana.item.search.domain.SkuPriceDO;
import io.terminus.parana.item.search.request.PrimarySearchRequest;
import io.terminus.parana.item.shop.api.bean.response.ShopInfo;
import io.terminus.parana.item.plugin.third.api.promotion.api.ActivitySearchSupportApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 搜索价格透出逻辑（价格中心+促销中心）
 * 导购商品列表透出统一逻辑：{@link io.terminus.parana.item.enhance.app##FrontItemReadApp}
 *
 * <AUTHOR>
 * @version 2019-04-28 下午1:53
 */
@Slf4j
public class BaseActivityPostHandler {
    @Autowired
    private SearchProperties properties;
    @Autowired(required = false)
    private ActivitySearchSupportApi activitySearchSupportApi;
    @Autowired
    private ItemComponentAdaptor componentAdaptor;

    public <ItemDoc extends AreaItemDO> void handle(List<ItemDoc> items, PrimarySearchRequest request) {

        if (CollectionUtils.isEmpty(items) || Objects.isNull(request.getDimension())) {
            return;
        }

        DimensionContext context = request.getDimension();

        List<AreaItemDO> itemList = Lists.newArrayList();
        itemList.addAll(items);
        ShopInfo shopInfo = componentAdaptor.getShopInfo(items.get(0).getOperatorId(), request.getTenantId());

        Map<Long, List<ItemSDPriceInfo>> sDPriceMap = null;
        Map<Long, Long> customerPriceMap = null;
        if(SdPriceEnum.BBC_PRICE.getValue() != shopInfo.getSdPriceType()){
            sDPriceMap = componentAdaptor.getSDPriceMap(itemList, context);
        } else {
            customerPriceMap = componentAdaptor.getCustomerPriceMap(itemList, context);
        }

        // 渠道id
        Long channelId = StringUtils.isEmpty(context.getChannelId()) ? 0L : Long.parseLong(context.getChannelId());
        // 促销计算因子
        Map<String, String> calculateOptions = getCalculateOptions(context);
        for (AreaItemDO item : items) {
            // 计算各sku对应该渠道的价格
            Map<Long, SkuPriceDO> skuPrices = getSkuPrices(item.getSkuPrices(), channelId);
            if(SdPriceEnum.BBC_PRICE.getValue() != shopInfo.getSdPriceType()){
                if (sDPriceMap != null) {
                    List<ItemSDPriceInfo> infos = sDPriceMap.get(item.getItemId());
                    if(!CollectionUtils.isEmpty(infos)){
                        //根据基本价/促销价分组取最小价格
                        Optional<ItemSDPriceInfo> minPriceInfo = infos.stream().filter(f -> !StringUtils.isEmpty(f.getPrice())).min(Comparator.comparing(ItemSDPriceInfo::getPrice));
                        //根据基本价取最小价格
                        Optional<ItemSDPriceInfo> minPriceInfo1 = infos.stream().filter(f -> !StringUtils.isEmpty(f.getBbcPrice1())).min(Comparator.comparing(ItemSDPriceInfo::getBbcPrice1));
                        for (Long skuId : skuPrices.keySet()) {
                            minPriceInfo.ifPresent(itemSDPriceInfo -> skuPrices.get(skuId).setPrice(itemSDPriceInfo.getBbcPrice()));
                        }

                        if(shopInfo.getSdPriceType() == 1){
                            for (Long skuId : skuPrices.keySet()) {
                                minPriceInfo1.ifPresent(itemSDPriceInfo -> skuPrices.get(skuId).setOriginalPrice(itemSDPriceInfo.getBbcPrice1()));
                            }
                        }
                    }
                }
            } else {
                for (Long skuId : skuPrices.keySet()) {
                    if (customerPriceMap != null && customerPriceMap.containsKey(skuId)) {
                        skuPrices.get(skuId).setPrice(customerPriceMap.get(skuId));
                    }
                }
            }

            // 取所有sku对应渠道的最低价格
            Long lowestPrice = getLowestSkuPrice(skuPrices);
            if (null != lowestPrice) {
                item.setLowPrice(lowestPrice);
            }

            // 如果走SD 重新赋值lowPrice、originalPrice
            if(SdPriceEnum.BBC_PRICE.getValue() != shopInfo.getSdPriceType()){
                if (sDPriceMap != null) {
                    List<ItemSDPriceInfo> infos = sDPriceMap.get(item.getItemId());
                    if(!CollectionUtils.isEmpty(infos)){
                        //根据基本价/促销价分组取最小价格
                        Optional<ItemSDPriceInfo> minPriceInfo = infos.stream().filter(f -> !StringUtils.isEmpty(f.getPrice())).min(Comparator.comparing(ItemSDPriceInfo::getPrice));
                        //根据基本价取最小价格
                        Optional<ItemSDPriceInfo> minPriceInfo1 = infos.stream().filter(f -> !StringUtils.isEmpty(f.getBbcPrice1())).min(Comparator.comparing(ItemSDPriceInfo::getBbcPrice1));
                        minPriceInfo.ifPresent(itemSDPriceInfo -> item.setLowPrice(itemSDPriceInfo.getBbcPrice()));
                        minPriceInfo.ifPresent(itemSDPriceInfo -> item.setOriginalPrice(itemSDPriceInfo.getBbcPrice()));
                        // 走SD促销的基本售价
                        if(shopInfo.getSdPriceType() == 1){
                            minPriceInfo1.ifPresent(itemSDPriceInfo -> item.setIspromotion(1L));
                        }else{
                            //如果不走sd促销 但是bbcprice1有值 证明sd做了促销 那么需要把基本价赋值到lowprice
                            minPriceInfo1.ifPresent(itemSDPriceInfo -> item.setLowPrice(itemSDPriceInfo.getBbcPrice1()));
                        }
                        minPriceInfo1.ifPresent(itemSDPriceInfo -> item.setOriginalPrice(itemSDPriceInfo.getBbcPrice1()));
                    }
                }
            }

            // 走sd促销价 则不走内部促销
            if(SdPriceEnum.SD_PRICE_PROMOTION_PRICE.getValue() != shopInfo.getSdPriceType()){
                if (CollectionUtils.isEmpty(item.getActivities())) {
                    // 无活动时，以定价作为划线价
                    item.setHighPrice(getOriginalPrice(skuPrices));
                    continue;
                }
                // 忽略最高售价，后续以定价作为划线价
                item.setHighPrice(null);
                if (null == activitySearchSupportApi) {
//                item.setActivities(null);
                    continue;
                }
                List<ActivityDO> activities = activitySearchSupportApi.reserveValidActivities(item.getActivities(), skuPrices, properties.getActivityCodeList(), properties.getActivityLimit(), calculateOptions);
                if (!CollectionUtils.isEmpty(activities)) {
                    item.setActivities(activities);
                    ActivityDO activity = activities.get(0);
                    if (null != activity.getDiscountPrice()) {
                        item.setLowPrice(activity.getDiscountPrice());
                    }
                    item.setHighPrice(activity.getOriginalPrice());
                }
                if (Objects.isNull(item.getHighPrice())) {
                    item.setHighPrice(getOriginalPrice(skuPrices));
                }
            }

        }
    }

    protected Map<String, String> getCalculateOptions(DimensionContext context) {
        Map<String, String> options = new HashMap<>();
        options.put("deviceType", context.getDeviceType());
        options.put("businessType", context.getBusinessType());
        return options;
    }

    private Long getOriginalPrice(Map<Long, SkuPriceDO> skuPrices) {
        return null;
//        怡亚通划线价
//        if (CollectionUtils.isEmpty(skuPrices)) {
//            return null;
//        }
//        List<Long> prices = skuPrices.values().stream().map(SkuPriceDO::getOriginalPrice).filter(Objects::nonNull).collect(Collectors.toList());
//        return CollectionUtils.isEmpty(prices) ? null : Collections.max(prices, Long::compareTo);
    }

    private Long getLowestSkuPrice(Map<Long, SkuPriceDO> skuPrices) {
        if (CollectionUtils.isEmpty(skuPrices)) {
            return null;
        }
        return Collections.min(skuPrices.values(), Comparator.comparing(SkuPriceDO::getPrice)).getPrice();
    }

    private Map<Long, SkuPriceDO> getSkuPrices(Map<Long, List<SkuPriceDO>> channelSkuPrices, Long channelId) {
        if (CollectionUtils.isEmpty(channelSkuPrices)) {
            return Collections.emptyMap();
        }
        List<SkuPriceDO> skuList = channelSkuPrices.get(0L);
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyMap();
        }
        Map<Long, SkuPriceDO> skuMap = skuList.stream().collect(Collectors.toMap(SkuPriceDO::getId, e -> e));
        if (channelId.equals(0L)) {
            return skuMap;
        }
        List<SkuPriceDO> skuChannelList = channelSkuPrices.get(channelId);
        if (CollectionUtils.isEmpty(skuChannelList)) {
            return skuMap;
        }
        Map<Long, SkuPriceDO> skuChannelMap = skuChannelList.stream().collect(Collectors.toMap(SkuPriceDO::getId, e -> e));
        // 渠道价格覆盖默认渠道价格
        skuChannelMap.forEach(skuMap::put);
        return skuMap;
    }

}
