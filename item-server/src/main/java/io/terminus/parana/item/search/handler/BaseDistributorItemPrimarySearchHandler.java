package io.terminus.parana.item.search.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import io.terminus.api.request.AbstractPageRequest;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.common.utils.Splitters;
import io.terminus.parana.item.brand.api.bean.response.BrandInfo;
import io.terminus.parana.item.category.api.bean.response.OperatorCategoryInfo;
import io.terminus.parana.item.category.api.bean.response.OperatorCategoryOfSearchInfo;
import io.terminus.parana.item.item.enums.ItemStatus;
import io.terminus.parana.item.plugin.third.api.promotion.api.ActivitySearchSupportApi;
import io.terminus.parana.item.search.adaptor.ItemComponentAdaptor;
import io.terminus.parana.item.search.config.SearchProperties;
import io.terminus.parana.item.search.consts.Constant;
import io.terminus.parana.item.search.context.SearchContext;
import io.terminus.parana.item.search.docobject.DistributorItemLibDO;
import io.terminus.parana.item.search.domain.ActivityDO;
import io.terminus.parana.item.search.dto.*;
import io.terminus.parana.item.search.enums.DistributorItemSortEnum;
import io.terminus.parana.item.search.request.DistributorItemPrimarySearchRequest;
import io.terminus.parana.item.search.request.PrimarySearchRequest;
import io.terminus.parana.item.search.request.handler.DistributorItemPrimarySearchHandler;
import io.terminus.parana.item.third.info.ScopeInfo;
import io.terminus.parana.item.third.info.SearchScopeInfo;
import io.terminus.parana.search.client.builder.QueryBuilder;
import io.terminus.parana.search.client.result.Document;
import io.terminus.parana.search.client.result.SearchResult;
import io.terminus.parana.search.client.search.Order;
import io.terminus.parana.search.client.search.ScoreMode;
import io.terminus.parana.search.client.search.SearchRequest;
import io.terminus.parana.search.client.search.Sort;
import io.terminus.parana.search.client.search.query.BoolQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/31 14:24
 */
@Slf4j
public class BaseDistributorItemPrimarySearchHandler<ItemDoc extends DistributorItemLibDO> implements DistributorItemPrimarySearchHandler {

    // 文档对象
    private final Class itemDoc;

    @Autowired
    private SearchProperties properties;

    @Autowired
    private ItemComponentAdaptor componentAdaptor;

    @Autowired(required = false)
    private ActivitySearchSupportApi activitySupportApi;

    @Value("${primary.search.maxPageNo}")
    private Integer maxPageNo;
    @Value("${primary.search.maxPageSize}")
    private Integer maxPageSize;

    public BaseDistributorItemPrimarySearchHandler() {
        if (getClass().getGenericSuperclass() instanceof ParameterizedType) {
            Type[] types = ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments();
            itemDoc = (Class) types[0];
        } else {
            itemDoc = DistributorItemLibDO.class;
        }
    }

    @Override
    public void handle(AbstractPageRequest abstractPageRequest, SearchRequest searchRequest) {
        DistributorItemPrimarySearchRequest primaryRequest = (DistributorItemPrimarySearchRequest) abstractPageRequest;
        // 主搜文档对象
        searchRequest.setDoc(itemDoc);
        // 主搜分页参数限制
        primaryRequest.setPageNo(primaryRequest.getPageNo());
        primaryRequest.setPageSize(primaryRequest.getPageSize());
        searchRequest.setFrom((primaryRequest.getPageNo() - 1) * primaryRequest.getPageSize());
        searchRequest.setSize(primaryRequest.getPageSize());
        // 主搜搜索条件
        BoolQuery boolQuery = (BoolQuery) searchRequest.getQuery();
        handle(primaryRequest, searchRequest, boolQuery);
    }

    protected void handle(DistributorItemPrimarySearchRequest primaryRequest, SearchRequest searchRequest, BoolQuery boolQuery) {
        handleStatus(boolQuery);
        handleKeyword(primaryRequest, boolQuery);
        handleBrand(primaryRequest, boolQuery);
        handleCategory(primaryRequest, boolQuery);
        handleActivity(primaryRequest, searchRequest, boolQuery);
        handleSort(primaryRequest, searchRequest ,boolQuery);
    }

    protected void handleStatus(BoolQuery boolQuery) {
        // 主搜默认限定上架状态的商品 正在售卖
        boolQuery.filter(QueryBuilder.termsQuery().field("status").value(ItemStatus.ON_SHELF.getValue()));
        boolQuery.filter(QueryBuilder.termsQuery().field("saleStatus").value(1));
        // 主搜索未删除商品
        boolQuery.filter(QueryBuilder.termsQuery().field("stateDeleted").value(BigDecimal.ZERO));
    }

    protected void handleKeyword(DistributorItemPrimarySearchRequest primaryRequest, BoolQuery boolQuery) {
        if (!StringUtils.isEmpty(primaryRequest.getKeyword())) {
            boolQuery.must(QueryBuilder.multiMatchQuery().type("best_fields").fields(properties.getMultiMatchFieldMap()).minimumShouldMatch("80%").query(primaryRequest.getKeyword()));
        }
        if (!StringUtils.isEmpty(primaryRequest.getSubKeyword())) {
            boolQuery.must(QueryBuilder.multiMatchQuery().type("best_fields").fields(properties.getMultiMatchFieldMap()).minimumShouldMatch("80%").query(primaryRequest.getSubKeyword()));
        }
    }

    protected void handleBrand(DistributorItemPrimarySearchRequest primaryRequest,  BoolQuery boolQuery) {
        if (!StringUtils.isEmpty(primaryRequest.getBrandIds())) {
            List<Long> brandIds = Splitters.UNDERSCORE.splitToList(primaryRequest.getBrandIds()).stream().map(Long::valueOf).collect(Collectors.toList());
            boolQuery.filter(QueryBuilder.termsQuery().field("brandId").values(brandIds));
            // 已选择品牌处理
            Map<Long, BrandInfo> brandMap = componentAdaptor.getBrandMap(new HashSet<>(brandIds), primaryRequest.getTenantId());
            for (Long id : brandIds) {
                BrandInfo model = brandMap.get(id);
                if (null != model) {
                    SearchContext.addChosen((new Chosen(Constant.CHOSE_TYPE_BRAND, id, model.getName())));
                }
            }
        }
    }

    protected void handleCategory(DistributorItemPrimarySearchRequest primaryRequest, BoolQuery boolQuery) {
        Set<Long> front2backCatIdSet = new HashSet<>();
        if (null != primaryRequest.getFrontCategoryId()) {
            OperatorCategoryOfSearchInfo info = componentAdaptor.getOperatorCategory(primaryRequest.getFrontCategoryId(), primaryRequest.getTenantId(), primaryRequest.getDistributorId(), 1);
            if (null != info.getBackCategoryIds() && null!=info.getAncestors()) {
                if (!CollectionUtils.isEmpty(info.getBackCategoryIds())) {
                    front2backCatIdSet.addAll(info.getBackCategoryIds());
                }
                List<OperatorCategoryInfo> frontCategoryModels = info.getAncestors();
                if (!CollectionUtils.isEmpty(frontCategoryModels)) {
                    OperatorCategoryInfo current = frontCategoryModels.get(frontCategoryModels.size() - 1);
                    // 已选择前台类目处理
                    SearchContext.addChosen(new Chosen(Constant.CHOSE_TYPE_FRONT_CATEGORY, current.getId(), current.getName()));
                    // 面包屑处理
                    List<IdAndName> crumbList = new ArrayList<>(frontCategoryModels.size());
                    for (OperatorCategoryInfo model : frontCategoryModels) {
                        crumbList.add(new IdAndName(model.getId(), model.getName()));
                    }
                    SearchContext.setBreadCrumbs(crumbList);
                }
            }else {
                front2backCatIdSet.add(-1L);
            }
        }

        if(StringUtils.hasText(primaryRequest.getStCategoryId())){
            List<Long> cateIds = componentAdaptor.getStCategory(primaryRequest.getStCategoryId());
            if(CollectionUtil.isNotEmpty(cateIds)){
                front2backCatIdSet.addAll(cateIds);
            }else{
                front2backCatIdSet.add(-1L);
            }
        }

        Set<Long> backCatIdSet = new HashSet<>();
        if (StringUtils.hasText(primaryRequest.getBackCategoryIds())) {
            List<Long> backCatIdList = Splitters.UNDERSCORE.splitToList(primaryRequest.getBackCategoryIds()).stream().map(Long::valueOf).collect(Collectors.toList());
            backCatIdSet.addAll(backCatIdList);
            // 已选择后台类目处理
            Map<Long, String> categoryNameMap = componentAdaptor.getCategoryNameMap(backCatIdSet, primaryRequest.getTenantId());
            for (Long id : backCatIdList) {
                String name = categoryNameMap.get(id);
                if (null != name) {
                    SearchContext.addChosen(new Chosen(Constant.CHOSE_TYPE_BACK_CATEGORY, id, name));
                }
            }
        }
        log.info("primaryRequest.getFrontCategoryId front2backCatIdSet {} , backCatIdSet {} ", JSON.toJSONString(front2backCatIdSet)
                , JSON.toJSONString(backCatIdSet));
        if (!front2backCatIdSet.isEmpty() && !backCatIdSet.isEmpty()) {
            backCatIdSet.retainAll(front2backCatIdSet);
        } else if (backCatIdSet.isEmpty()) {
            backCatIdSet = front2backCatIdSet;
        }
        if (!backCatIdSet.isEmpty()) {
            boolQuery.filter(QueryBuilder.termsQuery().field("categoryIds").values(backCatIdSet));
        }
    }

    protected void handleActivity(PrimarySearchRequest primaryRequest, SearchRequest searchRequest, BoolQuery boolQuery) {
        if (null == activitySupportApi || null == primaryRequest.getActivityId()) {
            return;
        }
        if (!(primaryRequest instanceof DistributorItemPrimarySearchRequest)) {
            return;
        }
        DistributorItemPrimarySearchRequest areaPrimarySearchRequest = (DistributorItemPrimarySearchRequest) primaryRequest;
        if (CollectionUtils.isEmpty(areaPrimarySearchRequest.getOperatorIds())) {
            return;
        }
        SearchScopeInfo searchScopeInfo = activitySupportApi.queryActivityCondition(primaryRequest.getActivityId(), areaPrimarySearchRequest.getOperatorIds(), primaryRequest.getTenantId());
        if (!searchScopeInfo.isValid()) {
            throw new ServiceException("活动已失效");
        }
        boolean hasActivityId = false;
        for (ScopeInfo scopeInfo : searchScopeInfo.getScopes()) {
            // 限定商品或sku的活动，直接按照活动id搜索
            if ("SKU_ID".equals(scopeInfo.getKey()) || "ITEM_ID".equals(scopeInfo.getKey())) {
                if (!hasActivityId) {
                    boolQuery.filter(QueryBuilder.nestedQuery().path("activityObject").scoreMode(ScoreMode.none).query(
                            QueryBuilder.termQuery().field("activityObject.id").value(primaryRequest.getActivityId())
                    ));
                    hasActivityId = true;
                }
            } else {
                String field = properties.getActivityKeyField(scopeInfo.getKey());
                if (Objects.isNull(field)) {
                    log.error("Unknown search activity key, {}", scopeInfo.getKey());
                } else {
                    if (!CollectionUtils.isEmpty(scopeInfo.getIncludes())) {
                        boolQuery.filter(QueryBuilder.termsQuery().field(field).values(scopeInfo.getIncludes()));
                    }
                    if (!CollectionUtils.isEmpty(scopeInfo.getExcludes())) {
                        boolQuery.mustNot(QueryBuilder.termsQuery().field(field).values(scopeInfo.getExcludes()));
                    }
                }
            }
        }
    }

    protected void handleSort(DistributorItemPrimarySearchRequest primaryRequest, SearchRequest searchRequest, BoolQuery boolQuery) {
        // 排序
        DistributorItemSortEnum sort = DistributorItemSortEnum.fromValue(primaryRequest.getSort());
        if (null != sort) {
            switch (sort) {
                case NEW_ITEM_ASC:
                    searchRequest.sort("updatedAt", Order.ASC);
                    break;
                case NEW_ITEM_DESC:
                    searchRequest.sort("updatedAt", Order.DESC);
                    break;
                case DISTRIBUTOR_PRICE_ASC:
                    searchRequest.sort("lowPrice", Order.ASC);
                    break;
                case DISTRIBUTOR_PRICE_DESC:
                    searchRequest.sort("lowPrice", Order.DESC);
                    break;
                case SALE_QUANTITY_ASC:
                    searchRequest.sort("saleQuantity", Order.ASC);
                    break;
                case SALE_QUANTITY_DESC:
                    searchRequest.sort("saleQuantity", Order.DESC);
                    break;
            }
        }else{
            log.info("商品库商品进入综合排序。。。");
            List<Sort> sortList = new ArrayList<>();
            Sort sort1 = new Sort();
            sort1.field("_score");
            sort1.order(Order.DESC);
            sortList.add(sort1);

            Sort sort3 = new Sort();
            sort3.field("updatedAt");
            sort3.order(Order.DESC);
            sortList.add(sort3);
            searchRequest.setSort(sortList);
            log.info("渠道商品进入综合排序-排序顺序："+searchRequest.getSort().toString());
        }
    }


    /**
     * 查询结果处理
     *
     * @param result
     * @return
     */
    public Paging<ItemDoc> parseSearchResult(SearchResult<ItemDoc> result) {
        if (result.getDocuments().isEmpty()) {
            return Paging.empty();
        }
        List<Document<ItemDoc>> documents = result.getDocuments().getData();
        List<ItemDoc> itemList = new ArrayList<>(documents.size());
        for (Document<ItemDoc> doc : documents) {
            itemList.add(doc.getSource());
        }
        return new Paging<>(result.getDocuments().getTotal(), itemList);
    }

    /**
     * 查询结果转换
     *
     * @param paging
     * @return
     */
    public SearchedItemWithAggs<DistributorFrontItemDTO> convertToSearchResult(Paging<ItemDoc> paging,PrimarySearchRequest searchRequest) {
        SearchedItemWithAggs<DistributorFrontItemDTO> result = new SearchedItemWithAggs<>();
        result.setEntities(convert(paging, searchRequest));
        result.setBrands(SearchContext.getBrand());
        result.setAttributes(SearchContext.getAttribute());
        result.setFrontCategories(SearchContext.getCategory());
        result.setBreadCrumbs(SearchContext.getBreadCrumbs());
        result.setChosen(SearchContext.getChosen());
        if (!paging.isEmpty()) {
            result.setShop(SearchContext.getBanner());
        }
        return result;
    }


    public Paging<DistributorFrontItemDTO> convert(Paging<ItemDoc> paging, PrimarySearchRequest searchRequest) {
        if (paging.isEmpty()) {
            return Paging.empty();
        }
        List<ItemDoc> documents = paging.getData();
        List<DistributorFrontItemDTO> itemList = new ArrayList<>(documents.size());
        for (ItemDoc doc : documents) {
            itemList.add(convertToThinItem(doc, searchRequest));
        }
        return new Paging<>(paging.getTotal(), itemList);
    }

    protected DistributorFrontItemDTO convertToThinItem(ItemDoc item, PrimarySearchRequest searchRequest) {
        log.info("----------------BasePrimarySearchHandler----convertToThinItem:" + JSON.toJSONString(item));
        DistributorFrontItemDTO thinItem = new DistributorFrontItemDTO();
        thinItem.setId(item.getId());
        thinItem.setChoiceLotLibId(item.getChoiceLotLibId());
        thinItem.setChoiceLotLibName(item.getChoiceLotLibName());
        thinItem.setOperatorId(item.getOperatorId());
        thinItem.setOperatorName(item.getOperatorName());
        thinItem.setItemId(item.getItemId());
        thinItem.setName(item.getName());
        thinItem.setItemCode(item.getItemCode());
        thinItem.setBrandId(item.getBrandId());
        thinItem.setBrandName(item.getBrandName());
        thinItem.setCategoryId(item.getCategoryId());
        thinItem.setType(item.getType());
        thinItem.setBusinessType(item.getBusinessType());
        thinItem.setDistributorIds(item.getDistributorIds());
        thinItem.setBitTag(item.getBitTag());
        thinItem.setHqOperatorStatus(item.getHqOperatorStatus());
        thinItem.setVendorStatus(item.getVendorStatus());
        thinItem.setAreaOperatorStatus(item.getAreaOperatorStatus());
        thinItem.setStatus(item.getStatus());
        thinItem.setMainImage(item.getMainImage());
        thinItem.setMinQuantity(item.getSaleQuantity());
        thinItem.setLowPrice(item.getLowPrice());
        thinItem.setHighPrice(item.getHighPrice());
        thinItem.setLowOriginalPrice(item.getLowOriginalPrice());
        thinItem.setHighOriginalPrice(item.getHighOriginalPrice());
        thinItem.setInStock(item.getInStock());
        thinItem.setInStock(item.getInStock());
        thinItem.setLogisticsMode(item.getLogisticsMode());
        thinItem.setSupportReturn(item.getSupportReturn());
        thinItem.setVendorId(item.getVendorId());
        thinItem.setVendorName(item.getVendorName());
        thinItem.setSaleQuantity(item.getSaleQuantity());
        if (!CollectionUtils.isEmpty(item.getActivities())) {
            List<ActivityDO> activities = item.getActivities().stream()
                    .sorted(Comparator.comparing(ActivityDO::getCreatedAt).reversed())
                    .collect(Collectors.toList());
            thinItem.setActivities(activities.stream().map(activity -> {
                Activity act = new Activity();
                act.setCode(activity.getCode());
                act.setTag(activity.getTag());
                return act;
            }).collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(item.getDistributorIds()) && item.getDistributorIds().contains(searchRequest.getDistributorId())){
            thinItem.setInMyLibrary(Boolean.TRUE);
        }
        thinItem.setSourceType(item.getSourceType());
        thinItem.setSourceOperatorId(item.getSourceOperatorId());
        thinItem.setSourceOperatorName(item.getSourceOperatorName());

        //后端设置售价可以不限制比建议零售价大  这里判断如果售价大于零售价  零售价赋值为售价
        if(thinItem.getLowPrice() != null && thinItem.getLowOriginalPrice() != null
                && thinItem.getLowPrice() > thinItem.getLowOriginalPrice()){
            thinItem.setLowOriginalPrice(thinItem.getLowPrice());
        }
        if(thinItem.getHighPrice() != null && thinItem.getHighOriginalPrice() != null
                && thinItem.getHighPrice() > thinItem.getHighOriginalPrice()){
            thinItem.setHighOriginalPrice(thinItem.getHighPrice());
        }
        return thinItem;
    }
}
