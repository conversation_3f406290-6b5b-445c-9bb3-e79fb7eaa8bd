package io.terminus.parana.item.footprint.api.converter;

import io.terminus.parana.item.footprint.api.bean.dto.FootPrintDTO;
import io.terminus.parana.item.footprint.model.Footprint;
import org.mapstruct.Mapper;

/**
 * dto到domain的转换
 *
 * <AUTHOR> xlt
 * @since : 2018-5-4
 */
@Mapper(componentModel = "spring")
public interface FootprintConverter {

    FootPrintDTO domain2dto(Footprint footprint);

    Footprint dto2domain(FootPrintDTO footPrintDTO);

}
