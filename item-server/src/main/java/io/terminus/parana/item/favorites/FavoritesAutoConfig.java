package io.terminus.parana.item.favorites;

import io.terminus.parana.item.favorites.enums.FavoritesType;
import io.terminus.parana.item.favorites.manager.base.FavoritesTypeManager;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * <AUTHOR>
 */
@Configuration
@AutoConfigureOrder(Ordered.LOWEST_PRECEDENCE)
public class FavoritesAutoConfig {

    /**
     * 指定收藏的类型
     * 目前支持商品、店铺、品牌
     */
    @Bean
    @ConditionalOnMissingBean
    public FavoritesTypeManager favoritesTypeManager() {
        return new FavoritesTypeManager.Builder()
                .addSupportType(FavoritesType.ITEM.getKey(), FavoritesType.ITEM.getType())
                .addSupportType(FavoritesType.SHOP.getKey(), FavoritesType.SHOP.getType())
                .addSupportType(FavoritesType.BRAND.getKey(), FavoritesType.BRAND.getType())
                .build();
    }

}
