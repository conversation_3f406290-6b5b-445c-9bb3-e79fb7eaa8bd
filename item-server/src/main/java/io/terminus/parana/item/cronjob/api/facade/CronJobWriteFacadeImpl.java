package io.terminus.parana.item.cronjob.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.common.converter.GeneralConverter;
import io.terminus.parana.item.cronjob.api.bean.request.*;
import io.terminus.parana.item.cronjob.api.bean.request.param.CronJobParam;
import io.terminus.parana.item.cronjob.api.convert.CronJobApiConverter;
import io.terminus.parana.item.cronjob.enums.CronJobStatus;
import io.terminus.parana.item.cronjob.model.CronJob;
import io.terminus.parana.item.cronjob.service.CronJobWriteDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class CronJobWriteFacadeImpl implements CronJobWriteFacade {

    @Autowired
    private CronJobWriteDomainService cronJobWriteDomainService;
    @Autowired
    private CronJobApiConverter cronJobApiConverter;

    @Override
    public Response<Boolean> create(CronJobCreateRequest request) {
        try {
            CronJob cronJob = new CronJob();
            cronJob.setTenantId(request.getTenantId());
            cronJob.setTargetCode(request.getTargetCode());
            cronJob.setExecuteJson(request.getExecuteJson());
            cronJob.setType(request.getType());
            cronJob.setStatus(CronJobStatus.WAITING_FOR_EXECUTION);
            cronJob.setExecuteAt(request.getExecuteAt());
            return Response.ok(cronJobWriteDomainService.create(cronJob));
        } catch (Exception e) {
            log.error("cron job create failed, param:{}, cause:{}", request, e);
            return Response.fail("cron.job.create.failed", "定时任务创建失败");
        }
    }

    @Override
    public Response<Boolean> batchCreate(CronJobBatchCreateRequest request) {
        try {
            List<CronJobParam> cronJobParamList = request.getCronJobParamList();
            if (CollectionUtils.isEmpty(cronJobParamList)) {
                return Response.ok(Boolean.TRUE);
            }
            cronJobWriteDomainService.batchCreate(request.getTenantId(), GeneralConverter.batchConvert(cronJobParamList, cronJobApiConverter::param2domain));
            return Response.ok(true);
        } catch (Exception e) {
            log.error("cron job batch create failed, param:{}, cause:{}", request, e);
            return Response.fail("cron.job.batch.create.failed", "定时任务创建失败");
        }
    }

    @Override
    public Response<Boolean> updateStatusAndLog(CronJobUpdateRequest request) {
        try {
            Integer tenantId = request.getTenantId();
            CronJobParam cronJobParam = request.getCronJobParam();
            cronJobParam.setTenantId(tenantId);
            return Response.ok(cronJobWriteDomainService.update(cronJobApiConverter.param2domain(cronJobParam)));
        } catch (Exception e) {
            log.error("cron job update failed, param:{}, cause:{}", request, e);
            return Response.fail("cron.job.update.failed", "定时任务更新失败");
        }
    }

    @Override
    public Response<Boolean> batchUpdateStatusAndLog(CronJobBatchUpdateRequest request) {
        try {
            List<CronJobParam> cronJobParamList = request.getCronJobParamList();
            cronJobWriteDomainService.batchUpdate(request.getTenantId(),
                    GeneralConverter.batchConvert(cronJobParamList, cronJobApiConverter::param2domain));
            return Response.ok(true);
        } catch (Exception e) {
            log.error("cron job batch update failed, param:{}, cause:{}", request, e);
            return Response.fail("cron.job.batch.update.failed", "定时任务更新失败");
        }
    }

    @Override
    public Response<Boolean> batchAbandon(CronJobBatchAbandonRequest request) {
        try {
            this.cronJobWriteDomainService.batchAbandon(request.getTenantId(), request.getTargetCodeList());
            return Response.ok(Boolean.FALSE);
        } catch (Exception var3) {
            log.error("cron job batch abandon failed, param:{}, cause:{}", request, var3);
            return Response.fail("cron.job.batch.abandon.failed", "定时任务废弃失败");
        }
    }


}
