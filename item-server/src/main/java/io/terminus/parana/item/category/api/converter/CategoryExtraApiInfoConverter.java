package io.terminus.parana.item.category.api.converter;

import io.terminus.parana.item.category.api.bean.response.CategoryExtraInfo;
import io.terminus.parana.item.category.model.CategoryExtra;
import io.terminus.parana.item.common.converter.BaseConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-04-02
 */
@Mapper
public interface CategoryExtraApiInfoConverter extends BaseConverter {

    @Mapping(source = "extraJson", target = "extra")
    CategoryExtraInfo get(CategoryExtra categoryExtra);

    List<CategoryExtraInfo> get(List<CategoryExtra> list);
}
