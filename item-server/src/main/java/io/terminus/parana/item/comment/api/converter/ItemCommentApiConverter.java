package io.terminus.parana.item.comment.api.converter;

import io.terminus.parana.item.comment.api.bean.request.ItemCommentSimplePagingRequest;
import io.terminus.parana.item.comment.api.bean.request.param.ItemCommentParam;
import io.terminus.parana.item.comment.model.Comment;
import io.terminus.parana.item.comment.search.so.ItemCommentSO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-15
 */
@Mapper
public interface ItemCommentApiConverter {

    ItemCommentSO get(ItemCommentSimplePagingRequest request);

    Comment get(ItemCommentParam param);

    List<Comment> get(List<ItemCommentParam> list);
}
