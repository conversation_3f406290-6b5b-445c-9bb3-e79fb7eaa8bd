package io.terminus.parana.item.choicelot.service;

import com.google.common.collect.Maps;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceLotLibQueryRequest;
import io.terminus.parana.item.choicelot.model.ChoiceLotLib;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibDao;
import io.terminus.parana.item.plugin.third.api.trade.api.OrderReadApi;
import io.terminus.parana.trade.buy.api.request.ServiceOrderQueryRequest;
import io.terminus.parana.trade.buy.api.request.param.FirmMealParam;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/3 15:54
 */
@Service
@RequiredArgsConstructor
public class ChoiceLotLibReadDomainService {

    private final ChoiceLotLibDao choiceLotLibDao;

    private final OrderReadApi orderReadApi;

    public ChoiceLotLib getById(Long id, Long operatorId){
        return choiceLotLibDao.getById(id, operatorId);
    }

    public List<ChoiceLotLib> getChoiceByOperatorId(Long operatorId){
        ChoiceLotLibQueryRequest request = new ChoiceLotLibQueryRequest();
        request.setOperatorId(operatorId);
        return this.listInfoFilter(request);
    }

    public Paging<ChoiceLotLib> paging(String nameKw, Integer distributorVisible,Long operatorId,Integer type, PageInfo page){
        Map<String, Object> params = Maps.newHashMap();
        params.put("nameKw", nameKw);
        params.put("distributorVisible", distributorVisible);
        params.put("operatorId", operatorId);
        params.put("offset", page.getOffset());
        params.put("limit", page.getLimit());
        params.put("type", type);
        return choiceLotLibDao.paging(params);
    }

    public Paging<ChoiceLotLib> queryAllOperatorAdminChoiceLotLib(Map<String, Object> params){
        return choiceLotLibDao.paging(params);
    }

    public List<ChoiceLotLib> listInfoFilter(ChoiceLotLibQueryRequest request){
        Map<String, Object> params = Maps.newHashMap();
        params.put("nameKw", request.getNameKw());
        params.put("name", request.getName());
        params.put("choiceIds", request.getChoiceIds());
        params.put("choiceNotIds", request.getChoiceNotIds());
        params.put("operatorId", request.getOperatorId());
        params.put("type", request.getType());
        return choiceLotLibDao.listInfoFilter(params);
    }

    public  List<FirmMealParam> queryFirmMealList(Long channelId,  Long projectId, Long operatorId){
        return queryFirmMealList(channelId, projectId, null, operatorId, Boolean.FALSE);
    }

    public  List<FirmMealParam> queryFirmMealList(List<Long> distributorIds,Long operatorId){
        return queryFirmMealList(null, null, distributorIds , operatorId, Boolean.FALSE);
    }

    public  List<FirmMealParam> queryFirmMealList(Long channelId,  Long projectId, List<Long> distributorIds, Long operatorId, Boolean stateDeleted){
        ServiceOrderQueryRequest requestApi = new ServiceOrderQueryRequest();
        requestApi.setChannelId(channelId);
        requestApi.setServiceTypes("1,3");
        requestApi.setOperatorId(operatorId);
        requestApi.setProjectId(projectId);
        requestApi.setChannelIds(distributorIds);
        requestApi.setStateDeleted(stateDeleted);
        return orderReadApi.queryFirmMealList(requestApi);
    }

    public List<ChoiceLotLib> listInfoFilter(Map<String, Object> params){
        return choiceLotLibDao.listInfoFilter(params);
    }
}
