package io.terminus.parana.item.favorites.service;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.Cached;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.category.util.Constant;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.favorites.cache.FavoritesCache;
import io.terminus.parana.item.favorites.enums.FavoritesStatus;
import io.terminus.parana.item.favorites.manager.FavoritesExtensionManager;
import io.terminus.parana.item.favorites.manager.base.FavoritesTypeManager;
import io.terminus.parana.item.favorites.model.Favorites;
import io.terminus.parana.item.favorites.repository.FavoritesDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;

import static io.terminus.parana.item.favorites.util.FavoritesUtils.favoritesIdAssemble;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FavoritesWriteDomainService extends AbsServiceBase {

    private final FavoritesCache favoritesCache;
    private final FavoritesDao favoritesDao;
    private final FavoritesTypeManager favoritesTypeManager;
    private final FavoritesExtensionManager favoritesExtensionManager;

    /**
     * 添加收藏用
     *
     * @param favorites
     * @return
     */
    public Boolean add(Favorites favorites) {
        try {
            favoritesTypeManager.checkFavoritesTypeIsSupported(favorites.getTargetType());

            String uniqueKey = favoritesIdAssemble(
                    favorites.getTenantId(), favorites.getUserId(),
                    favorites.getTargetId(), favorites.getTargetType(), favorites.getOperatorId());
            favorites.setUniqueKey(uniqueKey);

            favoritesExtensionManager.beforeWriteExtension(Lists.newArrayList(favorites));

            favoritesDao.add(favorites);
            // cache invalidate
            favoritesCache.cacheInvalidate(favorites.getUserId(), uniqueKey);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[Favorites]add failed, param:{} cause:{}", favorites, e);
            throw new ServiceException("favorites.add.failed");
        }
    }

    /**
     * 取消收藏用
     */
    public Boolean cancel(Integer tenantId, Long userId, Long targetId, Integer targetType, Long operatorId) {
        try {
            HashMap<String, Object> params = Maps.newHashMap();
            params.put("userId", userId);
            params.put("tenantId", tenantId);
            params.put("targetId", targetId);
            params.put("targetType", targetType);
            params.put("operatorId", operatorId);
            params.put("status", FavoritesStatus.DELETED.getValue());

            favoritesDao.cancel(params);
            // cache invalidate
            favoritesCache.cacheInvalidate(tenantId, userId, targetId, targetType, operatorId);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[Favorites]cancelByTargetInfo failed, userId:{} targetId:{} targetType:{} cause:{}",
                    userId, targetId, targetType, e);
            throw new ServiceException("favorites.cancel.failed");
        }
    }

    public Boolean delete(Integer tenantId, Long targetId, Integer targetType) {
        try {
            HashMap<String, Object> params = Maps.newHashMap();
            params.put("tenantId", tenantId);
            params.put("targetId", targetId);
            params.put("targetType", targetType);
            params.put("status", FavoritesStatus.DELETED.getValue());

            favoritesDao.delete(params);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[Favorites]cancelByTargetInfo failed,  targetId:{} targetType:{} cause:{}",
                    targetId, targetType, e);
            throw new ServiceException("favorites.cancel.failed");
        }
    }

    /**
     * 取消收藏用
     */
    public Boolean batchCancelById(Integer tenantId, Long userId, List<Long> ids) {
        try {
            HashMap<String, Object> params = Maps.newHashMap();
            params.put("userId", userId);
            params.put("tenantId", tenantId);
            params.put("ids", ids);
            params.put("status", FavoritesStatus.DELETED.getValue());

            favoritesDao.batchCancelById(params);

            List<String> uniqueKeys = favoritesDao.getUniqueKeyByIds(ids);
            // cache invalidate
            if (!CollectionUtils.isEmpty(uniqueKeys)) {
                favoritesCache.cacheInvalidate(userId, uniqueKeys);
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[Favorites]batchCancelByTargetInfo failed, userId:{} ids:{} cause:{}",
                    userId, ids, e);
            throw new ServiceException("favorites.cancel.failed");
        }
    }

    /**
     * 取消收藏用
     */
    public Boolean cancelByUserId(Integer tenantId, Long userId, Integer targetType, Long operatorId) {
        try {
            HashMap<String, Object> params = Maps.newHashMap();
            params.put("tenantId", tenantId);
            params.put("userId", userId);
            params.put("targetType", targetType);
            params.put("operatorId", operatorId);
            params.put("status", FavoritesStatus.DELETED.getValue());

            favoritesDao.cancelByUserId(params);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[Favorites]cancelByUserId failed, userId:{} targetType:{} cause:{}",
                    userId, targetType, e);
            throw new ServiceException("favorites.cancel.failed");
        }
    }

    /**
     * 清理收藏用
     */
    public Boolean clearByTargetId(Integer tenantId, Long userId, List<Long> targetIds, Integer targetType) {
        try {
            HashMap<String, Object> params = Maps.newHashMap();
            params.put("tenantId", tenantId);
            params.put("userId", userId);
            params.put("targetIds", targetIds);
            params.put("targetType", targetType);
            params.put("status", FavoritesStatus.DELETED.getValue());

            favoritesDao.clearByTargetId(params);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[Favorites]clearByTargetId failed, userId:{} targetIds:{} targetType:{} cause:{}",
                    userId, targetIds, targetType, e);
            throw new ServiceException("favorites.clear.failed");
        }
    }

    /**
     * 添加收藏用
     *
     * @param favoritesList
     * @return
     */
    public Boolean batchAdd(List<Favorites> favoritesList) {
        try {
            List<String> uniqueKeys = Lists.newArrayList();
            favoritesList.forEach(favorites -> {
                String uniqueKey = favoritesIdAssemble(
                        favorites.getTenantId(), favorites.getUserId(),
                        favorites.getTargetId(), favorites.getTargetType(), favorites.getOperatorId());
                favorites.setUniqueKey(uniqueKey);
                uniqueKeys.add(uniqueKey);
            });
            favoritesDao.batchAdd(favoritesList);
            // cache invalidate
            favoritesCache.cacheInvalidate(favoritesList.get(0).getUserId(), uniqueKeys);
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[Favorites]batchAdd failed, param:{} cause:{}", favoritesList, e);
            throw new ServiceException("favorites.add.failed");
        }
    }
}
