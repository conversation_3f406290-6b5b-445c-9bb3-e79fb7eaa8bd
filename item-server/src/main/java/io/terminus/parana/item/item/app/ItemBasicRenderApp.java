package io.terminus.parana.item.item.app;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.parana.inventory.api.bean.response.info.inventory.InventoryEntityResponseInfo;
import io.terminus.parana.ipm.api.bean.request.inventory.InventoryQueryByEntityAndVendorIdRequest;
import io.terminus.parana.ipm.api.facade.IpmInventoryReadFacade;
import io.terminus.parana.item.area.api.bean.request.AreaSkuQueryBySingleItemIdRequest;
import io.terminus.parana.item.area.api.bean.response.AreaSkuInfo;
import io.terminus.parana.item.area.api.converter.AreaItemApiInfoConverter;
import io.terminus.parana.item.area.api.facade.AreaSkuReadFacade;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.attribute.CategoryAttributeRender;
import io.terminus.parana.item.attribute.api.bean.response.MergedGroupOtherAttributeInfo;
import io.terminus.parana.item.attribute.api.converter.ItemCategoryAttributeApiInfoConverter;
import io.terminus.parana.item.attribute.bo.CategoryAttributeGroupSymbol;
import io.terminus.parana.item.attribute.bo.MergedItemAttributeBO;
import io.terminus.parana.item.attribute.model.CategoryAttributeBinding;
import io.terminus.parana.item.attribute.service.CategoryAttributeReadDomainService;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.common.utils.CollectionObjectUtil;
import io.terminus.parana.item.delivery.api.coverter.DeliveryFeeApiInfoConverter;
import io.terminus.parana.item.delivery.model.DeliveryFeeTemplateDetail;
import io.terminus.parana.item.delivery.service.DeliveryFeeReadDomainService;
import io.terminus.parana.item.item.api.bean.response.item.FullItemEditInfo;
import io.terminus.parana.item.item.api.bean.response.item.FullItemInfo;
import io.terminus.parana.item.item.api.bean.response.item.ItemDetailContentInfo;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.item.api.converter.output.ItemApiInfoConverter;
import io.terminus.parana.item.item.constant.ItemExtraConstantKey;
import io.terminus.parana.item.item.enums.ItemType;
import io.terminus.parana.item.item.model.*;
import io.terminus.parana.item.item.repository.ParanaItemGroupDao;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.ItemUpdateLongReadDomainService;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.plugin.third.api.inventory.api.InventoryReadApi;
import io.terminus.parana.item.plugin.third.impl.tongka.service.util.TongkaItemUtil;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.service.ShopReadDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-12-24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ItemBasicRenderApp {
    private final ItemApiInfoConverter itemApiInfoConverter;
    private final ItemReadDomainService itemReadDomainService;
    private final CategoryAttributeRender categoryAttributeRender;
    private final AreaSkuReadFacade areaSkuReadFacade;
    private final AreaItemReadDomainService areaItemReadDomainService;
    private final CategoryAttributeReadDomainService categoryAttributeReadDomainService;
    private final ItemCategoryAttributeApiInfoConverter itemCategoryAttributeApiInfoConverter;
    private final SkuReadDomainService skuReadDomainService;
    private final ShopReadDomainService shopReadDomainService;
    private final ItemUpdateLongReadDomainService itemUpdateLongReadDomainService;
    private final IpmInventoryReadFacade ipmInventoryReadFacade;
    private final DeliveryFeeReadDomainService deliveryFeeReadDomainService;
    private final DeliveryFeeApiInfoConverter deliveryFeeApiInfoConverter;
    private final AreaItemApiInfoConverter areaItemApiInfoConverter;
    private final ParanaItemGroupDao paranaItemGroupDao;

    public FullItemEditInfo renderSellerEdit(Long operatorId, Long itemId, String dimensionType, String dimensionCode) {
        FullItemBO itemBO = itemReadDomainService.findFullItemById(itemId, RequestContext.getTenantId(), dimensionType, dimensionCode);
        FullItemEditInfo info = new FullItemEditInfo();
        FullItemInfo fullItemInfo = itemApiInfoConverter.get(itemBO);
        Item item = itemBO.getItem();
        Assert.nonNull(item, "item.not.found");
        if (ObjectUtil.isNotNull(operatorId)) {
            AreaItem areaItem = areaItemReadDomainService.findByOperatorIdAndItemId(operatorId, itemId);
            fullItemInfo.getItemInfo().setMainImage(areaItem.getMainImage());
            fullItemInfo.getItemInfo().setDeliveryFeeTempId(areaItem.getDeliveryFeeTempId());
            fullItemInfo.getItemInfo().setName(areaItem.getName());
            fullItemInfo.getItemInfo().setSalesArea(areaItem.getSalesArea());
            fullItemInfo.getItemInfo().setSalesChannel(areaItem.getSalesChannel());
            fullItemInfo.getItemInfo().setStatus(areaItem.getStatus());
            log.info("areaItem.getSalesArea():::::::::::::::::::::::::::" + areaItem.getSalesArea());
            log.info("areaItem.getSalesChannel():::::::::::::::::::::::::::" + areaItem.getSalesChannel());
//            fullItemInfo.setImageList(AssembleDataUtils.list2list(areaItem.getImages(), itemApiInfoConverter::out));

            Assert.nonNull(areaItem, "areaItem.not.found");
            DeliveryFeeTemplateDetail deliveryFeeTemplateDetail = deliveryFeeReadDomainService.findDeliveryFeeTemplateDetailById(areaItem.getDeliveryFeeTempId());
            fullItemInfo.getItemInfo().setDeliveryFeeTemp(deliveryFeeApiInfoConverter.getV2(deliveryFeeTemplateDetail));
            fullItemInfo.getItemInfo().setAreaItem(areaItemApiInfoConverter.domain2info(areaItem));

            //查询中台商品 获取分组信息
            AreaItem centerAreaItem = areaItemReadDomainService.findByOperatorIdAndItemId(1L, itemId);
            if(centerAreaItem != null && centerAreaItem.getItemGroupId() != null){
                fullItemInfo.getItemInfo().setItemGroupId(centerAreaItem.getItemGroupId());
                ParanaItemGroup group = paranaItemGroupDao.findById(centerAreaItem.getItemGroupId());
                if(group != null){
                    fullItemInfo.getItemInfo().setItemGroupName(group.getGroupName());
                    if(group.getLevel() == 2){
                        ParanaItemGroup parentGroup = paranaItemGroupDao.findById(group.getParentId());
                        fullItemInfo.getItemInfo().setParentItemGroupId(parentGroup.getId());
                        fullItemInfo.getItemInfo().setParentItemGroupName(parentGroup.getGroupName());
                        fullItemInfo.getItemInfo().setParentItemGroupImg(parentGroup.getOssImg());
                    }
                }
                fullItemInfo.getItemInfo().setItemGroupAlias(centerAreaItem.getItemGroupAlias());
                fullItemInfo.getItemInfo().setItemGroupOrder(centerAreaItem.getItemGroupOrder());
            }
        }

        log.info("itemId:{}item:{}", itemId, item);

        Map<String, List<CategoryAttributeBinding>> categoryAttributeMap = categoryAttributeReadDomainService.
                renderExcludeRefuse(item.getCategoryId(), false);
        Map<CategoryAttributeGroupSymbol, List<MergedItemAttributeBO>> mergeBO = categoryAttributeRender.merge(categoryAttributeMap,
                item.getOtherAttributes());

        if (fullItemInfo.getItemInfo().getSourceid() == null) {
            fullItemInfo.getItemInfo().setSourceid("");
        }
        if (fullItemInfo.getSkuYLInfo() != null) {
            fullItemInfo.getSkuYLInfo().setVendorName(fullItemInfo.getItemInfo().getShopName());
        }
        fullItemInfo.getItemInfo().setIsCrossBorder(item.getIsCrossBorder());
        ItemInfo itemInfo = fullItemInfo.getItemInfo();
        if (ObjectUtil.isNotNull(operatorId)) {
            Set<Long> itemIds = new HashSet<>();
            itemIds.add(itemInfo.getId());
            List<AreaItem> byVendorIdAndItemIds = areaItemReadDomainService.findByVendorIdAndItemIds(itemInfo.getShopId(), itemIds);
            log.info("区域商品信息:{}", byVendorIdAndItemIds);
            if (!CollectionObjectUtil.isEmpty(byVendorIdAndItemIds)) {
                itemInfo.setStatus(byVendorIdAndItemIds.get(0).getStatus());
            }
        }
        info.setItemInfo(itemInfo);
        if (fullItemInfo.getItemInfo().getSourceid() == null) {
            fullItemInfo.getItemInfo().setSourceid("");
        }

        //查下库存
        sendSkuIpm(fullItemInfo.getSkuInfoList(),itemInfo.getShopId());

        info.setSkuInfoList(fullItemInfo.getSkuInfoList());
        info.setImageList(fullItemInfo.getImageList());
        info.setExtra(fullItemInfo.getExtra());
        info.setSkuYLInfo(fullItemInfo.getSkuYLInfo());
        info.setMergedOtherAttributes(AssembleDataUtils.map2list(mergeBO, this::buildGroupedInfoBO));
        info.setItemDetailInfo(fullItemInfo.getItemDetailInfo());
        if(info.getItemDetailInfo() != null){
            //1.如果只有pc端 没有移动端  pc端的赋值到移动端
            //2.如果只有移动端 没有pc端  移动端的赋值到pc端
            if(CollectionUtil.isNotEmpty(info.getItemDetailInfo().getPcDetail()) && CollectionUtil.isEmpty(info.getItemDetailInfo().getWapDetail())){
                info.getItemDetailInfo().setWapDetail(info.getItemDetailInfo().getPcDetail());
            }
            if(CollectionUtil.isNotEmpty(info.getItemDetailInfo().getWapDetail()) && CollectionUtil.isEmpty(info.getItemDetailInfo().getPcDetail())){
                info.getItemDetailInfo().setPcDetail(info.getItemDetailInfo().getWapDetail());
            }

        }

        // 补充直充商品和卡卷商品信息
        TongkaItemUtil.fillItemInfo(itemInfo);
        return info;
    }

    private void sendSkuIpm(List<SkuInfo> skuInfoList, Long shopId) {
        try {
            Map<Long, List<InventoryEntityResponseInfo>> inventoryMap = getBatchVendorInventory(skuInfoList, shopId);
            if (!CollectionUtils.isEmpty(inventoryMap)) {
                for (SkuInfo skuInfo : skuInfoList) {
                    List<InventoryEntityResponseInfo> inventories = inventoryMap.get(skuInfo.getId());
                    if (!CollectionUtils.isEmpty(inventories)) {
                        Map<String, String> extra = skuInfo.getExtra();
                        extra.put("physicalInventory", String.valueOf(inventories.stream().mapToLong(InventoryEntityResponseInfo::getSellableQuantity).sum()));
                        skuInfo.setExtra(extra);
                    }
                }
            }
        } catch (Exception e) {
            log.error("查下库存失败::{}", Throwables.getStackTraceAsString(e));
            e.printStackTrace();
        }
    }

    public Map<Long, List<InventoryEntityResponseInfo>> getBatchVendorInventory(List<SkuInfo> skuInfos, Long vendorId) {
        Map<Long, List<InventoryEntityResponseInfo>> resultMap = Maps.newHashMap();
        Set<Long> skuIds = skuInfos.stream().map(SkuInfo::getId).collect(Collectors.toSet());
        InventoryQueryByEntityAndVendorIdRequest req = new InventoryQueryByEntityAndVendorIdRequest();
        req.setVendorId(vendorId);
        req.setSkuIdSet(skuIds);
        log.info("getBatchVendorInventory queryByEntityAndVendorId is req {}", JSONUtil.toJsonStr(req));
        List<InventoryEntityResponseInfo> inventoryInfos = Assert.take(ipmInventoryReadFacade.queryByEntityAndVendorId(req));
        log.info("getBatchVendorInventory queryByEntityAndVendorId is resp {}", JSONUtil.toJsonStr(inventoryInfos));
        if (!CollectionUtils.isEmpty(inventoryInfos)) {
            resultMap = inventoryInfos.stream().collect(Collectors.groupingBy(e -> Long.parseLong(e.getEntityId())));
        }
        return resultMap;
    }

    public FullItemEditInfo renderSellerEditOpenApiV2(Long itemId,Long  vendorId, String dimensionType, String dimensionCode) {
        FullItemBO itemBO = itemReadDomainService.findFullItemByIdOpenApiV2(itemId, vendorId, RequestContext.getTenantId(), dimensionType, dimensionCode);
        Item item = itemBO.getItem();
        Assert.nonNull(item, "item.not.found");
        log.info("itemId:{}item:{}", itemId, item);
        //政企修改信息渲染
        Long shopId = item.getShopId();
        Shop shopVO = shopReadDomainService.findById(shopId, item.getTenantId(), null);
        if (null != shopVO && StringUtils.isNotBlank(shopVO.getIsZq()) && StringUtils.equals("Y", shopVO.getIsZq())) {

            if (null != item.getZqPushStatus() && item.isZqRenderUpdateLog()) {
                ItemUpdateLog updateLog = itemUpdateLongReadDomainService.findZqUpdateLogByItemId(item.getId());
                if (null != updateLog) {
                    item.setName(updateLog.getName());
                    item.setUnit(updateLog.getUnit());
                    item.setBrandId(updateLog.getBrandId());
                    item.setBrandName(updateLog.getBrandName());
                    item.setAdvertise(updateLog.getAdvertise());
                    item.setMainImage(updateLog.getMainImage());
                    item.setVideoUrl(updateLog.getVideoUrl());
                    item.setSkuAttributes(updateLog.getSkuAttributes());
                    item.setOtherAttributes(updateLog.getOtherAttributes());
                    item.setExtra(updateLog.getItemExtra());
                    item.setVatrate(updateLog.getVatrate());
                    item.setTaxcode(updateLog.getTaxcode());
                    item.setTaxname(updateLog.getTaxname());
                    item.setZqCustDesc(updateLog.getZqCustDesc());
                    itemBO.setItem(item);

                    ItemDetail itemDetail = itemBO.getItemDetail();
                    if (null != itemDetail) {
                        itemDetail.setImages(updateLog.getImages());
                        if (StringUtils.isNotBlank(updateLog.getPcDetail())) {
                            itemDetail.setPcDetail(updateLog.getPcDetail());
                        }
                        if (StringUtils.isNotBlank(updateLog.getWapDetail())) {
                            itemDetail.setWapDetail(updateLog.getWapDetail());
                        }
                        itemBO.setItemDetail(itemDetail);
                    }

                }

            }
        }

        Map<String, List<CategoryAttributeBinding>> categoryAttributeMap = categoryAttributeReadDomainService.
                renderExcludeRefuse(item.getCategoryId(), false);
        Map<CategoryAttributeGroupSymbol, List<MergedItemAttributeBO>> mergeBO = categoryAttributeRender.merge(categoryAttributeMap,
                item.getOtherAttributes());

        FullItemEditInfo info = new FullItemEditInfo();
        FullItemInfo fullItemInfo = itemApiInfoConverter.get(itemBO);
        if (fullItemInfo.getItemInfo().getSourceid() == null) {
            fullItemInfo.getItemInfo().setSourceid("");
        }
        if (fullItemInfo.getSkuYLInfo() != null) {
            fullItemInfo.getSkuYLInfo().setVendorName(fullItemInfo.getItemInfo().getShopName());
        }
        fullItemInfo.getItemInfo().setIsCrossBorder(item.getIsCrossBorder());
        ItemInfo itemInfo = fullItemInfo.getItemInfo();
        Set<Long> itemIds = new HashSet<>();
        itemIds.add(itemInfo.getId());
        List<AreaItem> byVendorIdAndItemIds = areaItemReadDomainService.findByVendorIdAndItemIds(itemInfo.getShopId(), itemIds);
        log.info("区域商品信息:{}", byVendorIdAndItemIds);
        if (!CollectionObjectUtil.isEmpty(byVendorIdAndItemIds)) {
            itemInfo.setStatus(byVendorIdAndItemIds.get(0).getStatus());
        }
        info.setItemInfo(itemInfo);
        if (fullItemInfo.getItemInfo().getSourceid() == null) {
            fullItemInfo.getItemInfo().setSourceid("");
        }
        info.setSkuInfoList(fullItemInfo.getSkuInfoList());
        info.setImageList(fullItemInfo.getImageList());
        info.setExtra(fullItemInfo.getExtra());
        info.setSkuYLInfo(fullItemInfo.getSkuYLInfo());
        info.setMergedOtherAttributes(AssembleDataUtils.map2list(mergeBO, this::buildGroupedInfoBO));

        return info;
    }

    public List<SkuInfo> skuSpecificationDetails(Long itemId, Long vendorId, String dimensionType, String dimensionCode) {
        List<Sku> skuList = skuReadDomainService.findByItemIdOpenApiV2(itemId, vendorId, 1, dimensionType, dimensionCode);
        return itemApiInfoConverter.getSkuList(skuList);
    }

    public FullItemEditInfo renderOperatorEdit(Long itemId, Long operatorId, String dimensionType, String dimensionCode) {
        FullItemEditInfo fullItemEditInfo = renderSellerEdit(operatorId, itemId, dimensionType, dimensionCode);
        if (fullItemEditInfo == null) {
            return null;
        }

        AreaSkuQueryBySingleItemIdRequest request = new AreaSkuQueryBySingleItemIdRequest();
        request.setItemId(itemId);
        request.setOperatorId(operatorId);
        request.setTenantId(RequestContext.getTenantId());
        List<AreaSkuInfo> areaSkuList = Assert.take(areaSkuReadFacade.queryBySingleItemId(request));
        Map<Long, AreaSkuInfo> areaSkuMap = AssembleDataUtils.list2map(areaSkuList, AreaSkuInfo::getSkuId);
        for (SkuInfo skuInfo : fullItemEditInfo.getSkuInfoList()) {
            AreaSkuInfo areaSku = areaSkuMap.get(skuInfo.getId());
            skuInfo.setAreaSku(areaSku);
        }

        return fullItemEditInfo;
    }

    private MergedGroupOtherAttributeInfo buildGroupedInfoBO(CategoryAttributeGroupSymbol groupSymbol,
                                                             List<MergedItemAttributeBO> boList) {
        return new MergedGroupOtherAttributeInfo(groupSymbol.getName(), groupSymbol.isManaged(),
                itemCategoryAttributeApiInfoConverter.getAttribute(boList));
    }
}
