package io.terminus.parana.item.choicelot.repository;

import com.google.common.collect.ImmutableMap;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibDistributor;
import io.terminus.parana.item.choicelot.model.bo.ChoiceLotDistributorTotalBO;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ChoiceLotDistributorDao
 * @projectName item-center
 * @description: 选品库-选品加价率数据操作 TODO
 * @date 2022/3/3 15:15
 */
@Repository
public class ChoiceLotLibDistributorDao extends AbstractMybatisDao<ChoiceLotLibDistributor> {

    /**
     * 根据选品库ID获取选品库渠道商关系
     * @param params
     * @return
     */
    public List<ChoiceLotLibDistributor> getDistributorByChoiceLotId(Map<String, Object> params){
        return getSqlSession().selectList(sqlId("getDistributorByChoiceLotId"), params);
    }

    /**
     * 根据选品库Id删除选品库渠道商关系
     * @param param
     * @return
     */
    public Boolean  deleteByLotId(Map<String, Object> param){
        int delete = getSqlSession().update(this.sqlId("deleteByLotId"), param);
        return delete > 0;
    }

    /**
     * 根据选品库Ids删除选品库渠道商关系
     * @return
     */
    public Boolean  deletesByChoiceLotLibId(List<Long> removeList, Long choiceLotLibId){
        int delete = getSqlSession().update(this.sqlId("deletesByChoiceLotLibId"), ImmutableMap.of("removeList", removeList, "choiceLotLibId", choiceLotLibId));
        return delete > 0;
    }

    /**
     * 根据选品库Id获取绑定的渠道商数量
     * @param params
     * @return
     */
    public List<ChoiceLotDistributorTotalBO> getStatisticalDistributorCount(Map<String, Object> params){
        return getSqlSession().selectList(sqlId("getStatisticalDistributorCount"), params);
    }
}
