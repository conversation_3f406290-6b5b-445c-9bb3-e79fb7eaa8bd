package io.terminus.parana.item.favorites.api.facade;

import cn.hutool.core.bean.BeanUtil;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.favorites.api.bean.request.CommonItemListQueryPageRequest;
import io.terminus.parana.item.favorites.api.bean.response.UserCommonItemListInfo;
import io.terminus.parana.item.favorites.api.converter.UserCommonItemListConverter;
import io.terminus.parana.item.favorites.model.UserCommonItemList;
import io.terminus.parana.item.favorites.service.UserCommonItemListReadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserCommonItemListReadFacadeImpl implements UserCommonItemListReadFacade {

    private final UserCommonItemListReadService userCommonItemListReadService;
    private final UserCommonItemListConverter userCommonItemListConverter;

    @Override
    public Paging<UserCommonItemListInfo> queryCommonItemList(CommonItemListQueryPageRequest request) {
        Date createdAt = null;
        if (request.getCreatedAt() != null) {
            createdAt = new Date(request.getCreatedAt());
        }
        Paging<UserCommonItemList> userCommonItemList = userCommonItemListReadService.queryCommonItemListPaging(request, createdAt);
        Paging<UserCommonItemListInfo> paging=new Paging<>();
        BeanUtil.copyProperties(userCommonItemList,paging);
        paging.setData(AssembleDataUtils.list2list(userCommonItemList.getData(), userCommonItemListConverter::domain2info));
        return paging;
    }

}
