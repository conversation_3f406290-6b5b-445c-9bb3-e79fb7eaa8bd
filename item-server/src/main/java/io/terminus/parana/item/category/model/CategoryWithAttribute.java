package io.terminus.parana.item.category.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @program: category-center
 * @description:
 * @author: heshaoyi
 * @create: 2018-10-29 21:45
 **/
@Data
public class CategoryWithAttribute implements Serializable{
    private static final long serialVersionUID = 8008878414986590947L;
    /**
     * id
     */
    private Long id;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 属性id
     */
    private Long attributeId;
    /**
     * 属性分组
     */
    private String group;
    /**
     * 顺序编号
     */
    private Long index;
    /**
     * 状态,1启用,-1禁用
     */
    private Integer status;
    /**
     * createdAt
     */
    private Date createdAt;
    /**
     * updatedAt
     */
    private Date updatedAt;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 属性实体
     */
    private PublicAttribute attribute;
}
