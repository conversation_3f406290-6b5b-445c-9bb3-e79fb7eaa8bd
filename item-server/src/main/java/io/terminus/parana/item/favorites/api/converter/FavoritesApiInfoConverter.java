package io.terminus.parana.item.favorites.api.converter;

import io.terminus.parana.item.favorites.api.bean.response.FavoritesInfo;
import io.terminus.parana.item.favorites.model.Favorites;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * dto到domain的转换
 *
 * <AUTHOR> xlt
 * @since : 2018-12-17
 */
@Mapper(componentModel = "spring")
public interface FavoritesApiInfoConverter {

    FavoritesInfo domain2info(Favorites favorite);

}
