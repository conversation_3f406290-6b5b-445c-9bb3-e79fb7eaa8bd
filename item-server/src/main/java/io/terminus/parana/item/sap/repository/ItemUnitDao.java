package io.terminus.parana.item.sap.repository;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.sap.model.ItemUnit;
import io.terminus.parana.item.sap.model.SapSkuS;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;


@Repository
public class ItemUnitDao extends MyBatisDao<ItemUnit> {


    public ItemUnit findById(Long id, Integer tenantId) {
        return getSqlSession().selectOne(sqlId("findById"), ImmutableMap.of(
                "id", id,
                "tenantId", tenantId)
        );
    }

    public List<ItemUnit> findByItemId(Long itemId, Integer tenantId) {
        return getSqlSession().selectList(sqlId("findByItemId"),
                ImmutableMap.of(
                        "itemId", itemId,
                        "tenantId", tenantId)
        );
    }

    public List<ItemUnit> findByItemIdAndName(Long itemId,Set<String> nameSet,Integer tenantId) {
        return getSqlSession().selectList(sqlId("findByItemIdAndName"),
                ImmutableMap.of(
                        "itemId", itemId,
                        "nameSet", nameSet,
                        "tenantId", tenantId)
        );
    }

    public List<ItemUnit> findByItemIdsIn(Set<Long> itemIds, Integer tenantId) {

        int MAXSIZE = 500;

        List<ItemUnit> result = new ArrayList<>();

        //小于等于500条
        if(itemIds.size() <= MAXSIZE){

            List<ItemUnit> queryRes = getSqlSession().selectList(sqlId("findByItemIdsIn"),
                    ImmutableMap.of(
                            "itemIds", itemIds,
                            "tenantId", tenantId)
            );

            result.addAll(queryRes);
        }else {
            //大于500条
            List<Long> pnoList = new ArrayList<Long>(itemIds);

            int s = pnoList.size() / MAXSIZE;

            for (int i = 0; i < s; i++) {
                List ll = pnoList.subList((i * MAXSIZE), i * MAXSIZE + MAXSIZE);

                List<ItemUnit> queryRes = getSqlSession().selectList(sqlId("findByItemIdsIn"),
                        ImmutableMap.of(
                                "itemIds", ll,
                                "tenantId", tenantId)
                );
                result.addAll(queryRes);
            }

            List ll = pnoList.subList((s * MAXSIZE), pnoList.size());

            //循环后还有结余
            if (ll.size() > 0) {

                List<ItemUnit> queryRes = getSqlSession().selectList(sqlId("findByItemIdsIn"),
                        ImmutableMap.of(
                                "itemIds", ll,
                                "tenantId", tenantId)
                );
                result.addAll(queryRes);
            }
        }
        //返回整体结果
        return result;
    }

    public boolean updateStatus(Long id, Integer status, Integer tenantId, String updatedBy) {
        return sqlSession.update(sqlId("updateStatus"), ImmutableMap.of(
                "id", id,
                "status", status,
                "tenantId", tenantId,
                "updatedBy", updatedBy)
        ) == 1;
    }


    public Integer updateBatchStatus(Set<Long> ids, Integer status, Integer tenantId, String updatedBy) {
        return sqlSession.update(sqlId("updateBatchStatus"), ImmutableMap.of(
                "ids", ids,
                "status", status,
                "tenantId", tenantId,
                "updatedBy", updatedBy)
        ) ;
    }


    public ItemUnit findByItemIdAndMatnr(Long itemId,String matnr,Integer tenantId) {
        return getSqlSession().selectOne(sqlId("findByItemIdAndMatnr"),
                ImmutableMap.of(
                        "itemId", itemId,
                        "matnr", matnr,
                        "tenantId", tenantId)
        );
    }

}
