package io.terminus.parana.item.export.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.area.manager.AreaItemAuditManager;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.model.ItemAudit;
import io.terminus.parana.item.area.repository.ItemAuditDao;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.choicelot.model.ChoiceLotLib;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibItemModel;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibItemReadService;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibReadDomainService;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibSkuReadService;
import io.terminus.parana.item.choicelot.utils.MarkupCalculateUtils;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.item.enums.ItemAuditStatusEnum;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.ItemPriceHistory;
import io.terminus.parana.item.item.model.ParanaItemPriceHistoryMainModel;
import io.terminus.parana.item.item.service.ItemPriceHistoryReadService;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.shop.service.ShopReadDomainService;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageBean;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageCreateRequest;
import io.terminus.parana.misc.itemorder.api.api.facade.ParanaThirdMessageWriteFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ItemPriceUpdateManager {

    @Autowired
    private ItemAuditDao itemAuditDao;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ShopReadDomainService shopReadDomainService;

    @Autowired
    private ItemReadDomainService itemReadDomainService;

    @Autowired
    private ChoiceLotLibSkuReadService choiceLotLibSkuReadService;

    @Autowired
    private ChoiceLotLibItemReadService choiceLotLibItemReadService;

    @Autowired
    private AreaItemAuditManager areaItemAuditManager;

    @Autowired
    private AreaSkuReadDomainService areaSkuReadDomainService;

    @Autowired
    private ParanaThirdMessageWriteFacade paranaThirdMessageWriteFacade;

    @Autowired
    private ChoiceLotLibReadDomainService choiceLotLibReadDomainService;

    @Autowired
    private AreaItemReadDomainService areaItemReadDomainService;

    @Autowired
    private ItemPriceHistoryReadService itemPriceHistoryReadService;

    public void execute() {
        log.info("商品价格变更JOB  execute()方法开始");
        Date now = new Date();
        //获取所有待生效状态的价格审核单
        List<ItemAudit> itemAudits = itemAuditDao.queryAllUpdatePrice();
        if (CollectionUtil.isEmpty(itemAudits)) {
            log.info("商品价格变更JOB  execute()方法结束，没有待生效的价格审核单");
            return;
        }
        List<Long> itemAuditIds = itemAudits.stream().map(ItemAudit::getId).collect(Collectors.toList());
        Set<Long> itemIdSet = itemAudits.stream().map(ItemAudit::getItemId).collect(Collectors.toSet());
        Set<Long> skuIds = itemAudits.stream().map(ItemAudit::getSkuId).collect(Collectors.toSet());
        List<Item> itemList = itemReadDomainService.findByIdSet(itemIdSet, 1, null, null);
        HashMap<String, Object> param1 = Maps.newHashMap();
        param1.put("auditIds", itemAuditIds);
        List<ItemPriceHistory> itemPriceHistoryList = itemPriceHistoryReadService.listByMap(param1);
        Map<Long, List<ItemPriceHistory>> itemPriceHistoryMap = itemPriceHistoryList.stream().collect(Collectors.groupingBy(ItemPriceHistory::getAuditId));
        List<ParanaItemPriceHistoryMainModel> paranaItemPriceHistoryMainModels = Lists.newArrayList();
        List<ItemPriceHistory> itemPriceHistoryArrayList = Lists.newArrayList();
        List<ChoiceLotLibSkuModel> choiceLotLibSkuUpdateList = Lists.newArrayList();
        List<AreaSku> areaSkuUpdateList = Lists.newArrayList();
        List<AreaItem> areaItemUpdateList = Lists.newArrayList();
        List<ParanaThirdMessageCreateRequest> thirdMessageCreateRequestList = Lists.newArrayList();
        List<ItemPriceHistory> itemPriceHistoryUpdateList = Lists.newArrayList();
        //待批量更新的审核记录
        List<ItemAudit> toUpdateAudits = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(itemAudits)) {
            //查询所有运营商
            List<AreaSku> allOperatorAreaSku = areaSkuReadDomainService.findByOperatorIdAndSkuIds(null, skuIds);
            Map<String, List<AreaSku>> areaSkuMap = allOperatorAreaSku.stream().collect(Collectors.groupingBy(e -> e.getOperatorId() + "-" + e.getSkuId()));
            Map<String, AreaItem> areaItemMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(allOperatorAreaSku)) {
                Set<Long> operatorSet = allOperatorAreaSku.stream().map(AreaSku::getOperatorId).collect(Collectors.toSet());
                List<AreaItem> areaItems = areaItemReadDomainService.queryByOperatorAndItemIds(new ArrayList<>(itemIdSet), operatorSet);
                areaItemMap = areaItems.stream().collect(Collectors.toMap(e -> e.getOperatorId() + "-" + e.getItemId(), Function.identity(), (e1, e2) -> e1));
            }
            //查询选品库商品
            Map<String, Object> param = new HashMap<>();
            param.put("skuIds", skuIds);
            List<ChoiceLotLibSkuModel> choiceLotLibSkuBySkuIds = choiceLotLibSkuReadService.findChoiceLotLibSkuBySkuIds(param);
            Map<String, ChoiceLotLibSkuModel> lotLibSkuModelMap = choiceLotLibSkuBySkuIds.stream().collect(Collectors.toMap(e -> e.getOperatorId() + "-" + e.getChoiceLotLibId() + "-" + e.getSkuId(), Function.identity(), (e1, e2) -> e1));
            List<ChoiceLotLibItemModel> choiceLotLibItemModels = new ArrayList<>();
            List<ChoiceLotLib> choiceLotLibs = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(choiceLotLibSkuBySkuIds)) {
                List<Long> choiceLotLibIdList = choiceLotLibSkuBySkuIds.stream().map(ChoiceLotLibSkuModel::getChoiceLotLibId).collect(Collectors.toList());
                choiceLotLibItemModels = choiceLotLibItemReadService.findChoiceItemByChoiceIdsAndItemIds(new ArrayList<>(itemIdSet), choiceLotLibIdList);
                HashMap<String, Object> map = Maps.newHashMap();
                map.put("choiceIds", choiceLotLibIdList);
                choiceLotLibs = choiceLotLibReadDomainService.listInfoFilter(map);

            }
            Map<Long, List<ChoiceLotLibSkuModel>> choiceLotLibSkuMap = choiceLotLibSkuBySkuIds.stream().collect(Collectors.groupingBy(ChoiceLotLibSkuModel::getSkuId));
            Map<Long, List<ChoiceLotLibItemModel>> choiceLotLibItemModelMap = choiceLotLibItemModels.stream().collect(Collectors.groupingBy(ChoiceLotLibItemModel::getItemId));
            Map<Long, ChoiceLotLib> choiceLotLibMap = choiceLotLibs.stream().collect(Collectors.toMap(ChoiceLotLib::getId, Function.identity(), (e1, e2) -> e1));
            for (ItemAudit itemAudit : itemAudits) {
                itemAudit.setStatus(ItemAuditStatusEnum.ADOPT.getValue());
                AreaItem platformAreaItem = areaItemMap.get("1-" + itemAudit.getItemId());
                //判断平台是否存在该商品
                if (ObjectUtil.isEmpty(platformAreaItem)) {
                    continue;
                }

                List<ItemPriceHistory> thisAuditHistory = itemPriceHistoryMap.get(itemAudit.getId());
                ItemPriceHistory itemPriceHistory = null;
                if (ObjectUtil.isNotEmpty(thisAuditHistory)) {
                    for (ItemPriceHistory priceHistory : thisAuditHistory) {
                        if (priceHistory.getUpstreamOperatorId().equals(platformAreaItem.getSourceOperatorId()) && priceHistory.getUpstreamOperatorChoiceLotLibId().equals(platformAreaItem.getSourceChoiceLotLibId())) {
                            itemPriceHistory = priceHistory;
                            break;
                        }
                    }
                }

                //判断平台选品库是否存在这个商品        如果不存在的话  说明品台存在这个品  但是没有加到任何一个选品库
                if (ObjectUtil.isNotEmpty(choiceLotLibSkuMap) && choiceLotLibSkuMap.containsKey(itemAudit.getSkuId()) && choiceLotLibSkuMap.get(itemAudit.getSkuId()).stream().anyMatch(choiceLotLibSkuModel -> choiceLotLibSkuModel.getOperatorId().equals(1L))) {

                    //修改平台选品库及商品池价格 并生成改价记录
                    for (ChoiceLotLibSkuModel choiceLotLibSkuModel : choiceLotLibSkuMap.get(itemAudit.getSkuId())) {
                        if (choiceLotLibSkuModel.getOperatorId().equals(1L)) {
                            ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel = new ParanaItemPriceHistoryMainModel();

                            if (ObjectUtil.isNotEmpty(itemAudit.getExpJson())) {
                                //用于修改选品库加价率的情况
                                JSONObject jsonObject = JSONObject.parseObject(itemAudit.getExpJson());
                                if (jsonObject.containsKey(choiceLotLibSkuModel.getChoiceLotLibId() + "_" + choiceLotLibSkuModel.getSkuId())) {
                                    Long markup = jsonObject.getLong(choiceLotLibSkuModel.getChoiceLotLibId() + "_" + choiceLotLibSkuModel.getSkuId());
                                    choiceLotLibSkuModel.setMarkup(markup);
                                }
                            }

                            Long basePrice = null;
                            Long distriPrice = null;

                            //判断当前审核是否为平台自己创建
                            if (itemAudit.getInitiateOperatorId().equals(1L)) {
                                //当为平台自己创建的审核时只有两种情况   修改分销价  修改选品库加价率
                                //当为平台自己创建的审核时 无需更新AreaSku的basePrice
                                distriPrice = itemAudit.getPlatformAfterBasePrice();
                            } else {
                                //来自上游的审核单  只需要判断当前商品来源类型
                                //如果为品牌供品平台，取itemAudit的basePrice
                                //如果为运营供品平台则按照当前areaItem的来源operatorId和sourceChoiceLotLibId取出对应的选品库sku  获取分销价
                                //都要更新areaSku的basePrice
                                if (platformAreaItem.getSourceType().equals(1)) {
                                    basePrice = itemAudit.getBasePrice();
                                    distriPrice = MarkupCalculateUtils.getDistriPrice(itemAudit.getBasePrice(), choiceLotLibSkuModel.getMarkup());
                                } else if (platformAreaItem.getSourceType().equals(2)) {
                                    ChoiceLotLibSkuModel choiceLotLibSkuModel1 = lotLibSkuModelMap.get(platformAreaItem.getSourceOperatorId() + "-" + platformAreaItem.getSourceChoiceLotLibId() + "-" + itemAudit.getSkuId());
                                    if (ObjectUtil.isNotEmpty(choiceLotLibSkuModel1)) {
                                        basePrice = choiceLotLibSkuModel1.getDistributorPrice();
                                        distriPrice = MarkupCalculateUtils.getDistriPrice(basePrice, choiceLotLibSkuModel.getMarkup());
                                    }
                                }
                                for (AreaSku y : areaSkuMap.get(choiceLotLibSkuModel.getOperatorId() + "-" + choiceLotLibSkuModel.getSkuId())) {
                                    y.setBasePrice(basePrice);
                                    y.setOriginalPrice(itemAudit.getOriginalPrice());
                                    areaItemUpdateList.add(platformAreaItem);
                                    areaSkuUpdateList.add(y);
                                }
                            }

                            paranaItemPriceHistoryMainModel.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                            paranaItemPriceHistoryMainModel.setTenantId(1);
                            paranaItemPriceHistoryMainModel.setOperatorId(choiceLotLibSkuModel.getOperatorId());
                            paranaItemPriceHistoryMainModel.setSkuId(itemAudit.getSkuId());
                            paranaItemPriceHistoryMainModel.setItemId(itemAudit.getItemId());
                            paranaItemPriceHistoryMainModel.setChoiceLotLibId(choiceLotLibSkuModel.getChoiceLotLibId());
                            paranaItemPriceHistoryMainModel.setAuditId(itemAudit.getId());
                            paranaItemPriceHistoryMainModel.setItemName(itemAudit.getName());
                            paranaItemPriceHistoryMainModel.setCreatedAt(now);
                            paranaItemPriceHistoryMainModel.setUpdatedAt(now);

                            if (itemAudit.getOperatorId().equals(1L)) {
                                paranaItemPriceHistoryMainModel.setType(2);
                            } else {
                                //运营商调价
                                paranaItemPriceHistoryMainModel.setType(1);
                            }

                            if (ObjectUtil.isEmpty(itemPriceHistory)) {
                                itemPriceHistory = new ItemPriceHistory();
                                itemPriceHistory.setPlatformBeforeBasePrice(choiceLotLibSkuModel.getDistributorPrice());
                                itemPriceHistory.setPlatformAfterBasePrice(distriPrice);

                                itemPriceHistory.setId(idGenerator.nextValue(ItemPriceHistory.class));
                                itemPriceHistory.setTenantId(1);
                                itemPriceHistory.setOperatorId(itemAudit.getOperatorId());
                                itemPriceHistory.setChoiceLotLibId(0L);
                                itemPriceHistory.setPlatformChoiceLotLibId(choiceLotLibSkuModel.getChoiceLotLibId());
                                itemPriceHistory.setSkuId(itemAudit.getSkuId());
                                itemPriceHistory.setItemId(itemAudit.getItemId());
                                itemPriceHistory.setAuditId(itemAudit.getId());
                                itemPriceHistory.setCreatedBy(itemAudit.getAuditBy());
                                if (itemAudit.getOperatorId().equals(1L)) {
                                    itemPriceHistory.setType(2);
                                } else {
                                    //运营商调价
                                    itemPriceHistory.setType(1);
                                }
                                if (platformAreaItem.getSourceType().equals(1)) {
                                    itemPriceHistory.setVendorBeforeBasePrice(itemAudit.getBeforeBasePrice());
                                    itemPriceHistory.setVendorAfterBasePrice(itemAudit.getBasePrice());
                                }
                                itemPriceHistoryArrayList.add(itemPriceHistory);
                            } else {
                                itemPriceHistory.setPlatformBeforeBasePrice(choiceLotLibSkuModel.getDistributorPrice());
                                itemPriceHistory.setPlatformAfterBasePrice(distriPrice);
                                itemPriceHistory.setPlatformChoiceLotLibId(choiceLotLibSkuModel.getChoiceLotLibId());
                                itemPriceHistoryUpdateList.add(itemPriceHistory);
                            }

                            paranaItemPriceHistoryMainModels.add(paranaItemPriceHistoryMainModel);


                            if (ObjectUtil.isNotEmpty(basePrice)) {
                                choiceLotLibSkuModel.setBasePrice(basePrice);
                            }


                            // 加价率 = (分销价-供货价）/供货价*100%。
                            choiceLotLibSkuModel.setMarkup(MarkupCalculateUtils.getResellerRate(distriPrice, choiceLotLibSkuModel.getBasePrice()));


                            // 分销毛利=分销价-供货价
                            choiceLotLibSkuModel.setResellerGross(MarkupCalculateUtils.sub(distriPrice, choiceLotLibSkuModel.getBasePrice()));
                            // 分销毛利率=（分销价-供货价）/分销价*100%
                            choiceLotLibSkuModel.setResellerGrossRate(MarkupCalculateUtils.getProfitRate(distriPrice, choiceLotLibSkuModel.getBasePrice()));
                            // 预估毛利率=（建议零售价 - 采购价）/ 建议零售价 × 100%
                            choiceLotLibSkuModel.setPreResellerGrossRate(MarkupCalculateUtils.getProfitRate(itemAudit.getOriginalPrice(), distriPrice));
                            choiceLotLibSkuModel.setDistributorPrice(distriPrice);
                            if (ObjectUtil.isNotEmpty(itemAudit.getOriginalPrice())) {
                                choiceLotLibSkuModel.setOriginalPrice(itemAudit.getOriginalPrice());
                            }
                            //更新选品库
                            choiceLotLibSkuUpdateList.add(choiceLotLibSkuModel);

                            int count = 1;
                            if (choiceLotLibSkuMap.get(itemAudit.getSkuId()).stream().anyMatch(e -> !e.getOperatorId().equals(1L) && !e.getOperatorId().equals(itemAudit.getOperatorId()))) {
                                //排除上游运营商及平台选品库数据
                                Long finalDistriPrice = distriPrice;

                                for (ChoiceLotLibSkuModel downstreamChoiceLotLibSkuModel : choiceLotLibSkuMap.get(itemAudit.getSkuId())) {
                                    if (!downstreamChoiceLotLibSkuModel.getOperatorId().equals(1L) && !downstreamChoiceLotLibSkuModel.getOperatorId().equals(itemAudit.getOperatorId())) {
                                        if (areaItemMap.containsKey(downstreamChoiceLotLibSkuModel.getOperatorId() + "-" + downstreamChoiceLotLibSkuModel.getItemId())) {
                                            AreaItem areaItem = areaItemMap.get(downstreamChoiceLotLibSkuModel.getOperatorId() + "-" + downstreamChoiceLotLibSkuModel.getItemId());
                                            if (ObjectUtil.isNotEmpty(areaItem.getSourceChoiceLotLibId()) && areaItem.getSourceChoiceLotLibId().equals(choiceLotLibSkuModel.getChoiceLotLibId())) {
                                                ChoiceLotLib choiceLotLib = choiceLotLibMap.get(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                                if (choiceLotLib == null) {
                                                    throw new ServiceException("选品库不存在");
                                                }
                                                ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel2 = new ParanaItemPriceHistoryMainModel();


                                                Long beforeBasePrice2 = downstreamChoiceLotLibSkuModel.getDistributorPrice();

                                                // 分销价=供货价*（1+加价率）
                                                Long distriPrice1 = MarkupCalculateUtils.getDistriPrice(finalDistriPrice, downstreamChoiceLotLibSkuModel.getMarkup());


                                                if (count > 1) {
                                                    //判断订购改商品的下游运营商数量  如果大于1的话则需要复制多条记录
                                                    ItemPriceHistory itemPriceHistory2 = BeanUtil.toBean(itemPriceHistory, ItemPriceHistory.class);
                                                    itemPriceHistory2.setId(idGenerator.nextValue(ItemPriceHistory.class));
                                                    itemPriceHistory2.setDownstreamOperatorChoiceLotLibId(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                                    itemPriceHistory2.setDownstreamOperatorId(downstreamChoiceLotLibSkuModel.getOperatorId());
                                                    itemPriceHistory2.setDownstreamOperatorBeforePrice(beforeBasePrice2);
                                                    itemPriceHistory2.setDownstreamOperatorAfterPrice(distriPrice1);
                                                    itemPriceHistory2.setType(2);
                                                    itemPriceHistoryArrayList.add(itemPriceHistory2);
                                                } else {
                                                    itemPriceHistory.setDownstreamOperatorBeforePrice(beforeBasePrice2);
                                                    itemPriceHistory.setDownstreamOperatorAfterPrice(distriPrice1);
                                                    itemPriceHistory.setDownstreamOperatorId(downstreamChoiceLotLibSkuModel.getOperatorId());
                                                    itemPriceHistory.setDownstreamOperatorChoiceLotLibId(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                                }

                                                paranaItemPriceHistoryMainModel2.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                                                paranaItemPriceHistoryMainModel2.setTenantId(1);
                                                paranaItemPriceHistoryMainModel2.setOperatorId(downstreamChoiceLotLibSkuModel.getOperatorId());
                                                paranaItemPriceHistoryMainModel2.setSkuId(itemAudit.getSkuId());
                                                paranaItemPriceHistoryMainModel2.setItemId(itemAudit.getItemId());
                                                paranaItemPriceHistoryMainModel2.setChoiceLotLibId(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                                paranaItemPriceHistoryMainModel2.setItemName(itemAudit.getName());
                                                paranaItemPriceHistoryMainModel2.setAuditId(itemAudit.getId());
                                                paranaItemPriceHistoryMainModel2.setCreatedAt(now);
                                                paranaItemPriceHistoryMainModel2.setUpdatedAt(now);


                                                //平台调价
                                                paranaItemPriceHistoryMainModel2.setType(2);


                                                //更新下游运营商AreaSku的basePrice
                                                areaSkuMap.get(downstreamChoiceLotLibSkuModel.getOperatorId() + "-" + itemAudit.getSkuId()).forEach(y -> {
                                                    if (y.getSkuId().equals(itemAudit.getSkuId()) && !y.getOperatorId().equals(itemAudit.getOperatorId())) {
                                                        y.setBasePrice(finalDistriPrice);
                                                        y.setOriginalPrice(itemAudit.getOriginalPrice());
                                                        areaItemUpdateList.add(areaItem);
                                                        areaSkuUpdateList.add(y);
                                                    }
                                                });

                                                //发送渠道商改价消息
                                                //下游运营商选品库
                                                ChoiceLotLib downChoiceLotLib = choiceLotLibMap.get(downstreamChoiceLotLibSkuModel.getChoiceLotLibId());
                                                if (downChoiceLotLib.getType() == 0) {
                                                    choiceLotLibItemModelMap.get(downstreamChoiceLotLibSkuModel.getItemId()).stream().filter(choiceLotLibItemModel -> choiceLotLibItemModel.getChoiceLotLibId().equals(downstreamChoiceLotLibSkuModel.getChoiceLotLibId()) && choiceLotLibItemModel.getItemId().equals(downstreamChoiceLotLibSkuModel.getItemId())).forEach(choiceLotLibItemModel -> {
                                                        if (ObjectUtil.isNotEmpty(choiceLotLibItemModel.getDistributorIds())) {
                                                            String[] split = choiceLotLibItemModel.getDistributorIds().replace("#", "").split(",");
                                                            ParanaThirdMessageBean paranaThirdMessageBean = new ParanaThirdMessageBean();
                                                            paranaThirdMessageBean.setId(choiceLotLibItemModel.getChoiceLotLibId());
                                                            paranaThirdMessageBean.setItemMessage(JSON.toJSONString(downstreamChoiceLotLibSkuModel.getSkuId()));
                                                            for (String authId : split) {
                                                                ParanaThirdMessageCreateRequest paranaThirdMessageCreateRequest = new ParanaThirdMessageCreateRequest();
                                                                paranaThirdMessageCreateRequest.setOperatorId(choiceLotLibItemModel.getOperatorId());
                                                                paranaThirdMessageCreateRequest.setAuthId(Long.parseLong(authId));
                                                                paranaThirdMessageCreateRequest.setBean(paranaThirdMessageBean);
                                                                thirdMessageCreateRequestList.add(paranaThirdMessageCreateRequest);
                                                            }
                                                        }
                                                    });
                                                }

                                                downstreamChoiceLotLibSkuModel.setBasePrice(finalDistriPrice);

                                                // 加价率 = (分销价-供货价）/供货价*100%。
                                                downstreamChoiceLotLibSkuModel.setMarkup(MarkupCalculateUtils.getResellerRate(distriPrice1, downstreamChoiceLotLibSkuModel.getBasePrice()));
                                                // 分销毛利=分销价-供货价
                                                downstreamChoiceLotLibSkuModel.setResellerGross(MarkupCalculateUtils.sub(distriPrice1, downstreamChoiceLotLibSkuModel.getBasePrice()));
                                                // 分销毛利率=（分销价-供货价）/分销价*100%
                                                downstreamChoiceLotLibSkuModel.setResellerGrossRate(MarkupCalculateUtils.getProfitRate(distriPrice1, downstreamChoiceLotLibSkuModel.getBasePrice()));
                                                // 预估毛利率=（建议零售价 - 采购价）/ 建议零售价 × 100%
                                                downstreamChoiceLotLibSkuModel.setPreResellerGrossRate(MarkupCalculateUtils.getProfitRate(downstreamChoiceLotLibSkuModel.getOriginalPrice(), distriPrice1));
                                                downstreamChoiceLotLibSkuModel.setDistributorPrice(distriPrice1);
                                                if (ObjectUtil.isNotEmpty(itemAudit.getOriginalPrice())) {
                                                    downstreamChoiceLotLibSkuModel.setOriginalPrice(itemAudit.getOriginalPrice());
                                                }
                                                //更新选品库
                                                choiceLotLibSkuUpdateList.add(downstreamChoiceLotLibSkuModel);
                                                paranaItemPriceHistoryMainModels.add(paranaItemPriceHistoryMainModel2);
                                                count++;
                                            }
                                        }

                                    }
                                }
                            }
                            for (AreaSku e : allOperatorAreaSku) {
                                if (!e.getOperatorId().equals(1L) && !e.getOperatorId().equals(itemAudit.getOperatorId())) {
                                    AreaItem areaItem = areaItemMap.get(e.getOperatorId() + "-" + e.getItemId());
                                    if (ObjectUtil.isNotEmpty(areaItem.getSourceChoiceLotLibId()) && areaItem.getSourceChoiceLotLibId().equals(choiceLotLibSkuModel.getChoiceLotLibId())) {
                                        boolean falg = false;
                                        for (AreaSku areaSkuUpdate : areaSkuUpdateList) {
                                            if (e.getOperatorId().equals(areaSkuUpdate.getOperatorId()) && e.getSkuId().equals(areaSkuUpdate.getSkuId())) {
                                                falg = true;
                                                break;
                                            }
                                        }
                                        if (falg) {
                                            continue;
                                        }
                                        ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel3 = new ParanaItemPriceHistoryMainModel();
                                        paranaItemPriceHistoryMainModel3.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                                        paranaItemPriceHistoryMainModel3.setTenantId(1);
                                        paranaItemPriceHistoryMainModel3.setOperatorId(e.getOperatorId());
                                        paranaItemPriceHistoryMainModel3.setSkuId(itemAudit.getSkuId());
                                        paranaItemPriceHistoryMainModel3.setItemId(itemAudit.getItemId());
                                        paranaItemPriceHistoryMainModel3.setItemName(itemAudit.getName());
                                        paranaItemPriceHistoryMainModel3.setAuditId(itemAudit.getId());
                                        paranaItemPriceHistoryMainModel3.setCreatedAt(now);
                                        paranaItemPriceHistoryMainModel3.setUpdatedAt(now);
                                        if (itemAudit.getOperatorId().equals(1L)) {
                                            paranaItemPriceHistoryMainModel.setType(2);
                                        } else {
                                            //运营商调价
                                            paranaItemPriceHistoryMainModel.setType(1);
                                        }

                                        if (count > 1) {
                                            //判断订购改商品的下游运营商数量  如果大于1的话则需要复制多条记录
                                            ItemPriceHistory itemPriceHistory2 = BeanUtil.toBean(itemPriceHistory, ItemPriceHistory.class);
                                            itemPriceHistory2.setDownstreamOperatorChoiceLotLibId(null);
                                            itemPriceHistory2.setId(idGenerator.nextValue(ItemPriceHistory.class));
                                            itemPriceHistory2.setDownstreamOperatorId(e.getOperatorId());
//                                            itemPriceHistory2.setDownstreamOperatorBeforePrice(e.getBasePrice());
//                                            itemPriceHistory2.setDownstreamOperatorAfterPrice(distriPrice);
                                            if (itemAudit.getOperatorId().equals(1L)) {
                                                itemPriceHistory2.setType(2);
                                            } else {
                                                //运营商调价
                                                itemPriceHistory2.setType(1);
                                            }
                                            itemPriceHistoryArrayList.add(itemPriceHistory2);
                                        } else {
//                                            itemPriceHistory.setDownstreamOperatorBeforePrice(e.getBasePrice());
//                                            itemPriceHistory.setDownstreamOperatorAfterPrice(distriPrice);
                                            itemPriceHistory.setDownstreamOperatorId(e.getOperatorId());
                                        }

                                        e.setBasePrice(distriPrice);
                                        e.setOriginalPrice(itemAudit.getOriginalPrice());
                                        e.setUpdatedAt(now);
                                        e.setUpdatedBy(itemAudit.getAuditBy());
                                        paranaItemPriceHistoryMainModels.add(paranaItemPriceHistoryMainModel3);
                                        areaSkuUpdateList.add(e);
                                        areaItemUpdateList.add(areaItem);
                                        count++;
                                    }
                                }
                            }

                        }
                    }


                } else {
                    if (areaSkuMap.get("1-" + itemAudit.getSkuId()).stream().anyMatch(areaSku -> areaSku.getOperatorId().equals(1L))) {
                        for (AreaSku areaSku : areaSkuMap.get("1-" + itemAudit.getSkuId())) {
                            if (areaSku.getOperatorId().equals(1L)) {
                                ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel = new ParanaItemPriceHistoryMainModel();
                                if (ObjectUtil.isEmpty(itemPriceHistory)) {
                                    itemPriceHistory = new ItemPriceHistory();
                                    itemPriceHistory.setId(idGenerator.nextValue(ItemPriceHistory.class));
                                    itemPriceHistory.setTenantId(1);
                                    itemPriceHistory.setOperatorId(itemAudit.getOperatorId());
                                    itemPriceHistory.setSkuId(itemAudit.getSkuId());
                                    itemPriceHistory.setItemId(itemAudit.getItemId());
                                    itemPriceHistory.setChoiceLotLibId(0L);
                                    itemPriceHistory.setAuditId(itemAudit.getId());
                                    itemPriceHistory.setType(1);
                                    itemPriceHistory.setCreatedBy("System");
                                    itemPriceHistoryArrayList.add(itemPriceHistory);
                                } else {
                                    itemPriceHistoryUpdateList.add(itemPriceHistory);
                                }

                                itemPriceHistory.setPlatformBeforeBasePrice(areaSku.getBasePrice());
                                if (platformAreaItem.getSourceType().equals(1)) {
                                    areaSku.setBasePrice(itemAudit.getBasePrice());
                                    areaSku.setOriginalPrice(itemAudit.getOriginalPrice());
                                } else {
                                    if (ObjectUtil.isNotEmpty(platformAreaItem.getSourceOperatorId()) && (platformAreaItem.getSourceOperatorId().equals(areaSku.getOperatorId()) || platformAreaItem.getSourceOperatorId().equals(itemAudit.getOperatorId()))) {
                                        if (ObjectUtil.isNotEmpty(itemAudit.getExpJson()) && JSONObject.parseObject(itemAudit.getExpJson()).getLong("choiceLotLibId").equals(platformAreaItem.getSourceChoiceLotLibId())) {
                                            areaSku.setBasePrice(itemAudit.getUpstreamOperatorAfterBasePrice());
                                            areaSku.setOriginalPrice(itemAudit.getOriginalPrice());
                                        }
                                    }
                                }
                                itemPriceHistory.setPlatformAfterBasePrice(areaSku.getBasePrice());
                                paranaItemPriceHistoryMainModel.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
                                paranaItemPriceHistoryMainModel.setTenantId(1);
                                paranaItemPriceHistoryMainModel.setOperatorId(areaSku.getOperatorId());
                                paranaItemPriceHistoryMainModel.setSkuId(itemAudit.getSkuId());
                                paranaItemPriceHistoryMainModel.setItemId(itemAudit.getItemId());
                                paranaItemPriceHistoryMainModel.setAuditId(itemAudit.getId());
                                paranaItemPriceHistoryMainModel.setCreatedAt(now);
                                paranaItemPriceHistoryMainModel.setUpdatedAt(now);

                                if (itemAudit.getOperatorId().equals(1L)) {
                                    paranaItemPriceHistoryMainModel.setType(2);
                                } else {
                                    //运营商调价
                                    paranaItemPriceHistoryMainModel.setType(1);
                                }

                                areaSku.setUpdatedAt(now);
                                areaSku.setUpdatedBy(itemAudit.getAuditBy());

                                paranaItemPriceHistoryMainModels.add(paranaItemPriceHistoryMainModel);
                                areaItemUpdateList.add(areaItemMap.get(areaSku.getOperatorId() + "-" + areaSku.getItemId()));
                                areaSkuUpdateList.add(areaSku);
                            }
                        }

                    }
                }

                toUpdateAudits.add(itemAudit);

            }
            //DB操作
            boolean flag = areaItemAuditManager.updateStatus(toUpdateAudits, null, null, null,
                    paranaItemPriceHistoryMainModels, itemPriceHistoryArrayList, choiceLotLibSkuUpdateList, areaSkuUpdateList, null,
                    null, itemPriceHistoryUpdateList);

            if (flag) {
                try {
                    if (ObjectUtil.isNotEmpty(thirdMessageCreateRequestList)) {
                        for (ParanaThirdMessageCreateRequest paranaThirdMessageCreateRequest : thirdMessageCreateRequestList) {
                            paranaThirdMessageWriteFacade.create(paranaThirdMessageCreateRequest);
                        }
                    }
                } catch (Exception e) {
                    log.error("变价审核-发送渠道商改价消息失败:{},request:{}", Throwables.getStackTraceAsString(e), JSON.toJSONString(thirdMessageCreateRequestList));
                }
            }
        }
    }

}
