package io.terminus.parana.item.category.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @program: category-center
 * @description: 属性库公共属性
 * @author: he<PERSON><PERSON>i
 * @create: 2018-10-25 16:27
 **/
@Data
public class PublicAttribute implements Serializable{
    private static final long serialVersionUID = 4671835966981503525L;
    /**
     * id
     */
    private Long id;
    /**
     * 扩展类型
     */
    private Integer extensionType;
    /**
     * 属性名
     */
    private String attrKey;
    /**
     * 状态,1启用,0删除
     */
    private Boolean status;
    /**
     * json 格式存储的属性元信息
     */
    private String attrMetasJson;
    /**
     * json 格式存储的属性值信息
     */
    private String attrValsJson;
    /**
     * 附加字段
     */
    private String extraJson;
    /**
     * createdAt
     */
    private Date createdAt;
    /**
     * updatedAt
     */
    private Date updatedAt;

    /**
     * 更新操作用户id
     */
    private String updatedBy;

    /**
     * 租户id
     */
    private Integer tenantId;

}
