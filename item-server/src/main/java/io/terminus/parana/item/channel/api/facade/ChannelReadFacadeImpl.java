package io.terminus.parana.item.channel.api.facade;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.channel.api.bean.request.*;
import io.terminus.parana.item.channel.api.bean.response.ChannelInfo;
import io.terminus.parana.item.channel.api.bean.response.ChannelTreeinfo;
import io.terminus.parana.item.channel.api.converter.ChannelApiConverter;
import io.terminus.parana.item.channel.api.converter.ChannelApiInfoConverter;
import io.terminus.parana.item.channel.bo.ChannelPagingCriteria;
import io.terminus.parana.item.channel.model.Channel;
import io.terminus.parana.item.channel.service.ChannelReadDomainService;
import io.terminus.parana.item.common.annotation.MicroService;
import io.terminus.parana.item.common.converter.GeneralConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-23
 */
@Service
public class ChannelReadFacadeImpl implements ChannelReadFacade {

    @Autowired
    private ChannelReadDomainService channelReadDomainService;

    @Autowired
    private ChannelApiInfoConverter channelApiInfoConverter;

    @Autowired
    private ChannelApiConverter channelApiConverter;

    @Override
    @MicroService
    public Response<ChannelInfo> findById(ChannelQueryBySingleIdRequest request) {
        try {
            Channel channel = channelReadDomainService.findById(request.getId());
            return Response.ok(channelApiInfoConverter.get(channel));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<Boolean> isValid(ChannelCheckValidRequest request) {
        try {
            Boolean valid = channelReadDomainService.isValid(request.getId());
            return Response.ok(valid);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<List<ChannelInfo>> listAll(ChannelListAllRequest request) {
        try {
            List<Channel> channelList = channelReadDomainService.listAll();
            return Response.ok(channelApiInfoConverter.get(channelList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<Paging<ChannelInfo>> paging(ChannelPagingRequest request) {
        ChannelPagingCriteria criteria = channelApiConverter.get(request);
        try {
            Paging<Channel> paging = channelReadDomainService.paging(criteria);
            return Response.ok(GeneralConverter.batchConvert(paging, channelApiInfoConverter::get));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<Boolean> verifyToken(ChannelVerifyTokenRequest request) {
        try {
            Boolean ok = channelReadDomainService.verifyToken(request.getLink(), request.getToken());
            return Response.ok(ok);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<ChannelInfo> queryByToken(ChannelQueryByTokenRequest request) {
        try {
            Channel channel = channelReadDomainService.findByToken(request.getToken(), request.getExcludeInvalid());
            return Response.ok(channelApiInfoConverter.get(channel));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<List<ChannelInfo>> queryChildrenChannel(ChannelQueryByPidRequest request) {
        try {
            List<Channel> list = channelReadDomainService.findByPid(request.getPid());
            List<ChannelInfo> channelInfoList = channelApiInfoConverter.get(list);
            return Response.ok(channelInfoList);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<List<ChannelInfo>> queryChildrenChannelByLever(ChannelQueryByLeverRequest request) {
        try {
            List<Channel> list = channelReadDomainService.findByPidAndLevel(request.getPid(),request.getLevel());
            List<ChannelInfo> channelInfoList = channelApiInfoConverter.get(list);
            return Response.ok(channelInfoList);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<List<ChannelInfo>> queryChannelByIds(ChannelQueryByIdsRequest request) {
        try {
            List<Channel> list = channelReadDomainService.findByIds(request.getIds());
            List<ChannelInfo> channelInfoList = channelApiInfoConverter.get(list);
            return Response.ok(channelInfoList);
        } catch (ServiceException e) {
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }


    @Override
    public Response<ChannelTreeinfo> queryChannelTreeById(ChannelQueryBySingleIdRequest request) {
        try {
            ChannelTreeinfo model = new ChannelTreeinfo();
            Channel channel = channelReadDomainService.findById(request.getId());
            ChannelInfo channelInfo = channelApiInfoConverter.get(channel);
            model.setCurrent(channelInfo);
            if (channel != null) {
                List<Channel> childrenList = channelReadDomainService.findByPid(channel.getId());
                model.setChildren(channelApiInfoConverter.get(childrenList));
            }
            return Response.ok(model);
        } catch (ServiceException e) {
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Map<Long, ChannelInfo>> queryAllChannel(ChannelMapAllRequest request) {
        try {
            Map<Long, ChannelInfo> map=new HashMap<Long, ChannelInfo>();
            //渠道表,待后期增加租户ID
            List<Channel> channelList = channelReadDomainService.listAll();
            for(Channel channel:channelList){
                ChannelInfo channelInfo = channelApiInfoConverter.get(channel);
                Long id=channelInfo.getId();
                map.put(id,channelInfo);
            }
            return Response.ok(map);
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<List<ChannelTreeinfo>> queryAllChannelTree(ChannelQueryByPidRequest request) {
        try {
            List<ChannelTreeinfo> result = new ArrayList<>();
            List<Channel> list = channelReadDomainService.findByPid(request.getPid());
            List<ChannelInfo> channelInfoList = channelApiInfoConverter.get(list);
            for (ChannelInfo channel : channelInfoList) {
                ChannelTreeinfo channelTreeinfo = new ChannelTreeinfo();
                List<Channel> childrenList = channelReadDomainService.findByPid(channel.getId());
                channelTreeinfo.setCurrent(channel);
                channelTreeinfo.setChildren(channelApiInfoConverter.get(childrenList));
                result.add(channelTreeinfo);
            }
            return Response.ok(result);
        } catch (ServiceException e) {
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }


}
