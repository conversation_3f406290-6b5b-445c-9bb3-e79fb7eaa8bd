package io.terminus.parana.item.category.manager;

import io.terminus.parana.item.category.model.FrontCategory;
import io.terminus.parana.item.category.repository.CategoryBindingDAO;
import io.terminus.parana.item.category.repository.FrontCategoryDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2018-07-28 上午10:57
 */
@Component
public class FrontCategoryManager {
    @Autowired
    private FrontCategoryDAO frontCategoryDAO;
    @Autowired
    private CategoryBindingDAO categoryBindingDAO;

    @Transactional(rollbackFor = Exception.class)
    public void create(FrontCategory parent, FrontCategory create) {
        parent.setHasChildren(true);
        parent.setUpdatedBy(create.getUpdatedBy());
        frontCategoryDAO.update(parent);
        frontCategoryDAO.create(create);
    }

    @Transactional(rollbackFor = Exception.class)
    public void create(Long pid, Long id, Integer tenantId, String updatedBy) {
        FrontCategory parent = new FrontCategory();
        parent.setId(pid);
        parent.setTenantId(tenantId);
        parent.setHasChildren(true);
        parent.setUpdatedBy(updatedBy);
        frontCategoryDAO.update(parent);
        frontCategoryDAO.updateDeleted(id, Boolean.FALSE, updatedBy);
    }

    @Transactional(rollbackFor = Exception.class)
    public void disable(Long id, Long pid, Integer tenantId, String updatedBy, Integer extensionType) {
        frontCategoryDAO.updateDeleted(id, Boolean.TRUE, updatedBy);
        //删除前台类目绑定关系
        categoryBindingDAO.delete(id, null);
        //检查是否更新父级hasChildren
        if (pid > 0) {
            List<FrontCategory> children = frontCategoryDAO.findByPid(pid, tenantId, extensionType);
            if (children.isEmpty()) {
                FrontCategory parent = new FrontCategory();
                parent.setId(pid);
                parent.setHasChildren(false);
                parent.setUpdatedBy(updatedBy);
                frontCategoryDAO.update(parent);
            }
        }
    }
}
