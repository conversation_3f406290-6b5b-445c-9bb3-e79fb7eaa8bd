package io.terminus.parana.item.category.manager;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CreateCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.category.api.bean.response.FrontCategoryThinInfo;
import io.terminus.parana.item.category.api.bean.response.FrontCategoryTreeInfo;
import io.terminus.parana.item.category.api.converter.CategoryConverter;
import io.terminus.parana.item.category.extension.CategoryBindingExtension;
import io.terminus.parana.item.category.model.BackCategory;
import io.terminus.parana.item.category.model.BackFrontBinding;
import io.terminus.parana.item.category.model.CategoryBinding;
import io.terminus.parana.item.category.model.FrontCategory;
import io.terminus.parana.item.category.repository.BackCategoryDao;
import io.terminus.parana.item.category.repository.CategoryBindingDAO;
import io.terminus.parana.item.category.repository.FrontCategoryDAO;
import io.terminus.parana.item.category.util.Constant;
import io.terminus.parana.item.common.extension.AbstractExtensionDeal;
import io.terminus.parana.item.common.extension.ExtensionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2018-07-25 下午4:59
 */
@Slf4j
@Service
public class FrontCategoryService extends AbstractExtensionDeal {
    @Autowired
    private BackCategoryDao backCategoryDAO;
    @Autowired
    private FrontCategoryDAO frontCategoryDAO;
    @Autowired
    private FrontCategoryManager frontCategoryManager;
    @Autowired
    private CategoryBindingDAO categoryBindingDAO;
    @Autowired
    private CategoryConverter categoryConverter;

    @Autowired(required = false)
    private CategoryBindingExtension categoryBindingExtension;

    //较为复杂的缓存失效逻辑，后续再做结构优化
    @CreateCache(name = "FrontCategory:single", expire = Constant.CACHE_REMOTE_EXPIRE)
    private Cache<Long, FrontCategory> singleCache;
    @CreateCache(name = "FrontCategory:children", expire = Constant.CACHE_REMOTE_EXPIRE)
    private Cache<String, List<FrontCategory>> childrenCache;
    @CreateCache(name = "FrontCategory:ancestor", expire = Constant.CACHE_REMOTE_EXPIRE)
    private Cache<Long, List<FrontCategory>> ancestorCache;
    @CreateCache(name = "FrontCategory:tree", expire = Constant.CACHE_REMOTE_EXPIRE)
    private Cache<String, FrontCategoryTreeInfo> treeCache;
    @CreateCache(name = "FrontCategory:allBack", expire = Constant.CACHE_REMOTE_EXPIRE)
    private Cache<Long, List<BackCategory>> backTreeCache;

    /**
     * 后台类目id指向前台类目缓存
     */
    @CreateCache(name = "FrontCategory:backToFront", expire = Constant.CACHE_REMOTE_EXPIRE)
    private Cache<Long, List<FrontCategoryThinInfo>> backToFrontCache;

    public Map<Long, List<FrontCategoryThinInfo>> findFrontCategoryListByBackCategoryId(List<Long> backCategoryIds) {
        Map<Long, List<FrontCategoryThinInfo>> cacheMap = backToFrontCache.getAll(new HashSet<>(backCategoryIds));
        if (null == cacheMap) {
            cacheMap = new HashMap<>(backCategoryIds.size());
        }
        List<Long> notExistedCategoryIds = Lists.newArrayList();
        for (Long backCategoryId : backCategoryIds) {
            List<FrontCategoryThinInfo> list = cacheMap.get(backCategoryId);
            if (null == list) {
                notExistedCategoryIds.add(backCategoryId);
            }
        }
        // 查询后台类目绑定的前台类目
        if (!notExistedCategoryIds.isEmpty()) {
            List<BackFrontBinding> bindings = categoryBindingDAO.findFrontCategoriesByBackCategoryIds(notExistedCategoryIds);
            if (!CollectionUtils.isEmpty(bindings)) {
                Map<Long, List<BackFrontBinding>> bindMap = bindings.stream().collect(Collectors.groupingBy(BackFrontBinding::getBackCategoryId));
                for (Long backCategoryId : notExistedCategoryIds) {
                    List<BackFrontBinding> binds = bindMap.get(backCategoryId);
                    List<FrontCategoryThinInfo> models = null == binds ? Collections.emptyList() : binds.stream().map(this::do2model).collect(Collectors.toList());
                    cacheMap.put(backCategoryId, models);
                    backToFrontCache.putAll(cacheMap);
                }
            }
        }
        // 结果处理
        int count = 0;
        Map<Long, List<FrontCategoryThinInfo>> retMap = Maps.newHashMapWithExpectedSize(backCategoryIds.size());
        for (Long backCategoryId : backCategoryIds) {
            if (count >= backCategoryIds.size()) {
                break;
            }
            List<FrontCategoryThinInfo> list = cacheMap.get(backCategoryId);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            List<FrontCategoryThinInfo> retList = Lists.newArrayListWithCapacity(list.size());
            for (FrontCategoryThinInfo model : list) {
                if (count >= backCategoryIds.size()) {
                    break;
                }
                count++;
                retList.add(model);
            }
            retMap.put(backCategoryId, retList);
        }
        return retMap;
    }

    public FrontCategory create(FrontCategory fc) {
        //查询是否有同名兄弟类目
        FrontCategory exist = frontCategoryDAO.findByPidAndName(fc.getPid(), fc.getName(), fc.getTenantId(), fc.getExtensionType());
        if (null != exist) {
            if (Boolean.FALSE == exist.getDeleted()) {
                throw new ServiceException("front.category.name.duplicated");
            } else {
                //启用原来的
                frontCategoryManager.create(fc.getPid(), exist.getId(), fc.getTenantId(), fc.getUpdatedBy());
                //淘汰 父级子类目查询缓存，所有父级类目的类目树缓存
                removeAllFatherCache(fc);
                return exist;
            }
        }
        //如果是1级类目
        if (0L == fc.getPid()) {
            frontCategoryDAO.create(fc);
            // 只需要删除根结点缓存（根结点缓存策略不同）
            childrenCache.remove(String.format("%d#type#%d", fc.getTenantId(), fc.getExtensionType()));
            // 删除前台类目根结点的树形缓存
            treeCache.remove( String.format("%d#%d#%d", fc.getTenantId(), fc.getExtensionType(), fc.getPid()) );
            return fc;
        }
        //非1级类目,查询其父类目
        exist = frontCategoryDAO.findById(fc.getPid());
        if (null == exist) {
            throw new ServiceException("front.category.parent.not.found");
        }
        fc.setLevel(exist.getLevel() + 1);
        frontCategoryManager.create(exist, fc);

        //淘汰 父级子类目查询缓存，所有父级类目的类目树缓存
        removeAllFatherCache(fc);
        return fc;
    }

    public Boolean update(FrontCategory fc) {
        FrontCategory origin = frontCategoryDAO.findById(fc.getId());
        if (null == origin) {
            throw new ServiceException("frontCategory.not.found");
        }
        //重名校验
        if (null != fc.getName()) {
            FrontCategory exist = frontCategoryDAO.findByPidAndName(origin.getPid(), fc.getName(), fc.getTenantId(), origin.getExtensionType());
            if (null != exist && Boolean.FALSE == exist.getDeleted() && !fc.getId().equals(exist.getId())) {
                throw new ServiceException("frontCategory.name.duplicated");
            }
        }
        // 入参fc没有extensionType和pid，确保参数完整性，保证缓存重置
        fc.setExtensionType(origin.getExtensionType());
        fc.setPid(origin.getPid());
        Boolean b = frontCategoryDAO.update(fc);
        //淘汰 自身缓存，类目树缓存，孩子节点的类目树缓存，父级子类目查询缓存，所有父级类目的类目树缓存
        if (b) {
            singleCache.remove(fc.getId());
            Set<Long> s = findChildrenIdByPid(fc.getId(), fc.getTenantId(), fc.getExtensionType());
            s.add(fc.getId());
            ancestorCache.removeAll(s);
            if(0L == origin.getPid()) {
                // 删除根结点缓存，根结点需要缓存策略特判
                childrenCache.remove(String.format("%d#type#%d", origin.getTenantId(), origin.getExtensionType()));
            } else {
                // 删除其他结点缓存
                childrenCache.remove(String.format("%d#%d", origin.getTenantId(), origin.getPid()));
            }
            // 获取所有上级前台类目id集合
            Set<Long> treeIdSet = new HashSet<>(findAncestorIdsOf(origin.getId()));
            // 获得父类前台类目的树形Key集合
            Set<String> treeKeys = treeIdSet.stream().map(tempId ->
                    String.format("%d#%d#%d", fc.getTenantId(), fc.getExtensionType(), tempId)).collect(Collectors.toSet());
            if (!treeKeys.isEmpty()) {
                treeCache.removeAll(treeKeys);
            }
            // 插入当前结点
            treeIdSet.add(fc.getId());
            // 删除所有子级前台类目backTree缓存（包含自身结点）
            backTreeCache.removeAll(treeIdSet);
        } else {
            throw new ServiceException("frontCategory.update.fail");
        }
        return b;
    }

    public Boolean delete(Long id, Integer tenantId, String updatedBy) {
        FrontCategory origin = frontCategoryDAO.findById(id);
        if (null == origin) {
            throw new ServiceException("frontCategory.not.found");
        }
        if (Boolean.TRUE == origin.getDeleted()) {
            return Boolean.TRUE;
        }
        if (origin.getHasChildren()) {
            throw new ServiceException("frontCategory.notLeaf");
        }
        if (!CollectionUtils.isEmpty(findByFrontCategoryId(id))) {
            throw new ServiceException("front.category.has.binding");
        }
        frontCategoryManager.disable(id, origin.getPid(), tenantId, updatedBy, origin.getExtensionType());
        //淘汰 自身缓存，类目树缓存，孩子节点的类目树缓存，父级子类目查询缓存，所有父级类目的类目树缓存
        singleCache.remove(id);
        Set<Long> s = findChildrenIdByPid(id, tenantId, origin.getExtensionType());
        s.add(id);
        ancestorCache.removeAll(s);

        //淘汰 父级子类目查询缓存，所有父级类目的类目树缓存
        removeAllFatherCache(origin);
        return Boolean.TRUE;
    }

    public Boolean bindBack(CategoryBinding binding) {
        onBind(binding);

        Integer c = categoryBindingDAO.findBinding(binding);
        if (null == c || c <= 0) {
            categoryBindingDAO.create(binding);
            List<Long> ancestorIds = findAncestorIdsOf(binding.getFrontCategoryId());
            Set<Long> idSet = Sets.newHashSet();
            idSet.addAll(ancestorIds);
            idSet.add(binding.getFrontCategoryId());
            backTreeCache.removeAll(idSet);
        }
        return Boolean.TRUE;
    }

    private void onBind(CategoryBinding categoryBinding) {
        if (categoryBindingExtension == null) {
            return;
        }

        StopWatch stopWatch = prepare();
        ExtensionResult result = categoryBindingExtension.onBinding(categoryBinding);
        finish(stopWatch, result);
    }

    public Boolean unBindBack(CategoryBinding binding) {
        List<Long> ancestorIds = findAncestorIdsOf(binding.getFrontCategoryId());
        Set<Long> idSet = Sets.newHashSet();
        idSet.addAll(ancestorIds);
        idSet.add(binding.getFrontCategoryId());
        backTreeCache.removeAll(idSet);
        return categoryBindingDAO.delete(binding.getFrontCategoryId(), binding.getBackCategoryId());
    }

    public FrontCategory findById(Long id) {
        return singleCache.computeIfAbsent(id, aLong -> {
            FrontCategory origin = frontCategoryDAO.findById(aLong);
            if (null == origin || origin.getDeleted().equals(Boolean.TRUE)) {
                throw new ServiceException("frontCategory.not.found");
            }
            return origin;
        });
    }

    private Set<Long> findChildrenIdByPid(Long pid, Integer tenantId, Integer extensionType) {
        return findChildrenByPid(pid, tenantId, extensionType).stream().map(FrontCategory::getId).collect(Collectors.toSet());
    }

    public List<FrontCategory> findChildrenByPid(Long pid, Integer tenantId, Integer extensionType) {
        return childrenCache.computeIfAbsent(0L == pid
                                            ? String.format("%d#type#%d", tenantId, extensionType)
                                            : String.format("%d#%d", tenantId, pid),
                s -> frontCategoryDAO.findByPid(pid, tenantId, extensionType));
    }

    public List<FrontCategory> findByIds(List<Long> ids) {
        Map<Long, FrontCategory> map = singleCache.getAll(new HashSet<>(ids));
        if (CollectionUtils.isEmpty(map)) {
            List<FrontCategory> list = frontCategoryDAO.findByIds(ids);
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            singleCache.putAll(Maps.uniqueIndex(list, FrontCategory::getId));
            return list;
        }
        return new ArrayList<>(map.values());
    }

    public List<FrontCategory> findAncestorsByBackCategoryId(Long id) {
        Long firstFrontCategoryId = categoryBindingDAO.findByBackCategoryId(id);
        return null == firstFrontCategoryId ? Collections.emptyList() : findAncestorsOf(firstFrontCategoryId);
    }

    private List<Long> findAncestorIdsOf(Long id) {
        if(0L == id){
            return Lists.newArrayList();
        }
        List<FrontCategory> ancestors = findAncestorsOf(id);
        return ancestors.stream().map(FrontCategory::getId).collect(Collectors.toList());
    }

    public List<FrontCategory> findAncestorsOf(Long id) {
        return ancestorCache.computeIfAbsent(id, aLong -> {
            List<FrontCategory> ancestors = Lists.newArrayListWithCapacity(4);
            Long currentId = id;
            while (currentId > 0) {
                FrontCategory current = frontCategoryDAO.findById(currentId);
                if (current != null && Boolean.FALSE == current.getDeleted()) {
                    ancestors.add(current);
                    currentId = current.getPid();
                } else {
                    log.error("front category not found, id = {}", currentId);
                    break;
                }
            }
            List<FrontCategory> list = new ArrayList<>(4);
            for (int i = ancestors.size() - 1; i >= 0; --i) {
                list.add(ancestors.get(i));
            }
            return list;
        });
    }

    public List<FrontCategory> findByBackCategoryId(Long id) {
        List<Long> ids = categoryBindingDAO.listByBackCategoryId(id);
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return frontCategoryDAO.findByIds(ids);
    }

    public List<BackCategory> findByFrontCategoryId(Long id) {
        List<Long> ids = categoryBindingDAO.listByFrontCategoryId(id);
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return backCategoryDAO.findByIds(ids);
    }

    public List<BackCategory> findAllByFrontCategoryId(Long id, Integer tenantId, Integer extensionType) {
        return backTreeCache.computeIfAbsent(id, aLong -> {
            Set<Long> frontCategoryIdSet = findChildrenIdByPid(id, tenantId, extensionType);
            if (CollectionUtils.isEmpty(frontCategoryIdSet)) {
                return findByFrontCategoryId(id);
            }

            Set<Long> allFrontCategoryIdSet = Sets.newHashSet();
            allFrontCategoryIdSet.addAll(frontCategoryIdSet);

            do {
                Set<Long> tempSet = Sets.newHashSet();
                for (Long frontCategoryId : frontCategoryIdSet) {
                    tempSet.addAll(findChildrenIdByPid(frontCategoryId, tenantId, extensionType));
                }
                allFrontCategoryIdSet.addAll(tempSet);
                frontCategoryIdSet = tempSet;
            } while (!CollectionUtils.isEmpty(frontCategoryIdSet));

            List<Long> ids = categoryBindingDAO.listByFrontCategoryIdSet(Lists.newArrayList(allFrontCategoryIdSet));
            if (CollectionUtils.isEmpty(ids)) {
                return Collections.emptyList();
            }
            return backCategoryDAO.findByIds(ids);
        });
    }

    public List<FrontCategoryTreeInfo> findTreeByIds(List<Long> ids, Integer tenantId, Integer extensionType) {
        Set<String> keySet = ids.stream().map(tempId ->
                String.format("%d#%d#%d", tenantId, extensionType, tempId)).collect(Collectors.toSet());
        Map<String, FrontCategoryTreeInfo> map = treeCache.getAll(keySet);
        if (CollectionUtils.isEmpty(map) || map.size() != ids.size()) {
            List<FrontCategoryTreeInfo> list = buildTreeByIds(ids);
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            treeCache.putAll(Maps.uniqueIndex(list, f -> String.format("%d#%d#%d", tenantId, extensionType, f.getCurrent().getId())));
            return list;
        }
        List<FrontCategoryTreeInfo> result = new ArrayList<>();
        for (Long id : ids) {
            FrontCategoryTreeInfo m = map.get(String.format("%d#%d#%d", tenantId, extensionType, id));
            if (null == m) {
                continue;
            }
            result.add(m);
        }
        return result;
    }

    private List<FrontCategoryTreeInfo> buildTreeByIds(List<Long> ids) {
        List<FrontCategory> rootList = frontCategoryDAO.findByIds(ids);
        Map<Long, FrontCategory> rootOrderMap = Maps.uniqueIndex(rootList, FrontCategory::getId);
        List<FrontCategory> rootOrderList = Lists.newArrayListWithCapacity(ids.size());
        for (Long id : ids) {
            rootOrderList.add(rootOrderMap.get(id));
        }
        List<FrontCategoryTreeInfo> result = Lists.newArrayListWithCapacity(ids.size());
        for (FrontCategory frontCategory : rootOrderList) {
            if (null == frontCategory) {
                continue;
            }
            result.add(buildTree(frontCategory));
        }
        return result;
    }

    private FrontCategoryTreeInfo buildTree(FrontCategory frontCategory) {
        FrontCategoryTreeInfo model = new FrontCategoryTreeInfo();
        model.setCurrent(categoryConverter.domain2dto(frontCategory));
        List<FrontCategory> childrenList = frontCategoryDAO.findByPid(frontCategory.getId(), frontCategory.getTenantId(), frontCategory.getExtensionType());
        List<FrontCategoryTreeInfo> children = model.getChildren();
        for (FrontCategory fc : childrenList) {
            children.add(buildTree(fc));
        }
        return model;
    }

    private FrontCategoryThinInfo do2model(BackFrontBinding binding) {
        if (null == binding) {
            return null;
        }
        FrontCategoryThinInfo model = new FrontCategoryThinInfo();
        model.setId(binding.getFrontCategoryId());
        model.setName(binding.getFrontCategoryName());
        return model;
    }

    //淘汰 父级子类目查询缓存，所有父级类目的类目树缓存
    private void removeAllFatherCache(FrontCategory fc){
        if(fc.getPid() == null || fc.getExtensionType() == null || fc.getTenantId() == null){
            throw new ServiceException("frontCategory.parameter.is.null");
        }

        // 查找所有上级结点
        List<Long> pids = findAncestorIdsOf(fc.getPid());

        // 删除根结点缓存（根结点缓存策略不同）
        childrenCache.remove(String.format("%d#type#%d", fc.getTenantId(), fc.getExtensionType()));
        // 删除其他结点缓存
        childrenCache.removeAll(pids.stream().map(tmpid ->
                String.format("%d#%d", fc.getTenantId(), tmpid)).collect(Collectors.toSet()));

        // 转换成树形缓存的key集合
        Set<String> ancestorKeySet = pids.stream().map(tmpid ->
                String.format("%d#%d#%d", fc.getTenantId(), fc.getExtensionType(), tmpid)).collect(Collectors.toSet());
        // 删除所有父级前台类目的缓存树
        treeCache.removeAll(ancestorKeySet);

        // 获得前台类目id集合
        Set<Long> treeIdSet = Sets.newHashSet(pids);
        // 插入当前结点
        treeIdSet.add(fc.getId());
        // 删除所有父级前台类目对应的后台类目缓存（包含本级类目）
        backTreeCache.removeAll(treeIdSet);
    }

}
