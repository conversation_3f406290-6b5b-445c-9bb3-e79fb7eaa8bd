package io.terminus.parana.item.choicelot.repository;

import com.google.common.collect.ImmutableMap;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibMarkup;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ChoiceLotMarkupDao
 * @projectName item-center
 * @description: TODO
 * @date 2022/3/3 15:15
 */
@Repository
public class ChoiceLotLibMarkupDao extends AbstractMybatisDao<ChoiceLotLibMarkup> {

    /**
     * 根据选品库ID获取选品库加价率
     * @param params
     * @return
     */
    public List<ChoiceLotLibMarkup> getMarkupByChoiceLotId(Map<String, Object> params){
        return getSqlSession().selectList(sqlId("getMarkupByChoiceLotId"), params);
    }

    /**
     * 根据选品库Id删除选品库加价率
     * @param param
     * @return
     */
    public Boolean  deleteByLotId(Map<String, Object> param){
        int delete = getSqlSession().update(this.sqlId("deleteByLotId"), param);
        return delete > 0;
    }
}
