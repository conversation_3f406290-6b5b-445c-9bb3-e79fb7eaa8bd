package io.terminus.parana.item.favorites.repository;

import io.terminus.parana.item.common.base.AbstractMybatisDao;
import io.terminus.parana.item.favorites.model.Favorites;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 收藏Dao
 *
 * <AUTHOR>
 * @since 2018-12-17
 */
@Repository
public class FavoritesDao extends AbstractMybatisDao<Favorites> {

    /**
     * 添加收藏
     *
     * @param favorites
     * @return
     */
    public Integer add(Favorites favorites) {
        return getSqlSession().insert(sqlId("add"), favorites);
    }

    /**
     * 批量添加收藏
     *
     * @param favoritesList
     * @return
     */
    public Integer batchAdd(List<Favorites> favoritesList) {
        return getSqlSession().insert(sqlId("batchAdd"), favoritesList);
    }

    /**
     * 取消收藏
     *
     * @param params
     * @return
     */
    public Integer cancel(Map<String, Object> params) {
        return getSqlSession().update(sqlId("cancel"), params);
    }

    /**
     * 收藏删除
     * @param params
     * @return
     */
    public Integer delete(Map<String, Object> params) {
        return getSqlSession().update(sqlId("delete"), params);
    }

    /**
     * 取消收藏
     *
     * @param params
     * @return
     */
    public Integer batchCancelById(Map<String, Object> params) {
        return getSqlSession().update(sqlId("batchCancelById"), params);
    }

    /**
     * 取消收藏
     *
     * @param params
     * @return
     */
    public Integer cancelByUserId(Map<String, Object> params) {
        return getSqlSession().update(sqlId("cancelByUserId"), params);
    }

    /**
     * 取消收藏
     *
     * @param params
     * @return
     */
    public Integer clearByTargetId(Map<String, Object> params) {
        return getSqlSession().update(sqlId("clearByTargetId"), params);
    }


    /**
     * 查询收藏
     *
     * @param params
     * @return
     */
    public List<Favorites> findByConditions(Map<String, Object> params) {
        return getSqlSession().selectList(sqlId("findByConditions"), params);
    }

    /**
     * 检查是否已收藏
     *
     * @param params
     * @return
     */
    public Favorites checkFavorite(Map<String, Object> params) {
        return getSqlSession().selectOne(sqlId("checkFavorite"), params);
    }

    /**
     * 统计收藏
     *
     * @param params
     * @return
     */
    public Long countByConditions(Map<String, Object> params) {
        return getSqlSession().selectOne(sqlId("countByConditions"), params);
    }

    /**
     * 获取唯一键
     *
     * @param ids
     * @return
     */
    public List<String> getUniqueKeyByIds(List<Long> ids) {
        return getSqlSession().selectList(sqlId("getUniqueKeyByIds"), ids);
    }

}
