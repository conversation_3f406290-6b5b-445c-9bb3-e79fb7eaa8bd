package io.terminus.parana.item.enhance.plugin;

import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.enhance.api.bean.response.ItemAggQueryForTradeInfo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-19
 */
@Component
public class ItemTradeDimensionInvoker {

    public void consume(List<ItemAggQueryForTradeInfo> result, String dimensionCode, String dimensionType) {
    }


    @FunctionalInterface
    public interface Executor {
        ExtensionResult consume(List<ItemAggQueryForTradeInfo> result, String dimensionCode, String dimensionType);
    }
}
