package io.terminus.parana.item.plugin.third.impl.tongka.service.business.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.api.utils.StringUtil;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.area.manager.TongKaItemManager;
import io.terminus.parana.item.area.manager.TongKaItemRequest;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.repository.AreaItemDao;
import io.terminus.parana.item.area.repository.AreaSkuDao;
import io.terminus.parana.item.brand.api.manager.BrandService;
import io.terminus.parana.item.brand.model.Brand;
import io.terminus.parana.item.category.manager.BackCategoryService;
import io.terminus.parana.item.category.model.BackCategory;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibMarkup;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.choicelot.model.DistributorItemLibModel;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibMarkupDao;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibSkuDao;
import io.terminus.parana.item.choicelot.repository.DistributorItemLibDao;
import io.terminus.parana.item.choicelot.utils.MarkupCalculateUtils;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.distributorskulib.dao.DistributorSkuLibDao;
import io.terminus.parana.item.distributorskulib.model.DistributorSkuLibModel;
import io.terminus.parana.item.export.thirdparty.storage.aliyun.EascsAliyunOssFactory;
import io.terminus.parana.item.item.api.bean.request.item.param.ItemDetailContentParam;
import io.terminus.parana.item.item.api.facade.ItemWriteFacade;
import io.terminus.parana.item.item.cache.CacheItemById;
import io.terminus.parana.item.item.cache.CacheItemDetailById;
import io.terminus.parana.item.item.cache.CacheSkuById;
import io.terminus.parana.item.item.enums.ItemStatus;
import io.terminus.parana.item.item.enums.ItemType;
import io.terminus.parana.item.item.model.*;
import io.terminus.parana.item.item.repository.ItemDetailDao;
import io.terminus.parana.item.item.repository.ParanaItemOutImgDao;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.partnership.model.VendorPartnership;
import io.terminus.parana.item.partnership.service.VendorPartnershipReadDomainService;
import io.terminus.parana.item.plugin.third.api.inventory.api.InventoryWriteApi;
import io.terminus.parana.item.plugin.third.api.tongka.dto.*;
import io.terminus.parana.item.plugin.third.api.tongka.service.TongKaService;
import io.terminus.parana.item.plugin.third.impl.tongka.service.business.TongKaSyncTopUpItemService;
import io.terminus.parana.item.relation.model.BaseSku;
import io.terminus.parana.item.relation.utils.GenerateHelper;
import io.terminus.parana.item.search.docobject.AreaItemDO;
import io.terminus.parana.item.search.facade.AreaItemSearchFacade;
import io.terminus.parana.item.search.request.AreaItemSearchRequest;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.service.ShopReadDomainService;
import io.terminus.parana.item.third.param.ThirdInventoryAdjustParam;
import io.terminus.parana.item.third.param.ThirdInventoryAdjustRequest;
import io.terminus.parana.item.transcript.extension.ItemMd5Describer;
import io.terminus.parana.log.api.facade.StarlinkReqErrorLogWriteFacade;
import io.terminus.parana.log.api.starlinkreqerrorLog.api.bean.request.StarlinkReqErrorLogCreateRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通卡直充商品同步服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
//@Service
public class TongKaSyncTopUpItemServiceImplBAK implements TongKaSyncTopUpItemService {

    @Autowired
    private TongKaService tongKaService;

    @Autowired
    private TongKaItemManager tongKaItemManager;

    @Autowired
    private GenerateHelper generateHelper;
    @Autowired
    private ItemReadDomainService itemReadDomainService;
    @Autowired
    private SkuReadDomainService skuReadDomainService;
    @Autowired
    private ItemDetailDao itemDetailDao;
    @Autowired
    private AreaItemDao areaItemDao;
    @Autowired
    private AreaSkuDao areaSkuDao;
    @Autowired
    private BackCategoryService backCategoryService;
    @Autowired
    private ShopReadDomainService shopReadDomainService;
    @Autowired
    private BrandService brandService;
    @Autowired
    private ParanaItemOutImgDao paranaItemOutImgDao;
    @Autowired
    private EascsAliyunOssFactory eascsAliyunOssFactory;
    @Autowired
    private  IdGenerator idGenerator;
    @Autowired
    private ItemMd5Describer itemMd5Describer;
    @Autowired
    private VendorPartnershipReadDomainService vendorPartnershipReadDomainService;
    @Autowired
    private InventoryWriteApi inventoryWriteApi;
    @Autowired
    private StarlinkReqErrorLogWriteFacade starlinkReqErrorLogWriteFacade;
    @Autowired
    private AreaItemSearchFacade areaItemSearchFacade;
    @Autowired
    private ItemWriteFacade itemWriteFacade;

    @Autowired
    private CacheItemById cacheItemById;
    @Autowired
    private CacheItemDetailById cacheItemDetailById;
    @Autowired
    private CacheSkuById cacheSkuById;

    // 新增的依赖注入
    @Autowired
    private ChoiceLotLibSkuDao choiceLotLibSkuDao;
    @Autowired
    private DistributorSkuLibDao distributorSkuLibDao;
    @Autowired
    private DistributorItemLibDao distributorItemLibDao;
    @Autowired
    private ChoiceLotLibMarkupDao choiceLotLibMarkupDao;

    /**
     * 中台运营商ID
     */
    private static final Long CENTER_OPERATOR_ID = 1L;


    @Value("${tongka.top-item.sync.pageSize:50}")
    private Integer pageSize;

    @Value("${tongka.vendorId:1100871001}")
    private Long vendorId;

    @Value("${tongka.top-item.sync.categoryId:29005}")
    private Long defaultCategoryId;

    @Value("${tongka.top-item.sync.brandId:122746800}")
    private Long brandId;

    private static final String SYNC_USER = "system";
    private static final Integer DEFAULT_TENANT_ID = 1;

    @Override
    public void syncItems() {
        log.info("开始同步通卡直充商品（一对一模式）");

        try {
            // 1. 先加载所有商品数据
            List<DealerGoodsInfo> allGoodsList = loadAllDealerGoods();
            if (CollectionUtils.isEmpty(allGoodsList)) {
                log.info("没有获取到商品数据，同步结束");
                return;
            }

            log.info("总共获取到{}条商品数据，将创建{}个独立商品", allGoodsList.size(), allGoodsList.size());

            int totalProcessed = 0;

            // 2. 直接处理每个商品（一对一模式）
            for (DealerGoodsInfo goods : allGoodsList) {
                try {
                    log.info("处理商品：{}（ID：{}）", goods.getGoodsTitle(), goods.getGoodsId());

                    // 检查商品是否存在（使用goodsId作为外部ID）
                    Item item = itemReadDomainService.findByOutId(goods.getGoodsId(), DEFAULT_TENANT_ID, vendorId);
                    if (item != null) {
                        log.info("商品已存在，进行更新判断: {}", item.getName());

                        // 获取详细信息
                        GoodsDetailByIdInfo detailInfo = enrichSingleItemDetail(goods);

                        // 执行商品更新逻辑
                        updateSingleTongKaItem(goods, detailInfo, item);

                        totalProcessed++;
                        log.info("成功更新商品：{}，累计处理{}个商品", goods.getGoodsTitle(), totalProcessed);
                        continue;
                    }

                    // 商品下级不处理
                    if (!StrUtil.equals(goods.getStatus(), "1")) {
                        log.info("商品未上架，跳过处理：{}（ID：{}）", goods.getGoodsTitle(), goods.getGoodsId());
                        continue;
                    }

                    // 获取详细信息
                    GoodsDetailByIdInfo detailInfo = enrichSingleItemDetail(goods);

                    // 数据转换
                    TongKaItemRequest tongKaItemRequest = buildSingleItemRequest(goods, detailInfo);

                    // 保存数据
                    saveItem(tongKaItemRequest);

                    totalProcessed++;
                    log.info("成功处理商品：{}，累计处理{}个商品", goods.getGoodsTitle(), totalProcessed);

                } catch (Exception e) {
                    log.error("处理商品失败：{}（ID：{}），错误：{}", goods.getGoodsTitle(), goods.getGoodsId(), e.getMessage(), e);
                    // 继续处理下一个商品，不中断整个同步流程
                    errorSave("通卡-直充商品同步事变", goods.getGoodsId(), Throwables.getStackTraceAsString(e));
                }
            }

            log.info("通卡直充商品同步完成，共处理{}个商品", totalProcessed);

        } catch (Exception e) {
            log.error("通卡直充商品同步失败", e);
            throw new ServiceException("通卡直充商品同步失败: " + e.getMessage());
        }
    }

    @Override
    public void removeItem() {
        List<Item> items = itemReadDomainService.findByShopIdItems(vendorId);
        items.forEach(itemInfo -> {
            AreaItemSearchRequest areaItemSearchRequest = new AreaItemSearchRequest();
            areaItemSearchRequest.setItemId(itemInfo.getId());
            areaItemSearchRequest.setPageSize(1024);
            Paging<AreaItemDO> areaItemList = Assert.take(areaItemSearchFacade.search(areaItemSearchRequest));
            Set<Long> operatorIds = areaItemList.getData().stream().map(AreaItemDO::getOperatorId).collect(Collectors.toSet());

            // 删除商品
            Map<Integer, List<Long>> itemMap = new LinkedHashMap<>();
            itemMap.put(ItemStatus.DELETED.getValue(), Collections.singletonList(itemInfo.getId()));
            itemWriteFacade.updateItemStatus(itemMap, operatorIds);
        });
    }

    /**
     * 加载所有经销商商品数据
     * 通过分页方式获取所有商品，确保分组的完整性
     */
    private List<DealerGoodsInfo> loadAllDealerGoods() {
        List<DealerGoodsInfo> allGoodsList = Lists.newArrayList();
        int page = 1;

        try {
            while (true) {
                log.info("正在获取第{}页商品数据，每页{}条", page, pageSize);

                List<DealerGoodsInfo> pageGoodsList = getDealerGoodsWithPaging(page, pageSize);
                if (CollectionUtils.isEmpty(pageGoodsList)) {
                    log.info("第{}页没有更多商品数据，数据加载完成", page);
                    break;
                }

                allGoodsList.addAll(pageGoodsList);
                log.info("第{}页获取到{}条商品数据，累计{}条", page, pageGoodsList.size(), allGoodsList.size());

                // 如果返回的数据少于页面大小，说明已经是最后一页
                if (pageGoodsList.size() < pageSize) {
                    log.info("已获取到最后一页数据，数据加载完成");
                    break;
                }

                page++;

                // 添加短暂延迟，避免API调用过于频繁
                Thread.sleep(200);
            }

            log.info("成功加载所有商品数据，总计{}条", allGoodsList.size());
            return allGoodsList;

        } catch (Exception e) {
            log.error("加载所有商品数据失败", e);
            throw new ServiceException("加载所有商品数据失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取经销商商品列表
     */
    private List<DealerGoodsInfo> getDealerGoodsWithPaging(int page, int size) {
        try {
            DealerGoodsListRequest request = new DealerGoodsListRequest(page, size);
            TkResult<DealerGoodsListResponse> result = tongKaService.getDealerGoodsList(request);

            if (!result.isSuccess()) {
                log.error("获取经销商商品列表失败：{}", result.getMsg());
                return Lists.newArrayList();
            }

            DealerGoodsListResponse response = result.getData();
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getList())) {
                return Lists.newArrayList();
            }

            return response.getData().getList();

        } catch (Exception e) {
            log.error("获取经销商商品列表异常：page={}, size={}", page, size, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 按类目分组商品
     */
    private Map<String, List<DealerGoodsInfo>> groupItemsByCategory(List<DealerGoodsInfo> goodsList) {
        return goodsList.stream()
                .filter(this::validateGoodsData)
                .collect(Collectors.groupingBy(this::generateGroupKey));
    }

    /**
     * 生成分组Key
     */
    private String generateGroupKey(DealerGoodsInfo goods) {
//        return goods.getCategoryOneName() + "|" + goods.getCategoryTwoName() + "|" + goods.getCategoryThreeName();
        return goods.getCategoryOneName() + "|" + goods.getCategoryTwoName();
    }

    /**
     * 验证商品数据
     */
    private boolean validateGoodsData(DealerGoodsInfo goods) {
        if (!StringUtils.hasText(goods.getGoodsTitle())) {
            log.warn("商品标题为空，跳过：{}", goods.getGoodsId());
            return false;
        }

        if (!StringUtils.hasText(goods.getCategoryTwoName())) {
            log.warn("二级分类为空，跳过：{}", goods.getGoodsId());
            return false;
        }

        if (!StringUtils.hasText(goods.getPrice()) || new BigDecimal(goods.getPrice()).compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("价格无效，跳过：{}", goods.getGoodsId());
            return false;
        }

        return true;
    }

    /**
     * 获取单个商品详细信息（一对一模式）
     */
    private GoodsDetailByIdInfo enrichSingleItemDetail(DealerGoodsInfo goods) {
        try {
            GoodsDetailByIdRequest request = new GoodsDetailByIdRequest(Integer.valueOf(goods.getGoodsId()));
            TkResult<GoodsDetailByIdResponse> result = tongKaService.getGoodsDetailById(request);

            if (result.isSuccess() && result.getData() != null && result.getData().getData() != null) {
                return result.getData().getData();
            } else {
                log.warn("获取商品详情失败：goodsId={}, msg={}", goods.getGoodsId(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("获取商品详情异常：goodsId={}", goods.getGoodsId(), e);
        }

        // 返回默认的详情信息，避免空指针
        GoodsDetailByIdInfo defaultDetail = new GoodsDetailByIdInfo();
        defaultDetail.setGoodsId(Integer.valueOf(goods.getGoodsId()));
        defaultDetail.setStatus(Integer.valueOf(goods.getStatus()));
        defaultDetail.setGoodsNeedKnow("暂无购买须知");
        defaultDetail.setGoodsInstruction("暂无使用说明");
        return defaultDetail;
    }

    /**
     * 转换为TongKaItemRequest
     */
    private TongKaItemRequest buildCreateItemRequest(String categoryKey, List<DealerGoodsInfo> goodsList, List<GoodsDetailByIdInfo> detailInfos) {
        if (CollectionUtils.isEmpty(goodsList)) {
            throw new ServiceException("商品列表为空");
        }

        // 因为直充商品是按照分类分组的需要按照goodsList聚合状态，判断整体商品是否上架或者下架
        boolean isOnShelf = goodsList.stream().anyMatch(goods -> "1".equals(goods.getStatus()));
        ItemStatus itemStatus = isOnShelf ? ItemStatus.ON_SHELF : ItemStatus.OFF_SHELF;

        // 使用第一个商品的信息作为Item基础信息
        DealerGoodsInfo firstGoods = goodsList.get(0);
        GoodsDetailByIdInfo goodsDetailByIdInfo = detailInfos.get(0);

        // 创建商品基本信息
        Item item = buildItem(categoryKey, firstGoods);
        generateHelper.generateId(item);

        //主图上传oss
        itemImgPack(item, item.getOuterId(), goodsDetailByIdInfo);

        // 创建商品详情
        ItemDetail itemDetail = buildItemDetail(item, detailInfos);

        // 创建SKU列表
        List<Sku> skuList = buildSkuList(item, goodsList, detailInfos);
        skuList.forEach(generateHelper::generateId);

        item.setMd5Info(itemMd5Describer.describe(item, skuList, itemDetail));

        //初始化商品上的sku属性
        initSkuAttributes(item,skuList);

        // 创建区域商品信息
        AreaItem areaItem = buildAreaItem(item,itemStatus);
        generateHelper.generateId(areaItem);

        // 创建区域SKU列表
        List<AreaSku> areaSkuList = buildAreaSkuList(areaItem, skuList);
        areaSkuList.forEach(generateHelper::generateId);

        // 构建TongKaItemRequest
        return new TongKaItemRequest(item, skuList, itemDetail, areaItem, areaSkuList, SYNC_USER);
    }

    /**
     * 转换为TongKaItemRequest（一对一模式）
     */
    private TongKaItemRequest buildSingleItemRequest(DealerGoodsInfo goods, GoodsDetailByIdInfo detailInfo) {
        if (goods == null) {
            throw new ServiceException("商品信息为空");
        }

        // 商品状态判断（直接使用商品状态）
        ItemStatus itemStatus = "1".equals(goods.getStatus()) ? ItemStatus.ON_SHELF : ItemStatus.OFF_SHELF;

        // 创建商品基本信息
        Item item = buildSingleItem(goods);
        generateHelper.generateId(item);

        //主图上传oss
        itemImgPack(item, item.getOuterId(), detailInfo);

        // 创建商品详情
        ItemDetail itemDetail = buildSingleItemDetail(item, detailInfo);

        // 创建单个SKU`
        Sku sku = buildSingleSku(item, goods, detailInfo);
        generateHelper.generateId(sku);
        List<Sku> skuList = Lists.newArrayList(sku);

        item.setMd5Info(itemMd5Describer.describe(item, skuList, itemDetail));

        //初始化商品上的sku属性
        initSkuAttributes(item, skuList);

        // 创建区域商品信息
        AreaItem areaItem = buildAreaItem(item, itemStatus);
        generateHelper.generateId(areaItem);

        // 创建区域SKU
        AreaSku areaSku = buildAreaSku(areaItem, sku);
        generateHelper.generateId(areaSku);
        List<AreaSku> areaSkuList = Lists.newArrayList(areaSku);

        // 构建TongKaItemRequest
        return new TongKaItemRequest(item, skuList, itemDetail, areaItem, areaSkuList, SYNC_USER);
    }

    public void initSkuAttributes(Item item, List<Sku> skus){
        Map<String, List<SkuAttribute>> map = new HashMap<>();
        for (Sku sku : skus) {
            // 关闭的sku不处理
            if (Boolean.FALSE.equals(sku.getEnable())) {
                continue;
            }
            List<SkuAttribute> attributes = sku.getAttributes();
            for (SkuAttribute attribute : attributes) {
                String attrKey = attribute.getAttrKey();
                List<SkuAttribute> skuAttributes = map.get(attrKey);
                SkuAttribute skuAttribute = new SkuAttribute();
                skuAttribute.setAttrKey(attrKey);
                skuAttribute.setAttrVal(attribute.getAttrVal());
                skuAttribute.setShowImage(attribute.getShowImage());
                skuAttribute.setImage(attribute.getImage());
                skuAttribute.setId(attribute.getId());
                if(org.springframework.util.CollectionUtils.isEmpty(skuAttributes)){
                    List<SkuAttribute> skuAttributeList = new ArrayList<>();
                    skuAttributeList.add(skuAttribute);
                    map.put(attrKey, skuAttributeList);
                }else {
                    skuAttributes.add(skuAttribute);
                }
            }
        }

        List<GroupedSkuAttribute> skuAttributes = new ArrayList<>();
        Set<Map.Entry<String, List<SkuAttribute>>> entries = map.entrySet();
        for (Map.Entry<String, List<SkuAttribute>> entry : entries) {
            GroupedSkuAttribute skuAttribute = new GroupedSkuAttribute();
            String key = entry.getKey();
            List<SkuAttribute> attributes = entry.getValue();
            List<SkuAttribute> skuAttributeList = new ArrayList<>();
            for (SkuAttribute attribute : attributes) {
                SkuAttribute skuAttr = new SkuAttribute();
                BeanUtils.copyProperties(attribute, skuAttr);
                skuAttributeList.add(skuAttr);
            }
            skuAttribute.setAttrKey(key);
            skuAttribute.setSkuAttributes(skuAttributeList);

            skuAttributes.add(skuAttribute);
        }
        item.setSkuAttributes(skuAttributes);

    }

    /**
     * 商品主图上传oss
     *
     * @param item
     * @param outerId
     */
    private void itemImgPack(Item item, String outerId, GoodsDetailByIdInfo goodsDetailByIdInfo){
        item.setMainImage(upload(item.getId(), outerId, goodsDetailByIdInfo.getTopBannerImgUrl()));
    }

    /**
     * 创建商品基本信息
     */
    private Item buildItem(String categoryKey, DealerGoodsInfo firstGoods) {
        Item item = new Item();

        // 商品名称使用二级分类名
        item.setName(firstGoods.getCategoryTwoName());

        //商品修改 类目不允许更新
        if(item.getId() == null){
            // 外部ID使用分组Key的MD5
            item.setOuterId(DigestUtils.md5Hex(categoryKey));
            // 判断三级分类是否存在
            BackCategory category = backCategoryService.findByNameAndPid(firstGoods.getCategoryOneName(), defaultCategoryId);
            // 不存在就创建
            if (category == null) {
                category = createCategory(firstGoods);
            }
            // 设置默认分类ID
            item.setCategoryId(category.getId());
            List<Long> categoryIds = backCategoryService.findAncestorIdsOf(category.getId());
            Map<String, String> map = new HashMap<>();
            map.put("categoryList", categoryIds.toString());
            // 默认不能退款
            map.put("supportReturn", "0");
            item.setExtra(map);

            item.setUnit("个");
            item.setBitTag(0L);
            item.setVersion(1);
            item.setExtensionType(0);
            item.setCreatedAt(new Date());
            // 设置店铺ID
            item.setShopId(vendorId);
            item.setShopName("通卡供应链");
            Shop shop = shopReadDomainService.findById(vendorId, DEFAULT_TENANT_ID, null);
            if(shop != null){
                item.setShopName(shop.getName());
            }

            // 设置品牌
            item.setBrandId(brandId);
            item.setBrandName("通卡");
            Brand brand = brandService.findById(brandId);
            if(brand != null){
                item.setBrandName(brand.getName());
            }

            // 设置商品类型为充值商品
            item.setType(ItemType.TOP_UP.getValue());
            item.setBusinessType(1);
            item.setOtherAttributesJson("[]");
            item.setIsCrossBorder(0);
            item.setIsSaleAttributes(0);
            item.setStatus(-1);
            item.setVatrate(new BigDecimal("0.13"));
        }


        item.setDeliveryFeeTempId(0L);
        // 默认销售全国
        item.setSalesArea("110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000");
        // 设置租户ID
        item.setTenantId(DEFAULT_TENANT_ID);
        // 设置更新人
        item.setUpdatedBy(SYNC_USER);
        item.setUpdatedAt(new Date());
        item.setUpdatedBy(SYNC_USER);
        return item;
    }

    /**
     * 创建商品基本信息（一对一模式）
     */
    private Item buildSingleItem(DealerGoodsInfo goods) {
        Item item = new Item();

        // 商品名称使用商品标题
        item.setName(goods.getCategoryTwoName() + "-" +goods.getGoodsTitle());

        // 外部ID使用商品ID
        item.setOuterId(goods.getGoodsId());
        // 设置默认分类ID
        item.setCategoryId(defaultCategoryId);
        List<Long> categoryIds = backCategoryService.findAncestorIdsOf(defaultCategoryId);
        Map<String, String> map = new HashMap<>();
        map.put("categoryList", categoryIds.toString());
        // 默认不能退款
        map.put("supportReturn", "0");
        map.put("faceValue", goods.getAmount());       // 面额
        map.put("accountType", goods.getAccountType().toString()); // 充值类型
        item.setExtra(map);

        item.setUnit("个");
        item.setBitTag(0L);
        item.setVersion(1);
        item.setExtensionType(0);
        // 设置店铺ID
        item.setShopId(vendorId);
        item.setShopName("通卡供应链");
        Shop shop = shopReadDomainService.findById(vendorId, DEFAULT_TENANT_ID, null);
        if(shop != null){
            item.setShopName(shop.getName());
        }

        // 设置品牌
        item.setBrandId(brandId);
        item.setBrandName("通卡");
        Brand brand = brandService.findById(brandId);
        if(brand != null){
            item.setBrandName(brand.getName());
        }

        // 设置商品类型为充值商品
        item.setType(ItemType.TOP_UP.getValue());
        item.setBusinessType(1);
        item.setOtherAttributesJson("[]");
        item.setIsCrossBorder(0);
        item.setIsSaleAttributes(0);
        item.setStatus(-1);
        item.setVatrate(new BigDecimal("0.13"));

        item.setDeliveryFeeTempId(0L);
        // 默认销售全国
        item.setSalesArea("110000,120000,130000,140000,150000,210000,220000,230000,310000,320000,330000,340000,350000,360000,370000,410000,420000,430000,440000,450000,460000,500000,510000,520000,530000,540000,610000,620000,630000,640000,650000,710000,810000,820000");
        // 设置租户ID
        item.setTenantId(DEFAULT_TENANT_ID);
        item.setCreatedAt(new Date());
        // 设置更新人
        item.setUpdatedBy(SYNC_USER);
        item.setUpdatedAt(new Date());
        return item;
    }

    private BackCategory createCategory(DealerGoodsInfo firstGoods) {
        BackCategory category =  new BackCategory();
        category.setName(firstGoods.getCategoryOneName());
        category.setPid(defaultCategoryId);
        category.setTenantId(DEFAULT_TENANT_ID);
        category.setExtensionType(0);
        category.setLevel(3);
        category.setHasSpu(false);
        category.setHasChildren(false);
        category.setSourceType("tongka");
        category.setStatus(1);
        category = backCategoryService.create(category);
        return category;
    }

    /**
     * 创建SKU列表
     */
    private List<Sku> buildSkuList(Item item, List<DealerGoodsInfo> goodsList, List<GoodsDetailByIdInfo> detailInfos) {
        List<Sku> skuList = Lists.newArrayList();

        // 创建详情信息映射，便于查找
        Map<String, GoodsDetailByIdInfo> detailMap = detailInfos.stream()
                .collect(Collectors.toMap(
                        detail -> detail.getGoodsId().toString(),
                        detail -> detail,
                        (existing, replacement) -> existing
                ));

        for (DealerGoodsInfo goods : goodsList) {
            try {
                Sku sku = buildSku(item, goods, detailMap.get(goods.getGoodsId()));
                skuList.add(sku);
            } catch (Exception e) {
                log.error("创建SKU失败：goodsId={}", goods.getGoodsId(), e);
            }
        }

        return skuList;
    }





    /**
     * 保存商品
     */
    private void saveItem(TongKaItemRequest request) {
        try {
            log.info("开始保存商品：{}", request.getItem().getName());

            boolean success = tongKaItemManager.saveDb(request);
            if (!success) {
                throw new ServiceException("保存商品失败");
            }

            // 同步ES
            dumpEs(request);

            // 同步库存
            syncIpm(request.getSkuList());

            log.info("成功保存商品：{}，ID：{}", request.getItem().getName(), request.getItem().getId());

        } catch (Exception e) {
            log.error("保存商品失败：{}", request.getItem().getName(), e);
            throw new ServiceException("保存商品失败: " + e.getMessage());
        }
    }

    private void dumpEs(TongKaItemRequest request){
        tongKaItemManager.dumpItemEs(request);
        tongKaItemManager.dumpSkuEs(request);
        tongKaItemManager.dumpAreaItemEs(request);
        tongKaItemManager.dumpAreaSkuEs(request);
        tongKaItemManager.dumpChoiceItemEs(request);
        tongKaItemManager.dumpDistributorItemEs(request);
    }

    /**
     * 创建单个SKU
     */
    private Sku buildSku(Item item, DealerGoodsInfo goods, GoodsDetailByIdInfo detailInfo) {
        Sku sku = new Sku();
        // 设置基本属性
        sku.setItemId(item.getId());
        sku.setExtensionType(1);
        sku.setTenantId(DEFAULT_TENANT_ID);
        // 外部ID使用goods_id
        sku.setOuterId(goods.getGoodsId());
        sku.setSkuCode("-");
        sku.setBarcode("-");
        sku.setShopId(vendorId);
        // SKU名称使用goods_title
        sku.setName(goods.getGoodsTitle());
        sku.setVersion(1);
        sku.setStatus(-1);
        sku.setType(item.getType());
        sku.setBusinessType(item.getBusinessType());
        sku.setBitTag(0L);
        sku.setEnable(true);
        if(!Objects.equals(detailInfo.getStatus(),1)){
            sku.setEnable(false);
        }

        sku.setUpdatedBy(SYNC_USER);

        // 设置商品价格参数
        Map<String, Long> extraPrice = new HashMap<>();
        // 设置成本价
        BigDecimal defaultBasePrice = new BigDecimal(goods.getSettlePrice()).multiply(new BigDecimal(100));
        extraPrice.put("defaultBasePrice", defaultBasePrice.longValue());
        extraPrice.put("defaultPrice", defaultBasePrice.longValue());
        // 使用面值作为吊牌价
        BigDecimal centralizedPurchasePrice = new BigDecimal(goods.getAmount()).multiply(new BigDecimal(100));
        extraPrice.put("centralizedPurchasePrice", centralizedPurchasePrice.longValue());
        // 销售价作为建议零售价
        BigDecimal originalPrice = new BigDecimal(goods.getPrice()).multiply(new BigDecimal(100));
        sku.setOriginalPrice(originalPrice.longValue());
        sku.setPrice(originalPrice.longValue());
        sku.setExtraPrice(extraPrice);

        // 设置扩展信息
        Map<String, String> extraInfo = Maps.newHashMap();
        extraInfo.put("minQuantity", "1");
        extraInfo.put("physicalInventory", "********");
        extraInfo.put("faceValue", goods.getAmount());       // 面额
        extraInfo.put("accountType", goods.getAccountType().toString()); // 充值类型
        if (detailInfo != null) {
            extraInfo.put("brandName", detailInfo.getBrandName());
            extraInfo.put("baseImg", detailInfo.getBaseImg());
            extraInfo.put("listImg", detailInfo.getListImg());
            extraInfo.put("gallery", detailInfo.getGallery());
        }
        sku.setExtra(extraInfo);

        // 设置销售属性
        List<SkuAttribute> skuAttributes = new ArrayList<>();
        SkuAttribute skuAttribute = new SkuAttribute();
        skuAttribute.setAttrKey("规格");
        skuAttribute.setAttrVal(goods.getGoodsTitle());
        if (detailInfo != null) {
            skuAttribute.setImage(detailInfo.getBaseImg());
        }
        skuAttribute.setShowImage(false);
        skuAttribute.setId(1L);
        skuAttributes.add(skuAttribute);
        sku.setAttributes(skuAttributes);

        return sku;
    }

    /**
     * 创建单个SKU（一对一模式）
     */
    private Sku buildSingleSku(Item item, DealerGoodsInfo goods, GoodsDetailByIdInfo detailInfo) {
        Sku sku = new Sku();
        // 设置基本属性
        sku.setItemId(item.getId());
        sku.setExtensionType(1);
        sku.setTenantId(DEFAULT_TENANT_ID);
        // 外部ID使用goods_id
        sku.setOuterId(goods.getGoodsId());
        sku.setSkuCode("-");
        sku.setBarcode("-");
        sku.setShopId(vendorId);
        // SKU名称使用goods_title
        sku.setName(item.getName());
        sku.setVersion(1);
        sku.setStatus(-1);
        sku.setType(item.getType());
        sku.setBusinessType(item.getBusinessType());
        sku.setBitTag(0L);
        sku.setEnable(true);
//        if(!Objects.equals(detailInfo.getStatus(),1)){
//            sku.setEnable(false);
//        }

        sku.setUpdatedBy(SYNC_USER);

        // 设置商品价格参数
        Map<String, Long> extraPrice = new HashMap<>();
        // 设置成本价
        BigDecimal defaultBasePrice = new BigDecimal(goods.getSettlePrice()).multiply(new BigDecimal(100));
        extraPrice.put("defaultBasePrice", defaultBasePrice.longValue());
        extraPrice.put("defaultPrice", defaultBasePrice.longValue());
        // 使用面值作为吊牌价
        BigDecimal centralizedPurchasePrice = new BigDecimal(goods.getAmount()).multiply(new BigDecimal(100));
        extraPrice.put("centralizedPurchasePrice", centralizedPurchasePrice.longValue());
        // 销售价作为建议零售价
        BigDecimal originalPrice = new BigDecimal(goods.getPrice()).multiply(new BigDecimal(100));
        sku.setOriginalPrice(originalPrice.longValue());
        sku.setPrice(originalPrice.longValue());
        sku.setExtraPrice(extraPrice);

        // 设置扩展信息
        Map<String, String> extraInfo = Maps.newHashMap();
        extraInfo.put("minQuantity", "1");
        extraInfo.put("physicalInventory", "********");
        extraInfo.put("faceValue", goods.getAmount());       // 面额
        extraInfo.put("accountType", goods.getAccountType().toString()); // 充值类型
        if (detailInfo != null) {
            extraInfo.put("brandName", detailInfo.getBrandName());
            extraInfo.put("baseImg", detailInfo.getBaseImg());
            extraInfo.put("listImg", detailInfo.getListImg());
            extraInfo.put("gallery", detailInfo.getGallery());
        }
        sku.setExtra(extraInfo);

        // 设置销售属性
        List<SkuAttribute> skuAttributes = new ArrayList<>();
        SkuAttribute skuAttribute = new SkuAttribute();
        skuAttribute.setAttrKey("规格");
        skuAttribute.setAttrVal(goods.getGoodsTitle());
        if (detailInfo != null) {
            skuAttribute.setImage(detailInfo.getBaseImg());
        }
        skuAttribute.setShowImage(false);
        skuAttribute.setId(1L);
        skuAttributes.add(skuAttribute);
        sku.setAttributes(skuAttributes);

        return sku;
    }

    /**
     * 创建商品详情
     */
    private ItemDetail buildItemDetail(Item item, List<GoodsDetailByIdInfo> detailInfos) {
        ItemDetail itemDetail = new ItemDetail();

        itemDetail.setItemId(item.getId());
        itemDetail.setTenantId(1);


        GoodsDetailByIdInfo goodsDetailByIdInfo = detailInfos.get(0);
        // 设置商品详情
        StringBuilder detailBuilder = new StringBuilder();
        detailBuilder.append("<h3>购买须知</h3>");
        detailBuilder.append("<p>")
                .append(goodsDetailByIdInfo.getGoodsNeedKnow())
                .append("</p>")
        ;
        detailBuilder.append("<h3>使用说明</h3>");
        detailBuilder.append("<p>")
                .append(goodsDetailByIdInfo.getGoodsInstruction())
                .append("</p>")
        ;

        ItemDetailContentParam itemDetailContentParam = new ItemDetailContentParam();
        itemDetailContentParam.setTitle("商品详情");


        // 添加详细信息
        if (!CollectionUtils.isEmpty(detailInfos)) {
            for (GoodsDetailByIdInfo detailInfo : detailInfos) {
                if (StringUtils.hasText(detailInfo.getBrandName())) {
                    detailBuilder.append("<p>品牌：").append(detailInfo.getBrandName()).append("</p>");
                    break; // 只显示第一个品牌信息
                }
            }
        }
        itemDetailContentParam.setContent(detailBuilder.toString());

        itemDetail.setPcDetail(JSONUtil.toJsonStr(itemDetailContentParam));
        itemDetail.setWapDetail(JSONUtil.toJsonStr(itemDetailContentParam));

        return itemDetail;
    }

    /**
     * 创建商品详情（一对一模式）
     */
    private ItemDetail buildSingleItemDetail(Item item, GoodsDetailByIdInfo detailInfo) {
        ItemDetail itemDetail = new ItemDetail();

        itemDetail.setItemId(item.getId());
        itemDetail.setTenantId(1);

        // 设置商品详情
        StringBuilder detailBuilder = new StringBuilder();
        detailBuilder.append("<h3>购买须知</h3>");
        detailBuilder.append("<p>")
                .append(detailInfo.getGoodsNeedKnow())
                .append("</p>");
        detailBuilder.append("<h3>使用说明</h3>");
        detailBuilder.append("<p>")
                .append(detailInfo.getGoodsInstruction())
                .append("</p>");

        // 添加品牌信息
        if (StringUtils.hasText(detailInfo.getBrandName())) {
            detailBuilder.append("<p>品牌：").append(detailInfo.getBrandName()).append("</p>");
        }

        ItemDetailContentParam itemDetailContentParam = new ItemDetailContentParam();
        itemDetailContentParam.setTitle("商品详情");
        itemDetailContentParam.setContent(detailBuilder.toString());

        List<ItemDetailContentParam> list = Lists.newArrayList(itemDetailContentParam);

        itemDetail.setPcDetail(JSONUtil.toJsonStr(list));
        itemDetail.setWapDetail(JSONUtil.toJsonStr(list));

        return itemDetail;
    }

    /**
     * 创建区域商品信息
     */
    private AreaItem buildAreaItem(Item item, ItemStatus itemStatus) {
        AreaItem areaItem = new AreaItem();

        areaItem.setItemId(item.getId());
        areaItem.setUpdatedBy(SYNC_USER);
        areaItem.setVendorId(vendorId);
        areaItem.setStatus(item.getStatus());

        if(areaItem.getId() == null){
            areaItem.setVendorId(item.getShopId());
            areaItem.setOperatorId(1L);
            areaItem.setSourceType(1);
            areaItem.setSupportReturn(true);
            areaItem.setIsNationwideAgencyItem(false);
            areaItem.setVendorStatus(-1);
            //非集货
            areaItem.setLogisticsMode(2);
            //低价模式
            areaItem.setCooperationMode(1);
            areaItem.setCreatedAt(item.getCreatedAt());
            areaItem.setItemId(item.getId());
        }
        areaItem.setName(item.getName());
        areaItem.setMainImage(item.getMainImage());
        areaItem.setImages(item.getImages());
        areaItem.setDeliveryFeeTempId(item.getDeliveryFeeTempId());
        areaItem.setHqOperatorStatus(itemStatus.getValue());
        areaItem.setAreaOperatorStatus(itemStatus.getValue());
        areaItem.setStatus(itemStatus.getValue());
        areaItem.setUpdatedAt(item.getUpdatedAt());
        areaItem.setUpdatedBy(item.getUpdatedBy());
        areaItem.setSalesArea(item.getSalesArea());
        areaItem.setOuterItemId(item.getOuterId());

        return areaItem;
    }

    /**
     * 创建区域SKU列表
     */
    private List<AreaSku> buildAreaSkuList(AreaItem areaItem, List<Sku> skuList) {
        List<AreaSku> areaSkuList = Lists.newArrayList();
        Map<Long, AreaSku> areaSkuMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(areaSkuList)){
            areaSkuMap = areaSkuList.stream().collect(Collectors.toMap(AreaSku::getSkuId, Function.identity(), (k1, k2) -> k1));
        }
        for (Sku sku : skuList) {
            AreaSku areaSku = new AreaSku();
            //已存在商品 只修改对应信息
            if(areaSkuMap.containsKey(sku.getId())){
                areaSku = areaSkuMap.get(sku.getId());
            }else{
                areaSku.setCreatedAt(areaItem.getCreatedAt());
            }
            areaSku.setOperatorId(areaItem.getOperatorId());
            areaSku.setVendorId(areaItem.getVendorId());
            areaSku.setAreaItemId(areaItem.getId());
            areaSku.setItemId(areaItem.getItemId());
            areaSku.setSkuId(sku.getId());
            areaSku.setName(areaItem.getName());
            areaSku.setImage(areaItem.getMainImage());
            areaSku.setDefaultPrice(sku.getPrice());
            areaSku.setBasePrice(sku.getExtraPrice().get("defaultBasePrice"));
            areaSku.setHqOperatorStatus(areaItem.getHqOperatorStatus());
            areaSku.setVendorStatus(areaItem.getVendorStatus());
            areaSku.setAreaOperatorStatus(areaItem.getAreaOperatorStatus());
            areaSku.setStatus(areaItem.getStatus());
            areaSku.setCooperationMode(areaItem.getCooperationMode());
            areaSku.setLogisticsMode(areaItem.getLogisticsMode());
            areaSku.setMinQuantity(1L);
            areaSku.setAttributes(sku.getAttributes());
            areaSku.setUpdatedAt(areaItem.getUpdatedAt());
            areaSku.setUpdatedBy(areaItem.getUpdatedBy());
            areaSku.setOriginalPrice(sku.getOriginalPrice());
            areaSku.setOuterItemId(areaItem.getOuterItemId());
            areaSku.setOuterSkuId(sku.getOuterId());
            areaSkuList.add(areaSku);
        }

        return areaSkuList;
    }

    /**
     * 创建单个区域SKU
     */
    private AreaSku buildAreaSku(AreaItem areaItem, Sku sku) {
        AreaSku areaSku = new AreaSku();
        areaSku.setCreatedAt(new Date());
        areaSku.setOperatorId(areaItem.getOperatorId());
        areaSku.setVendorId(areaItem.getVendorId());
        areaSku.setAreaItemId(areaItem.getId());
        areaSku.setItemId(areaItem.getItemId());
        areaSku.setSkuId(sku.getId());
        areaSku.setName(areaItem.getName());
        areaSku.setImage(areaItem.getMainImage());
        areaSku.setDefaultPrice(sku.getPrice());
        areaSku.setBasePrice(sku.getExtraPrice().get("defaultBasePrice"));
        areaSku.setHqOperatorStatus(areaItem.getHqOperatorStatus());
        areaSku.setVendorStatus(areaItem.getVendorStatus());
        areaSku.setAreaOperatorStatus(areaItem.getAreaOperatorStatus());
        areaSku.setStatus(areaItem.getStatus());
        areaSku.setCooperationMode(areaItem.getCooperationMode());
        areaSku.setLogisticsMode(areaItem.getLogisticsMode());
        areaSku.setMinQuantity(1L);
        areaSku.setAttributes(sku.getAttributes());
        areaSku.setUpdatedAt(new Date());
        areaSku.setUpdatedBy(SYNC_USER);
        areaSku.setOriginalPrice(sku.getOriginalPrice());
        areaSku.setOuterItemId(areaItem.getOuterItemId());
        areaSku.setOuterSkuId(sku.getOuterId());
        return areaSku;
    }

    private String upload(Long itemId,String outId,String outImg){
        try {
            //通过外部商品id查询图片信息 如果图片已存在 不在重新上传oss 节省资源
            ParanaItemOutImg query = new ParanaItemOutImg();
            query.setVendorId(vendorId);
            query.setOuterId(outId);
            List<ParanaItemOutImg> outImgs = paranaItemOutImgDao.listByModel(query);
            if(CollectionUtil.isNotEmpty(outImgs)){
                Map<String, String> map = outImgs.stream().collect(Collectors.toMap(ParanaItemOutImg::getOutImg, ParanaItemOutImg::getOssImg, (k, v) -> k));
                if(map.containsKey(outImg)){
                    return map.get(outImg);
                }
            }
            URL url = new  URL(outImg);
            InputStream inputStream = url.openStream();
            // 获取图片的输入流
            String uuid = UUID.randomUUID().toString().replaceAll("-","");
            // 上传主图
            String mainUrl = eascsAliyunOssFactory.uploadFile(uuid+ "." + getImageFormat(outImg), inputStream);
            // 关闭连接和输入流
            inputStream.close();
            query.setId(idGenerator.nextValue(ParanaItemOutImg.class));
            query.setItemId(itemId);
            query.setTenantId(1L);
            query.setOssImg(mainUrl);
            query.setOutImg(outImg);
            paranaItemOutImgDao.create(query);
            return mainUrl;
        }catch (Exception e){
            log.error("读取候鸟商品主图失败，直接用候鸟图片：{},case:{}",outImg, Throwables.getStackTraceAsString(e));
            return outImg;
        }
    }

    /**
     * 获取图片后缀格式
     * @param image
     * @return
     */
    private String getImageFormat(String image){
        if(StringUtil.isEmpty(image)){
            return "";
        }
        String[] split = image.split("\\.");
        return split[split.length-1];
    }

    /**
     * 库存同步
     * @param skuList SKU列表
     */
    private void syncIpm(List<Sku> skuList) {
        if (CollUtil.isEmpty(skuList)) {
            log.warn("同步库存时参数为空，跳过同步");
            return;
        }

        //查询合作关系，找到供应商对应的仓库
        VendorPartnership partnership = vendorPartnershipReadDomainService.findByVendorIdAndOperatorId(vendorId, CENTER_OPERATOR_ID);
        if (partnership == null) {
            log.error("找不到供应商对应的仓库信息，vendorId={}, operatorId={}", vendorId, CENTER_OPERATOR_ID);
            throw new ServiceException("找不到供应商对应的仓库信息");
        }

        List<ThirdInventoryAdjustParam> params = null;
        try {
            // 过滤掉没有对应候鸟SKU的记录
            params = skuList.stream()
                    .map(sku -> {
                        ThirdInventoryAdjustParam setParam = new ThirdInventoryAdjustParam();
                        setParam.setEntityId(sku.getId().toString());
                        setParam.setEntityType(1);
                        setParam.setRealQuantity(********L);
                        setParam.setWarehouseCode(partnership.getWarehouseCode());
                        setParam.setOperatorId(CENTER_OPERATOR_ID);
                        setParam.setVendorId(vendorId);
                        return setParam;
                    })
                    .collect(Collectors.toList());
            log.info("vendorId :{} ", vendorId);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(params)) {
                log.warn("没有需要同步的库存数据");
                return;
            }

            log.debug("同步库存数量: {}", params.size());

            // 批量处理，每批最多50个
            int batchSize = 50;
            int totalSize = params.size();
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<ThirdInventoryAdjustParam> batchParams = params.subList(i, endIndex);

                ThirdInventoryAdjustRequest request = new ThirdInventoryAdjustRequest();
                request.setThirdInventoryAdjustParams(batchParams);
                inventoryWriteApi.adjust(request);

                log.debug("同步库存批次 {}/{} 完成", (i / batchSize) + 1, (totalSize + batchSize - 1) / batchSize);
            }

            log.info("库存同步成功，共同步 {} 条记录", totalSize);
        } catch (Exception e) {
            String errorMsg = Throwables.getStackTraceAsString(e);
            log.error("同步库存失败，error: {}", errorMsg);
            errorSave("通卡-库存同步失败", "1", errorMsg);
            throw new ServiceException("同步库存失败");
        }
    }

    /**
     * 更新通卡直充商品
     *
     * @param categoryKey 分类组合键
     * @param categoryGoods 该分类下的商品列表
     * @param detailInfos 商品详情列表
     * @param existingItem 现有的商品信息
     */
    private void updateTongKaTopUpItem(String categoryKey, List<DealerGoodsInfo> categoryGoods,
                                      List<GoodsDetailByIdInfo> detailInfos, Item existingItem) {
        try {
            log.info("开始更新通卡直充商品：{}", existingItem.getName());

            // 1. 构建更新请求对象
            TongKaItemRequest updateRequest = buildUpdateItemRequest(categoryKey, categoryGoods, detailInfos, existingItem);

            // 2. 执行数据库更新
            boolean success = tongKaItemManager.updateDb(updateRequest);
            if (!success) {
                throw new ServiceException("更新商品失败");
            }

            // 3. 同步ES
            dumpEs(updateRequest);

            // 4. 同步库存
            List<Sku> allSkus = Lists.newArrayList();
            if (CollUtil.isNotEmpty(updateRequest.getInsertSkuList())) {
                allSkus.addAll(updateRequest.getInsertSkuList());
            }
            if (CollUtil.isNotEmpty(updateRequest.getUpdateSkuList())) {
                allSkus.addAll(updateRequest.getUpdateSkuList());
            }
            if (CollUtil.isNotEmpty(allSkus)) {
                syncIpm(allSkus);
            }

            log.info("成功更新通卡直充商品：{}，ID：{}", existingItem.getName(), existingItem.getId());

        } catch (Exception e) {
            log.error("更新通卡直充商品失败：{}", existingItem.getName(), e);
            throw new ServiceException("更新商品失败: " + e.getMessage());
        }
    }

    /**
     * 更新单个通卡直充商品（一对一模式）
     *
     * @param goods 商品信息
     * @param detailInfo 商品详情信息
     * @param existingItem 现有的商品信息
     */
    private void updateSingleTongKaItem(DealerGoodsInfo goods, GoodsDetailByIdInfo detailInfo, Item existingItem) {
        try {
            log.info("开始更新通卡直充商品：{}", existingItem.getName());

            // 1. 构建更新请求对象
            TongKaItemRequest updateRequest = buildSingleUpdateItemRequest(goods, detailInfo, existingItem);

            // 2. 处理选品库SKU、分销商SKU和消息类型
            tongKaSkuPak(existingItem, updateRequest);

            // 3. 执行数据库更新
            boolean success = tongKaItemManager.updateDb(updateRequest);
            if (!success) {
                throw new ServiceException("更新商品失败");
            }

            // 4. 同步ES
            dumpEs(updateRequest);

            // 5. 同步库存
            List<Sku> allSkus = Lists.newArrayList();
            if (CollUtil.isNotEmpty(updateRequest.getInsertSkuList())) {
                allSkus.addAll(updateRequest.getInsertSkuList());
            }
            if (CollUtil.isNotEmpty(updateRequest.getUpdateSkuList())) {
                allSkus.addAll(updateRequest.getUpdateSkuList());
            }
            if (CollUtil.isNotEmpty(allSkus)) {
                syncIpm(allSkus);
            }

            // 6. 发送消息通知
            tongKaItemManager.sendMessage(updateRequest);

            log.info("成功更新通卡直充商品：{}，ID：{}", existingItem.getName(), existingItem.getId());

        } catch (Exception e) {
            log.error("更新通卡直充商品失败：{}", existingItem.getName(), e);
            throw new ServiceException("更新商品失败: " + e.getMessage());
        }
    }

    /**
     * 构建商品更新请求对象
     *
     * @param categoryKey 分类组合键
     * @param categoryGoods 该分类下的商品列表
     * @param detailInfos 商品详情列表
     * @param existingItem 现有的商品信息
     * @return 更新请求对象
     */
    private TongKaItemRequest buildUpdateItemRequest(String categoryKey, List<DealerGoodsInfo> categoryGoods,
                                                    List<GoodsDetailByIdInfo> detailInfos, Item existingItem) {

        // 因为直充商品是按照分类分组的需要按照goodsList聚合状态，判断整体商品是否上架或者下架
        boolean isOnShelf = categoryGoods.stream().anyMatch(goods -> "1".equals(goods.getStatus()));
        ItemStatus itemStatus = isOnShelf ? ItemStatus.ON_SHELF : ItemStatus.OFF_SHELF;

        // 使用第一个商品的信息作为Item基础信息
        DealerGoodsInfo firstGoods = categoryGoods.get(0);
        GoodsDetailByIdInfo goodsDetailByIdInfo = detailInfos.get(0);

        // 1. 更新商品基本信息
        updateItemBasicInfo(existingItem, categoryKey, firstGoods, itemStatus);

        // 2. 更新商品图片
        itemImgPack(existingItem, existingItem.getOuterId(), goodsDetailByIdInfo);

        // 3. 更新商品详情
        ItemDetail existingItemDetail = itemDetailDao.findByItemId(existingItem.getId(), DEFAULT_TENANT_ID);
        if (existingItemDetail == null) {
            existingItemDetail = buildItemDetail(existingItem, detailInfos);
//            generateHelper.generateId(existingItemDetail);
        } else {
            updateItemDetail(existingItemDetail, detailInfos);
        }

        // 4. 分析SKU差异并构建SKU列表
        SkuDiffResult skuDiffResult = compareAndBuildSkuLists(existingItem, categoryGoods, detailInfos);

        // 5. 更新商品MD5信息
        List<Sku> allSkus = Lists.newArrayList();
        if (CollUtil.isNotEmpty(skuDiffResult.getInsertSkuList())) {
            allSkus.addAll(skuDiffResult.getInsertSkuList());
        }
        if (CollUtil.isNotEmpty(skuDiffResult.getUpdateSkuList())) {
            allSkus.addAll(skuDiffResult.getUpdateSkuList());
        }
        // 查询现有的未删除SKU
        List<Sku> existingSkus = skuReadDomainService.findByItemId(existingItem.getId(), DEFAULT_TENANT_ID, null, null);
        if (CollUtil.isNotEmpty(existingSkus)) {
            allSkus.addAll(existingSkus.stream()
                    .filter(sku -> sku.getStatus() != -1)
                    .collect(Collectors.toList()));
        }

        existingItem.setMd5Info(itemMd5Describer.describe(existingItem, allSkus, existingItemDetail));

        // 6. 初始化商品上的sku属性
        initSkuAttributes(existingItem, allSkus);

        // 7. 处理区域商品信息
        AreaItem existingAreaItem = areaItemDao.findByOperatorIdAndItemId(CENTER_OPERATOR_ID, existingItem.getId());
        if (existingAreaItem == null) {
            existingAreaItem = buildAreaItem(existingItem, itemStatus);
            generateHelper.generateId(existingAreaItem);
        } else {
            existingAreaItem.setStatus(itemStatus.getValue());
            existingAreaItem.setUpdatedAt(new Date());
        }

        // 8. 分析区域SKU差异
        AreaSkuDiffResult areaSkuDiffResult = compareAndBuildAreaSkuLists(existingAreaItem, skuDiffResult);

        // 9. 构建更新请求对象
        TongKaItemRequest updateRequest = new TongKaItemRequest();
        updateRequest.setItem(existingItem);
        updateRequest.setItemDetail(existingItemDetail);
        updateRequest.setAreaItem(existingAreaItem);
        updateRequest.setUpdatedBy(SYNC_USER);

        // 设置SKU相关列表
        updateRequest.setInsertSkuList(skuDiffResult.getInsertSkuList());
        updateRequest.setUpdateSkuList(skuDiffResult.getUpdateSkuList());
        updateRequest.setDeleteSkuList(skuDiffResult.getDeleteSkuList());

        // 设置区域SKU相关列表
        updateRequest.setInsertAreaSkuList(areaSkuDiffResult.getInsertAreaSkuList());
        updateRequest.setUpdateAreaSkuList(areaSkuDiffResult.getUpdateAreaSkuList());
        updateRequest.setDeleteAreaSkuList(areaSkuDiffResult.getDeleteAreaSkuList());

        return updateRequest;
    }

    /**
     * 构建单个商品更新请求对象（一对一模式）
     *
     * @param goods 商品信息
     * @param detailInfo 商品详情信息
     * @param existingItem 现有的商品信息
     * @return 更新请求对象
     */
    private TongKaItemRequest buildSingleUpdateItemRequest(DealerGoodsInfo goods, GoodsDetailByIdInfo detailInfo, Item existingItem) {
        // 商品状态判断（直接使用商品状态）
        ItemStatus itemStatus = "1".equals(goods.getStatus()) ? ItemStatus.ON_SHELF : ItemStatus.OFF_SHELF;

        // 1. 更新商品基本信息
        updateSingleItemBasicInfo(existingItem, goods);

        // 2. 更新商品图片
        itemImgPack(existingItem, existingItem.getOuterId(), detailInfo);

        // 3. 更新商品详情
        ItemDetail existingItemDetail = itemDetailDao.findByItemId(existingItem.getId(), DEFAULT_TENANT_ID);
        if (existingItemDetail == null) {
            existingItemDetail = buildSingleItemDetail(existingItem, detailInfo);
        } else {
            updateSingleItemDetail(existingItemDetail, detailInfo);
        }

        // 4. 分析SKU差异（一对一模式下只有一个SKU）
        SkuDiffResult skuDiffResult = compareAndBuildSingleSkuList(existingItem, goods, detailInfo);

        // 5. 更新商品MD5信息
        List<Sku> allSkus = Lists.newArrayList();
        if (CollUtil.isNotEmpty(skuDiffResult.getInsertSkuList())) {
            allSkus.addAll(skuDiffResult.getInsertSkuList());
        }
        if (CollUtil.isNotEmpty(skuDiffResult.getUpdateSkuList())) {
            allSkus.addAll(skuDiffResult.getUpdateSkuList());
        }
        // 查询现有的未删除SKU
        List<Sku> existingSkus = skuReadDomainService.findByItemId(existingItem.getId(), DEFAULT_TENANT_ID, null, null);
        if (CollUtil.isNotEmpty(existingSkus)) {
            allSkus.addAll(existingSkus.stream()
                    .filter(sku -> sku.getStatus() != -1)
                    .collect(Collectors.toList()));
        }

        existingItem.setMd5Info(itemMd5Describer.describe(existingItem, allSkus, existingItemDetail));

        // 6. 初始化商品上的sku属性
        initSkuAttributes(existingItem, allSkus);

        // 7. 处理区域商品信息
        AreaItem existingAreaItem = areaItemDao.findByOperatorIdAndItemId(CENTER_OPERATOR_ID, existingItem.getId());
        if (existingAreaItem == null) {
            existingAreaItem = buildAreaItem(existingItem, itemStatus);
            generateHelper.generateId(existingAreaItem);
        } else {
            existingAreaItem.setStatus(itemStatus.getValue());
            existingAreaItem.setUpdatedAt(new Date());
        }

        // 8. 分析区域SKU差异
        AreaSkuDiffResult areaSkuDiffResult = compareAndBuildAreaSkuLists(existingAreaItem, skuDiffResult);

        // 9. 构建更新请求对象
        TongKaItemRequest updateRequest = new TongKaItemRequest();
        updateRequest.setItem(existingItem);
        updateRequest.setItemDetail(existingItemDetail);
        updateRequest.setAreaItem(existingAreaItem);
        updateRequest.setUpdatedBy(SYNC_USER);

        // 设置SKU相关列表
        updateRequest.setInsertSkuList(skuDiffResult.getInsertSkuList());
        updateRequest.setUpdateSkuList(skuDiffResult.getUpdateSkuList());
        updateRequest.setDeleteSkuList(skuDiffResult.getDeleteSkuList());

        // 设置区域SKU相关列表
        updateRequest.setInsertAreaSkuList(areaSkuDiffResult.getInsertAreaSkuList());
        updateRequest.setUpdateAreaSkuList(areaSkuDiffResult.getUpdateAreaSkuList());
        updateRequest.setDeleteAreaSkuList(areaSkuDiffResult.getDeleteAreaSkuList());

        cacheItemById.remove(existingItem.getId());
        cacheItemDetailById.remove(existingItem.getId());
        cacheSkuById.remove(AssembleDataUtils.list2set(allSkus, BaseSku::getId));

        return updateRequest;
    }

    /**
     * 更新商品基本信息
     */
    private void updateItemBasicInfo(Item existingItem, String categoryKey, DealerGoodsInfo firstGoods, ItemStatus itemStatus) {
        // 使用第一个商品的信息作为商品名称
        String itemName = firstGoods.getCategoryTwoName();
        if (!Objects.equals(existingItem.getName(), itemName)) {
            existingItem.setName(itemName);
        }

        // 更新商品状态
        if (!Objects.equals(existingItem.getStatus(), itemStatus.getValue())) {
            existingItem.setStatus(itemStatus.getValue());
        }

        // 更新修改时间
        existingItem.setUpdatedAt(new Date());
    }

    /**
     * 更新商品基本信息（一对一模式）
     */
    private void updateSingleItemBasicInfo(Item existingItem, DealerGoodsInfo goods) {
        // 使用商品标题作为商品名称
        String itemName = goods.getCategoryTwoName() + "-" + goods.getGoodsTitle();
        if (!Objects.equals(existingItem.getName(), itemName)) {
            existingItem.setName(itemName);
        }

        // 更新修改时间
        existingItem.setUpdatedAt(new Date());
    }

    /**
     * 更新商品详情
     */
    private void updateItemDetail(ItemDetail existingItemDetail, List<GoodsDetailByIdInfo> detailInfos) {
        GoodsDetailByIdInfo goodsDetailByIdInfo = detailInfos.get(0);
        // 设置商品详情
        StringBuilder detailBuilder = new StringBuilder();
        detailBuilder.append("<h3>购买须知</h3>");
        detailBuilder.append("<p>")
                .append(goodsDetailByIdInfo.getGoodsNeedKnow())
                .append("</p>")
        ;
        detailBuilder.append("<h3>使用说明</h3>");
        detailBuilder.append("<p>")
                .append(goodsDetailByIdInfo.getGoodsInstruction())
                .append("</p>")
        ;

        ItemDetailContentParam itemDetailContentParam = new ItemDetailContentParam();
        itemDetailContentParam.setTitle("商品详情");


        // 添加详细信息
        if (!CollectionUtils.isEmpty(detailInfos)) {
            for (GoodsDetailByIdInfo detailInfo : detailInfos) {
                if (StringUtils.hasText(detailInfo.getBrandName())) {
                    detailBuilder.append("<p>品牌：").append(detailInfo.getBrandName()).append("</p>");
                    break; // 只显示第一个品牌信息
                }
            }
        }
        itemDetailContentParam.setContent(detailBuilder.toString());

        existingItemDetail.setPcDetail(JSONUtil.toJsonStr(itemDetailContentParam));
        existingItemDetail.setWapDetail(JSONUtil.toJsonStr(itemDetailContentParam));
    }

    /**
     * 更新商品详情（一对一模式）
     */
    private void updateSingleItemDetail(ItemDetail existingItemDetail, GoodsDetailByIdInfo detailInfo) {
        // 设置商品详情
        StringBuilder detailBuilder = new StringBuilder();
        detailBuilder.append("<h3>购买须知</h3>");
        detailBuilder.append("<p>")
                .append(detailInfo.getGoodsNeedKnow())
                .append("</p>");
        detailBuilder.append("<h3>使用说明</h3>");
        detailBuilder.append("<p>")
                .append(detailInfo.getGoodsInstruction())
                .append("</p>");

        // 添加品牌信息
        if (StringUtils.hasText(detailInfo.getBrandName())) {
            detailBuilder.append("<p>品牌：").append(detailInfo.getBrandName()).append("</p>");
        }

        ItemDetailContentParam itemDetailContentParam = new ItemDetailContentParam();
        itemDetailContentParam.setTitle("商品详情");
        itemDetailContentParam.setContent(detailBuilder.toString());

        existingItemDetail.setPcDetail(JSONUtil.toJsonStr(itemDetailContentParam));
        existingItemDetail.setWapDetail(JSONUtil.toJsonStr(itemDetailContentParam));
    }

    /**
     * 比较并构建SKU列表
     */
    private SkuDiffResult compareAndBuildSkuLists(Item existingItem, List<DealerGoodsInfo> categoryGoods,
                                                 List<GoodsDetailByIdInfo> detailInfos) {
        SkuDiffResult result = new SkuDiffResult();

        // 1. 查询现有SKU列表
        List<Sku> existingSkus = skuReadDomainService.findByItemId(existingItem.getId(), DEFAULT_TENANT_ID, null, null);
        Map<String, Sku> existingSkuMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(existingSkus)) {
            existingSkuMap = existingSkus.stream()
                    .filter(sku -> sku.getStatus() != -1) // 过滤已删除的SKU
                    .collect(Collectors.toMap(Sku::getOuterId, Function.identity(), (k1, k2) -> k1));
        }

        // 2. 构建新的SKU列表
        List<Sku> newSkus = buildSkuList(existingItem, categoryGoods, detailInfos);
        Map<String, Sku> newSkuMap = newSkus.stream()
                .collect(Collectors.toMap(Sku::getOuterId, Function.identity(), (k1, k2) -> k1));

        // 3. 分析差异
        for (Sku newSku : newSkus) {
            if (existingSkuMap.containsKey(newSku.getOuterId())) {
                // 存在的SKU，检查是否需要更新
                Sku existingSku = existingSkuMap.get(newSku.getOuterId());
                if (isSkuNeedUpdate(existingSku, newSku)) {
                    // 需要更新
                    newSku.setId(existingSku.getId());
                    newSku.setVersion(existingSku.getVersion());
                    result.getUpdateSkuList().add(newSku);
                    log.info("SKU需要更新：{}", newSku.getOuterId());
                }
            } else {
                // 新增的SKU
                generateHelper.generateId(newSku);
                result.getInsertSkuList().add(newSku);
                log.info("新增SKU：{}", newSku.getOuterId());
            }
        }

        // 4. 查找需要删除的SKU
        for (Sku existingSku : existingSkuMap.values()) {
            if (!newSkuMap.containsKey(existingSku.getOuterId())) {
                // 需要删除的SKU
                existingSku.setStatus(-1);
                existingSku.setUpdatedAt(new Date());
                result.getDeleteSkuList().add(existingSku);
                log.info("删除SKU：{}", existingSku.getOuterId());
            }
        }

        log.info("SKU差异分析完成：新增{}个，更新{}个，删除{}个",
                result.getInsertSkuList().size(),
                result.getUpdateSkuList().size(),
                result.getDeleteSkuList().size());

        return result;
    }

    /**
     * 比较并构建单个SKU列表（一对一模式）
     */
    private SkuDiffResult compareAndBuildSingleSkuList(Item existingItem, DealerGoodsInfo goods, GoodsDetailByIdInfo detailInfo) {
        SkuDiffResult result = new SkuDiffResult();

        // 1. 查询现有SKU列表
        List<Sku> existingSkus = skuReadDomainService.findByItemId(existingItem.getId(), DEFAULT_TENANT_ID, null, null);
        Map<String, Sku> existingSkuMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(existingSkus)) {
            existingSkuMap = existingSkus.stream()
                    .collect(Collectors.toMap(Sku::getOuterId, Function.identity(), (k1, k2) -> k1));
        }

        // 2. 构建新的SKU
        Sku newSku = buildSingleSku(existingItem, goods, detailInfo);

        // 3. 分析差异
        if (existingSkuMap.containsKey(newSku.getOuterId())) {
            // 存在的SKU，检查是否需要更新
            Sku existingSku = existingSkuMap.get(newSku.getOuterId());
            if (isSkuNeedUpdate(existingSku, newSku)) {
                // 需要更新
                newSku.setId(existingSku.getId());
                newSku.setVersion(existingSku.getVersion());
                result.getUpdateSkuList().add(newSku);
                log.info("SKU需要更新：{}", newSku.getOuterId());
            }
        } else {
            // 新增的SKU
            generateHelper.generateId(newSku);
            result.getInsertSkuList().add(newSku);
            log.info("新增SKU：{}", newSku.getOuterId());
        }

        // 4. 查找需要删除的SKU（一对一模式下，如果有其他SKU则删除）
        for (Sku existingSku : existingSkuMap.values()) {
            if (!Objects.equals(existingSku.getOuterId(), goods.getGoodsId())) {
                // 需要删除的SKU
                existingSku.setStatus(-1);
                existingSku.setUpdatedAt(new Date());
                result.getDeleteSkuList().add(existingSku);
                log.info("删除SKU：{}", existingSku.getOuterId());
            }
        }

        log.info("SKU差异分析完成（一对一模式）：新增{}个，更新{}个，删除{}个",
                result.getInsertSkuList().size(),
                result.getUpdateSkuList().size(),
                result.getDeleteSkuList().size());

        return result;
    }

    /**
     * 判断SKU是否需要更新
     */
    private boolean isSkuNeedUpdate(Sku existingSku, Sku newSku) {
        // 比较价格
        if (!Objects.equals(existingSku.getPrice(), newSku.getPrice())) {
            log.info("SKU价格变化：{} {} -> {}", existingSku.getOuterId(), existingSku.getPrice(), newSku.getPrice());
            return true;
        }

        // 比较原价
        if (!Objects.equals(existingSku.getOriginalPrice(), newSku.getOriginalPrice())) {
            log.info("SKU原价变化：{} {} -> {}", existingSku.getOuterId(), existingSku.getOriginalPrice(), newSku.getOriginalPrice());
            return true;
        }

        // 比较状态
        if (!Objects.equals(existingSku.getStatus(), newSku.getStatus())) {
            log.info("SKU状态变化：{} {} -> {}", existingSku.getOuterId(), existingSku.getStatus(), newSku.getStatus());
            return true;
        }

        // 比较名称
        if (!Objects.equals(existingSku.getName(), newSku.getName())) {
            log.info("SKU名称变化：{} {} -> {}", existingSku.getOuterId(), existingSku.getName(), newSku.getName());
            return true;
        }

        // 比较其他价格
        if (!Objects.equals(existingSku.getExtraPrice(), newSku.getExtraPrice())) {
            log.info("SKU其他价格变化：{} {} -> {}", existingSku.getOuterId(), existingSku.getExtraPrice(), newSku.getExtraPrice());
            return true;
        }

        return false;
    }

    /**
     * 比较并构建区域SKU列表
     */
    private AreaSkuDiffResult compareAndBuildAreaSkuLists(AreaItem areaItem, SkuDiffResult skuDiffResult) {
        AreaSkuDiffResult result = new AreaSkuDiffResult();

        // 1. 查询现有区域SKU列表
        List<AreaSku> existingAreaSkus = areaSkuDao.findByOperatorIdAndItemId(areaItem.getOperatorId(), areaItem.getItemId());
        Map<Long, AreaSku> existingAreaSkuMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(existingAreaSkus)) {
            existingAreaSkuMap = existingAreaSkus.stream()
                    .filter(areaSku -> areaSku.getStatus() != -1) // 过滤已删除的区域SKU
                    .collect(Collectors.toMap(AreaSku::getSkuId, Function.identity(), (k1, k2) -> k1));
        }

        // 2. 处理新增的SKU对应的区域SKU
        if (CollUtil.isNotEmpty(skuDiffResult.getInsertSkuList())) {
            for (Sku sku : skuDiffResult.getInsertSkuList()) {
                AreaSku areaSku = buildAreaSku(areaItem, sku);
                generateHelper.generateId(areaSku);
                result.getInsertAreaSkuList().add(areaSku);
            }
        }

        // 3. 处理更新的SKU对应的区域SKU
        if (CollUtil.isNotEmpty(skuDiffResult.getUpdateSkuList())) {
            for (Sku sku : skuDiffResult.getUpdateSkuList()) {
                if (existingAreaSkuMap.containsKey(sku.getId())) {
                    AreaSku existingAreaSku = existingAreaSkuMap.get(sku.getId());
                    AreaSku newAreaSku = buildAreaSku(areaItem, sku);
                    newAreaSku.setId(existingAreaSku.getId());
                    result.getUpdateAreaSkuList().add(newAreaSku);
                } else {
                    // 如果区域SKU不存在，则新增
                    AreaSku areaSku = buildAreaSku(areaItem, sku);
                    generateHelper.generateId(areaSku);
                    result.getInsertAreaSkuList().add(areaSku);
                }
            }
        }

        // 4. 处理删除的SKU对应的区域SKU
        if (CollUtil.isNotEmpty(skuDiffResult.getDeleteSkuList())) {
            for (Sku sku : skuDiffResult.getDeleteSkuList()) {
                if (existingAreaSkuMap.containsKey(sku.getId())) {
                    AreaSku existingAreaSku = existingAreaSkuMap.get(sku.getId());
                    existingAreaSku.setStatus(-1);
                    existingAreaSku.setUpdatedAt(new Date());
                    result.getDeleteAreaSkuList().add(existingAreaSku);
                }
            }
        }

        log.info("区域SKU差异分析完成：新增{}个，更新{}个，删除{}个",
                result.getInsertAreaSkuList().size(),
                result.getUpdateAreaSkuList().size(),
                result.getDeleteAreaSkuList().size());

        return result;
    }

    /**
     * SKU差异分析结果
     */
    private static class SkuDiffResult {
        private List<Sku> insertSkuList = Lists.newArrayList();
        private List<Sku> updateSkuList = Lists.newArrayList();
        private List<Sku> deleteSkuList = Lists.newArrayList();

        public List<Sku> getInsertSkuList() { return insertSkuList; }
        public void setInsertSkuList(List<Sku> insertSkuList) { this.insertSkuList = insertSkuList; }
        public List<Sku> getUpdateSkuList() { return updateSkuList; }
        public void setUpdateSkuList(List<Sku> updateSkuList) { this.updateSkuList = updateSkuList; }
        public List<Sku> getDeleteSkuList() { return deleteSkuList; }
        public void setDeleteSkuList(List<Sku> deleteSkuList) { this.deleteSkuList = deleteSkuList; }
    }

    /**
     * 区域SKU差异分析结果
     */
    private static class AreaSkuDiffResult {
        private List<AreaSku> insertAreaSkuList = Lists.newArrayList();
        private List<AreaSku> updateAreaSkuList = Lists.newArrayList();
        private List<AreaSku> deleteAreaSkuList = Lists.newArrayList();

        public List<AreaSku> getInsertAreaSkuList() { return insertAreaSkuList; }
        public void setInsertAreaSkuList(List<AreaSku> insertAreaSkuList) { this.insertAreaSkuList = insertAreaSkuList; }
        public List<AreaSku> getUpdateAreaSkuList() { return updateAreaSkuList; }
        public void setUpdateAreaSkuList(List<AreaSku> updateAreaSkuList) { this.updateAreaSkuList = updateAreaSkuList; }
        public List<AreaSku> getDeleteAreaSkuList() { return deleteAreaSkuList; }
        public void setDeleteAreaSkuList(List<AreaSku> deleteAreaSkuList) { this.deleteAreaSkuList = deleteAreaSkuList; }
    }

    private void errorSave(String apiName, String spu, String errorMsg) {
        StarlinkReqErrorLogCreateRequest starlinkReqErrorLogCreateRequest = new StarlinkReqErrorLogCreateRequest();
        starlinkReqErrorLogCreateRequest.setApiName(apiName);
        starlinkReqErrorLogCreateRequest.setRequestUrl(spu);
        // 获取错误信息 errorMsg 2000个字符
        starlinkReqErrorLogCreateRequest.setRequestBody(errorMsg.substring(0, Math.min(errorMsg.length(), 2000)));
        starlinkReqErrorLogWriteFacade.create(starlinkReqErrorLogCreateRequest);
    }

    /**
     * 通卡SKU处理逻辑，参考HnApiServiceImpl.hnSkuPak风格
     * 处理选品库SKU、分销商SKU和消息类型
     */
    private void tongKaSkuPak(Item item, TongKaItemRequest request) {
        try {
            log.info("开始处理通卡SKU相关字段，商品ID：{}", item.getId());

            // 初始化消息类型
            request.getMessageTypes().add(4); // 信息变更

            // 查询区域商品信息，获取选品库ID
            Set<Long> choiceLotLibIds = Sets.newHashSet();
            List<AreaItem> areaItemList = areaItemDao.findByItemId(item.getId());
            for (AreaItem areaItem : areaItemList) {
                if (areaItem.getChoiceLotLibIds() != null && !areaItem.getChoiceLotLibIds().isEmpty()) {
                    Set<Long> ids = Arrays.stream(areaItem.getChoiceLotLibIds().replace("#", "").split(","))
                            .filter(id -> !id.trim().isEmpty())
                            .map(Long::parseLong)
                            .collect(Collectors.toSet());
                    choiceLotLibIds.addAll(ids);
                }
            }

            if (choiceLotLibIds.isEmpty()) {
                log.info("商品未加入选品库，跳过选品库SKU处理，商品ID：{}", item.getId());
                return;
            }

            // 处理选品库SKU
            processChoiceLotLibSku(item, request, choiceLotLibIds);

            // 处理分销商SKU
            processDistributorSku(item, request, choiceLotLibIds);

            // 查询分销商商品信息用于发送消息
            processDistributorItemLib(item, request, choiceLotLibIds);

            // 重新计算成本价
            recalculateSkuPrices(item, request, choiceLotLibIds);

            log.info("通卡SKU相关字段处理完成，商品ID：{}", item.getId());

        } catch (Exception e) {
            log.error("处理通卡SKU相关字段失败，商品ID：{}", item.getId(), e);
        }
    }

    /**
     * 处理选品库SKU
     */
    private void processChoiceLotLibSku(Item item, TongKaItemRequest request, Set<Long> choiceLotLibIds) {
        try {
            // 查询现有的选品库SKU
            Map<String, Object> params = Maps.newHashMap();
            params.put("itemId", item.getId());
            params.put("choiceLotLibIds", choiceLotLibIds);
            List<ChoiceLotLibSkuModel> existingChoiceSkus = choiceLotLibSkuDao.findChoiceLotLibSkuBySkuIds(params);

            Map<String, ChoiceLotLibSkuModel> existingSkuMap = Maps.newHashMap();
            if (!existingChoiceSkus.isEmpty()) {
                existingSkuMap = existingChoiceSkus.stream()
                        .collect(Collectors.toMap(
                                sku -> sku.getChoiceLotLibId() + "_" + sku.getSkuId(),
                                Function.identity(),
                                (k1, k2) -> k1
                        ));
            }

            // 获取当前商品的SKU列表
            List<Sku> currentSkus = request.getInsertSkuList() != null ? request.getInsertSkuList() :
                                   (request.getUpdateSkuList() != null ? request.getUpdateSkuList() : request.getSkuList());

            if (currentSkus == null || currentSkus.isEmpty()) {
                return;
            }

            // 查询加价率配置
            Map<String, Object> markupParams = Maps.newHashMap();
            markupParams.put("choiceLotLibIds", choiceLotLibIds);
            List<ChoiceLotLibMarkup> markupList = choiceLotLibMarkupDao.getMarkupByChoiceLotId(markupParams);

            List<ChoiceLotLibSkuModel> insertChoiceSkus = Lists.newArrayList();
            List<ChoiceLotLibSkuModel> updateChoiceSkus = Lists.newArrayList();

            for (Long choiceLotLibId : choiceLotLibIds) {
                for (Sku sku : currentSkus) {
                    String key = choiceLotLibId + "_" + sku.getId();

                    if (existingSkuMap.containsKey(key)) {
                        // 更新现有记录
                        ChoiceLotLibSkuModel existingSku = existingSkuMap.get(key);

                        // 检查价格变化 - 需要在更新前检查，使用原有SKU的价格信息
                        if (hasSkuPriceChangedWithOldSku(sku)) {
                            request.getMessageTypes().add(7); // 价格变更
                            request.getPriceSkuIds().add(sku.getId());
                        }

                        updateChoiceLotLibSkuModel(existingSku, sku, markupList);
                        updateChoiceSkus.add(existingSku);
                    } else {
                        // 新增记录
                        ChoiceLotLibSkuModel newChoiceSku = buildChoiceLotLibSkuModel(choiceLotLibId, sku, markupList);
                        insertChoiceSkus.add(newChoiceSku);
                        request.getMessageTypes().add(5); // 规格变动
                    }
                }
            }

            request.setInsertChoiceLotSkuList(insertChoiceSkus);
            request.setUpdateChoiceLotSkuList(updateChoiceSkus);

            log.info("选品库SKU处理完成，新增：{}，更新：{}", insertChoiceSkus.size(), updateChoiceSkus.size());

        } catch (Exception e) {
            log.error("处理选品库SKU失败", e);
        }
    }

    /**
     * 处理分销商SKU
     */
    private void processDistributorSku(Item item, TongKaItemRequest request, Set<Long> choiceLotLibIds) {
        try {
            // 查询现有的分销商SKU
            Map<String, Object> params = Maps.newHashMap();
            params.put("itemId", item.getId());
            params.put("choiceLotLibIds", choiceLotLibIds);
            List<DistributorSkuLibModel> existingDistributorSkus = distributorSkuLibDao.listByMap(params);

            List<DistributorSkuLibModel> insertDistributorSkus = Lists.newArrayList();
            List<DistributorSkuLibModel> deleteDistributorSkus = Lists.newArrayList();

            // 如果有新增的选品库SKU，需要同步创建分销商SKU
            if (request.getInsertChoiceLotSkuList() != null && !request.getInsertChoiceLotSkuList().isEmpty()) {
                for (ChoiceLotLibSkuModel choiceSku : request.getInsertChoiceLotSkuList()) {
                    // 查询该选品库的分销商
                    Map<String, Object> distributorItemParams = Maps.newHashMap();
                    distributorItemParams.put("choiceLotLibId", choiceSku.getChoiceLotLibId());
                    distributorItemParams.put("itemId", choiceSku.getItemId());
                    List<DistributorItemLibModel> distributorItems = distributorItemLibDao.listByMap(distributorItemParams);

                    for (DistributorItemLibModel distributorItem : distributorItems) {
                        DistributorSkuLibModel distributorSku = new DistributorSkuLibModel();
                        distributorSku.setId(generateHelper.generateId(distributorSku));
                        distributorSku.setDistributorId(distributorItem.getDistributorId());
                        distributorSku.setChoiceLotLibId(choiceSku.getChoiceLotLibId());
                        distributorSku.setItemId(choiceSku.getItemId());
                        distributorSku.setSkuId(choiceSku.getSkuId());
                        distributorSku.setCreatedBy(SYNC_USER);
                        distributorSku.setUpdatedBy(SYNC_USER);

                        insertDistributorSkus.add(distributorSku);
                    }
                }
            }

            // 如果有删除的SKU，需要删除对应的分销商SKU
            if (request.getDeleteSkuList() != null && !request.getDeleteSkuList().isEmpty()) {
                Set<Long> deleteSkuIds = request.getDeleteSkuList().stream()
                        .map(Sku::getId)
                        .collect(Collectors.toSet());

                deleteDistributorSkus = existingDistributorSkus.stream()
                        .filter(distributorSku -> deleteSkuIds.contains(distributorSku.getSkuId()))
                        .collect(Collectors.toList());
            }

            request.setInsertDistributorSkuList(insertDistributorSkus);
            request.setDeleteDistributorSkuList(deleteDistributorSkus);

            log.info("分销商SKU处理完成，新增：{}，删除：{}", insertDistributorSkus.size(), deleteDistributorSkus.size());

        } catch (Exception e) {
            log.error("处理分销商SKU失败", e);
        }
    }

    /**
     * 处理分销商商品信息，用于发送消息
     */
    private void processDistributorItemLib(Item item, TongKaItemRequest request, Set<Long> choiceLotLibIds) {
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("itemId", item.getId());
            params.put("choiceLotLibIds", choiceLotLibIds);
            List<DistributorItemLibModel> distributorItemLibList = distributorItemLibDao.listByMap(params);

            request.setDistributorItemLibList(distributorItemLibList);

            log.info("查询到{}个分销商商品信息用于发送消息", distributorItemLibList.size());

        } catch (Exception e) {
            log.error("处理分销商商品信息失败", e);
        }
    }

    /**
     * 重新计算SKU成本价，参考HnApiServiceImpl的处理逻辑
     */
    private void recalculateSkuPrices(Item item, TongKaItemRequest request, Set<Long> choiceLotLibIds) {
        try {
            log.info("开始重新计算SKU成本价，商品ID：{}", item.getId());

            // 查询区域商品信息
            List<AreaItem> areaItemList = areaItemDao.findByItemId(item.getId());

            // 查询加价率配置
            Map<String, Object> markupParams = Maps.newHashMap();
            markupParams.put("choiceLotLibIds", choiceLotLibIds);
            List<ChoiceLotLibMarkup> markupList = choiceLotLibMarkupDao.getMarkupByChoiceLotId(markupParams);
            Map<Long, List<ChoiceLotLibMarkup>> choiceMarkupMap = markupList.stream()
                    .collect(Collectors.groupingBy(ChoiceLotLibMarkup::getChoiceLotLibId));

            // 处理新增的区域SKU
            if (request.getInsertAreaSkuList() != null && !request.getInsertAreaSkuList().isEmpty()) {
                request.getInsertAreaSkuList().forEach(generateHelper::generateId);
                // 运营商品sku重新计算成本价
                areaSkuPricePack(request.getInsertAreaSkuList(), areaItemList, choiceMarkupMap);
                // 选品库sku重新计算成本价
                if (request.getInsertChoiceLotSkuList() != null && !request.getInsertChoiceLotSkuList().isEmpty()) {
                    request.getInsertChoiceLotSkuList().forEach(generateHelper::generateId);
                    choiceLotLibSkuPricePack(request.getInsertChoiceLotSkuList(), request.getInsertAreaSkuList(), choiceMarkupMap);
                }
            }

            // 处理更新的区域SKU
            if (request.getUpdateAreaSkuList() != null && !request.getUpdateAreaSkuList().isEmpty()) {
                // 运营商品sku重新计算成本价
                areaSkuPricePack(request.getUpdateAreaSkuList(), areaItemList, choiceMarkupMap);
                // 选品库sku重新计算成本价
                if (request.getUpdateChoiceLotSkuList() != null && !request.getUpdateChoiceLotSkuList().isEmpty()) {
                    choiceLotLibSkuPricePack(request.getUpdateChoiceLotSkuList(), request.getUpdateAreaSkuList(), choiceMarkupMap);
                }
            }

            log.info("SKU成本价重新计算完成，商品ID：{}", item.getId());

        } catch (Exception e) {
            log.error("重新计算SKU成本价失败，商品ID：{}", item.getId(), e);
        }
    }

    /**
     * 区域商品SKU成本价根据选品库计算，参考HnApiServiceImpl.areaSkuPricePack
     */
    private void areaSkuPricePack(List<AreaSku> areaSkuList,
                                  List<AreaItem> areaItemList,
                                  Map<Long, List<ChoiceLotLibMarkup>> choiceMarkupMap) {
        try {
            Map<String, AreaItem> areaItemMap = areaItemList.stream()
                    .collect(Collectors.toMap(m -> m.getOperatorId() + "_" + m.getItemId(),
                            Function.identity(), (k1, k2) -> k1));

            for (AreaSku areaSku : areaSkuList) {
                // 非平台的重新计算成本价
                if (areaSku.getOperatorId() == 1) {
                    continue;
                }

                AreaItem areaItem = areaItemMap.get(areaSku.getOperatorId() + "_" + areaSku.getItemId());
                if (areaItem == null || areaItem.getSourceChoiceLotLibId() == null) {
                    continue;
                }

                List<ChoiceLotLibMarkup> markupList = choiceMarkupMap.get(areaItem.getSourceChoiceLotLibId());
                if (markupList == null || markupList.isEmpty()) {
                    continue;
                }

                // 匹配最佳加价率
                ChoiceLotLibMarkup choiceLotLibMarkup = markupList.stream()
                        .filter(v -> areaSku.getBasePrice().compareTo(v.getBasePrice()) > 0)
                        .max(Comparator.comparing(ChoiceLotLibMarkup::getBasePrice))
                        .orElse(null);

                if (choiceLotLibMarkup != null && choiceLotLibMarkup.getMarkup() != null) {
                    // 成本价=成本价*（1+加价率）
                    areaSku.setBasePrice(MarkupCalculateUtils.getDistriPrice(areaSku.getBasePrice(), choiceLotLibMarkup.getMarkup()));
                }
            }

            log.info("区域商品SKU成本价计算完成，处理{}个SKU", areaSkuList.size());

        } catch (Exception e) {
            log.error("区域商品SKU成本价计算失败", e);
        }
    }

    /**
     * 选品库SKU成本价重新计算，参考HnApiServiceImpl.choiceLotLibSkuPricePack
     */
    private void choiceLotLibSkuPricePack(List<ChoiceLotLibSkuModel> choiceLotLibSkuList,
                                          List<AreaSku> areaSkuList,
                                          Map<Long, List<ChoiceLotLibMarkup>> choiceMarkupMap) {
        try {
            Map<String, AreaSku> areaSkuMap = areaSkuList.stream()
                    .collect(Collectors.toMap(m -> m.getOperatorId() + "_" + m.getSkuId(),
                            Function.identity(), (k1, k2) -> k1));

            for (ChoiceLotLibSkuModel libSkuModel : choiceLotLibSkuList) {
                AreaSku areaSku = areaSkuMap.get(libSkuModel.getOperatorId() + "_" + libSkuModel.getSkuId());
                if (areaSku == null) {
                    continue;
                }

                // 设置基础价格
                libSkuModel.setBasePrice(areaSku.getBasePrice());

                List<ChoiceLotLibMarkup> markupList = choiceMarkupMap.get(libSkuModel.getChoiceLotLibId());
                if (markupList == null || markupList.isEmpty()) {
                    continue;
                }

                // 匹配最佳加价率
                ChoiceLotLibMarkup choiceLotLibMarkup = markupList.stream()
                        .filter(v -> areaSku.getBasePrice().compareTo(v.getBasePrice()) > 0)
                        .max(Comparator.comparing(ChoiceLotLibMarkup::getBasePrice))
                        .orElse(null);

                if (choiceLotLibMarkup != null && choiceLotLibMarkup.getMarkup() != null) {
                    // 成本价
                    libSkuModel.setBasePrice(areaSku.getBasePrice());
                    // 利润率=（建议零售价-商品供货价）/建议零售价*100% [上入一位]
                    libSkuModel.setProfitRate(MarkupCalculateUtils.getProfitRate(libSkuModel.getOriginalPrice(), libSkuModel.getBasePrice()));
                    // 加价率
                    libSkuModel.setMarkup(choiceLotLibMarkup.getMarkup());
                    // 分销价=供货价*（1+加价率）
                    libSkuModel.setDistributorPrice(MarkupCalculateUtils.getDistriPrice(libSkuModel.getBasePrice(), libSkuModel.getMarkup()));
                    // 分销毛利=分销价-供货价
                    libSkuModel.setResellerGross(MarkupCalculateUtils.sub(libSkuModel.getDistributorPrice(), libSkuModel.getBasePrice()));
                    // 分销毛利率=（分销价-供货价）/分销价*100%
                    libSkuModel.setResellerGrossRate(MarkupCalculateUtils.getProfitRate(libSkuModel.getDistributorPrice(), libSkuModel.getBasePrice()));
                    // 预估毛利率=（建议零售价 - 采购价）/ 建议零售价 × 100%
                    libSkuModel.setPreResellerGrossRate(MarkupCalculateUtils.getProfitRate(libSkuModel.getOriginalPrice(), libSkuModel.getDistributorPrice()));
                }
            }

            log.info("选品库SKU成本价计算完成，处理{}个SKU", choiceLotLibSkuList.size());

        } catch (Exception e) {
            log.error("选品库SKU成本价计算失败", e);
        }
    }

    /**
     * 构建选品库SKU模型
     */
    private ChoiceLotLibSkuModel buildChoiceLotLibSkuModel(Long choiceLotLibId, Sku sku, List<ChoiceLotLibMarkup> markupList) {
        ChoiceLotLibSkuModel model = new ChoiceLotLibSkuModel();
        model.setId(generateHelper.generateId(model));
        model.setItemId(sku.getItemId());
        model.setChoiceLotLibId(choiceLotLibId);
        model.setTenantId(DEFAULT_TENANT_ID.longValue());
        model.setSkuId(sku.getId());
        model.setSource(0); // 默认来源
        model.setOperatorId(CENTER_OPERATOR_ID);

        // 使用defaultBasePrice作为basePrice
        Long defaultBasePrice = null;
        if (sku.getExtraPrice() != null && sku.getExtraPrice().containsKey("defaultBasePrice")) {
            defaultBasePrice = sku.getExtraPrice().get("defaultBasePrice");
        }
        model.setBasePrice(defaultBasePrice != null ? defaultBasePrice : sku.getPrice());
        model.setOriginalPrice(sku.getOriginalPrice());
        model.setCreatedBy(SYNC_USER);
        model.setUpdatedBy(SYNC_USER);

        // 匹配最佳加价率 - 使用defaultBasePrice
        Long basePriceForMarkup = defaultBasePrice != null ? defaultBasePrice : sku.getPrice();
        ChoiceLotLibMarkup choiceLotLibMarkup = markupList.stream()
                .filter(v -> basePriceForMarkup != null && basePriceForMarkup.compareTo(v.getBasePrice()) > 0)
                .max(Comparator.comparing(ChoiceLotLibMarkup::getBasePrice))
                .orElse(null);

        if (choiceLotLibMarkup == null || choiceLotLibMarkup.getMarkup() == null) {
            // 未匹配到加价格配置则按原价计算
            model.setMarkup(0L);
        } else {
            model.setMarkup(choiceLotLibMarkup.getMarkup());
        }

        // 利润率=（建议零售价-商品供货价）/建议零售价*100% [上入一位]
        model.setProfitRate(MarkupCalculateUtils.getProfitRate(sku.getOriginalPrice(), basePriceForMarkup));
        // 分销价=供货价*（1+加价率）
        model.setDistributorPrice(MarkupCalculateUtils.getDistriPrice(basePriceForMarkup, model.getMarkup()));

        return model;
    }

    /**
     * 更新选品库SKU模型 - 使用defaultBasePrice
     */
    private void updateChoiceLotLibSkuModel(ChoiceLotLibSkuModel existingSku, Sku sku, List<ChoiceLotLibMarkup> markupList) {
        // 使用defaultBasePrice作为basePrice
        Long defaultBasePrice = null;
        if (sku.getExtraPrice() != null && sku.getExtraPrice().containsKey("defaultBasePrice")) {
            defaultBasePrice = sku.getExtraPrice().get("defaultBasePrice");
        }
        Long basePriceForUpdate = defaultBasePrice != null ? defaultBasePrice : sku.getPrice();

        existingSku.setBasePrice(basePriceForUpdate);
        existingSku.setOriginalPrice(sku.getOriginalPrice());
        existingSku.setUpdatedBy(SYNC_USER);

        // 重新匹配加价率 - 使用defaultBasePrice
        ChoiceLotLibMarkup choiceLotLibMarkup = markupList.stream()
                .filter(v -> basePriceForUpdate != null && basePriceForUpdate.compareTo(v.getBasePrice()) > 0)
                .max(Comparator.comparing(ChoiceLotLibMarkup::getBasePrice))
                .orElse(null);

        if (choiceLotLibMarkup == null || choiceLotLibMarkup.getMarkup() == null) {
            existingSku.setMarkup(0L);
        } else {
            existingSku.setMarkup(choiceLotLibMarkup.getMarkup());
        }

        // 重新计算利润率和分销价
        existingSku.setProfitRate(MarkupCalculateUtils.getProfitRate(sku.getOriginalPrice(), basePriceForUpdate));
        existingSku.setDistributorPrice(MarkupCalculateUtils.getDistriPrice(basePriceForUpdate, existingSku.getMarkup()));
    }

    /**
     * 检查SKU价格是否变化 - 根据原有SKU的defaultBasePrice判断
     */
    private boolean hasSkuPriceChangedWithOldSku(Sku sku) {
        try {
            // 通过skuReadDomainService查询原有的SKU信息
            Sku oldSku = skuReadDomainService.findById(sku.getId(), DEFAULT_TENANT_ID,null, null);
            if (oldSku == null) {
                return false; // 如果找不到原有SKU，认为没有变化
            }

            // 获取原有的defaultBasePrice
            Long oldDefaultBasePrice = null;
            if (oldSku.getExtraPrice() != null && oldSku.getExtraPrice().containsKey("defaultBasePrice")) {
                oldDefaultBasePrice = oldSku.getExtraPrice().get("defaultBasePrice");
            }

            // 获取新的defaultBasePrice
            Long newDefaultBasePrice = null;
            if (sku.getExtraPrice() != null && sku.getExtraPrice().containsKey("defaultBasePrice")) {
                newDefaultBasePrice = sku.getExtraPrice().get("defaultBasePrice");
            }

            // 比较defaultBasePrice和originalPrice的变化
            return !Objects.equals(oldDefaultBasePrice, newDefaultBasePrice);

        } catch (Exception e) {
            log.error("检查SKU价格变化失败，SKU ID：{}", sku.getId(), e);
            return false;
        }
    }
}
