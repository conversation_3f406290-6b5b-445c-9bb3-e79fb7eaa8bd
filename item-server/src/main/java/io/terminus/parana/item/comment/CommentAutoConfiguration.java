package io.terminus.parana.item.comment;

import io.terminus.parana.item.comment.extension.CommentWriteExtension;
import io.terminus.parana.item.comment.extension.strategy.DefaultCommentWriteExtension;
import io.terminus.parana.item.comment.repository.CommentDao;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-16
 */
@Configuration
public class CommentAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public CommentWriteExtension commentWriteExtension(CommentDao commentDao) {
        return new DefaultCommentWriteExtension(commentDao);
    }
}
