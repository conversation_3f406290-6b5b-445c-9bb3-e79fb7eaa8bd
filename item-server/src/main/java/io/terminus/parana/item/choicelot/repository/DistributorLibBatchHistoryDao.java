package io.terminus.parana.item.choicelot.repository;

import io.terminus.common.model.Paging;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.choicelot.model.DistributorLibBatchHistoryModel;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class DistributorLibBatchHistoryDao extends MyBatisDao<DistributorLibBatchHistoryModel> {

	/**
	 *	创建商品库批量同步记录
	 */
	public Boolean createModel(DistributorLibBatchHistoryModel model) {
		return this.create(model);
	}
	/**
	 *	修改商品库批量同步记录
	 */
	public Boolean updateModel(DistributorLibBatchHistoryModel model) {
		return this.update(model);
	}

	/**
	 *	分页查询商品库批量同步记录
	 */
	public Paging<DistributorLibBatchHistoryModel> page(Map<String, Object> params, Integer offset, Integer limit){
		return this.paging(offset, limit, params);
	}

	/**
	 *	根据Id获取商品库批量同步记录
	 */
	public DistributorLibBatchHistoryModel queryOne(Map<String, Object> params){
		return this.sqlSession.selectOne(sqlId("queryOne"), params);
	}
}
