package io.terminus.parana.item.item.api.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.area.api.bean.response.ItemAuditInfo;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.api.bean.request.item.ItemOffShelfRecordPageRequest;
import io.terminus.parana.item.item.api.bean.request.item.ItemOffShelfRecordQueryRequest;
import io.terminus.parana.item.item.api.bean.request.sku.SkuQueryByIdRequest;
import io.terminus.parana.item.item.api.bean.response.item.ItemOffShelfRecordInfoResponse;
import io.terminus.parana.item.item.api.bean.response.item.ItemOffShelfRecordVendorInfoResponse;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.item.api.converter.input.ItemOffShelfRecordApiConverter;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.ItemOffShelfRecord;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.service.ItemOffShelfRecordReadService;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.service.ShopReadDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Sets;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ItemOffShelfRecordReadFacadeImpl implements ItemOffShelfRecordReadFacade {

	private final ItemOffShelfRecordReadService itemOffShelfRecordReadService;
	private final ItemOffShelfRecordApiConverter itemOffShelfRecordApiConverter;
	private final ItemReadDomainService itemReadDomainService;
	private final AreaSkuReadDomainService areaSkuReadDomainService;
	private final AreaItemReadDomainService areaItemReadDomainService;
	private final ShopReadDomainService shopReadDomainService;
	private final SkuReadDomainService skuReadDomainService;

	@Override
	public Response<ItemOffShelfRecordInfoResponse> view(ItemOffShelfRecordQueryRequest request) {
		ItemOffShelfRecord model = itemOffShelfRecordReadService.view(itemOffShelfRecordApiConverter.get(request));
		if(model == null){
			model = new ItemOffShelfRecord();
		}
		return Response.ok(itemOffShelfRecordApiConverter.model2InfoResponse(model));
	}

	@Override
	public Response<List<ItemOffShelfRecordInfoResponse>> list(ItemOffShelfRecordQueryRequest request) {
		List<ItemOffShelfRecord> modelList = itemOffShelfRecordReadService.list(itemOffShelfRecordApiConverter.get(request));
		if(modelList == null){
			modelList = new ArrayList<ItemOffShelfRecord>();
		}
		return Response.ok(itemOffShelfRecordApiConverter.modelList2InfoResponseList(modelList));
	}

	@Override
	public Response<Paging<ItemOffShelfRecordInfoResponse>> page(ItemOffShelfRecordPageRequest request) {
		Map<String, Object> params = JSONUtil.parseObj(request);
		PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize());
		Paging<ItemOffShelfRecord> modelPage = itemOffShelfRecordReadService.page(params, pageInfo.getOffset(), pageInfo.getLimit());
		Paging<ItemOffShelfRecordInfoResponse> data = itemOffShelfRecordApiConverter.modePage2InfoPage(modelPage);

		if(CollectionUtil.isNotEmpty(data.getData())){
			//根据商品id获取商品信息
			Set<Long> itemIds = data.getData().stream().filter(f -> f.getItemId() != null).map(ItemOffShelfRecordInfoResponse::getItemId).collect(Collectors.toSet());
			List<Item> itemSet = itemReadDomainService.findByIdSet(itemIds, 1, null, null);
			List<Long> vendorIdList = data.getData().stream().map(ItemOffShelfRecordInfoResponse::getVendorId).distinct().collect(Collectors.toList());
			Map<Long, Shop> shopMap = Maps.newHashMap();

			if (ObjectUtil.isNotEmpty(vendorIdList)) {
				List<Shop> shopList = shopReadDomainService.findByIds(vendorIdList);
				shopMap = shopList.stream().collect(Collectors.toMap(Shop::getId, Function.identity(), (k1, k2) -> k1));
			}
			// 获取最高最低供货价、价格
			List<AreaSku> areaSkuList = areaSkuReadDomainService.queryByItem(request.getOperatorId(), itemIds);
			for (ItemOffShelfRecordInfoResponse record : data.getData()) {
				List<Item> list = itemSet.stream().filter(f -> f.getId().equals(record.getItemId())).collect(Collectors.toList());
				List<AreaSku> areaSkus = areaSkuList.stream().filter(areaSku -> areaSku.getItemId().equals(record.getItemId())).collect(Collectors.toList());
				if(CollectionUtil.isNotEmpty(list)){
					record.setItemName(list.get(0).getName());
					record.setSpecification(itemOffShelfRecordApiConverter.getGroupSkuInfo(list.get(0).getSkuAttributes()));
				}

				if(CollectionUtil.isNotEmpty(areaSkus)){
					// 建议零售价
					Long highPrice = areaSkus.stream().max(Comparator.comparing(AreaSku::getOriginalPrice)).get().getOriginalPrice();
					Long minPrice = areaSkus.stream().min(Comparator.comparing(AreaSku::getOriginalPrice)).get().getOriginalPrice();

					// 供货价（成本价）
					Long supplyHighPrice = areaSkus.stream().max(Comparator.comparing(AreaSku::getBasePrice)).get().getBasePrice();
					Long supplyMinPrice = areaSkus.stream().min(Comparator.comparing(AreaSku::getBasePrice)).get().getBasePrice();

					record.setHighBasePrice(supplyHighPrice);
					record.setLowBasePrice(supplyMinPrice);
					record.setHighPrice(highPrice);
					record.setLowPrice(minPrice);
				}

				if (shopMap.containsKey(record.getVendorId()) && ObjectUtil.isNotEmpty(shopMap.get(record.getVendorId()))) {
					record.setVendorName(shopMap.get(record.getVendorId()).getName());
				}
			}
			setCentralizedPurchasePrice(data);
		}

		return Response.ok(data);
	}

	//增加吊牌价
	private void setCentralizedPurchasePrice(Paging<ItemOffShelfRecordInfoResponse> paging){
		//增加吊牌价
		if(paging != null && !paging.isEmpty()){
			Set<Long> itemIdSet = AssembleDataUtils.list2set(paging.getData(), ItemOffShelfRecordInfoResponse::getItemId);
			List<Sku> skuInfoList = skuReadDomainService.findByItemIdSet(itemIdSet,1,null,null);
			log.info("setCentralizedPurchasePrice skuInfoList {}",skuInfoList);
			//使用stream将sku列表根据itemId转换为分组的map
			Map<Long, List<Sku>> skuInfoMap = skuInfoList.stream().collect(Collectors.groupingBy(Sku::getItemId));
			for(ItemOffShelfRecordInfoResponse info:paging.getData()){
				List<Sku> skuInfos = skuInfoMap.get(info.getItemId());
				if(CollectionUtil.isEmpty(skuInfos)){
					log.error("Warning:paging2Plus,sku info not get,skuId:{}",info.getItemId());
					continue;
				}
				List<Long>  centralizedPurchasePriceList = new ArrayList<>();//吊牌价
				for(Sku skuInfo:skuInfos){
					if(skuInfo.getExtraPrice()!=null && skuInfo.getExtraPrice().containsKey("centralizedPurchasePrice")){
						centralizedPurchasePriceList.add(skuInfo.getExtraPrice().get("centralizedPurchasePrice"));
					}
				}
				if(centralizedPurchasePriceList.size()>0) {
					info.setLowCentralizedPurchasePrice(centralizedPurchasePriceList.stream().min(Comparator.comparing(Long::longValue)).get());
					info.setHighCentralizedPurchasePrice(centralizedPurchasePriceList.stream().max(Comparator.comparing(Long::longValue)).get());
				}
			}
		}
	}
	@Override
	public Response<Paging<ItemOffShelfRecordVendorInfoResponse>> vendorPage(ItemOffShelfRecordPageRequest request) {
		log.info("ItemOffShelfRecordReadFacadeImpl vendorPage request:{}", JSONUtil.toJsonStr(request));
		Map<String, Object> params = JSONUtil.parseObj(request);
		PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize());
		Paging<ItemOffShelfRecord> modelPage = itemOffShelfRecordReadService.page(params, pageInfo.getOffset(), pageInfo.getLimit());
		log.info("ItemOffShelfRecordReadFacadeImpl vendorPage modelPage:{}", JSONUtil.toJsonStr(modelPage));
		Paging<ItemOffShelfRecordVendorInfoResponse> data = itemOffShelfRecordApiConverter.modePage2InfoPages(modelPage);

		if(CollectionUtil.isNotEmpty(data.getData())){
			//根据运营商ID查询运营商名称
			List<Long> operatorIds = data.getData().stream().map(ItemOffShelfRecordVendorInfoResponse::getOperatorId).collect(Collectors.toList());
			List<Shop> shops = shopReadDomainService.findByIds(operatorIds);
			Map<Long, Shop> shopMap = shops.stream().collect(Collectors.toMap(Shop::getId, Function.identity(), (e1, e2) -> e1));
			//根据商品id获取商品信息
			Set<Long> itemIds = data.getData().stream().filter(f -> f.getItemId() != null).map(ItemOffShelfRecordVendorInfoResponse::getItemId).collect(Collectors.toSet());
			List<Item> itemSet = itemReadDomainService.findByIdSet(itemIds, 1, null, null);
			List<AreaItem> itemStatusList = areaItemReadDomainService.findByOperatorIdsAndItemIds(Sets.newHashSet(operatorIds), itemIds);
			//转为map
			Map<String, AreaItem> itemStatusMap = itemStatusList.stream().collect(Collectors.toMap(e -> e.getOperatorId() + "-" + e.getItemId(), Function.identity()));
			// 获取最高最低供货价、价格
			List<AreaSku> areaSkuList = areaSkuReadDomainService.queryByItem(request.getOperatorId(), itemIds);
			for (ItemOffShelfRecordVendorInfoResponse record : data.getData()) {
				List<Item> list = itemSet.stream().filter(f -> f.getId().equals(record.getItemId())).collect(Collectors.toList());
				List<AreaSku> areaSkus = areaSkuList.stream().filter(areaSku -> areaSku.getItemId().equals(record.getItemId()) && areaSku.getOperatorId().equals(record.getOperatorId())).collect(Collectors.toList());
				if(CollectionUtil.isNotEmpty(list)){
					record.setItemName(list.get(0).getName());
					record.setItemImage(list.get(0).getMainImage());
					record.setSpecification(itemOffShelfRecordApiConverter.getGroupSkuInfo(list.get(0).getSkuAttributes()));
				}
				if(itemStatusMap.containsKey(record.getOperatorId() + "-" + record.getItemId())){
					record.setOffStatus(itemStatusMap.get(record.getOperatorId() + "-" + record.getItemId()).getStatus());
				}
				if (shopMap.containsKey(record.getOperatorId())) {
					record.setOperatorName(shopMap.get(record.getOperatorId()).getName());
				}
				if(CollectionUtil.isNotEmpty(areaSkus)){
					// 建议零售价
					Long highPrice = areaSkus.stream().max(Comparator.comparing(AreaSku::getOriginalPrice)).get().getOriginalPrice();
					Long minPrice = areaSkus.stream().min(Comparator.comparing(AreaSku::getOriginalPrice)).get().getOriginalPrice();

					// 供货价（成本价）
					Long supplyHighPrice = areaSkus.stream().max(Comparator.comparing(AreaSku::getBasePrice)).get().getBasePrice();
					Long supplyMinPrice = areaSkus.stream().min(Comparator.comparing(AreaSku::getBasePrice)).get().getBasePrice();

					record.setHighBasePrice(supplyHighPrice);
					record.setLowBasePrice(supplyMinPrice);
					record.setHighPrice(highPrice);
					record.setLowPrice(minPrice);
				}
			}
		}

		return Response.ok(data);
	}

}
