package io.terminus.parana.item.category.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.common.utils.JsonMapper;
import io.terminus.parana.common.constants.JacksonType;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Map;

/**
 * <p/>
 *
 * <AUTHOR> href="mailto:<EMAIL>">张成栋</a> at 2018
 * @date 17/01/2018 1:54 PM<br/>
 */
@Slf4j
public class FundamentalModel {
    private static final ObjectMapper JSON_MAPPER = JsonMapper.nonEmptyMapper().getMapper();

    @Getter
    @Setter
    private Long id;
    @Getter
    @Setter
    private Date createdAt;
    @Getter
    @Setter
    private Date updatedAt;

    @Getter
    @Setter
    protected Integer tenantId;

    @Getter
    @Setter
    protected Integer options;

    @Getter
    @Setter
    protected Boolean deleted;

    @Getter
    @Setter
    protected Long createdBy;

    @Getter
    @Setter
    protected String updatedBy;

    @Getter
    @JsonIgnore
    protected String extraJson;

    @Getter
    protected Map<String, String> extra;

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if (extra == null) {
            this.extraJson = null;
        } else {
            try {
                this.extraJson = JSON_MAPPER.writeValueAsString(extra);
            } catch (Exception e) {
                log.error("failed to parse extra map:{} to json, cause: {}", extra, Throwables.getStackTraceAsString(e));
            }
        }
    }

    public void setExtraJson(String extraJson) {
        this.extraJson = extraJson;
        if (StringUtils.isEmpty(extraJson)) {
            this.extra = Maps.newHashMap();
        } else {
            try {
                this.extra = JSON_MAPPER.readValue(extraJson, JacksonType.MAP_OF_STRING);
            } catch (Exception e) {
                log.error("failed to decode json:{} to map, cause: {}", extraJson, Throwables.getStackTraceAsString(e));
            }
        }
    }
}
