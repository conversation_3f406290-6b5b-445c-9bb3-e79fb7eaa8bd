package io.terminus.parana.item.favorites.manager.base;

import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class FavoritesTypeManager {

    private Map<String, Integer> favoritesSupportType;

    public FavoritesTypeManager() {
        this(new Builder());
    }

    public FavoritesTypeManager(Builder builder) {
        this.favoritesSupportType = builder.favoritesSupportType;
    }

    public Integer getFavoritesTypeValue(String key) {
        if (CollectionUtils.isEmpty(favoritesSupportType)) {
            throw new ServiceException("no favorites type supported");
        }

        Integer typeValue = favoritesSupportType.get(key);
        if (typeValue == null) {
            throw new ServiceException("unsupported favorites type");
        }
        return typeValue;
    }

    public Boolean checkFavoritesTypeIsSupported(String key) {
        if (CollectionUtils.isEmpty(favoritesSupportType)) {
            throw new ServiceException("no favorites type supported");
        }

        Integer typeValue = favoritesSupportType.get(key);
        if (typeValue == null) {
            throw new ServiceException("unsupported favorites type");
        }
        return Boolean.TRUE;
    }

    public Boolean checkFavoritesTypeIsSupported(Integer value) {
        if (CollectionUtils.isEmpty(favoritesSupportType)) {
            throw new ServiceException("no favorites type supported");
        }

        if (!favoritesSupportType.containsValue(value)) {
            throw new ServiceException("unsupported favorites type");
        }

        return Boolean.TRUE;
    }

    public static class Builder {
        private Map<String, Integer> favoritesSupportType;

        public Builder() {
            favoritesSupportType = Maps.newHashMap();
        }

        public Builder addSupportType(String key, Integer value) {
            favoritesSupportType.put(key, value);
            return this;
        }

        public FavoritesTypeManager build() {
            return new FavoritesTypeManager(this);
        }
    }
}
