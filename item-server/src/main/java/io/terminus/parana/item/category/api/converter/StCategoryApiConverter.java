package io.terminus.parana.item.category.api.converter;

import java.util.List;
import java.util.Map;

import io.terminus.parana.item.category.api.bean.request.*;
import io.terminus.parana.item.category.api.bean.response.StCategoryInfoResponse;
import io.terminus.parana.item.category.model.StCategory;
import org.mapstruct.Mapper;

import io.terminus.common.model.Paging;

@Mapper(componentModel = "spring")
public interface StCategoryApiConverter {

	StCategory get(StCategoryCreateRequest request);

	StCategory get(StCategoryUpdateRequest request);

	StCategory get(StCategoryDeleteRequest request);

	StCategory get(StCategoryQueryRequest request);

	StCategory get(StCategoryPageRequest request);

	StCategoryInfoResponse model2InfoResponse(StCategory model);

	List<StCategoryInfoResponse> modelList2InfoResponseList(List<StCategory> modelList);

	Paging<StCategoryInfoResponse> modePage2InfoPage(Paging<StCategory> model);
}
