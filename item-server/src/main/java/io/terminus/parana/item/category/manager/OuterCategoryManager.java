package io.terminus.parana.item.category.manager;

import io.terminus.parana.item.category.model.OuterCategory;
import io.terminus.parana.item.category.model.OuterCategoryBinding;
import io.terminus.parana.item.category.repository.OuterCategoryBindingDAO;
import io.terminus.parana.item.category.repository.OuterCategoryDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 2018-08-06 下午8:30
 */
@Component
public class OuterCategoryManager {
    @Autowired
    private OuterCategoryDAO categoryDAO;
    @Autowired
    private OuterCategoryBindingDAO bindingDAO;

    @Transactional(rollbackFor = Exception.class)
    public void disable(Long id) {
        categoryDAO.delete(id);
        //删除外部类目绑定关系
        bindingDAO.delete(id, null, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void bind(Long oid, Long bid, String updatedBy,Long vendorId) {
        OuterCategoryBinding binding = new OuterCategoryBinding();
        binding.setOuterCategoryId(oid);
        binding.setBackCategoryId(bid);
        binding.setVendorId(vendorId);
        bindingDAO.create(binding);
        OuterCategory oc = new OuterCategory();
        oc.setId(oid);
        oc.setStatus(Boolean.TRUE);
        oc.setUpdatedBy(updatedBy);
        categoryDAO.update(oc);
    }

    @Transactional(rollbackFor = Exception.class)
    public void unbind(Long oid, String updatedBy, Long vendorId) {
        bindingDAO.delete(oid, null, vendorId);
        OuterCategory oc = new OuterCategory();
        oc.setId(oid);
        oc.setStatus(Boolean.FALSE);
        oc.setUpdatedBy(updatedBy);
        categoryDAO.update(oc);
    }
}
