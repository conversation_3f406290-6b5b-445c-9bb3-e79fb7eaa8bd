package io.terminus.parana.item.footprint.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.common.annotation.MicroService;
import io.terminus.parana.item.footprint.api.bean.request.FootprintClearByUserIdRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintCreateOnRedisRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintDeleteOnRedisRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintPersistRequest;
import io.terminus.parana.item.footprint.service.FootprintWriteDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FootprintWriteFacadeImpl implements FootprintWriteFacade {

    private final FootprintWriteDomainService itemFootprintWriteDomainService;

    @Override
    public Response<Boolean> createOnRedis(FootprintCreateOnRedisRequest request) {
        try {
            return Response.ok(itemFootprintWriteDomainService.createOnRedisAddChoiceLotLibId(
                    request.getTenantId(), request.getUserId(), request.getTargetId(),
                    request.getTargetType(), request.getOperatorId(), request.getCurrentOperatorId(),request.getChoiceLotLibId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> batchDeleteOnRedis(FootprintDeleteOnRedisRequest request) {
        try {
            return Response.ok(itemFootprintWriteDomainService.batchDeleteOnRedis(
                    request.getTenantId(), request.getUserId(),
                    request.getTargetIdList(), request.getTargetType(), request.getOperatorId(), request.getCurrentOperatorId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<Boolean> clearByUserId(FootprintClearByUserIdRequest request) {
        try {
            return Response.ok(itemFootprintWriteDomainService.clearByUserIdAndTypeOnRedis(
                    request.getTenantId(), request.getUserId(),
                    request.getTargetTypeList(), request.getCurrentOperatorId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> dataPersistToDB(FootprintPersistRequest request) {
        try {
            return Response.ok(itemFootprintWriteDomainService.dataPersistToDB(
                    request.getTenantId(), request.getUserId(),
                    request.getTargetTypeList(), request.getOperatorId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

}
