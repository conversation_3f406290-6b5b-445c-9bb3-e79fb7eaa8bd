package io.terminus.parana.item.category.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2021-01-11 16:24:43
 */
@Data
public class OuterCategoryNew implements Serializable {

    private static final long serialVersionUID = 7503495179470383133L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("租户ID")
    private Integer tenantId;

    @ApiModelProperty("区域运营ID")
    private Long operatorId;

    @ApiModelProperty("外部ID")
    private String outId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("父ID,类目模板根节点pid为-1，非模板一级类目为0")
    private Long pid;

    @ApiModelProperty("类目logo")
    private String logo;

    @ApiModelProperty("本类目所属层级")
    private Long level;

    @ApiModelProperty("父类目路径,分隔")
    private String path;

    @ApiModelProperty("标识是否有子类目")
    private Long hasChildren;

    @ApiModelProperty("叶子类目时绑定目标类型，0：后台类目，1：商品")
    private Long type;

    @ApiModelProperty("是否默认展开")
    private Long disclosed;

    @ApiModelProperty("外部层级")
    private Long outLevel;

    @ApiModelProperty("外部父节点")
    private String outPid;

    @ApiModelProperty("拓展属性json")
    private String extraJson;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("创建者")
    private String createdBy;

    @ApiModelProperty("创建者名")
    private String createdByName;

    @ApiModelProperty("最后更新时间")
    private Date updatedAt;

    @ApiModelProperty("更新者")
    private String updatedBy;

    @ApiModelProperty("更新人名称")
    private String updatedByName;


}
