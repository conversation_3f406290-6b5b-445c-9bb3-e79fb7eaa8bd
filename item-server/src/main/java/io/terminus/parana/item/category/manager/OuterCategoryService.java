package io.terminus.parana.item.category.manager;

import cn.hutool.core.collection.CollectionUtil;
import io.terminus.api.utils.StringUtil;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.category.api.bean.request.OuterCategoryPageRequest;
import io.terminus.parana.item.category.model.OuterCategory;
import io.terminus.parana.item.category.model.OuterCategoryBinding;
import io.terminus.parana.item.category.model.OuterCategoryRelation;
import io.terminus.parana.item.category.repository.OuterCategoryBindingDAO;
import io.terminus.parana.item.category.repository.OuterCategoryDAO;
import io.terminus.parana.item.common.spi.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2018-08-06 下午7:43
 */
@Service
public class OuterCategoryService {
    @Autowired
    private OuterCategoryDAO outerCategoryDAO;
    @Autowired
    private OuterCategoryBindingDAO bindingDAO;
    @Autowired
    private OuterCategoryManager categoryManager;
    @Autowired
    private IdGenerator idGenerator;

    public Long create(OuterCategory oc) {
        oc.setId(idGenerator.nextValue(OuterCategory.class));
        outerCategoryDAO.create(oc);
        return oc.getId();
    }

    public Boolean delete(Long id) {
        categoryManager.disable(id);
        return Boolean.TRUE;
    }

    public Long importByName(String name, Integer tenantId, String updatedBy, String type) {
        OuterCategory oc = outerCategoryDAO.findByName(name, tenantId, type);
        if (null == oc) {
            oc = new OuterCategory();
            oc.setTenantId(tenantId);
            oc.setName(name);
            oc.setType(type);
            oc.setStatus(Boolean.FALSE);
            oc.setUpdatedBy(updatedBy);
            outerCategoryDAO.create(oc);
            throw new ServiceException("outerCategory.not.exist.then.created");
        }

        if (Boolean.FALSE.equals(oc.getStatus())) {
            throw new ServiceException("outerCategory.not.binding");
        }

        OuterCategoryBinding binding = bindingDAO.findById(oc.getId());
        if (null == binding) {
            throw new ServiceException("outerCategory.not.binding");
        }
        return binding.getBackCategoryId();
    }

    public Boolean bind(Long selfId, Long otherId, String updatedBy) {
        categoryManager.bind(selfId, otherId, updatedBy, null);
        return Boolean.TRUE;
    }

    public Boolean bind(Long selfId, Long otherId, Long vendorId, String updatedBy) {
        categoryManager.bind(selfId, otherId, updatedBy, vendorId);
        return Boolean.TRUE;
    }

    public Boolean unbind(Long selfId, String updatedBy) {
        categoryManager.unbind(selfId, updatedBy, null);
        return Boolean.TRUE;
    }

    public Boolean unbind(Long selfId,Long vendorId, String updatedBy) {
        categoryManager.unbind(selfId, updatedBy, vendorId);
        return Boolean.TRUE;
    }

    public OuterCategory findById(Long id) {
        return outerCategoryDAO.findById(id);
    }

    public Paging<OuterCategory> paging(Long backCategoryId, OuterCategory oc, PageInfo of) {
        Map<String, Object> param = new HashMap<>();
        param.put("tenantId", oc.getTenantId());
        param.put("id", oc.getId());
        param.put("outerId", oc.getOuterId());
        param.put("name", oc.getName());
        param.put("status", oc.getStatus());
        param.put("type", oc.getType());
        if (null != backCategoryId) {
            List<Long> ids = bindingDAO.getByBackId(backCategoryId);
            if (!CollectionUtils.isEmpty(ids)) {
                param.put("list", ids);
            } else {
                return Paging.empty();
            }
        }
        return outerCategoryDAO.paging(of.getOffset(), of.getLimit(), param);
    }

    public Paging<OuterCategoryRelation> pagingRelation(OuterCategoryPageRequest request) {
        Map<String, Object> param = new HashMap<>();
        param.put("tenantId", request.getTenantId());
        param.put("outerId", request.getOuterId());
        param.put("name", request.getName());
        param.put("status", request.getStatus());
        param.put("type", request.getType());
        param.put("categoryId", request.getBackCategoryId());
        PageInfo page = PageInfo.of(request.getPageNo(), request.getPageSize());
        param.put("offset", page.getOffset());
        param.put("limit", page.getLimit());
        return outerCategoryDAO.pagingRelation(param);
    }

    // Question
    public List<OuterCategory> findByBackId(Long id) {
        List<Long> ids = bindingDAO.getByBackId(id);
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return outerCategoryDAO.findByIds(ids);
    }

    public List<OuterCategory> queryByOuterId(List<String> outerIds, Long vendorId) {
        Map<String, Object> param = new HashMap<>();
        param.put("outerIds", outerIds);
        param.put("vendorId", vendorId);
        return outerCategoryDAO.list(param);
    }

    public List<OuterCategoryBinding> findByOuterCategoryIds(List<Long> outerIds, Long vendorId) {
        Map<String, Object> param = new HashMap<>();
        param.put("outCategoryIds", outerIds);
        param.put("vendorId", vendorId);
        return  bindingDAO.list(param);
    }

    public void update(OuterCategory update) {
        outerCategoryDAO.update(update);
    }

    public OuterCategory queryByOuterIdWithBinding(String outerId, Long vendorId) {
        if(StringUtil.isEmpty(outerId) || vendorId == null){
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        //只查询末级的绑定关系
        param.put("outerId", outerId);
        param.put("vendorId", vendorId);
        List<OuterCategory> list = outerCategoryDAO.list(param);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<Long> ids = list.stream().map(OuterCategory::getId).collect(Collectors.toList());
        List<OuterCategoryBinding> bindingList = findByOuterCategoryIds(ids, vendorId);
        if (CollectionUtils.isEmpty(bindingList)) {
            return null;
        }
        for (OuterCategoryBinding binding : bindingList) {
            for (OuterCategory oc : list) {
                if (Objects.equals(binding.getOuterCategoryId(), oc.getId())) {
                    oc.setBackCategoryId(binding.getBackCategoryId());
                }
            }
        }
        return list.get(0);
    }
    public List<OuterCategory> queryByOuterIdWithBinding(List<String> outerIds, Long vendorId) {
        if(CollectionUtil.isEmpty(outerIds)){
            return new ArrayList<>();
        }
        Map<String, Object> param = new HashMap<>();
        //只查询末级的绑定关系
        param.put("outerId", outerIds.get(outerIds.size() - 1));
        param.put("vendorId", vendorId);
        List<OuterCategory> list = outerCategoryDAO.list(param);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> ids = list.stream().map(OuterCategory::getId).collect(Collectors.toList());
        List<OuterCategoryBinding> bindingList = findByOuterCategoryIds(ids, vendorId);
        if (CollectionUtils.isEmpty(bindingList)) {
            return new ArrayList<>();
        }
        for (OuterCategoryBinding binding : bindingList) {
            for (OuterCategory oc : list) {
                if (Objects.equals(binding.getOuterCategoryId(), oc.getId())) {
                    oc.setBackCategoryId(binding.getBackCategoryId());
                }
            }
        }
        return list;
    }
}
