package io.terminus.parana.item.comment.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.comment.api.bean.request.ItemCommentCreateRequest;
import io.terminus.parana.item.comment.api.bean.request.ItemCommentSingleCreateRequest;
import io.terminus.parana.item.comment.api.bean.request.ItemCommentUpdateStatusRequest;
import io.terminus.parana.item.comment.api.converter.ItemCommentApiConverter;
import io.terminus.parana.item.comment.enums.CommentType;
import io.terminus.parana.item.comment.model.Comment;
import io.terminus.parana.item.comment.service.CommentWriteDomainService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-15
 */
@Service
public class ItemCommentWriteFacadeImpl implements ItemCommentWriteFacade {

    private final ItemCommentApiConverter itemCommentApiConverter;
    private final CommentWriteDomainService commentWriteDomainService;

    public ItemCommentWriteFacadeImpl(ItemCommentApiConverter itemCommentApiConverter,
                                      CommentWriteDomainService commentWriteDomainService) {
        this.itemCommentApiConverter = itemCommentApiConverter;
        this.commentWriteDomainService = commentWriteDomainService;
    }

    @Override
    public Response<Long> createSingle(ItemCommentSingleCreateRequest request) {
        Comment comment = itemCommentApiConverter.get(request.getItemComment());
        comment.setUpdatedBy(request.getUpdatedBy());
        comment.setTenantId(request.getTenantId());

        try {
            return Response.ok(commentWriteDomainService.create(comment));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> create(ItemCommentCreateRequest request) {
        List<Comment> commentList = itemCommentApiConverter.get(request.getItemCommentList());
        commentList.forEach(it -> {
            it.setUpdatedBy(request.getUpdatedBy());
            it.setTenantId(request.getTenantId());
            it.setTopId(it.getParentId());
            it.setType(CommentType.ITEM_COMMENT.getValue());
        });

        try {
            return Response.ok(commentWriteDomainService.create(commentList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> updateStatus(ItemCommentUpdateStatusRequest request) {
        try {
            return Response.ok(commentWriteDomainService.updateStatus(request.getId(), request.getStatus(),
                    request.getUpdatedBy(), request.getOperatorType(), request.getTenantId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }
}
