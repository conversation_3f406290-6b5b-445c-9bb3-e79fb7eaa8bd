package io.terminus.parana.item.comment.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.comment.agreement.CommentAgreement;
import io.terminus.parana.item.comment.enums.CommentStatus;
import io.terminus.parana.item.comment.extension.CommentWriteExtension;
import io.terminus.parana.item.comment.manager.CommentManager;
import io.terminus.parana.item.comment.model.Comment;
import io.terminus.parana.item.comment.repository.CommentDao;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.common.extension.ProcessResult;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.common.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2018-11-19
 */
@Slf4j
@Service
public class CommentWriteDomainService extends AbsServiceBase {

    private final CommentDao commentDao;
    private final IdGenerator idGenerator;
    private final CommentManager commentManager;
    private final CommentWriteExtension commentWriteExtension;

    public CommentWriteDomainService(CommentDao commentDao,
                                     IdGenerator idGenerator,
                                     CommentManager commentManager,
                                     CommentWriteExtension commentWriteExtension) {
        this.commentDao = commentDao;
        this.idGenerator = idGenerator;
        this.commentManager = commentManager;
        this.commentWriteExtension = commentWriteExtension;
    }

    public Long create(Comment comment) {
        try {
            ExtensionResult extensionResult = commentWriteExtension.contentCheck(Collections.singletonList(comment));
            Assert.isTrue(!extensionResult.isDeadly(), extensionResult.getMessage());

            Long id = idGenerator.nextValue(Comment.class);
            comment.setId(id);

            ProcessResult<Comment> result = commentWriteExtension.create(comment);
            if (result.getParam() == null) {
                commentDao.create(comment);
            } else {
                commentManager.createChangeParent(comment, (Comment) result.getParam());
            }

            return id;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to createAndBinding comment with: {}, cause: {}", comment, printErrorStack(e));
            throw new ServiceException("comment.createAndBinding.fail");
        }
    }

    public Boolean create(List<Comment> commentList) {
        // 批量创建拒绝追评
        for (Comment comment : commentList) {
            Assert.equals(comment.getParentId(), CommentAgreement.STANDALONE_TOP_ID);
        }

        try {
            ExtensionResult extensionResult = commentWriteExtension.contentCheck(commentList);
            Assert.isTrue(!extensionResult.isDeadly(), extensionResult.getMessage());

            // 批量评价仅在第一次，不会出现parent，追评不允许
            commentList.forEach(commentWriteExtension::create);

            commentList.forEach(it -> it.setId(idGenerator.nextValue(Comment.class)));
            commentDao.creates(commentList);
            return Boolean.TRUE;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to batch createAndBinding comment with: {}, cause: {}", commentList, printErrorStack(e));
            throw new ServiceException("comment.createAndBinding.fail");
        }
    }

    public Boolean updateStatus(Long id, Integer status, String updatedBy, Integer operatorType, Integer tenantId) {
        try {
            Comment origin = commentDao.findById(id, tenantId);
            Assert.nonNull(origin, "comment.not.found");

            ProcessResult<Comment> result = commentWriteExtension.updateStatus(origin, CommentStatus.fromValue(status),
                    operatorType);

            return result.getParam() == null
                    ? commentDao.updateStatus(id, status, updatedBy)
                    : commentManager.updateStatusChangeParent(id, status, updatedBy, (Comment) result.getParam());
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to unbinding comment status by id: {}, status: {}, updatedBy: {}, cause: {}",
                    id, status, updatedBy, printErrorStack(e));
            throw new ServiceException("comment.unbinding.status.fail");
        }
    }
}
