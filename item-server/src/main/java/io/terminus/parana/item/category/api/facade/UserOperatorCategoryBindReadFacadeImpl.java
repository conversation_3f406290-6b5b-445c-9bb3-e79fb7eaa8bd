package io.terminus.parana.item.category.api.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.request.UserAndOperatorRequest;
import io.terminus.parana.item.category.api.bean.response.OperatorCategoryTreeInfo;
import io.terminus.parana.item.category.api.bean.response.OperatorCategoryWithChildrenInfo;
import io.terminus.parana.item.category.api.bean.response.UserOperatorCategoryBindingInfo;
import io.terminus.parana.item.category.service.OperatorCategoryReadDomainService;
import io.terminus.parana.item.category.service.UserOperatorCategoryBindingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserOperatorCategoryBindReadFacadeImpl implements UserOperatorCategoryBindReadFacade {

    private final UserOperatorCategoryBindingService userOperatorCategoryBindingService;

    private final OperatorCategoryReadDomainService operatorCategoryReadDomainService;

    @Override
    public Response<OperatorCategoryTreeInfo> queryUserOperatorCategoryTree(UserAndOperatorRequest request) {
        return Response.ok(queryUserOperatorCategoryTree(request.getUserId(), request.getOperatorId()));
    }

    @Override
    public Response<OperatorCategoryTreeInfo> queryHomeUserOperatorCategoryTree(UserAndOperatorRequest request) {
        OperatorCategoryTreeInfo operatorCategoryTreeInfo = queryUserOperatorCategoryTree(request.getUserId(), request.getOperatorId());

        //去除二级类目信息，三级类目直接挂靠一级类目下
        operatorCategoryTreeInfo.getOperatorCategoryWithChildrenInfoList().forEach(firstCategoryInfo -> {
            List<OperatorCategoryWithChildrenInfo> thirdCategoryList = new ArrayList<>();
            firstCategoryInfo.getChildrenList().forEach(secondCategory -> {
                thirdCategoryList.addAll(secondCategory.getChildrenList());
            });
            firstCategoryInfo.setChildrenList(thirdCategoryList);
        });

        return Response.ok(operatorCategoryTreeInfo);
    }

    @Override
    public Response<List<Long>> queryUserOperatorCategoryBindingIdList(UserAndOperatorRequest request) {
        List<Long> operatorCategoryIdList = userOperatorCategoryBindingService.queryUserOperatorCategoryBindingIdList(request.getOperatorId(), request.getUserId());
        return Response.ok(operatorCategoryIdList);
    }

    private OperatorCategoryTreeInfo queryUserOperatorCategoryTree(Long userId, Long operatorId) {
        //查询用户绑定的一级前台类目
        List<UserOperatorCategoryBindingInfo> userCategoryInfoList = userOperatorCategoryBindingService.queryByUserIdAndOperatorId(userId, operatorId);
        List<Long> operatorCategoryIdList = userCategoryInfoList.stream().map(UserOperatorCategoryBindingInfo::getOperatorCategoryId).collect(Collectors.toList());

        //根据operatorId查询出区域运营下的前台类目树信息
        OperatorCategoryTreeInfo operatorCategoryTreeInfo = operatorCategoryReadDomainService.buildOperatorCategoryTreeInfo(operatorId);

        //对前台类目树的一级类目进行排序，用户绑定的一级类目排序靠前，未绑定的无序
        List<OperatorCategoryWithChildrenInfo> sortedOperatorCategoryList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(operatorCategoryIdList) && !CollectionUtils.isEmpty(operatorCategoryIdList)) {
            for (Long id : operatorCategoryIdList) {
                Iterator<OperatorCategoryWithChildrenInfo> iterator = operatorCategoryTreeInfo.getOperatorCategoryWithChildrenInfoList().iterator();
                while (iterator.hasNext()) {
                    OperatorCategoryWithChildrenInfo childrenInfo = iterator.next();
                    if (id.equals(childrenInfo.getOperatorCategoryInfo().getId())) {
                        sortedOperatorCategoryList.add(childrenInfo);
                        iterator.remove();
                    }
                }
            }
            sortedOperatorCategoryList.addAll(operatorCategoryTreeInfo.getOperatorCategoryWithChildrenInfoList());
            operatorCategoryTreeInfo.setOperatorCategoryWithChildrenInfoList(sortedOperatorCategoryList);
        }
        //递归剔除三级类目隐藏的信息
        List<OperatorCategoryWithChildrenInfo> list = operatorCategoryTreeInfo.getOperatorCategoryWithChildrenInfoList();
        if(CollectionUtil.isNotEmpty(list)){
            for (OperatorCategoryWithChildrenInfo info : list) {
                hideCate(info);
            }
        }
        return operatorCategoryTreeInfo;
    }

    public static void hideCate(OperatorCategoryWithChildrenInfo info){
        if(info.getOperatorCategoryInfo().getLevel() == 2){
            List<OperatorCategoryWithChildrenInfo> newList = Lists.newArrayList();
            List<OperatorCategoryWithChildrenInfo> list = info.getChildrenList();
            if(CollectionUtil.isNotEmpty(list)){
                for (OperatorCategoryWithChildrenInfo info2 : list) {
                    if(info2.getOperatorCategoryInfo().getIsShow() != null && info2.getOperatorCategoryInfo().getIsShow() == 1){
                        newList.add(info2);
                    }
                }
                info.setChildrenList(newList);
            }
        }else{
            List<OperatorCategoryWithChildrenInfo> list = info.getChildrenList();
            if(CollectionUtil.isNotEmpty(list)) {
                for (OperatorCategoryWithChildrenInfo info2 : list) {
                    hideCate(info2);
                }
            }
        }
    }

}
