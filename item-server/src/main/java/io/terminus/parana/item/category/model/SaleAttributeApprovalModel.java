package io.terminus.parana.item.category.model;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.omg.PortableInterceptor.INACTIVE;

@Data
public class SaleAttributeApprovalModel implements Serializable{

	/**
	 * 关联系统属性ID
	 */
	private Long associatedAttributeId;
	/**
	 * 关联系统属性名称
	 */
	private String associatedAttributeName;
	/**
	 * 属性值
	 */
	private String associatedAttributeValue;

	/**
	 * 原属性值
	 */
	private String associatedAttributeOriginalValue;
	/**
	 * 属性名称
	 */
	private String attributeName;
	/**
	 * 审批状态；1-待审核,2-审核通过,3-已驳回
	 */
	private Integer auditStatus;
	/**
	 * 所属分类
	 */
	private String classification;

	private String classificationId;
	/**
	 * 创建时间
	 */
	private java.util.Date createTime;
	/**
	 * 创建人
	 */
	private String createdBy;
	/**
	 * 创建人名
	 */
	private String createdName;
	/**
	 * 0正常，1删除
	 */
	private Integer delFlag;
	/**
	 * 主键id
	 */
	private Long id;
	/**
	 * 审核备注
	 */
	private String remarks;
	/**
	 * 租户ID（分片字段）
	 */
	private Long tenantId;
	/**
	 * 最后更新时间
	 */
	private java.util.Date updateTime;
	/**
	 * 最后更新人
	 */
	private String updatedBy;
	/**
	 * 使用商品
	 */
	private String useGoods;

	@ApiModelProperty(value = "属性表id")
	private long sxId ;
	@ApiModelProperty(value = "属性名")
	private String sxName;
}
