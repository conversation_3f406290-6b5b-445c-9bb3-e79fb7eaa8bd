package io.terminus.parana.item.enhance.component.inventory;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.parana.item.area.api.bean.request.AreaSkuQueryByItemRequest;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.enhance.api.bean.response.DynamicRenderInfo;
import io.terminus.parana.item.enhance.model.LocationBO;
import io.terminus.parana.item.item.api.bean.response.item.ItemInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.plugin.third.api.inventory.api.InventoryReadApi;
import io.terminus.parana.item.third.info.InventoryEntityResponse;
import io.terminus.parana.item.third.param.ThirdInventoryQueryByOperatorRequest;
import io.terminus.parana.item.third.info.ActivityMatchedLine;
import io.terminus.parana.item.third.info.LineActivityInventoryInfo;
import io.terminus.parana.item.third.info.PromotionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-16
 */
@Slf4j
public class DefaultInventoryRender extends AbstractInventoryInvoker implements InventoryRender {

    @Autowired
    private InventoryReadApi inventoryReadApi;

    @Autowired
    private AreaSkuReadDomainService areaSkuReadDomainService;

    @Autowired
    private AreaItemReadDomainService areaItemReadDomainService;

    /**
     * 处理促销返回的库存不足信息
     *
     * @param promotion 促销
     * @param skuId     skuId
     * @return 库存值
     */
    private Long processStockNotEnough(PromotionResult promotion, Long skuId) {
        if (promotion == null) {
            return null;
        }
        log.info("stock promotion:::{}",promotion.toString());
        // 获取促销中返回的库存不足信息
        Map<String, LineActivityInventoryInfo> lines = promotion.getActivityInventoryNotEnoughLines();
        if (!CollectionUtils.isEmpty(lines)) {
            String lineKey = String.valueOf(skuId);

            LineActivityInventoryInfo lineActivityInventoryInfo = lines.get(lineKey);
            if (lineActivityInventoryInfo != null) {
                return (long) lineActivityInventoryInfo.getMaxInventoryQuota();
            }
        }

        return null;
    }

    /**
     * 查询库存值
     *
     * @param promotion   促销信息
     * @param selectedSku 选中的sku
     * @param location    位置信息
     * @param renderInfo  渲染信息
     */
    private void queryInventory(PromotionResult promotion,
                                SkuInfo selectedSku,
                                LocationBO location,
                                DynamicRenderInfo renderInfo) {
        Long stock = null;

        if (selectedSku.isSkipInventorySourcing()) {
            stock = Long.MAX_VALUE;
        }
        log.info("stock0:::::::::::::::::::::::::::"+stock);
        if (stock == null && selectedSku.isGroup()) {
            stock = queryGroupStock(selectedSku, location);
        }
        log.info("stock1:::::::::::::::::::::::::::"+stock);
        if (stock == null) {
            stock = processStockNotEnough(promotion, selectedSku.getId());
        }
        log.info("stock2:::::::::::::::::::::::::::"+stock);
        if (stock == null) {
            List<ActivityMatchedLine> matchedLineList = null;

            if (promotion != null && !CollectionUtils.isEmpty(promotion.getLineCandidateActivities())) {
                matchedLineList = promotion.getLineCandidateActivities();
            }

            Long activityId = renderInfo.getActivityForSku() == null
                    ? null
                    : renderInfo.getActivityForSku().getActivity().getId();

            ActivityMatchedLine activityMatchedLine = null;
            if (activityId != null && !CollectionUtils.isEmpty(matchedLineList)) {
                for (ActivityMatchedLine matchedLineInfo : matchedLineList) {
                    if (activityId.equals(matchedLineInfo.getActivity().getId())) {
                        activityMatchedLine = matchedLineInfo;
                    }
                }
            }

            stock = queryStock(activityId, selectedSku, location, activityMatchedLine);
            log.info("stock3:::::::::::::::::::::::::::"+stock);
            // 将库存与活动限购取交集
            if (activityMatchedLine != null && activityMatchedLine.getPurchaseLimit() != null) {
                stock = Math.min(stock, activityMatchedLine.getPurchaseLimit());
            }
        }
        log.info("stock4:::::::::::::::::::::::::::"+stock);
        renderInfo.setSelectedSkuStock(stock);
    }

    @Override
    public void render(SkuInfo selectedSku, LocationBO location,
                       PromotionResult promotionResult, DynamicRenderInfo renderInfo) {
        queryInventory(promotionResult, selectedSku, location, renderInfo);
    }

    @Override
    public Long pptRender(List<SkuInfo> skuInfos, Long operatorId, ItemInfo item) {
        Long realQuantityTotal = 0L;
        AreaSkuQueryByItemRequest queryByItemRequest = new AreaSkuQueryByItemRequest();
        queryByItemRequest.setOperatorId(operatorId);
        queryByItemRequest.setItemIdSet(Sets.newHashSet(item.getId()));

        List<AreaSku> areaSkuList = areaSkuReadDomainService.queryByItem(operatorId, Sets.newHashSet(item.getId()));
        if(!CollectionUtils.isEmpty(areaSkuList)){
            List<Long> operatorIds = Lists.newArrayList();
            operatorIds.add(operatorId);
            operatorIds.add(1L);

            AreaItem areaItem = areaItemReadDomainService.findByOperatorIdAndItemId(operatorId, item.getId());
            if(areaItem != null && areaItem.getSourceOperatorId() != null){
                operatorIds.add(areaItem.getSourceOperatorId());
            }

            Set<Long> skuIdSet = areaSkuList.stream().map(AreaSku::getSkuId).collect(Collectors.toSet());

            ThirdInventoryQueryByOperatorRequest req = new ThirdInventoryQueryByOperatorRequest();
            req.setVendorIdSet(Sets.newHashSet(item.getShopId()));
            req.setSkuIdSet(skuIdSet);
            req.setOperatorIds(operatorIds);
            Map<String, InventoryEntityResponse> inventoryMap = inventoryReadApi.groupInventoryQueryByOperator(req);
            if(areaItem != null){
                for (AreaSku areaSku : areaSkuList) {
                    InventoryEntityResponse inventory = inventoryMap.get(areaItem.getInventoryKey(areaSku.getSkuId()));
                    if(inventory != null){
                        areaSku.setRealQuantity(inventory.getSellableQuantity());
                        realQuantityTotal = realQuantityTotal + inventory.getSellableQuantity();
                    }
                }
                Map<Long, AreaSku> areaSkuMap = areaSkuList.stream().collect(Collectors.toMap(AreaSku::getSkuId, Function.identity(), (k1, k2) -> k1));
                for (SkuInfo skuInfo : skuInfos) {
                    if(areaSkuMap.get(skuInfo.getId()) != null){
                        skuInfo.setRealQuantity(areaSkuMap.get(skuInfo.getId()).getRealQuantity());
                        if(skuInfo.getAreaSku() != null){
                            skuInfo.getAreaSku().setRealQuantity(areaSkuMap.get(skuInfo.getId()).getRealQuantity());
                        }
                    }
                }
            }
        }

        return realQuantityTotal;
    }




}
