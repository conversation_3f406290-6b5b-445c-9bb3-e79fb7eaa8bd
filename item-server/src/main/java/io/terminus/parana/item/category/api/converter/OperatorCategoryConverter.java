package io.terminus.parana.item.category.api.converter;

import io.terminus.parana.item.category.api.bean.dto.OperatorCategoryBindingDTO;
import io.terminus.parana.item.category.api.bean.dto.OperatorCategoryDTO;
import io.terminus.parana.item.category.api.bean.dto.OperatorCategoryWithChildrenDTO;
import io.terminus.parana.item.category.model.OperatorCategory;
import io.terminus.parana.item.category.model.OperatorCategoryWithChildren;
import io.terminus.parana.item.category.model.OperatorCategoryBinding;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface OperatorCategoryConverter {

    OperatorCategoryDTO domain2dto(OperatorCategory category);

    OperatorCategory dto2domain(OperatorCategoryDTO categoryDTO);

    @Mapping(source = "operatorCategory", target = "operatorCategoryDTO")
    OperatorCategoryWithChildrenDTO domain2dto(OperatorCategoryWithChildren categoryWithChildren);

    OperatorCategoryBindingDTO domain2dto(OperatorCategoryBinding binding);
}
