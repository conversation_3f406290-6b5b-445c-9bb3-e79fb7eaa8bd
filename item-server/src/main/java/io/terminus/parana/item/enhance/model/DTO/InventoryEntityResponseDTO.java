package io.terminus.parana.item.enhance.model.DTO;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-12
 */
@Data
public class InventoryEntityResponseDTO implements Serializable {
    private static final long serialVersionUID = -2802184473326406203L;

    private String entityId;

    private Integer entityType;

    private Long operatorId;

    /**
     * 真实库存(物理库存)
     */
    private Long realQuantity;

    /**
     * 安全库存
     */
    private Long safeQuantity;

    /**
     * 在途库存(JIT)
     */
    private Long preorderQuantity;

    /**
     * 锁定库存
     */
    private Long withholdQuantity;

    /**
     * 占用库存
     */
    private Long occupyQuantity;

    /**
     * 可售库存
     */
    private Long sellableQuantity;

    private Long itemId;

    private Long sdItemId;

    private Long skuId;

    private Long vendorId;

    private Long sdProjectNo;

    private Long sdDeprNo;

    private Long sdLtdNo;

    private BigDecimal stockRatio;

    private String machineNo;
    /**
     * sd仓库编码
     */
    private String sdWarehouseCode;

    /**
     * sd仓库编码
     */
    private String warehouseCode;

    /**
     * 状态：1未绑定、2已绑定、3废弃
     */
    private Integer sts;

    private String isrf;
}
