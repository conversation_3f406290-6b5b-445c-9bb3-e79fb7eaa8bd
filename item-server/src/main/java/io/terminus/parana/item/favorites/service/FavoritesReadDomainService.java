package io.terminus.parana.item.favorites.service;

import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.common.lang.util.JsonUtils;
import io.terminus.parana.item.common.dimension.DimensionContext;
import io.terminus.parana.item.favorites.cache.FavoritesCache;
import io.terminus.parana.item.favorites.manager.FavoritesExtensionManager;
import io.terminus.parana.item.favorites.model.Favorites;
import io.terminus.parana.item.favorites.repository.FavoritesDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FavoritesReadDomainService {

    private final FavoritesCache favoritesCache;
    private final FavoritesExtensionManager favoritesExtensionManager;
    private final FavoritesDao favoritesDao;

    public List<Favorites> findByConditions(Integer tenantId, Long userId, Integer targetType,
                                            DimensionContext dimensionContext, Long operatorId, Long referrerId, Long authId) {
        try {
            HashMap<String, Object> params = Maps.newHashMap();
            params.put("tenantId", tenantId);
            params.put("userId", userId);
            params.put("targetType", targetType);
            params.put("operatorId", operatorId);

            List<Favorites> result = favoritesDao.findByConditions(params);
            setExtraInfo(result, dimensionContext, referrerId, authId);
            return result;
        } catch (Exception e) {
            log.error("[Favorites]findByConditions failed, userId:{} targetType:{}, dimensionContext:{}, cause:{}",
                    userId, targetType, dimensionContext, e);
            throw new ServiceException("find.favorites.failed");
        }
    }

    public Paging<Favorites> paging(Integer tenantId, Long userId, Integer targetType,
                                    Integer pageNo, Integer pageSize,
                                    DimensionContext dimensionContext, Long operatorId, Long referrerId, Long authId) {
        try {
            PageInfo page = new PageInfo(pageNo, pageSize);
            Map<String, Object> params = Maps.newHashMap();
            params.put("tenantId", tenantId);
            params.put("userId", userId);
            params.put("targetType", targetType);
            params.put("operatorId", operatorId);
            params.put("offset", page.getOffset());
            params.put("limit", page.getLimit());

            log.info("Favorite search params:{}", JsonUtils.toJson(params));
            Paging<Favorites> paging = favoritesDao.paging(params);
            log.info("Favorite search results:{}", JsonUtils.toJson(paging));
            setExtraInfo(paging.getData(), dimensionContext, referrerId, authId);

            return paging;
        } catch (Exception e) {
            log.error("[Favorites]paging failed, userId:{} targetType:{} pageNo/Size:{}/{}, dimensionContext:{}, cause:{}",
                    userId, targetType, pageNo, pageSize, dimensionContext, e);
            throw new ServiceException("paging.favorites.failed");
        }
    }

    public Boolean checkFavorite(Integer tenantId, Long userId, Long targetId, Integer targetType, Long operatorId) {
        try {
            return favoritesCache.checkFavorite(tenantId, userId, targetId, targetType, operatorId);
        } catch (Exception e) {
            log.error("[Favorites]checkByConditions failed, userId:{} targetId:{} targetType:{} cause:{}",
                    userId, targetId, targetType, e);
            throw new ServiceException("check.favorites.failed");
        }
    }

    public Long countByConditions(Integer tenantId, Long userId,
                                  Long targetId, Integer targetType, Long operatorId) {
        try {
            HashMap<String, Object> params = Maps.newHashMap();
            params.put("tenantId", tenantId);
            params.put("userId", userId);
            params.put("targetId", targetId);
            params.put("targetType", targetType);
            params.put("operatorId", operatorId);

            return favoritesDao.countByConditions(params);
        } catch (Exception e) {
            log.error("[Favorites]countByTypeAndUserId failed, userId:{} targetType:{}, cause:{}",
                    userId, targetType, e);
            throw new ServiceException("count.favorites.failed");
        }
    }

    private void setExtraInfo(List<Favorites> favoriteList, DimensionContext dimension, Long referrerId, Long authId) {
        // 设置统一维度信息
        DimensionContext.set(dimension);

        if (CollectionUtils.isEmpty(favoriteList)) {
            return;
        }
        favoritesExtensionManager.afterQueryExtension(favoriteList, referrerId, authId);
    }

}
