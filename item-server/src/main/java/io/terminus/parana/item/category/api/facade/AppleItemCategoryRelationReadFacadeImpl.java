package io.terminus.parana.item.category.api.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.request.AppleItemCategoryRelationRequest;
import io.terminus.parana.item.category.api.converter.AppleItemConverter;
import io.terminus.parana.item.category.model.AppleItemCategoryRelationModel;
import io.terminus.parana.item.category.repository.AppleItemCategoryRelationDao;
import io.terminus.parana.item.open.api.bean.response.AppleItemCategoryInfo;
import io.terminus.parana.item.open.api.bean.response.AppleItemCategoryRelationInfo;
import io.terminus.parana.item.open.model.AppleItemCategoryModel;
import io.terminus.parana.item.open.repository.AppleItemCategoryDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class AppleItemCategoryRelationReadFacadeImpl implements AppleItemCategoryRelationReadFacade {

    @Autowired
    private AppleItemCategoryDao appleItemCategoryDao;

    @Autowired
    private AppleItemCategoryRelationDao appleItemCategoryRelationDao;

    @Autowired
    private AppleItemConverter appleItemConverter;

    @Override
    public Response<List<AppleItemCategoryInfo>> listAppleCategory() {
        List<AppleItemCategoryInfo> categoryInfos = Lists.newArrayList();
        List<AppleItemCategoryModel> categoryModels = appleItemCategoryDao.listChildren();
        if (CollectionUtil.isEmpty(categoryModels)) {
            return Response.ok(categoryInfos);
        }
        return Response.ok(appleItemConverter.modelsToInfos(categoryModels));
    }

    @Override
    public Response<Paging<AppleItemCategoryRelationInfo>> paging(AppleItemCategoryRelationRequest request) {
        Map<String, Object> criteria = JSONUtil.parseObj(request);
        PageInfo pageInfo = new PageInfo(request.getPageNo(), request.getPageSize());
        Paging<AppleItemCategoryRelationModel> paging = appleItemCategoryRelationDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), criteria);
        if (CollectionUtil.isEmpty(paging.getData())) {
            return Response.ok(Paging.empty());
        }
        return Response.ok(new Paging<>(paging.getTotal(), appleItemConverter.modelsToInfos2(paging.getData())));
    }
}
