package io.terminus.parana.item.delivery.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Author:cp
 * Created on 6/7/16.
 */
@Data
public abstract class DeliveryFeeBase {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long operatorId;

    /**
     * 包邮类型 1 自定义运费  2 商家承担运费 3 到付
     */
    private Integer isFree;

    /**
     * 是否到付
     */
    private Boolean isFreightCollect;

    /**
     * 包邮时重量限制
     */
    private Long amountLimit;

    /**
     * 运送方式:1-快递,2-EMS,3-平邮
     */
    private Integer deliverMethod;

    /**
     * 计价方式:1-按计量单位,2-按固定运费,3-按金额
     */
    private Integer chargeMethod;

    /********** 按固定运费 ************/

    /**
     * 运费,当计价方式为固定运费时使用
     */
    private Integer fee;

    /********** 按计量单位 ************/

    /**
     * 首费数量
     */
    private Integer initAmount;

    /**
     * 首费金额
     */
    private Integer initFee;

    /**
     * 增费数量
     */
    private Integer incrAmount;

    /**
     * 增费金额
     */
    private Integer incrFee;

    /********** 按金额 ************/

    /**
     * 订单不满该金额时，运费为lowFee
     */
    private Integer lowPrice;

    private Integer lowFee;

    /**
     * 订单高于该金额时，运费为highFee
     */
    private Integer highPrice;

    private Integer highFee;

    /**
     * 订单价格在lowFee，highFee之间时，运费为middleFee
     */
    private Integer middleFee;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 起送金额
     */
    private Long minPrice;

    /**
     * 包邮金额
     */
    private Long freePrice;

    /**
     * 不满包邮运费
     */
    private Long noFreePrice;


    @ApiModelProperty("运费信息")
    private String deliveryFeeTemp;

    public String getDeliveryFeeTemp(){
        if(this.isFree != null){
            if(this.isFree == 1){
                //按计量单位
                if(this.chargeMethod != null){
                    if(this.chargeMethod == 1){
                        if(this.incrFee == 0 && this.initFee == 0){
                            this.deliveryFeeTemp = "包邮";
                        }else if(this.incrFee == 0 && this.initFee != 0){
                            this.deliveryFeeTemp = "运费" + this.initFee.doubleValue() / 100 + "元";
                        }else if(this.incrFee != 0  && this.initFee == 0){
                            this.deliveryFeeTemp = this.initAmount
                                    + "个计量单位内，包邮，每增加"
                                    + this.incrAmount
                                    + "增加"
                                    + this.incrFee.doubleValue() / 100
                                    + "元";
                        }else{
                            this.deliveryFeeTemp =
                                    this.initAmount
                                            + "个计量单位内，运费"
                                            + this.initFee.doubleValue() / 100
                                            + " 元，每增加"
                                            + this.incrAmount
                                            + "增加"
                                            + this.incrFee.doubleValue() / 100
                                            + "元";
                        }
                    }else if (this.chargeMethod == 2){
                        //固定运费
                        if(this.fee.doubleValue() == 0){
                            this.deliveryFeeTemp = "包邮";
                        }else{
                            this.deliveryFeeTemp = (this.fee.doubleValue() / 100) + "元";
                        }
                    }else if (this.chargeMethod == 3){
                        //按金额
                        this.deliveryFeeTemp =
                                "起送金额"
                                        + this.minPrice.doubleValue() / 100
                                        + "元，包邮金额"
                                        + this.freePrice.doubleValue() / 100
                                        + "元，不满包邮运费"
                                        + this.noFreePrice.doubleValue() / 100
                                        + "元";
                    }
                }
            }else if(this.isFree == 2){
                this.deliveryFeeTemp = "包邮";
            }else if(this.isFree == 3){
                this.deliveryFeeTemp = "邮费到付";
            }
        }
        return this.deliveryFeeTemp;
    }
}
