package io.terminus.parana.item.category.api.converter;

import io.terminus.parana.item.category.api.bean.response.OperatorCategoryBindingInfo;
import io.terminus.parana.item.category.api.bean.response.OperatorCategoryInfo;
import io.terminus.parana.item.category.api.bean.response.OperatorCategoryWithChildrenInfo;
import io.terminus.parana.item.category.model.OperatorCategory;
import io.terminus.parana.item.category.model.OperatorCategoryBinding;
import io.terminus.parana.item.category.model.OperatorCategoryWithChildren;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface OperatorCategoryApiInfoConverter {

    OperatorCategoryInfo domain2info(OperatorCategory category);

    @Mapping(source = "operatorCategory", target = "operatorCategoryInfo")
    OperatorCategoryWithChildrenInfo domain2info(OperatorCategoryWithChildren categoryWithChildren);

    OperatorCategoryBindingInfo domain2info(OperatorCategoryBinding categoryBinding);
}
