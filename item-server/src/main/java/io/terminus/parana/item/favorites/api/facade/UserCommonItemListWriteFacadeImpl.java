package io.terminus.parana.item.favorites.api.facade;

import io.terminus.common.model.Response;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.favorites.api.bean.request.UserCommonItemListAddRequest;
import io.terminus.parana.item.favorites.enums.UserCommonItemListSource;
import io.terminus.parana.item.favorites.model.UserCommonItemList;
import io.terminus.parana.item.favorites.service.UserCommonItemListReadService;
import io.terminus.parana.item.favorites.service.UserCommonItemListWriteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserCommonItemListWriteFacadeImpl implements UserCommonItemListWriteFacade {

    private final IdGenerator idGenerator;
    private final UserCommonItemListReadService userCommonItemListReadService;
    private final UserCommonItemListWriteService userCommonItemListWriteService;

    @Override
    public Response<Boolean> add(UserCommonItemListAddRequest request) {
        //来源是商详页收藏时，先查询是否有通过购买添加的清单信息，如果有则不进行插入
        if (UserCommonItemListSource.FAVORITE.getValue().equals(request.getSource())){
            Long buyCount = userCommonItemListReadService.queryCount(request.getUserId(), request.getItemId(), UserCommonItemListSource.BUY_ITEM.getValue());
            Long favoriteCount = userCommonItemListReadService.queryCount(request.getUserId(), request.getItemId(), request.getSource());
            if (buyCount > 0 || favoriteCount > 0){
                return Response.ok(Boolean.TRUE);
            }
        }
        UserCommonItemList userCommonItemList = new UserCommonItemList();
        userCommonItemList.setId(idGenerator.nextValue(UserCommonItemList.class));
        userCommonItemList.setUserId(request.getUserId());
        userCommonItemList.setItemId(request.getItemId());
        userCommonItemList.setSource(request.getSource());
        userCommonItemList.setOperatorId(request.getOperatorId());
        userCommonItemList.setCreatedAt(new Date());
        userCommonItemList.setUpdatedAt(new Date());
        userCommonItemList.setUpdatedBy(String.valueOf(request.getUserId()));
        return Response.ok(userCommonItemListWriteService.add(userCommonItemList));
    }

    @Override
    public Response<Boolean> remove(Long userId, List<Long> itemId, Long operatorId, Integer source) {
        log.info("UserCommonItemListWriteFacadeImpl remove userId:{} itemId:{} operatorId:{}",
                userId, itemId, operatorId);
        return Response.ok(userCommonItemListWriteService.deleteByItems(userId,
                itemId, operatorId, source));
    }

    @Override
    public Response<Boolean> addBatch(List<UserCommonItemListAddRequest> requests) {
        log.info("UserCommonItemListWriteFacadeImpl addBatch param {}", requests);
        if(null != requests && !requests.isEmpty()) {
            List<UserCommonItemList> addList =  new ArrayList<>();
            requests.forEach(itemList -> {
                UserCommonItemList userCommonItemList = new UserCommonItemList();
                userCommonItemList.setId(idGenerator.nextValue(UserCommonItemList.class));
                userCommonItemList.setUserId(itemList.getUserId());
                userCommonItemList.setItemId(itemList.getItemId());
                userCommonItemList.setOperatorId(itemList.getOperatorId());
                userCommonItemList.setSource(itemList.getSource());
                userCommonItemList.setCreatedAt(new Date());
                userCommonItemList.setUpdatedAt(new Date());
                userCommonItemList.setUpdatedBy(String.valueOf(itemList.getUserId()));
                addList.add(userCommonItemList);
            });
            int processCount = userCommonItemListWriteService.addBatch(addList);
            log.info("UserCommonItemListWriteFacadeImpl addBatch processCount {}", processCount);
        }
        return Response.ok(true);
    }
}
