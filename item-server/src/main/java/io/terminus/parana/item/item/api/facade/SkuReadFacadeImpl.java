package io.terminus.parana.item.item.api.facade;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import io.terminus.api.utils.ExceptionUtil;
import io.terminus.common.model.Response;
import io.terminus.parana.item.area.api.bean.request.AreaSkuQueryBySingleItemIdRequest;
import io.terminus.parana.item.area.api.bean.request.SkuSaleRequest;
import io.terminus.parana.item.area.api.bean.response.AreaSkuInfo;
import io.terminus.parana.item.area.api.facade.AreaSkuReadFacade;
import io.terminus.parana.item.common.annotation.MicroService;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.api.bean.request.sku.*;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.item.api.converter.output.ItemApiInfoConverter;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-08-01
 */
@Slf4j
@Service
public class SkuReadFacadeImpl implements SkuReadFacade {

    private final SkuReadDomainService skuReadDomainService;
    private final ItemApiInfoConverter itemApiInfoConverter;
    private final AreaSkuReadFacade areaSkuReadFacade;

    public SkuReadFacadeImpl(SkuReadDomainService skuReadDomainService,
                             ItemApiInfoConverter itemApiInfoConverter,
                             AreaSkuReadFacade areaSkuReadFacade) {
        this.skuReadDomainService = skuReadDomainService;
        this.itemApiInfoConverter = itemApiInfoConverter;
        this.areaSkuReadFacade = areaSkuReadFacade;
    }

    @Override
    @MicroService
    public Response<List<SkuInfo>> queryById(SkuQueryByIdRequest request) {
        try {
            List<Sku> skuList = skuReadDomainService.findByIdSet(request.getIdSet(), request.getTenantId(),
                    request.getDimensionType(), request.getDimensionCode());
            return Response.ok(itemApiInfoConverter.getSkuList(skuList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<List<SkuInfo>> queryByItemName(SkuQueryByItemNameRequest request) {
        try {
            List<Sku> skuList = skuReadDomainService.queryByItemNameNoCache(request.getItemName(), request.getTenantId(),
                    request.getDimensionType(), request.getDimensionCode());
            return Response.ok(itemApiInfoConverter.getSkuList(skuList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<List<SkuInfo>> queryByCode(SkuQueryByCodeRequest request) {
        try {
            List<Sku> skuList = skuReadDomainService.findBySkuCodeSet(request.getSkuCodeSet(), request.getShopId(),
                    request.getTenantId(), request.getDimensionType(), request.getDimensionCode());
            return Response.ok(itemApiInfoConverter.getSkuList(skuList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<List<SkuInfo>> queryByItem(SkuQueryByItemRequest request) {
        try {
            List<Sku> skuList = skuReadDomainService.findByItemIdSet(request.getItemIdSet(), request.getTenantId(),
                    request.getDimensionType(), request.getDimensionCode());
            List<SkuInfo> skuInfoList = itemApiInfoConverter.getSkuList(skuList);

            // 过滤未启用的sku集合
            if (request.getExcludeDisable()) {
                skuInfoList = AssembleDataUtils.listFilter(skuInfoList, SkuInfo::getEnable);
            }

            return Response.ok(skuInfoList);
        } catch (Exception e) {
            log.error("==queryByItem=== {}", ExceptionUtil.getFullStackString(e));
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<SkuInfo> queryBySingleId(SkuQueryBySingleIdRequest request) {
        try {
            Sku sku = skuReadDomainService.findById(request.getId(), request.getTenantId(), request.getDimensionType(),
                    request.getDimensionCode());
            return Response.ok(itemApiInfoConverter.get(sku));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }


    @Override
    @MicroService
    public Response<List<SkuInfo>> queryBySingleCode(SkuQueryBySingleCodeRequest request) {
        try {
            List<Sku> skuList = skuReadDomainService.findBySkuCode(request.getSkuCode(), request.getShopId(),
                    request.getTenantId(), request.getDimensionType(), request.getDimensionCode());
            return Response.ok(itemApiInfoConverter.getSkuList(skuList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<List<SkuInfo>> queryByOuterId(SkuQueryByOuterIdRequest request) {
        try {
            List<Sku> skuList = skuReadDomainService.findByOuterIdSet(request.getOuterIdSet(),request.getShopId(), request.getTenantId());
            return Response.ok(itemApiInfoConverter.getSkuList(skuList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<List<SkuInfo>> queryByBarcode(SkuQueryByBarcodeRequest request) {
        try {
            List<Sku> skuList = skuReadDomainService.findByBarcodeCheckShopIdSet(request.getBarcode(),
                    request.getShopIdSet(), request.getTenantId(), request.getDimensionType(), request.getDimensionCode());
            return Response.ok(itemApiInfoConverter.getSkuList(skuList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }


    @Override
    public Response<Long> queryCurrentSkuNumByVendorId(SkuSaleRequest request) {
        return Response.ok(skuReadDomainService.queryCurrentSkuNumByVendorId(request.getVendorId(),request.getStartTime(),request.getEndTime()));
    }

    @Override
    public Response<List<SkuInfo>> queryCurrentSkuNumByVendorIdGroupTime(SkuSaleRequest request) {
        List<Sku> skuList = skuReadDomainService.queryCurrentSkuNumByVendorIdGroupTime(
                request.getVendorId(), request.getStartTime(), request.getEndTime());
        return Response.ok(itemApiInfoConverter.getSkuList(skuList));
    }

    @Override
    public Response<List<SkuInfo>> querySkuWithAreaSku(Integer tenantId, Long itemId, Long operatorId) {
        try {
            SkuQueryByItemRequest skuRequest = new SkuQueryByItemRequest();
            skuRequest.setItemIdSet(Collections.singleton(itemId));
            skuRequest.setTenantId(tenantId);
            skuRequest.setExcludeDisable(false);
            List<SkuInfo> skuList = Assert.take(this.queryByItem(skuRequest));

            AreaSkuQueryBySingleItemIdRequest areaSkuRequest = new AreaSkuQueryBySingleItemIdRequest();
            areaSkuRequest.setItemId(itemId);
            areaSkuRequest.setOperatorId(operatorId);
            List<AreaSkuInfo> areaSkuList = Assert.take(areaSkuReadFacade.queryBySingleItemId(areaSkuRequest));
            return Response.ok(getSkuWithAreaInfoList(skuList, areaSkuList));
        }catch (Exception ex){
            log.error("==querySkuWithAreaSku=== {}", ExceptionUtil.getFullStackString(ex));
            return Response.fail(ex.getMessage());
        }


    }

    @Override
    @MicroService
    public Response<List<SkuInfo>> queryByIdV2(SkuQueryByIdRequest request) {
        try {
            List<Sku> skuList = skuReadDomainService.findByIdSet(request.getIdSet(), request.getItemIdSet(), request.getTenantId(),
                    request.getDimensionType(), request.getDimensionCode());
            return Response.ok(itemApiInfoConverter.getSkuList(skuList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    private List<SkuInfo> getSkuWithAreaInfoList(List<SkuInfo> skuList, List<AreaSkuInfo> areaSkuList) {
        List<SkuInfo> result = Lists.newArrayList();
        Map<Long, AreaSkuInfo> areaSkuMapWithSkuIdKey = AssembleDataUtils.list2map(areaSkuList, AreaSkuInfo::getSkuId);

        skuList.forEach(sku -> {
            AreaSkuInfo areaSkuInfo = areaSkuMapWithSkuIdKey.get(sku.getId());
            if (null != areaSkuInfo) {
                if (areaSkuInfo.getAttributes() == null) {
                    areaSkuInfo.setAttributes(Lists.newArrayList());
                }
                if (areaSkuInfo.getOriginalPrice() != null) {
                    sku.setOriginalPrice(areaSkuInfo.getOriginalPrice());
                }
                sku.setAreaSku(areaSkuInfo);
                result.add(sku);
            }
        });
        return result;
    }
}