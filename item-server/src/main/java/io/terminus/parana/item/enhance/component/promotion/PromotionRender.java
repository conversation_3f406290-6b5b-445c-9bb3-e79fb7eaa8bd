package io.terminus.parana.item.enhance.component.promotion;

import io.terminus.parana.item.area.api.bean.response.AreaSkuInfo;
import io.terminus.parana.item.common.dimension.DimensionContext;
import io.terminus.parana.item.enhance.api.bean.response.DynamicRenderInfo;
import io.terminus.parana.item.enhance.model.LocationBO;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.third.enums.ThirdDeviceType;
import io.terminus.parana.item.third.info.PromotionResult;

import java.util.List;

/**
 * 营销调用
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-06-06
 */
public interface PromotionRender {

    /**
     * 营销渲染-调用
     *
     * @param bo               位置信息
     * @param selectedSku      选中的sku
     * @param areaSku
     * @param categoryIdList   类目id
     * @param brandId          品牌id
     * @param deviceType       设备类型
     * @param dimensionContext
     * @return 营销结果
     */
    PromotionResult renderInvoke(LocationBO bo, SkuInfo selectedSku, AreaSkuInfo areaSku, List<Long> categoryIdList, Long brandId, ThirdDeviceType deviceType, DimensionContext dimensionContext, Long channelId);

    /**
     * 营销渲染-处理
     * @param selectedSku     选中的sku
     * @param renderInfo      商详渲染对象
     * @param promotionResult 促销结果
     */
    void renderHandle(SkuInfo selectedSku, DynamicRenderInfo renderInfo, PromotionResult promotionResult);
}
