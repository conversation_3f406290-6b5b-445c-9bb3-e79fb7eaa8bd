package io.terminus.parana.item.footprint.api.facade;

import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.parana.item.common.converter.GeneralConverter;
import io.terminus.parana.item.footprint.api.bean.request.FootprintCountRequest;
import io.terminus.parana.item.footprint.api.bean.request.FootprintPagingRequest;
import io.terminus.parana.item.footprint.api.bean.response.FootprintInfo;
import io.terminus.parana.item.footprint.api.converter.FootprintApiInfoConverter;
import io.terminus.parana.item.footprint.model.Footprint;
import io.terminus.parana.item.footprint.service.FootprintReadDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FootprintReadFacadeImpl implements FootprintReadFacade {

    private final FootprintReadDomainService itemFootPrintReadDomainService;
    private final FootprintApiInfoConverter itemFootprintApiInfoConverter;

    @Override
    public Response<Paging<FootprintInfo>> pagingOnRedis(FootprintPagingRequest request) {
        try {
            PageInfo page = new PageInfo(request.getPageNo(), request.getPageSize());
            Paging<Footprint> paging = itemFootPrintReadDomainService.pagingOnRedis(
                    request.getTenantId(), request.getUserId(), request.getTargetType(),
                    page.getOffset(), page.getLimit(), request.getDimensionContext(), request.getOperatorId(),request.getCurrentOperatorId(), request.getReferrerId(), request.getAuthId());
            return Response.ok(GeneralConverter.batchConvert(paging, itemFootprintApiInfoConverter::domain2info));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Long> count(FootprintCountRequest request) {
        try {
            return Response.ok(itemFootPrintReadDomainService.countOnRedisByUserId(
                    request.getTenantId(), request.getUserId(), request.getTargetType(), request.getOperatorId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }


}
