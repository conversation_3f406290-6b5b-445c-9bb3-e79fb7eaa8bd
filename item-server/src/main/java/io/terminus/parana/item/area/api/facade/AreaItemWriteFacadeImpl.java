package io.terminus.parana.item.area.api.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.api.utils.ParamUtil;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.item.area.api.bean.request.*;
import io.terminus.parana.item.area.api.bean.request.param.AreaItemFreezeOperateBO;
import io.terminus.parana.item.area.api.converter.AreaItemApiConverter;
import io.terminus.parana.item.area.bo.AreaItemOnShelfOperateBO;
import io.terminus.parana.item.area.component.AreaItemOnShelfHelper;
import io.terminus.parana.item.area.enums.AreaItemStatus;
import io.terminus.parana.item.area.enums.CooperationMode;
import io.terminus.parana.item.area.enums.LogisticsMode;
import io.terminus.parana.item.area.manager.AreaItemManager;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.model.YjrImportSkuPriceLog;
import io.terminus.parana.item.area.repository.AreaItemDao;
import io.terminus.parana.item.area.repository.AreaSkuDao;
import io.terminus.parana.item.area.repository.YjrImportSkuPriceLogDao;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.area.service.AreaItemWriteDomainService;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.choicelot.model.*;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibItemDao;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibMarkupDao;
import io.terminus.parana.item.choicelot.repository.DistributorItemLibDao;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibReadDomainService;
import io.terminus.parana.item.choicelot.service.ChoiceLotLibSkuReadService;
import io.terminus.parana.item.choicelot.utils.MarkupCalculateUtils;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.export.manager.*;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.item.api.bean.request.item.OpenUpdateItemStatusRequest;
import io.terminus.parana.item.item.api.bean.request.item.param.ImageParam;
import io.terminus.parana.item.item.api.bean.request.item.param.InventoryExportParam;
import io.terminus.parana.item.item.api.converter.input.ImageApiConverter;
import io.terminus.parana.item.item.api.facade.ItemDumpEmergencyWriteFacade;
import io.terminus.parana.item.item.bo.VendorChannelPriceCellBO;
import io.terminus.parana.item.item.bo.VendorDefaultPriceCellBO;
import io.terminus.parana.item.item.enums.YYTItemConstant;
import io.terminus.parana.item.item.manager.ItemOffShelfManager;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.repository.SkuDao;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.partnership.service.VendorPartnershipReadDomainService;
import io.terminus.parana.item.plugin.third.api.misc.api.ExcelReportWriteApi;
import io.terminus.parana.item.plugin.third.api.misc.api.ParanaThirdMessageWriteApi;
import io.terminus.parana.item.price.api.bean.response.ImportYjrPriceResult;
import io.terminus.parana.item.price.model.ItemQuotedPrice;
import io.terminus.parana.item.price.model.ItemQuotedPriceLog;
import io.terminus.parana.item.price.util.ItemPriceHelper;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.service.ShopReadDomainService;
import io.terminus.parana.item.third.param.ThirdParanaThirdMessageBean;
import io.terminus.parana.item.third.param.ThirdParanaThirdMessageCreateRequest;
import io.terminus.parana.item.third.param.ThirdUploadReportCreateRequest;
import io.terminus.parana.item.web.excel.ProcessResult;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageBean;
import io.terminus.parana.misc.itemorder.api.api.bean.request.ParanaThirdMessageCreateRequest;
import io.terminus.parana.misc.itemorder.api.api.facade.ParanaThirdMessageWriteFacade;
import io.terminus.parana.trade.common.util.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AreaItemWriteFacadeImpl implements AreaItemWriteFacade {
    private final ImageApiConverter imageApiConverter;
    private final AreaItemApiConverter areaItemApiConverter;
    private final AreaItemWriteDomainService areaItemWriteDomainService;
    private final ShopReadDomainService shopReadDomainService;
    private final AreaItemReadDomainService areaItemReadDomainService;
    private final ItemReadDomainService itemReadDomainService;
    private final VendorPartnershipReadDomainService vendorPartnershipReadDomainService;
    private final YjrImportSkuPriceLogDao yjrImportSkuPriceLogDao;
    private final AreaSkuReadDomainService areaSkuReadDomainService;
    private final ItemPriceHelper itemPriceHelper;
    private final AreaItemManager areaItemManager;
    private final ParanaThirdMessageWriteApi paranaThirdMessageWriteApi;

    private final AreaItemDao areaItemDao;

    private final ItemOffShelfManager itemOffShelfManager;
    private final ItemDumpEmergencyWriteFacade itemDumpEmergencyWriteFacade;
    private final AreaItemOnShelfHelper areaItemOnShelfHelper;

    private final SkuDao skuDao;
    private final AreaSkuDao areaSkuDao;
    private final ChoiceLotLibSkuReadService choiceLotLibSkuReadService;
    private final ChoiceLotLibMarkupDao choiceLotLibMarkupDao;
    private final ChoiceLotLibItemDao choiceLotLibItemDao;
    private final IdGenerator idGenerator;
    private final ParanaThirdMessageWriteFacade paranaThirdMessageWriteFacade;
    private final ChoiceLotLibReadDomainService choiceLotLibReadDomainService;
    private final ItemImportTranscriptProcessor itemImportTranscriptProcessor;
    private final ItemGroupImportTranscriptProcessor importTranscriptProcessor;
    private final ItemBatchShelfImportTranscriptProcessor itemBatchShelfImportTranscriptProcessor;
    private final DistributorItemLibDao distributorItemLibDao;

    @Value("${item.imageUrl.oss}")
    private String ossUrl;
    @Value("${item.imageUrl.cdn}")
    private String cdnUrl;

    @Value("${scce.vendorId}")
    private Long vendorId;

    @Autowired
    private ThirdPartyRegistry registry;

    @Autowired
    private ExcelReportWriteApi excelReportWriteApi;

    @Override
    public Response<Boolean> publish(VendorItemPublishRequest request) {
        return Response.ok(areaItemWriteDomainService.publishItem(request.getItemId(), request.getOperatorIdSet(), request.getZqAdminOperatorId(), request.getUserId(), request.getUserName()));
    }

    @Override
    public Response<Boolean> publishByItems(VendorItemPublishRequest request) {

        //查供应商的全部区域商品-已发布的信息
        List<AreaItem> areaItems = areaItemReadDomainService.findByVendorIdAndItemIds(request.getVendorId(), request.getItemIdSet());

        Map<Long, List<AreaItem>> areaItemHashMap = new HashMap<>();

        for (int i = 0; i < areaItems.size(); i++) {
            AreaItem areaItem = areaItems.get(i);
//            if (ItemStatus.OFF_SHELF.getValue() == areaItem.getAreaOperatorStatus()) {
//                continue;
//            }
            if (areaItemHashMap.get(areaItem.getItemId()) != null) {

                List<AreaItem> areaItemList = areaItemHashMap.get(areaItem.getItemId());
                areaItemList.add(areaItem);
                areaItemHashMap.put(areaItem.getItemId(), areaItemList);
            } else {
                List<AreaItem> areaItemList = new ArrayList<>();
                areaItemList.add(areaItem);
                areaItemHashMap.put(areaItem.getItemId(), areaItemList);
            }
        }
        Set<Long> itemIdSets = request.getItemIdSet();

        Set<Long> operatorIdSets = request.getOperatorIdSet();
        log.info("选中区域:{}",operatorIdSets);
//        //遍历itemId
//        itemIdSets.forEach((itemId) -> {
//            //前端勾选
//            operatorIdSets.forEach((operatorId) -> {
//
//                List<AreaItem> areaItemList = areaItemHashMap.get(itemId);
//
//                if (areaItemList != null && !request.getVendorId().equals(vendorId)) {
//                    for (AreaItem areaItem : areaItemList) {
//                        log.info("operatorId:::::::::{},areaitem.operatorId:::::::::{}", operatorId, areaItem.getOperatorId());
//                        if (operatorId.compareTo(areaItem.getOperatorId()) == 0) {
//                            throw new RestException("此商品:" + areaItem.getName() + "已发布到区域运营");
//                        }
//                    }
//                }
//            });
//        });

        return Response.ok(areaItemWriteDomainService.publishByItems(request.getItemIdSet(), request.getOperatorIdSet(), null, request.getUserId(), request.getUserName(),request.getIsApiUpdate()));
    }

    @Override
    public Response<Boolean> publishByItemsZq(VendorItemPublishZqRequest request) {

        ParamUtil.notEmpty(request.getItemIdSet(), "商品ID不能为空！");
        ParamUtil.notEmpty(request.getOperatorIdSet(), "发布的区域运营商不能为空！");
        ParamUtil.nonNull(request.getZqAdminOperatorId(), "政企总部运营商不能为空！");

        List<Item> itemList = itemReadDomainService.findByIdSet(request.getItemIdSet(), request.getTenantId(), null, null);
        ParamUtil.notEmpty(itemList, "选择的商品信息不存在！");

        Set<Long> vendorIdSet = AssembleDataUtils.list2set(itemList, Item::getShopId);
        ParamUtil.expectTrue(!vendorIdSet.isEmpty(), "供应商信息不存在！");

        List<AreaItem> exitsAreaItems = areaItemReadDomainService.findByVendorOperatorItemIdsOnshelf(vendorIdSet, request.getOperatorIdSet(), request.getItemIdSet());
        if (null != exitsAreaItems && !exitsAreaItems.isEmpty()) {
            String warnMsg = "";
            for (AreaItem areaItem : exitsAreaItems) {
                warnMsg = warnMsg + "商品:" + areaItem.getItemId() + "-" + areaItem.getName() + " 区域ID:" + areaItem.getOperatorId() + "已经上架，不允许重复发布！" + "\n\t";
            }
            ParamUtil.isBlank(warnMsg, warnMsg);
        }

        return Response.ok(areaItemWriteDomainService.publishByItems(request.getItemIdSet(), request.getOperatorIdSet(), request.getZqAdminOperatorId(), null, null, false));
    }


    @Override
    public Response<Boolean> setDefaultPrice(AreaSkuSetDefaultPriceRequest request) {
        VendorDefaultPriceCellBO priceCellBO = new VendorDefaultPriceCellBO();
        priceCellBO.setDefaultPrice(request.getDefaultPrice());
        priceCellBO.setDefaultBasePrice(request.getDefaultBasePrice());
        priceCellBO.setDefaultOriginalPrice(request.getDefaultOriginalPrice());
        Shop operator = shopReadDomainService.findById(request.getOperatorId(), request.getTenantId(), null);
        if (null != operator && operator.getBusinessType() == 2) {
            priceCellBO.setDefaultPCommission(0L);
            priceCellBO.setDefaultCommission(0L);
        } else {
            priceCellBO.setDefaultPCommission(request.getDefaultPCommission());
            priceCellBO.setDefaultCommission(request.getDefaultCommission());
        }
        return Response.ok(areaItemWriteDomainService.setSkuDefaultPrice(request.getUpdatedBy(), request.getTenantId(), request.getSkuId(), request.getOperatorId(),
                priceCellBO, null));
    }

    @Override
    public Response<Boolean> setDefaultPriceYTY(AreaSkuSetDefaultPriceRequest request) {
        VendorDefaultPriceCellBO priceCellBO = new VendorDefaultPriceCellBO();
        priceCellBO.setDefaultPrice(request.getDefaultPrice());
        priceCellBO.setDefaultBasePrice(request.getDefaultBasePrice());
        priceCellBO.setDefaultOriginalPrice(request.getDefaultOriginalPrice());
        priceCellBO.setDefaultSpreadPrice(request.getDefaultSpreadPrice());
        priceCellBO.setRegisterBuyPrice(request.getRegisterBuyPrice());
        priceCellBO.setRegisterSpreadPrice(request.getRegisterBuyPrice());
        priceCellBO.setWhiteListPrice(request.getWhiteListPrice());
        priceCellBO.setV1Price(request.getV1Price());
        priceCellBO.setV2Price(request.getV2Price());
        priceCellBO.setDefaultPCommission(0L);
        priceCellBO.setDefaultCommission(0L);
        return Response.ok(areaItemWriteDomainService.setSkuDefaultPriceYTY(request.getUpdatedBy(), request.getTenantId(), request.getSkuId(), request.getOperatorId(),
                priceCellBO));
    }

    @Override
    public Response<Boolean> setSupplyPrice(AreaSkuSetDefaultSupplyPriceRequest request) {
        VendorDefaultPriceCellBO priceCellBO = new VendorDefaultPriceCellBO();
        priceCellBO.setDefaultPrice(request.getDefaultPrice());
        priceCellBO.setDefaultBasePrice(request.getDefaultBasePrice());
        priceCellBO.setDefaultOriginalPrice(request.getDefaultOriginalPrice());
        priceCellBO.setDefaultSupplyPrice(request.getDefaultSupplyPrice());
        Shop operator = shopReadDomainService.findById(request.getOperatorId(), request.getTenantId(), null);
        if (null != operator && operator.getBusinessType() == 2) {
            priceCellBO.setDefaultPCommission(0L);
            priceCellBO.setDefaultCommission(0L);
        } else {
            priceCellBO.setDefaultPCommission(request.getDefaultPCommission());
            priceCellBO.setDefaultCommission(request.getDefaultCommission());
        }
        return Response.ok(areaItemWriteDomainService.setSkuSupplyPrice(request.getUpdatedBy(), request.getTenantId(), request.getSkuId(), request.getOperatorId(),
                priceCellBO));
    }

    @Override
    public Response<Boolean> setChannelPrice(AreaSkuSetChannelPriceRequest request) {
        VendorChannelPriceCellBO priceCellBO = new VendorChannelPriceCellBO();
        priceCellBO.setChannelBasePrice(request.getChannelBasePrice());
        priceCellBO.setChannelPrice(request.getChannelPrice());
        Shop operator = shopReadDomainService.findById(request.getOperatorId(), request.getTenantId(), null);
        if (null != operator && operator.getBusinessType() == 2) {
            priceCellBO.setChannelPCommission(0L);
            priceCellBO.setChannelCommission(0L);
        } else {
            priceCellBO.setChannelPCommission(request.getChannelPCommission());
            priceCellBO.setChannelCommission(request.getChannelCommission());
        }
        return Response.ok(areaItemWriteDomainService.setSkuChannelPrice(request.getUpdatedBy(), request.getTenantId(), request.getSkuId(), request.getOperatorId(),
                request.getChannelId(), priceCellBO));
    }

    @Override
    public Response<Boolean> deleteAreaSkuChannelPrice(AreaSkuChannelPriceDeleteRequest request) {
        return Response.ok(areaItemWriteDomainService.deleteSkuChannelPrice(request.getSkuId(), request.getOperatorId(),
                request.getChannelId()));
    }

    @Override
    public Response<Boolean> operatorOnShelf(AreaItemOnShelfRequest request) {
        return Response.ok(areaItemWriteDomainService.operatorOnShelf(
                request.getOperatorId(), request.getItemIdSet(), request.getTenantId(), request.getUpdatedBy()));
    }

    @Override
    public Response<Boolean> operatorOffShelf(AreaItemOffShelfRequest request) {
        return Response.ok(areaItemWriteDomainService.operatorOffShelf(
                request.getOperatorId(), request.getItemIdSet(), request.getTenantId(), request.getUpdatedBy()));
    }

    @Override
    public Response<Boolean> operatorOffShelfBatch(AreaItemOffShelfRequest request) {
        try {
            request.checkParam();
            // 判断数量
            if (request.getItemIdSet().size() > 200) {
                return Response.fail("一次仅支持200个商品的处理！");
            }
            // 校验所有商品是否都是上架状态
            Integer count = areaItemDao.selectAreaItemOffShelf(request.getOperatorId(), request.getItemIdSet());
            if(count != request.getItemIdSet().size()){
                return Response.fail("仅支持状态为上架的商品下架！");
            }
            // 添加下架定时任务数据
            areaItemManager.offShelfRecord(request.getOperatorId(), request.getItemIdSet(), new Date(), request.getTenantId(), request.getUpdateBy(), 1);
            // 商品下架逻辑
            itemOffShelfManager.itemOffShelf();
            // 刷新ES
            AreaItemEmergencyDumpRequest areaItemEmergencyDumpRequest = new AreaItemEmergencyDumpRequest();
            areaItemEmergencyDumpRequest.setId(request.getItemIdSet());
            areaItemEmergencyDumpRequest.setOperatorId(request.getOperatorId());
            itemDumpEmergencyWriteFacade.areaItemDump(areaItemEmergencyDumpRequest);
        }catch (Exception e) {
            log.error("运营强制下架失败，error is {}.", Throwables.getStackTraceAsString(e));
            return Response.fail("下架失败!,错误原因：" + e.getMessage());
        }
        return Response.ok(Boolean.TRUE);
    }

    @Override
    public Response<Boolean> adminOperatorOffShelfBatch(AdminAreaItemOffShelfRequest request) {
        request.checkParam();
        // 判断数量
        if (request.getOperatorIdAndItemIdsMap().size() > 200) {
            return Response.fail("一次仅支持200个商品的处理！");
        }
        try {
            areaItemWriteDomainService.adminOperatorOffShelfBatch(request);
        }catch (Exception e) {
            log.error("平台强制下架失败，error is {}.", Throwables.getStackTraceAsString(e));
            return Response.fail("下架失败!,错误原因：" + e.getMessage());
        }
        return Response.ok(Boolean.TRUE);
    }

    @Override
    public Response<Boolean> operatorDeleteAll(AreaItemDeleteAllRequest request) {
        return Response.ok(areaItemWriteDomainService.operatorDeleteAll(
                request.getOperatorId(), request.getVendorId(), request.getUpdatedBy()));
    }

    @Override
    public Response<Boolean> hqFreeze(AreaItemFreezeOperateRequest request) {
        List<AreaItemFreezeOperateBO> boList = AssembleDataUtils.list2list(request.getAreaItemParamList(), areaItemApiConverter::get);
        return Response.ok(areaItemWriteDomainService.hqFreeze(boList, request.getUpdatedBy()));
    }

    @Override
    public Response<Boolean> hqFreezeVendor(AreaItemFreezeVendorRequest request) {
        List<AreaItemFreezeOperateBO> boList = AssembleDataUtils.list2list(request.getAreaItemParamList(), areaItemApiConverter::get);
        return Response.ok(areaItemWriteDomainService.hqFreezeVendor(boList, request.getFrozen(), request.getVendorId()
                , request.getTenantId(), request.getUpdatedBy()));
    }

    @Override
    public Response<Boolean> hqFreezeVendor(AreaItemFreezeIdRequest request) {
        return Response.ok(areaItemWriteDomainService.hqFreezeVendor(request.getOwnerId()
                , request.getTenantId(), request.getUpdatedBy()));
    }

    @Override
    public Response<Boolean> hqFreezeOperator(AreaItemFreezeIdRequest request) {
        return Response.ok(areaItemWriteDomainService.hqFreezeOperator(request.getOwnerId()));
    }

    @Override
    public Response<Boolean> hqUnfreezeVendor(AreaItemFreezeIdRequest request) {
        return Response.ok(areaItemWriteDomainService.hqUnfreezeVendor(request.getOwnerId()
                , request.getTenantId(), request.getUpdatedBy()));
    }

    @Override
    public Response<Boolean> hqUnfreezeOperator(AreaItemFreezeIdRequest request) {
        return Response.ok(areaItemWriteDomainService.hqUnfreezeOperator(request.getOwnerId()));
    }

    @Override
    public Response<Boolean> hqUnfreeze(AreaItemFreezeOperateRequest request) {
        List<AreaItemFreezeOperateBO> boList = AssembleDataUtils.list2list(request.getAreaItemParamList(), areaItemApiConverter::get);
        return Response.ok(areaItemWriteDomainService.hqUnfreeze(boList));
    }

    @Override
    public Response<Boolean> vendorStopSelling(AreaItemStopSellingOperateRequest request) {
        return Response.ok(areaItemWriteDomainService.vendorStopSelling(request.getUpdatedBy(), request.getItemId(), request.getVersion(), request.getVendorId()));
    }

    @Override
    public Response<Boolean> vendorResumeSelling(AreaItemStopSellingOperateRequest request) {
        return Response.ok(areaItemWriteDomainService.vendorResumeSelling(request.getItemId(), request.getVersion(), request.getVendorId()));
    }

    @Override
    public Response<Boolean> updateAreaInfos(AreaItemQueryRequest request) {
        return Response.ok(areaItemWriteDomainService.updateAreaItemSkuAndQuotedInfo(request.getOperatorId(), request.getVendorId()
                , request.getLogisticsMode(), request.getCooperationMode()));
    }

    @Override


    public Response<Boolean> operatorUpdate(OperatorUpdateItemRequest request) {

        //CDN
        if (StringUtils.isNotBlank(ossUrl) && StringUtils.isNotBlank(cdnUrl)) {
            String mainImage = request.getMainImage();
            if (StringUtils.isNotBlank(mainImage)) {
                mainImage = mainImage.replaceAll(ossUrl, cdnUrl);
                request.setMainImage(mainImage);
            }
            if (request.getImages() != null
                    && !request.getImages().isEmpty()) {
                for (ImageParam imageParam : request.getImages()) {
                    String image = imageParam.getUrl();
                    if (StringUtils.isNotBlank(image)) {
                        image = image.replaceAll(ossUrl, cdnUrl);
                        imageParam.setUrl(image);
                    }
                }
            }
        }

        AreaItem areaItem = new AreaItem();
        areaItem.setItemId(request.getItemId());
        areaItem.setOperatorId(request.getOperatorId());
        areaItem.setName(request.getName());
        areaItem.setMainImage(request.getMainImage());
        areaItem.setImages(imageApiConverter.get(request.getImages()));
        areaItem.setDeliveryFeeTempId(request.getDeliveryTemplateFeeId());
        areaItem.setUpdatedBy(request.getUpdatedBy());

        return Response.ok(areaItemWriteDomainService.updateAreaItem(areaItem));
    }

    @Override
    public Response<Boolean> operatorUpdateItemWeight(AreaItemRecommendRequest request) {

        AreaItem areaItem = new AreaItem();
        areaItem.setItemId(request.getItemId());
        areaItem.setOperatorId(request.getOperatorId());
        areaItem.setUpdatedBy(request.getUpdatedBy());
        areaItem.setWeight(request.getWeight());

        return Response.ok(areaItemWriteDomainService.updateItemWeight(areaItem));
    }

    @Override
    public Response<Boolean> updateCommissionType(AreaItemUpdateCommissionTypeRequest request) {
        return Response.ok(areaItemWriteDomainService.updateCommissionType(request.getOperatorId(), request.getCommissionType(), request.getUpdatedBy(), request.getItemIdSet()));
    }

    @Override
    public Response<Boolean> updateOperatorOnAudi(AreaItemFreezeOperateRequest request) {
        List<AreaItemFreezeOperateBO> boList = AssembleDataUtils.list2list(request.getAreaItemParamList(), areaItemApiConverter::get);
        return Response.ok(areaItemWriteDomainService.updateOperatorOnAudi(boList));
    }

    @Override
    public Response<Boolean> updateOperatorOffAudi(AreaItemFreezeOperateRequest request) {
        List<AreaItemFreezeOperateBO> boList = AssembleDataUtils.list2list(request.getAreaItemParamList(), areaItemApiConverter::get);
        return Response.ok(areaItemWriteDomainService.updateOperatorOffAudit(boList));
    }

    @Override
    public Response<Boolean> updateAreaAndChannelByOperator(AreaItemUpdateAreaAndChannelRequest request) {
        try {
            return Response.ok(areaItemWriteDomainService.updateAreaAndChannelByOperator(request.getOperatorId(), request.getItemId(),
                    request.getSalesArea(), request.getSalesChannel(), request.getTenantId(), request.getUpdatedBy()));
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error("fail to update area and channel request: {}, cause: {}", request, e.getStackTrace());
            return Response.fail("area.item.update.areaAndChannel.fail");
        }
    }

    @Override
    public Response<Boolean> ytskPublishItem(YtskItemPublishRequest request) {
        try {
            return Response.ok(areaItemWriteDomainService.ytskPublishItem(request.getOperatorId(), request.getVendorId(),
                    request.getRealVendorId(), request.getRealOperatorId(), request.getItemIdSet()));
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error("fail to ytsk publish item request: {}, cause: {}", request, e.getStackTrace());
            return Response.fail("area.item.update.areaAndChannel.fail");
        }
    }

    @Override
    public Response<Boolean> updateOpenStatus(AreaItemOpenStatusUpdateRequest request) {
        try {
            log.info("facade action:::::::::{}", request.getAction());
            return Response.ok(areaItemWriteDomainService.updateOpenStatus(request.getTenantId(), request.getUpdatedBy(),
                    request.getAction(), request.getStatus(), request.getOperatorId(), request.getItemIdSet()));
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error("fail to update openStatus request: {}, cause: {}", request, Throwables.getStackTraceAsString(e));
            return Response.fail("area.item.update.openStatus.fail");
        }
    }

    @Override
    public Response<String> offShelfCheck(AreaItemOffShelfRequest request) {
        return Response.ok(areaItemWriteDomainService.offShelfCheck(
                Lists.newArrayList(request.getOperatorId()), request.getItemIdSet()));
    }

    @Override
    public Response<String> vendorOffShelfCheck(AreaItemOffShelfVendorRequest request) {
        for (Long itemId : request.getItemIdSet()) {
            Item item = itemReadDomainService.findById(itemId, RequestContext.getTenantId(), null, null);
            log.info("vendorOffShelfCheck item {}", item);
            Assert.nonNull(item, "item.not.found");
            List<Long> operatorIdList = vendorPartnershipReadDomainService.queryOperatorByVendor(item.getShopId());
            String ret = areaItemWriteDomainService.offShelfCheck(
                    operatorIdList, request.getItemIdSet());
            if (null != ret) {
                return Response.ok(ret);
            }
        }
        return Response.ok(null);
    }

    @Override
    public Response<ImportYjrPriceResult> importPrice(List<Object[]> list, String userId, String userName, Long operatorId) {
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmssSSS");

        log.info("importPrice param list {} userId {} userName {}", JSON.toJSONString(list)
                , userId, userName);
        ImportYjrPriceResult result = new ImportYjrPriceResult();
        Map<Long, YjrImportSkuPriceLog> dataMap = new HashMap<>();

        Date addDate = new Date();
        String batchNo = df.format(addDate);
        result.setBatchNo(batchNo);

        List<YjrImportSkuPriceLog> failtList = Lists.newArrayList();
        list.forEach(o -> {
            if (o[0] == null || StringUtils.isBlank(o[0].toString())) {
                return;
            }
            YjrImportSkuPriceLog log = getYjrImportSkuPriceLog(addDate, batchNo, userId, userName, o);
            if (StringUtils.isNotBlank(log.getReason())) {
                log.setStatus("0");
                failtList.add(log);
            } else {
                log.setStatus("1");
                dataMap.put(log.getSkuId(), log);
            }
        });


        try {
            List<AreaSku> areaSkuList = null;
            if (!CollectionUtils.isEmpty(dataMap)) {
                areaSkuList = areaSkuReadDomainService.findByOperatorIdAndSkuIds(operatorId, dataMap.keySet());
            }
            int successCount = 0;

            List<YjrImportSkuPriceLog> successList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(areaSkuList)) {
                ThreadPoolExecutor tagReadPool = (ThreadPoolExecutor) Executors.newFixedThreadPool(10);
                for (AreaSku areaSku : areaSkuList) {
                    YjrImportSkuPriceLog priceLog = dataMap.remove(areaSku.getSkuId());
                    if (areaSku.getCooperationMode() == CooperationMode.SERVICE_CHARGE.getValue()) {
                        priceLog.setReason("该商品为服务模式商品，无法修改价格!");
                        priceLog.setStatus("0");
                        failtList.add(priceLog);
                        continue;
                    }
                    if (priceLog != null) {
                        successList.add(priceLog);
                        successCount++;
                        tagReadPool.execute(() -> {
                            areaSku.setOriginalPrice(getLongPrice(priceLog.getOriginalPrice()));
                            areaSku.setRegisterBuyPrice(getLongPrice(priceLog.getPrice()));
                            areaSku.setWhiteListPrice(getLongPrice(priceLog.getWhiteListPrice()));
                            areaSku.setV1Price(getLongPrice(priceLog.getV1Price()));
                            areaSku.setV2Price(getLongPrice(priceLog.getV2Price()));
                            areaSku.setDefaultPrice(getLongPrice(priceLog.getV3Price()));
                            areaSku.setSpreadPrice(getLongPrice(priceLog.getPartnerSpreadPrice()));

                            areaSku.setPCommission(0L);
                            areaSku.setCommission(0L);
                            areaSku.setUpdatedBy(userId);
                            ItemQuotedPrice quotedPrice = itemPriceHelper.constructDefault(areaSku);

                            ItemQuotedPriceLog quotedPriceLog = itemPriceHelper.buildLog(quotedPrice);
                            areaItemManager.setQuotedPriceFromOperator(userId, RequestContext.getTenantId(), areaSku, quotedPrice, quotedPriceLog);
                        });
                    }
                }
                tagReadPool.shutdown();
            }
            if (!CollectionUtils.isEmpty(dataMap)) {
                dataMap.forEach((k, v) -> {
                    v.setStatus("0");
                    v.setReason("skuId不存在 或 已删除, 无法修改!");
                });
                failtList.addAll(dataMap.values());
            }
            result.setSuccessCount(successCount);

            result.setFailCount(failtList.size());
            if (failtList.size() == 0) {
                result.setCode(0);
            } else {
                result.setCode(1);
            }
            successList.addAll(failtList);
            yjrImportSkuPriceLogDao.saveBatch(successList);
            log.info("importPrice result data {} ", JSON.toJSONString(result));
            return Response.ok(result);
        } catch (Exception e) {
            log.info("importPrice result err data {} ", JSON.toJSONString(result));
            log.error("yjrImportSkuPriceLogDao creates error ", e);
            return Response.fail("保存失败记录异常");
        }
    }

    @Override
    public void categoryExcelImport(byte[] bytes, int size, Long userId, InventoryExportParam inventoryExportParam) throws Exception {
        String reportType = "供应商渠道及销售报价导入";
        InputStream is = new ByteArrayInputStream(bytes);
        String filePath = null;
        String fileName = "供应商渠道及销售报价导入" + TimeUtils.format(new Date(), "yyyy-MM-dd HH-mm-ss") + ".xlsx";
        filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(fileName, is);
        //写入记录表等待job执行
        ThirdUploadReportCreateRequest excelReportCreateRequest = new ThirdUploadReportCreateRequest();
        excelReportCreateRequest.setCreateAt(new Date());
        excelReportCreateRequest.setSize(size);
        excelReportCreateRequest.setFileName(fileName);
        excelReportCreateRequest.setCenter("item-sku");
        excelReportCreateRequest.setFileType("sku管理");
        excelReportCreateRequest.setFileUrl(filePath);
        excelReportCreateRequest.setReportType(reportType);
        excelReportCreateRequest.setUploadStauts(0);
        excelReportCreateRequest.setUserId(userId);
        excelReportCreateRequest.setExtendJson(JSON.toJSONString(inventoryExportParam));
        excelReportWriteApi.write(excelReportCreateRequest);
    }

    @Override
    public Response<Boolean> vendorOffShelf(AreaItemOffShelfRequest request) {
        return Response.ok(areaItemWriteDomainService.vendorOffShelf(request.getOperatorId(),
                request.getItemIdSet(), request.getOffShelTime(), request.getTenantId(), request.getUpdateBy()));
    }

    @Override
    public Response<Boolean> openItemOffShelf(OpenUpdateItemStatusRequest request) {
        Boolean flag = Boolean.FALSE;

        Set<Long> itemIdSet = new HashSet<>();
        Map<Long,String> map = Maps.newHashMap();
        for (String outerId : request.getOuterIds()) {
            Item item = itemReadDomainService.openFindByOutId(outerId, request.getTenantId());
            if(!Objects.isNull(item)){
                if(!request.getVendorId().equals(item.getShopId())){
                    throw new ServiceException(outerId + "非供应商商品，下架失败");
                }
                itemIdSet.add(item.getId());
            }else {
                log.error("商品不存在");
                throw new ServiceException(outerId + "商品不存在");
            }
            map.put(item.getId(),item.getOuterId());
        }


        // 查询绑定的区运营
        List<Long> operatorIdList = vendorPartnershipReadDomainService.queryOperatorByVendor(request.getVendorId());
        List<AreaItem> areaItems = areaItemReadDomainService.findByOperatorIdsAndItemIds(new HashSet<>(operatorIdList), itemIdSet);
        if(CollectionUtils.isEmpty(areaItems)){
            throw new ServiceException("商品不存在！");
        }
        List<String> keyList = areaItems.stream().map(m -> m.getOperatorId() + "-" + m.getItemId()).collect(Collectors.toList());

        AreaItemOnShelfOperateBO operateBO = areaItemOnShelfHelper.shelfPrepare(keyList, new HashSet<>(operatorIdList), itemIdSet, request.getUpdatedBy(), AreaItemStatus.OFF_SHELF.getValue());


        StringBuilder st = new StringBuilder();
        for (AreaItem areaItem : areaItems) {
            if(areaItem.getStatus() != null && areaItem.getStatus().equals(-1)){
                st.append(map.get(areaItem.getItemId())).append(",");
            }
        }
        if(st.length() > 0){
            st.append("已经是下架状态！");
            throw new ServiceException(st.toString());
        }

        List<AreaItem> list = operateBO.getAreaItems();
        for (Long operatorId : list.stream().map(AreaItem::getOperatorId).collect(Collectors.toList())) {
            flag = areaItemWriteDomainService.openItemOffShelf(
                    operatorId, itemIdSet, request.getTenantId());
            if(!flag){
                break;
            }else{
                if(operatorId != 1){
                    offOrShelfMessageAdd(operatorId,itemIdSet,"-1",request.getUpdatedBy());
                }
            }
        }

        return Response.ok(flag);
    }

    @Override
    public Response<Boolean> openItemOnShelf(OpenUpdateItemStatusRequest request) {
        Boolean flag = Boolean.FALSE;
        Set<Long> itemIdSet = new HashSet<>();
        Map<Long,String> map = Maps.newHashMap();
        for (String outerId : request.getOuterIds()) {
            Item item = itemReadDomainService.openFindByOutId(outerId, request.getTenantId());
            if(!Objects.isNull(item)){
                if(!request.getVendorId().equals(item.getShopId())){
                    throw new ServiceException(outerId + "非供应商商品，上架失败");
                }
                itemIdSet.add(item.getId());
            }else {
                log.error("商品不存在");
                throw new ServiceException(outerId + "商品不存在");
            }
            map.put(item.getId(),item.getOuterId());
        }

        // 查询绑定的区运营
        List<Long> operatorIdList = vendorPartnershipReadDomainService.queryOperatorByVendor(request.getVendorId());
        List<AreaItem> areaItems = areaItemReadDomainService.findByOperatorIdsAndItemIds(new HashSet<>(operatorIdList), itemIdSet);
        if(CollectionUtils.isEmpty(areaItems)){
            throw new ServiceException("商品不存在！");
        }
        List<String> keyList = areaItems.stream().map(m -> m.getOperatorId() + "-" + m.getItemId()).collect(Collectors.toList());

        AreaItemOnShelfOperateBO operateBO = areaItemOnShelfHelper.shelfPrepare(keyList, new HashSet<>(operatorIdList), itemIdSet, request.getUpdatedBy(), AreaItemStatus.ON_SHELF.getValue());


        StringBuilder st = new StringBuilder();
        for (AreaItem areaItem : areaItems) {
            if(areaItem.getStatus() != null && areaItem.getStatus().equals(1)){
                st.append(map.get(areaItem.getItemId())).append(",");
            }
        }
        if(st.length() > 0){
            st.append("已经是上架状态！");
            throw new ServiceException(st.toString());
        }
        List<AreaItem> list = operateBO.getAreaItems();
        for (Long operatorId : list.stream().map(AreaItem::getOperatorId).collect(Collectors.toList())) {
            flag = areaItemWriteDomainService.openItemOnShelf(
                    operatorId, itemIdSet, request.getTenantId(), request.getUpdatedBy());
            if(!flag){
                break;
            }else{
                if(operatorId != 1){
                    offOrShelfMessageAdd(operatorId,itemIdSet,"1",request.getUpdatedBy());
                }
            }
        }
        return Response.ok(flag);
    }

    /**
     * 商品上架或下架消息添加
     * @param operatorId
     * @param itemIdSet
     */
    public void offOrShelfMessageAdd(Long operatorId,Set<Long> itemIdSet,String status,String updatedBy){
        ThirdParanaThirdMessageCreateRequest messageCreateRequest = new ThirdParanaThirdMessageCreateRequest();
        messageCreateRequest.setCreatedBy(updatedBy);
        messageCreateRequest.setUpdatedBy(updatedBy);
        messageCreateRequest.setRemark("1".equals(status) ? "商品上架":"商品下架");
        messageCreateRequest.setOperatorId(operatorId);
        messageCreateRequest.setCreatedAt(new Date());
        messageCreateRequest.setUpdatedAt(new Date());
        messageCreateRequest.setMessageType(4);
        ThirdParanaThirdMessageBean thirdParanaThirdMessageBean = new ThirdParanaThirdMessageBean();
        thirdParanaThirdMessageBean.setStatus(status);
        for (Long itemId : itemIdSet) {
            thirdParanaThirdMessageBean.setId(itemId);
            messageCreateRequest.setBean(thirdParanaThirdMessageBean);
            paranaThirdMessageWriteApi.create(messageCreateRequest);
        }

    }

    @NotNull
    private YjrImportSkuPriceLog getYjrImportSkuPriceLog(Date addDate, String batchNo, String userId, String userName, Object[] o) {
        YjrImportSkuPriceLog log = new YjrImportSkuPriceLog();
        log.setSkuName(convertString(o[1]));
        log.setSpecName(convertString(o[2]));
        log.setVendorName(convertString(o[3]));
        log.setSkuStatus(convertString(o[4]));
        log.setBasePrice(convertString(o[5]));
        log.setPrice(convertString(o[6]));
        log.setWhiteListPrice(convertString(o[7]));
        log.setV1Price(convertString(o[8]));
        log.setV2Price(convertString(o[9]));
        log.setV3Price(convertString(o[10]));
        log.setPartnerSpreadPrice(convertString(o[11]));
        log.setOriginalPrice(convertString(o[12]));
        log.setCreateUserId(userId);
        log.setCreateUserName(userName);
        log.setCreateDate(addDate);
        log.setBatchNo(batchNo);
        Long skuId = null;
        boolean result = true;
        if (o[0] == null) {
            log.setReason("skuId不能为空");
            result = false;
        } else {
            try {
                skuId = Long.parseLong(o[0].toString());
            } catch (Exception e) {
                log.setReason("skuId不合法 : " + skuId);
                result = false;
            }
        }
        log.setSkuId(skuId);
        if (result) {
            result = checkAndGetReason(log, log.getPartnerSpreadPrice(), "合伙人推广价", "合伙人推广价");
        }
        if (result) {
            result = checkAndGetReason(log, log.getPrice(), "会员价", "会员价");
        }
        if (result) {
            result = checkAndGetReason(log, log.getV1Price(), "v1价", "v1价");
        }
        if (result) {
            result = checkAndGetReason(log, log.getV2Price(), "v2价", "v2价");
        }
        if (result) {
            result = checkAndGetReason(log, log.getV3Price(), "v3价", "v3价");
        }
        if (result) {
            result = checkAndGetReason(log, log.getOriginalPrice(), "零售价", "零售价");
        }

        if (result) {
            BigDecimal spreadPrice = new BigDecimal(log.getPartnerSpreadPrice());
            BigDecimal v1price = new BigDecimal(log.getV1Price());
            if (v1price.compareTo(spreadPrice) >= 0) {
                log.setReason("v1价必须小于合伙人推广价");
            }
            BigDecimal v2price = new BigDecimal(log.getV2Price());
            if (v2price.compareTo(spreadPrice) >= 0) {
                log.setReason("v2价必须小于合伙人推广价");
            }
            BigDecimal v3price = new BigDecimal(log.getV3Price());
            if (v3price.compareTo(spreadPrice) >= 0) {
                log.setReason("v3价必须小于合伙人推广价");
            }
        }
        return log;
    }

    private boolean checkAndGetReason(YjrImportSkuPriceLog log, String price, String nullTips, String checkTips) {
        if (StringUtils.isBlank(price)) {
            log.setReason(nullTips + "不能为空");
            return false;
        } else {
            try {
                BigDecimal decimal = new BigDecimal(price);
                if (BigDecimal.ZERO.compareTo(decimal) >= 0) {
                    log.setReason(checkTips + "必须大于0");
                    return false;
                }

            } catch (Exception e) {
                log.setReason(checkTips + "不合法，必须为数字类型");
                return false;
            }
        }
        return true;
    }

    public Long getLongPrice(String price) {
        BigDecimal bigDecimal = new BigDecimal(price);
        BigDecimal result = bigDecimal.multiply(new BigDecimal("100"));
        return result.longValue();
    }

    public String convertString(Object o) {
        if (o == null) {
            return "";
        } else {
            return o.toString();
        }
    }


    @Override
    public Response<Boolean> repairChoiceSku(RepairChoiceSkuRequest request) {
        //待处理的选品库SKU数据
        List<AreaSku> createAreaSku = Lists.newArrayList();
        List<AreaSku> deleteAreaSku = Lists.newArrayList();
        List<ChoiceLotLibSkuModel> createChoiceLotLibSku = Lists.newArrayList();
        List<ChoiceLotLibSkuModel> deleteChoiceLotLibSku = Lists.newArrayList();
        List<ParanaThirdMessageCreateRequest> thirdParanaThirdMessageCreateRequests = new ArrayList<>();
        Long operatorId = request.getOperatorId();
        Set<Long> itemIds = request.getItemIds();
        //1、查询areaItem 并查询关联品
        AreaItemOnShelfOperateBO bo = areaItemOnShelfHelper.buildOnShelfOperateBO(Sets.newHashSet(operatorId), itemIds);
        if (CollectionUtils.isEmpty(bo.getAreaItems())) {
            throw new ServiceException("区域商品不存在");
        }
        List<AreaItem> areaItems = bo.getAreaItems();
        List<AreaSku> areaSkus = bo.getAreaSkus();
        Map<String, List<AreaSku>> areaSkuMap = areaSkus.stream().collect(Collectors.groupingBy(e -> e.getOperatorId() + "-" + e.getItemId()));
        //3、查询sku
        Map<Long, List<Sku>> skuMap = skuDao.findByItemIdSet(itemIds).stream().collect(Collectors.groupingBy(Sku::getItemId));
        //4、查询加价率 查询选品库sku
        Set<Long> operatorIds = areaItems.stream().map(AreaItem::getOperatorId).collect(Collectors.toSet());
        Map<String, Object> params = Maps.newHashMap();
        params.put("operatorIds", operatorIds);
        List<ChoiceLotLib> choiceLotLibList = choiceLotLibReadDomainService.listInfoFilter(params);
        Set<Long> choiceLotLibIds = choiceLotLibList.stream().map(ChoiceLotLib::getId).collect(Collectors.toSet());
        List<ChoiceLotLibSkuModel> choiceLotLibSkuModels = choiceLotLibSkuReadService.listByWhere(null, null, choiceLotLibIds, null, itemIds);
        //5、查询选品库商品
        List<ChoiceLotLibItemModel> itemModelList = choiceLotLibItemDao.findChoiceItemByChoiceIdsAndItemIds(Lists.newArrayList(itemIds), new ArrayList<>(choiceLotLibIds));
        Map<Long, List<ChoiceLotLibMarkup>> markupMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(itemModelList)) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("choiceLotLibIds", itemModelList.stream().map(ChoiceLotLibItemModel::getChoiceLotLibId).collect(Collectors.toList()));
            List<ChoiceLotLibMarkup> markupByChoiceLotId = choiceLotLibMarkupDao.getMarkupByChoiceLotId(param);
            markupMap = markupByChoiceLotId.stream().collect(Collectors.groupingBy(ChoiceLotLibMarkup::getChoiceLotLibId));
        }
        //组装选品库商品信息用于发送消息
        Map<String,Set<Long>> authMap = Maps.newHashMap();
        for (ChoiceLotLibItemModel choiceLotLibItemModel : itemModelList) {
            if(StringUtils.isEmpty(choiceLotLibItemModel.getDistributorIds())){
                continue;
            }
            List<Long> authIdList = Arrays.stream(choiceLotLibItemModel.getDistributorIds().replace("#", "")
                    .split(",")).map(Long::parseLong).collect(Collectors.toList());
            for (Long authId : authIdList) {
                Set<Long> choiceItemIds = authMap.get(authId+","+choiceLotLibItemModel.getOperatorId());
                if(CollectionUtil.isEmpty(choiceItemIds)){
                    choiceItemIds = Sets.newHashSet();
                }
                choiceItemIds.add(choiceLotLibItemModel.getItemId());
                authMap.put(authId+","+choiceLotLibItemModel.getOperatorId(),choiceItemIds);
            }
        }
        //比对
        for (AreaItem areaItem : areaItems) {
            List<AreaSku> areaSkuList = areaSkuMap.get(areaItem.getOperatorId() + "-" + areaItem.getItemId());
            List<Sku> skus = skuMap.get(areaItem.getItemId());
            assembleSku(areaItem, skus, areaSkuList, createAreaSku, deleteAreaSku, markupMap);
            assembleChoiceLotLibSku(areaItem, skus,itemModelList, choiceLotLibSkuModels, markupMap, createChoiceLotLibSku, deleteChoiceLotLibSku);
        }
        //消息组装
        for (String auth : authMap.keySet()) {
            String[] split = auth.split(",");
            ParanaThirdMessageCreateRequest paranaThirdMessageCreateRequest = new ParanaThirdMessageCreateRequest();
            paranaThirdMessageCreateRequest.setAuthId(Long.parseLong(split[0]));
            paranaThirdMessageCreateRequest.setOperatorId(Long.parseLong(split[1]));
            paranaThirdMessageCreateRequest.setMessageType(5);
            paranaThirdMessageCreateRequest.setCreatedAt(new Date());
            paranaThirdMessageCreateRequest.setCreatedBy("repair");
            ParanaThirdMessageBean paranaThirdMessageBean = new ParanaThirdMessageBean();
            paranaThirdMessageBean.setId(Long.parseLong(split[0]));
            paranaThirdMessageBean.setItemMessage(JSON.toJSONString(authMap.get(auth)));
            paranaThirdMessageCreateRequest.setBean(paranaThirdMessageBean);
            thirdParanaThirdMessageCreateRequests.add(paranaThirdMessageCreateRequest);
        }
        Boolean isOk = areaItemManager.repairChoiceSku(createAreaSku, deleteAreaSku,
                createChoiceLotLibSku, deleteChoiceLotLibSku);
        if (isOk) {
            //发送消息
            paranaThirdMessageWriteFacade.batchCreate(thirdParanaThirdMessageCreateRequests);
        }
        return Response.ok(isOk);
    }

    private void assembleSku(AreaItem areaItem,
                             List<Sku> skuList,
                             List<AreaSku> areaSkuList,
                             List<AreaSku> createAreaSku,
                             List<AreaSku> deleteAreaSku,
                             Map<Long, List<ChoiceLotLibMarkup>> markupMap) {
        //标记有新增sku
        AreaSku areaSku = areaSkuList.get(0);
        Map<Long, AreaSku> areaSkuMapBySkuId = AssembleDataUtils.list2map(areaSkuList, AreaSku::getSkuId);
        for (Sku sku : skuList) {
            AreaSku existAreaSku = areaSkuMapBySkuId.remove(sku.getId());
            if (existAreaSku == null) {
                createAreaSku.add(assembleSkuOfCreate(areaItem, sku, areaItem.getUpdatedBy(), areaSku.getCooperationMode(), areaSku.getLogisticsMode(), markupMap));
            }
        }
        if (!CollectionUtils.isEmpty(areaSkuMapBySkuId)) {
            deleteAreaSku.addAll(areaSkuMapBySkuId.values());
        }
    }

    protected AreaSku assembleSkuOfCreate(AreaItem areaItem, Sku sku, String auditBy, Integer cooperationMode, Integer logisticsMode, Map<Long, List<ChoiceLotLibMarkup>> markupMap) {
        VendorDefaultPriceCellBO extraPriceBO = sku.getExtraPriceBO();
        Long basePrice;
        if (areaItem.getSourceType() == 0) {
            basePrice = extraPriceBO.getDefaultBasePrice();
        } else {
            basePrice = 0L;
            Long markup;
            if (areaItem.getSourceType() == 1) {
                if (areaItem.getOperatorId().equals(1L)) {
                    basePrice = extraPriceBO.getDefaultBasePrice();
                } else {
                    markup = getMarkupByChoiceLotLibId(markupMap, areaItem.getSourceChoiceLotLibId(), extraPriceBO.getDefaultBasePrice());
                    //获取来源选品库的加价率计算出的分销价则为当前运营商的基准价
                    basePrice = MarkupCalculateUtils.getDistriPrice(extraPriceBO.getDefaultBasePrice(), markup);
                }
            }
        }
        AreaSku areaSku = new AreaSku();
        areaSku.setId(idGenerator.nextValue(AreaSku.class));
        areaSku.setAreaItemId(areaItem.getItemId());
        areaSku.setItemId(sku.getItemId());
        areaSku.setSkuId(sku.getId());
        areaSku.setSkuCode(sku.getSkuCode());
        areaSku.setVendorId(areaItem.getVendorId());
        areaSku.setOperatorId(areaItem.getOperatorId());
        areaSku.setName(sku.getName());
        areaSku.setAttributes(sku.getAttributes());
        areaSku.setImage(sku.getImage());
        areaSku.setOriginalPrice(sku.getOriginalPrice());
        areaSku.setDefaultPrice(extraPriceBO.getDefaultPrice());
        areaSku.setBasePrice(basePrice);
        areaSku.setSpreadPrice(extraPriceBO.getDefaultBasePrice());
        areaSku.setPCommission(extraPriceBO.getDefaultPCommission());
        areaSku.setCommission(extraPriceBO.getDefaultCommission());
        Map<Long, VendorChannelPriceCellBO> channelPrice = sku.getVendorChannelPrice().get(areaItem.getOperatorId());
        if (channelPrice != null) {
            areaSku.setChannelPrice(channelPrice);
        }
        areaSku.setMinQuantity(YYTItemConstant.MIN_QUANTITY.getLong(sku.getExtra()));
        areaSku.setHqOperatorStatus(areaItem.getHqOperatorStatus());
        areaSku.setVendorStatus(areaItem.getVendorStatus());
        areaSku.setAreaOperatorStatus(areaItem.getAreaOperatorStatus());
        areaSku.setStatus(areaItem.getStatus());
        areaSku.setCooperationMode(ObjectUtils.isEmpty(cooperationMode) ? CooperationMode.BASE_PRICE.getValue() : cooperationMode);
        areaSku.setLogisticsMode(ObjectUtils.isEmpty(logisticsMode) ? LogisticsMode.NON_GOODS_COLLECTION.getValue() : logisticsMode);
        areaSku.setCreatedAt(new Date());
        areaSku.setUpdatedBy(auditBy);
        areaSku.setUpdatedAt(new Date());
        return areaSku;
    }

    private void assembleChoiceLotLibSku(AreaItem areaItem, List<Sku> skuList,List<ChoiceLotLibItemModel> itemModelList,
                                         List<ChoiceLotLibSkuModel> choiceLotLibSkuModels, Map<Long, List<ChoiceLotLibMarkup>> markupMap, List<ChoiceLotLibSkuModel> createChoiceLotLibSku, List<ChoiceLotLibSkuModel> deleteChoiceLotLibSku) {
        log.info("开始构建选品库SKU数据--------------------------");
        //判断当前商品是否加入过选品库  未加入过则直接跳过
        if(CollectionUtil.isEmpty(itemModelList)){
            return;
        }
        itemModelList = itemModelList.stream().filter(f -> f.getOperatorId().equals(areaItem.getOperatorId()) && f.getItemId().equals(areaItem.getItemId())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(itemModelList)){
            return;
        }
        //当前商品的选品库sku
        if(CollectionUtil.isNotEmpty(choiceLotLibSkuModels)){
            choiceLotLibSkuModels = choiceLotLibSkuModels.stream().filter(e -> e.getItemId().equals(areaItem.getItemId()) && e.getOperatorId().equals(areaItem.getOperatorId())).collect(Collectors.toList());
        }
        log.info("当前item:{}的选品库sku:{}", areaItem.getItemId(), JSONUtil.toJsonStr(choiceLotLibSkuModels));
        Map<String, List<ChoiceLotLibSkuModel>> choiceLotLibSkuMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(choiceLotLibSkuModels)){
            //根据选品库id+商品id分组
            choiceLotLibSkuMap = choiceLotLibSkuModels.stream().collect(Collectors.groupingBy(t -> t.getChoiceLotLibId() + "_" + t.getSkuId()));
        }
        for (Sku sku : skuList) {
            List<ChoiceLotLibSkuModel> newChoiceLotLibSku = new ArrayList<>();
            for (ChoiceLotLibItemModel choiceLotLibItemModel : itemModelList) {
                List<ChoiceLotLibSkuModel> choiceLotLibSkuModelList = choiceLotLibSkuMap.get(choiceLotLibItemModel.getChoiceLotLibId() + "_" + sku.getId());
                //判断是否存在   存在则跳过  不存在则需要创建
                if(CollectionUtil.isEmpty(choiceLotLibSkuModelList)){
                    VendorDefaultPriceCellBO extraPriceBO = sku.getExtraPriceBO();
                    Long basePrice;
                    if (areaItem.getSourceType() == 0) {
                        basePrice = extraPriceBO.getDefaultBasePrice();
                    } else {
                        basePrice = 0L;
                        Long markup;
                        if (areaItem.getSourceType() == 1) {
                            if (areaItem.getOperatorId().equals(1L)) {
                                basePrice = extraPriceBO.getDefaultBasePrice();
                            } else {
                                markup = getMarkupByChoiceLotLibId(markupMap, areaItem.getSourceChoiceLotLibId(), extraPriceBO.getDefaultBasePrice());
                                //获取来源选品库的加价率计算出的分销价则为当前运营商的基准价
                                basePrice = MarkupCalculateUtils.getDistriPrice(extraPriceBO.getDefaultBasePrice(), markup);
                            }
                        }
                    }
                    List<ChoiceLotLibMarkup> choiceLotLibMarkups = markupMap.get(choiceLotLibItemModel.getChoiceLotLibId());
                    // 匹配最佳加价率
                    Long finalBasePrice = basePrice;
                    ChoiceLotLibMarkup choiceLotLibMarkup = choiceLotLibMarkups.stream().filter(v -> finalBasePrice.compareTo(v.getBasePrice()) > 0)
                            .max(Comparator.comparing(ChoiceLotLibMarkup::getBasePrice)).orElse(null);
                    ChoiceLotLibSkuModel model = new ChoiceLotLibSkuModel();
                    model.setId(idGenerator.nextValue(ChoiceLotLibSkuModel.class));
                    model.setItemId(sku.getItemId());
                    model.setChoiceLotLibId(choiceLotLibItemModel.getChoiceLotLibId());
                    model.setTenantId(1L);
                    model.setSkuId(sku.getId());
                    model.setSource(0);
                    model.setOperatorId(areaItem.getOperatorId());
                    model.setBasePrice(basePrice);
                    model.setOriginalPrice(sku.getOriginalPrice());
                    model.setCreatedBy(sku.getUpdatedBy());
                    model.setUpdatedBy(sku.getUpdatedBy());
                    if (null == choiceLotLibMarkup || null == choiceLotLibMarkup.getMarkup()) {
                        // 未匹配到加价格配置则按原价计算
                        model.setMarkup(0L);
                    } else {
                        model.setMarkup(choiceLotLibMarkup.getMarkup());
                    }
                    // 利润率=（建议零售价-商品供货价）/建议零售价*100% [上入一位]
                    model.setProfitRate(MarkupCalculateUtils.getProfitRate(sku.getOriginalPrice(), model.getBasePrice()));
                    // 分销价=供货价*（1+加价率）
                    model.setDistributorPrice(MarkupCalculateUtils.getDistriPrice(model.getBasePrice(), model.getMarkup()));
                    // 分销毛利=分销价-供货价
                    model.setResellerGross(MarkupCalculateUtils.sub(model.getDistributorPrice(), model.getBasePrice()));
                    // 分销毛利率=（分销价-供货价）/分销价*100%
                    model.setResellerGrossRate(MarkupCalculateUtils.getProfitRate(model.getDistributorPrice(), model.getBasePrice()));
                    // 预估毛利率=（建议零售价 - 采购价）/ 建议零售价 × 100%
                    model.setPreResellerGrossRate(MarkupCalculateUtils.getProfitRate(model.getOriginalPrice(), model.getDistributorPrice()));
                    newChoiceLotLibSku.add(model);
                    createChoiceLotLibSku.add(model);
                }
            }

            log.info("当前需创建的选品库sku:{}", JSONUtil.toJsonStr(newChoiceLotLibSku));
        }

        //查找需要删除的选品库sku
        List<Long> nowSkuId = skuList.stream().map(Sku::getId).collect(Collectors.toList());
        log.info("当前skuId:{}", JSONUtil.toJsonStr(nowSkuId));
        List<Long> oldSkuId = choiceLotLibSkuModels.stream().map(ChoiceLotLibSkuModel::getSkuId).distinct().collect(Collectors.toList());
        log.info("原skuId:{}", JSONUtil.toJsonStr(oldSkuId));
        // 检查两个列表是否有任何一个元素相等
        boolean anyMatch = nowSkuId.stream().anyMatch(oldSkuId::contains);
        if (anyMatch) {
            // 如果有部分相等，则取出oldSku不存在于nowSkuId的数据进行删除
            List<Long> notExist = oldSkuId.stream().filter(item -> !nowSkuId.contains(item)).collect(Collectors.toList());
            log.info("需删除的skuId:{}", JSONUtil.toJsonStr(notExist));
            for (ChoiceLotLibSkuModel lotLibSkuModel : choiceLotLibSkuModels) {
                if (notExist.contains(lotLibSkuModel.getSkuId())) {
                    lotLibSkuModel.setStateDeleted(true);
                    log.info("删除的选品库sku:{}", JSONUtil.toJsonStr(lotLibSkuModel));
                    deleteChoiceLotLibSku.add(lotLibSkuModel);
                }
            }
        } else {
            for (ChoiceLotLibSkuModel lotLibSkuModel : choiceLotLibSkuModels) {
                lotLibSkuModel.setStateDeleted(true);
            }
            log.info("没有一个元素相等，删除的选品库sku:{}", JSONUtil.toJsonStr(choiceLotLibSkuModels));
            // 如果两个列表没有一个元素相等，则删除所有原有的lotLibSkuModels
            deleteChoiceLotLibSku.addAll(choiceLotLibSkuModels);
        }
    }

    private Long getMarkupByChoiceLotLibId(Map<Long, List<ChoiceLotLibMarkup>> markupMap, Long choiceLotLibId, Long price) {
        Long platformMarkup;
        List<ChoiceLotLibMarkup> choiceLotLibMarkups = markupMap.get(choiceLotLibId);
        // 匹配最佳加价率
        ChoiceLotLibMarkup choiceLotLibMarkup = choiceLotLibMarkups.stream().filter(v -> price.compareTo(v.getBasePrice()) > 0)
                .max(Comparator.comparing(ChoiceLotLibMarkup::getBasePrice)).orElse(null);
        if (null == choiceLotLibMarkup || null == choiceLotLibMarkup.getMarkup()) {
            // 未匹配到加价格配置则按原价计算
            platformMarkup = 0L;
        } else {
            platformMarkup = choiceLotLibMarkup.getMarkup();
        }
        return platformMarkup;
    }

    @Override
    public Response<Boolean> bindGroup(AreaItemOnShelfRequest request){
        try {
            if(request.getItemId() == null || request.getItemGroupId() == null || request.getOperatorId() == null || StringUtils.isEmpty(request.getItemGroupAlias())){
                return Response.fail("参数不全！");
            }
            AreaItem areaItem = areaItemReadDomainService.findByOperatorIdAndItemId(request.getOperatorId(), request.getItemId());
            if(areaItem == null){
                return Response.fail("商品不存在！");
            }
            areaItem.setItemGroupId(request.getItemGroupId());
            areaItem.setItemGroupAlias(request.getItemGroupAlias());
            areaItem.setItemGroupOrder(request.getItemGroupOrder());
            Boolean update = areaItemWriteDomainService.update(areaItem);
            if(update){
                List<DistributorItemLibModel> distributorItemLibList = distributorItemLibDao.findByDistributorIdAndItemIds(null, Collections.singletonList(areaItem.getItemId()));
                if(CollectionUtil.isNotEmpty(distributorItemLibList)){
                    List<ParanaThirdMessageCreateRequest> thirdParanaThirdMessageCreateRequests = new ArrayList<>();
                    for (DistributorItemLibModel distributorItem : distributorItemLibList) {
                        //审核通过发送消息
                        ParanaThirdMessageCreateRequest messageCreateRequest = new ParanaThirdMessageCreateRequest();
                        messageCreateRequest.setAuthId(distributorItem.getDistributorId());
                        messageCreateRequest.setCreatedBy(request.getUpdatedBy());
                        messageCreateRequest.setUpdatedBy(request.getUpdatedBy());
                        messageCreateRequest.setOperatorId(distributorItem.getOperatorId());
                        messageCreateRequest.setCreatedAt(new Date());
                        messageCreateRequest.setUpdatedAt(new Date());
                        messageCreateRequest.setMessageType(4);
                        ParanaThirdMessageBean thirdParanaThirdMessageBean = new ParanaThirdMessageBean();
                        thirdParanaThirdMessageBean.setId(distributorItem.getItemId());
                        messageCreateRequest.setBean(thirdParanaThirdMessageBean);
                        thirdParanaThirdMessageCreateRequests.add(messageCreateRequest);
                    }
                    paranaThirdMessageWriteFacade.batchCreate(thirdParanaThirdMessageCreateRequests);
                }
            }
            return Response.ok(update);
        }catch (ServiceException e){
            return Response.fail(e.getMessage());
        }catch (Exception e){
            return Response.fail("绑定商品失败！");
        }
    }

    @Override
    public Response<Boolean> importGroupBind(byte[] bytes, String name, Long shopId, Long userId, String userName, Integer tenantId) {
        try {
            InputStream inputStream = new ByteArrayInputStream(bytes);
            MultipartFile importfile = new MockMultipartFile(name, name, MediaType.MULTIPART_FORM_DATA_VALUE, inputStream);
            String filePath = "";
            ProcessResult<ItemGroupExcelImportBo> process = importTranscriptProcessor.process(importfile);

            String suffix = FileUtil.getSuffix(importfile.getName());
            InputStream is = importfile.getInputStream();
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(IdUtil.simpleUUID() + "." + suffix, is);
            //写入记录表等待job执行
            ThirdUploadReportCreateRequest excelReportCreateRequest = new ThirdUploadReportCreateRequest();
            excelReportCreateRequest.setCreateAt(new Date());
            excelReportCreateRequest.setSize(process.getData().size());
            excelReportCreateRequest.setFileName(name);
            excelReportCreateRequest.setCenter("item-upload");
            excelReportCreateRequest.setFileType("通卡商品绑定分组");
            excelReportCreateRequest.setFileUrl(filePath);
            excelReportCreateRequest.setReportType("item_group_bind_import");
            excelReportCreateRequest.setUploadStauts(0);
            excelReportCreateRequest.setUserId(userId);
            excelReportWriteApi.write(excelReportCreateRequest);
        } catch (IOException e) {
            log.error(Throwables.getStackTraceAsString(e));
            return Response.fail("导入失败");
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
            return Response.fail("请上传正确的 excel 格式文件！");
        }
        return Response.ok();
    }

    @Override
    public Response<Boolean> importItemBatchOffShelf(byte[] bytes, String name, Long shopId, Long userId, String userName, Integer tenantId) {
        try {
            InputStream inputStream = new ByteArrayInputStream(bytes);
            MultipartFile importfile = new MockMultipartFile(name, name, MediaType.MULTIPART_FORM_DATA_VALUE, inputStream);
            String filePath = "";
            ProcessResult<ItemBatchShelfExcelImportBo> process = itemBatchShelfImportTranscriptProcessor.process(importfile);

            String suffix = FileUtil.getSuffix(importfile.getName());
            InputStream is = importfile.getInputStream();
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(IdUtil.simpleUUID() + "." + suffix, is);
            //写入记录表等待job执行
            ThirdUploadReportCreateRequest excelReportCreateRequest = new ThirdUploadReportCreateRequest();
            excelReportCreateRequest.setCreateAt(new Date());
            excelReportCreateRequest.setSize(process.getData().size());
            excelReportCreateRequest.setFileName(name);
            excelReportCreateRequest.setCenter("item-upload");
            excelReportCreateRequest.setFileType("中台商品批量强制下架");
            excelReportCreateRequest.setFileUrl(filePath);
            excelReportCreateRequest.setReportType("item_batch_shelf_import");
            excelReportCreateRequest.setUploadStauts(0);
            excelReportCreateRequest.setUserId(userId);
            excelReportWriteApi.write(excelReportCreateRequest);
        } catch (IOException e) {
            log.error(Throwables.getStackTraceAsString(e));
            return Response.fail("导入失败");
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
            return Response.fail("请上传正确的 excel 格式文件！");
        }
        return Response.ok();
    }
}
