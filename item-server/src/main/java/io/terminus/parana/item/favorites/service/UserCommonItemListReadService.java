package io.terminus.parana.item.favorites.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.favorites.api.bean.request.CommonItemListQueryPageRequest;
import io.terminus.parana.item.favorites.model.UserCommonItemList;
import io.terminus.parana.item.favorites.repository.UserCommonItemListDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserCommonItemListReadService extends AbsServiceBase {

    private final UserCommonItemListDao userCommonItemListDao;

    /**
     * 条件查询用户常用清单信息
     *
     * @param request
     * @param createdAt
     * @return
     */
    public Paging<UserCommonItemList> queryCommonItemListPaging(CommonItemListQueryPageRequest request, Date createdAt) {
        try {
            HashMap<String, Object> params = Maps.newHashMap();
            PageInfo pageInfo = PageInfo.of(request.getPageNo(), request.getPageSize());
            params.put("operatorId", request.getOperatorId());
            params.put("userId", request.getUserId());
            params.put("source", request.getSource());
            params.put("createdAt", createdAt);
            params.put("offset", pageInfo.getOffset());
            params.put("limit", pageInfo.getLimit());
            Paging<UserCommonItemList> result = userCommonItemListDao.queryCommonItemListPaging(params);
            return result;
        } catch (Exception e) {
            log.error("[UserCommonItemList]findByConditions failed, params:{}, cause:{}",
                    request.getUserId(), e);
            throw new ServiceException("find.user.common.item.list.failed");
        }
    }

    public Long queryCount(Long userId, Long itemId, Integer source){
        Long count = userCommonItemListDao.count(ImmutableMap.of(
                "userId", userId,
                "itemId", itemId,
                "source", source
        ));
        return count;
    }

    public List<UserCommonItemList> queryList(Long userId, Long itemId, Integer source){
        return userCommonItemListDao.queryList(ImmutableMap.of(
                "userId", userId,
                "itemId", itemId,
                "source", source
        ));
    }

}
