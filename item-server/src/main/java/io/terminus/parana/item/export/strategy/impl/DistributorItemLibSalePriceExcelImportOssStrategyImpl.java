package io.terminus.parana.item.export.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibSkuDao;
import io.terminus.parana.item.common.export.ExcelExportType;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.distributorskulib.dao.DistributorSkuLibDao;
import io.terminus.parana.item.distributorskulib.model.DistributorSkuLibModel;
import io.terminus.parana.item.export.dto.DistributorItemLibSalePriceExcelDTO;
import io.terminus.parana.item.export.dto.DistributorItemLibSalePriceExcelTemplate;
import io.terminus.parana.item.export.strategy.ExcelExportStrategy;
import io.terminus.parana.item.export.thirdparty.ThirdPartyRegistry;
import io.terminus.parana.item.export.thirdparty.storage.ObjectStorageFactory;
import io.terminus.parana.item.export.utils.ExcelExportHelper;
import io.terminus.parana.item.item.cache.CacheItemById;
import io.terminus.parana.item.item.cache.CacheSkuById;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.search.docobject.DistributorItemLibDO;
import io.terminus.parana.item.search.facade.DistributorItemLibSearchFacade;
import io.terminus.parana.item.search.request.DistributorItemLibSearchRequest;
import io.terminus.parana.item.util.excel.in.ExcelParseResult;
import io.terminus.parana.item.util.excel.in.ExcelUtil;
import io.terminus.parana.item.util.excel.in.ItemExcelImportHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DistributorItemLibSalePriceExcelImportOssStrategyImpl implements ExcelExportStrategy {

    @Override
    public String getType() {
        return ExcelExportType.DISTRIBUTOR_ITEM_LIB_SALE_PRICE_IMPORT.getReportType();
    }

    @Autowired
    private ExcelExportHelper excelExportHelper;
    @Autowired
    private ThirdPartyRegistry registry;
    @Autowired
    private ItemExcelImportHelper itemExcelImportHelper;
    @Autowired
    private CacheItemById cacheItemById;
    @Autowired
    private CacheSkuById cacheSkuById;
    @Autowired
    private DistributorItemLibSearchFacade itemSearchFacade;
    @Autowired
    private DistributorSkuLibDao distributorSkuLibDao;
    @Autowired
    private ChoiceLotLibSkuDao choiceLotLibSkuDao;

    @Override
    public String execute(String requestJson, String name) {
        DistributorItemLibSearchRequest request = JSON.parseObject(requestJson, DistributorItemLibSearchRequest.class);
        log.info("distributor.item.sale.price.import,request:{}", request);
        List<DistributorItemLibSalePriceExcelDTO> excelDataList = Lists.newArrayList();
        try {
            //1、根据文件获取数据
            InputStream inputStream = ExcelUtil.getFileInputStream(request.getFileUrl());
            ExcelParseResult<DistributorItemLibSalePriceExcelTemplate> excelParseResult = itemExcelImportHelper.parseExcel(inputStream, "xlsx", DistributorItemLibSalePriceExcelTemplate.class);
            // 解析成功的 excel 行号
            LinkedHashMap<Integer/*行号*/, DistributorItemLibSalePriceExcelTemplate/*excel行*/> parseSuccessRows = excelParseResult.getRowNumber2Result();
            //2、校验文件数据
            // 解析失败的 excel 行号
            List<Integer> errorRowNumbers = excelParseResult.getErrorRowNumbers();
            if (!errorRowNumbers.isEmpty()) {
                log.error("Excel  解析错误行号 {}.", errorRowNumbers);
                excelDataList.addAll(buildErrorDto(parseSuccessRows, errorRowNumbers, 1));
                if (parseSuccessRows.isEmpty()) {
                    return getFilePath(excelDataList, name);
                }
            }
            //3、根据渠道商id，itemIds，查询商品库ES
            //查询商品
            Set<Long> itemIds = parseSuccessRows.values().stream().map(e -> Long.valueOf(e.getItemId())).collect(Collectors.toSet());
            Map<Long, Item> itemMap = cacheItemById.get(itemIds, request.getTenantId()).getResult();
            //查询sku
            Set<Long> skuIds = parseSuccessRows.values().stream().map(e -> Long.valueOf(e.getSkuId())).collect(Collectors.toSet());
            Map<Long, Sku> skuMap = cacheSkuById.get(skuIds, request.getTenantId()).getResult();
            //查询渠道商品库ES
            request.setItemIds(itemIds.stream().map(String::valueOf).collect(Collectors.joining("_")));
            List<DistributorItemLibDO> distributorItems = searchDistributorItem(request);
            Map<Long, DistributorItemLibDO> distributorItemMap = Maps.newHashMap();
            Map<String, ChoiceLotLibSkuModel> choiceLotLibSkuMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(distributorItems)) {
                distributorItemMap = distributorItems.stream().collect(Collectors.toMap(DistributorItemLibDO::getItemId, Function.identity(), (e1, e2) -> e1));
                //查询选品库sku
                Map<String, Object> param = Maps.newHashMap();
                param.put("choiceLotLibIds", distributorItems.stream().map(DistributorItemLibDO::getChoiceLotLibId).collect(Collectors.toList()));
                param.put("skuIds", skuIds);
                List<ChoiceLotLibSkuModel> choiceLotLibSkus = choiceLotLibSkuDao.findChoiceLotLibSkuBySkuIds(param);
                if (!CollectionUtils.isEmpty(choiceLotLibSkus)) {
                    choiceLotLibSkuMap = choiceLotLibSkus.stream().collect(Collectors.toMap(e -> e.getOperatorId() + "-" + e.getChoiceLotLibId() + "-" + e.getSkuId(), Function.identity(), (e1, e2) -> e1));
                }
            }
            //查询渠道商品库sku
            List<DistributorSkuLibModel> distributorSkus = distributorSkuLibDao.findByDistributorIdAndItemIds(request.getDistributorId(), itemIds);
            Map<String, DistributorSkuLibModel> distributorSkuMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(distributorSkus)) {
                distributorSkuMap = distributorSkus.stream().collect(Collectors.toMap(e -> e.getChoiceLotLibId() + "-" + e.getSkuId(), Function.identity(), (e1, e2) -> e1));
            }
            // 创建副本
            LinkedHashMap<Integer, DistributorItemLibSalePriceExcelTemplate> copyOfParseSuccessRows = new LinkedHashMap<>(parseSuccessRows);
            //业务规则校验，商品和sku是否存在，商品是否失效，销售价格不能大于建议零售价
            for (Integer row : parseSuccessRows.keySet()) {
                DistributorItemLibSalePriceExcelTemplate model = copyOfParseSuccessRows.get(row);
                log.info("distributor.item.sale.price.import,row:{},value:{}", row, JSONUtil.toJsonStr(model));
                //校验售价格式
                if (!NumberUtil.isNumber(model.getPrice())) {
                    excelDataList.addAll(buildErrorDto(copyOfParseSuccessRows, Lists.newArrayList(row), 8));
                    continue;
                }
                long price = new BigDecimal(model.getPrice()).multiply(new BigDecimal(100)).longValue();
                //校验售价大小
                if (price <= 0) {
                    excelDataList.addAll(buildErrorDto(copyOfParseSuccessRows, Lists.newArrayList(row), 9));
                    continue;
                }
                //商品和sku是否存在
                if (!itemMap.containsKey(Long.valueOf(model.getItemId())) || !distributorItemMap.containsKey(Long.valueOf(model.getItemId()))) {
                    excelDataList.addAll(buildErrorDto(copyOfParseSuccessRows, Lists.newArrayList(row), 2));
                    continue;
                }
                DistributorItemLibDO distributorItem = distributorItemMap.get(Long.valueOf(model.getItemId()));
                if (!skuMap.containsKey(Long.valueOf(model.getSkuId())) || !distributorSkuMap.containsKey(distributorItem.getChoiceLotLibId() + "-" + model.getSkuId())) {
                    excelDataList.addAll(buildErrorDto(copyOfParseSuccessRows, Lists.newArrayList(row), 3));
                    continue;
                }
                DistributorSkuLibModel distributorSku = distributorSkuMap.get(distributorItem.getChoiceLotLibId() + "-" + model.getSkuId());
                if (!choiceLotLibSkuMap.containsKey(distributorItem.getOperatorId() + "-" + distributorItem.getChoiceLotLibId() + "-" + model.getSkuId())) {
                    excelDataList.addAll(buildErrorDto(copyOfParseSuccessRows, Lists.newArrayList(row), 4));
                    continue;
                }
                ChoiceLotLibSkuModel choiceLotLibSku = choiceLotLibSkuMap.get(distributorItem.getOperatorId() + "-" + distributorItem.getChoiceLotLibId() + "-" + model.getSkuId());
                //商品是否失效
                if (1 == distributorItem.getLoseStatus()) {
                    excelDataList.addAll(buildErrorDto(copyOfParseSuccessRows, Lists.newArrayList(row), 5));
                    continue;
                }
//                //销售价格不能大于建议零售价
//                long originalPrice = new BigDecimal(choiceLotLibSku.getOriginalPrice()).longValue();
//                if (price > originalPrice) {
//                    excelDataList.addAll(buildErrorDto(copyOfParseSuccessRows, Lists.newArrayList(row), 6));
//                    continue;
//                }
                //4、导入销售价
                distributorSku.setSalePrice(price);
                distributorSku.setUpdatedAt(new Date());
                distributorSku.setUpdatedBy(request.getUpdatedBy());
                Boolean isOk = distributorSkuLibDao.update(distributorSku);
                if (!isOk) {
                    excelDataList.addAll(buildErrorDto(copyOfParseSuccessRows, Lists.newArrayList(row), 7));
                    continue;
                }
                //5、组装excel返回结果
                DistributorItemLibSalePriceExcelDTO dto = new DistributorItemLibSalePriceExcelDTO();
                BeanUtil.copyProperties(model, dto);
                dto.setStatus("导入成功");
                excelDataList.add(dto);
            }
        } catch (Exception e) {
            log.error("distributor.item.sale.price.import, error ::: {}.", Throwables.getStackTraceAsString(e));
        }
        return getFilePath(excelDataList, name);
    }

    public List<DistributorItemLibDO> searchDistributorItem(DistributorItemLibSearchRequest request) {
        List<DistributorItemLibDO> items = Lists.newArrayList();
        int pageSize = 500;
        int pageNo = 1;
        while (true) {
            request.setPageNo(pageNo);
            request.setPageSize(pageSize);
            log.info("distributor.item.sale.price.import, searchDistributorItem req:{}", JSONUtil.toJsonStr(request));
            Paging<DistributorItemLibDO> paging = Assert.take(itemSearchFacade.search(request));
            log.info("distributor.item.sale.price.import, searchDistributorItem paging size:{}", paging.getData().size());
            if (paging.isEmpty()) {
                break;
            }
            items.addAll(paging.getData());
            pageNo ++;
        }
        return items;
    }

    public List<DistributorItemLibSalePriceExcelDTO> buildErrorDto(LinkedHashMap<Integer, DistributorItemLibSalePriceExcelTemplate> parseSuccessRows, List<Integer> errorRowNumbers, Integer type) {
        List<DistributorItemLibSalePriceExcelDTO> excelDataList = Lists.newArrayList();
        // 删除row的集合
        Set<Integer> deletedRow = Sets.newHashSet();
        for (Map.Entry<Integer, DistributorItemLibSalePriceExcelTemplate> entry : parseSuccessRows.entrySet()) {
            if (errorRowNumbers.contains(entry.getKey())) {
                DistributorItemLibSalePriceExcelDTO dto = new DistributorItemLibSalePriceExcelDTO();
                BeanUtil.copyProperties(entry.getValue(), dto);
                dto.setStatus("导入失败");
                if (1 == type) {
                    dto.setReason("解析错误");
                } else if (2 == type) {
                    dto.setReason("商品ID不存在");
                } else if (3 == type) {
                    dto.setReason("商品SKU不存在");
                } else if (4 == type) {
                    dto.setReason("选品库SKU不存在");
                } else if (5 == type) {
                    dto.setReason("商品已失效");
                } else if (7 == type) {
                    dto.setReason("导入设置销售价失败");
                } else if (8 == type) {
                    dto.setReason("售价格式不正确");
                } else if (9 == type) {
                    dto.setReason("售价应该大于0元");
                }
                excelDataList.add(dto);
                // 添加移除记录
                deletedRow.add(entry.getKey());
            }
        }
        //移除
        for (Integer row : deletedRow) {
            parseSuccessRows.remove(row);
        }
        return excelDataList;
    }

    private String getFilePath(List<DistributorItemLibSalePriceExcelDTO> excelDataList, String name) {
        String filePath = null;
        InputStream is = null;
        try {
            ByteArrayOutputStream outputStream = null;
            if (excelDataList.isEmpty()) {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(DistributorItemLibSalePriceExcelDTO.class);
            } else {
                outputStream = excelExportHelper.downloadTemplateCustomPlus(excelDataList, DistributorItemLibSalePriceExcelDTO.class);
            }
            byte[] content = outputStream.toByteArray();
            is = new ByteArrayInputStream(content);
            filePath = registry.findBy(ObjectStorageFactory.class).uploadFile(name + ".xlsx", is);
            log.info("filePath：{}", filePath);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            log.error("distributor.item.sale.price.import, error {}", Throwables.getStackTraceAsString(e));
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("distributor.item.sale.price.import, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception et) {
                    log.error("distributor.item.sale.price.import, et {}", Throwables.getStackTraceAsString(et));
                }
            }
        }
        return filePath;
    }
}
