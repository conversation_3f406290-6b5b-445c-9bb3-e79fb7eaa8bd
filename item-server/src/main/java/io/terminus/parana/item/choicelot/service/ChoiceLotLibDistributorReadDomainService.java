package io.terminus.parana.item.choicelot.service;

import com.google.common.collect.Maps;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibDistributor;
import io.terminus.parana.item.choicelot.model.bo.ChoiceLotDistributorTotalBO;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibDistributorDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 选品库-选品加价率数据操作 TODO
 * @date 2022/3/3 15:15
 */
@Service
@RequiredArgsConstructor
public class ChoiceLotLibDistributorReadDomainService {

    private final ChoiceLotLibDistributorDao choiceLotLibDistributorDao;

    /**
     * 根据选品库ID获取选品库渠道商可见关系
     * @param choiceLotLibId 选品库ID
     * @return
     */
    public List<ChoiceLotLibDistributor> getDistributorByChoiceLotId(Long choiceLotLibId){
        return getChoiceVisibleByChoiceLotId(choiceLotLibId, null);
    }

    /**
     * 获取渠道商可见选品库
     * @return
     */
    public List<ChoiceLotLibDistributor> getDistributorChoiceShow( Set<Long> choiceLotLibIds, Long distributorId){
        Map<String, Object> params = Maps.newHashMap();
        params.put("choiceLotLibIds", choiceLotLibIds);
        params.put("distributorId", distributorId);
        return choiceLotLibDistributorDao.getDistributorByChoiceLotId(params);
    }


    /**
     * 根据选品库ID获取选品库渠道商可见关系
     * @param choiceLotLibIds
     * @return
     */
    public List<ChoiceLotLibDistributor> getChoiceVisibleByChoiceLotId(Long choiceLotLibId, Set<Long> choiceLotLibIds){
        Map<String, Object> params = Maps.newHashMap();
        params.put("choiceLotLibId",  choiceLotLibId);
        params.put("choiceLotLibIds", choiceLotLibIds);
        return choiceLotLibDistributorDao.getDistributorByChoiceLotId(params);
    }
}
