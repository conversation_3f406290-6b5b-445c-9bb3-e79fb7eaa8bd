package io.terminus.parana.item.category.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2018-07-25 16:47:13
 */
@Data
public class FrontCategory implements BaseCategory {

    private static final long serialVersionUID = 742130393307571472L;
    /**
     * id
     */
    private Long id;
    /**
     * 扩展类型
     */
    private Integer extensionType;
    /**
     * 父级id
     */
    private Long pid;
    /**
     * 名称
     */
    private String name;
    /**
     * logo
     */
    private String logo;
    /**
     * 级别
     */
    private Integer level;
    /**
     * 是否有孩子
     */
    private Boolean hasChildren;
    /**
     * 逻辑删除标识
     */
    private Boolean deleted;
    /**
     * 操作
     */
    private Integer options;
    /**
     * 租户Id
     */
    private Integer tenantId;
    /**
     * 其他属性
     */
    private String extraJson;
    /**
     * createdAt
     */
    private Date createdAt;
    /**
     * updatedAt
     */
    private Date updatedAt;

    /**
     * updatedBy
     */
    private String updatedBy;
}