package io.terminus.parana.item.choicelot.repository;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceLotLibItemBindRequest;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceLotLibItemUnBindRequest;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibItemModel;
import io.terminus.parana.item.choicelot.model.DistributorItemLibModel;
import io.terminus.parana.item.choicelot.model.bo.ChoiceLotLibItemTotalBO;
import io.terminus.parana.item.item.canal.ESMQInjection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ChoiceLotLibItemDao extends MyBatisDao<ChoiceLotLibItemModel> {

	/**
	 *	创建选品库Item
	 */
	public Boolean createModel(List<ChoiceLotLibItemModel> modelList, ChoiceLotLibItemBindRequest request) {
		boolean create =  this.creates(modelList) > 0;
		if(create){
           // ESMQInjection.entryChoiceByItemIds(request.getChoiceLotLibId(), request.getOperatorId(), new HashSet<>(request.getItemIds()), null, "绑定选品库商品变更");
        }
		return create;
	}

	/**
	 *	解绑选品库Item
	 */
	public Boolean updateUnBindByItems(Map<String, Object> params, ChoiceLotLibItemUnBindRequest request, Long delBatchNo) {
		boolean update = this.sqlSession.update(sqlId("updateUnBind"), params) > 0;
		if(update){
             ESMQInjection.entryChoiceByItemIds(request.getChoiceLotLibId(), request.getOperatorId(), new HashSet<>(request.getItemIds()), delBatchNo, "解绑选品库商品变更");
        }
		return update;
    }

	public void updateUnBind(Long operatorId, Long choiceLotLibId, String updateName, Long delBatchNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("operatorId", operatorId);
        params.put("updateName", updateName);
        params.put("delBatchNo", delBatchNo);
        boolean update = this.sqlSession.update(sqlId("updateUnBind"), params) > 0;
        if(update){
            ESMQInjection.entryChoiceByChoiceLotLibId(choiceLotLibId, operatorId, delBatchNo, "删除选品库变更");
        }
    }

	/**
	 * 更新选品库可见渠道商字段
	 * @param model
	 */
	public Boolean updateShowDistributorIds(ChoiceLotLibItemModel model, String showDistributorIds) {
		Map<String, Object> params = new HashMap<>();
		params.put("operatorId", model.getOperatorId());
		params.put("choiceLotLibId", model.getChoiceLotLibId());
		params.put("showDistributorIds", showDistributorIds);
		params.put("itemId", model.getItemId());
		params.put("updatedBy", model.getUpdatedBy());
		boolean update = this.sqlSession.update(sqlId("updateShowDistributorIds"), params) > 0;
		if(update){
			ESMQInjection.entryChoiceByItemId(model.getChoiceLotLibId(), model.getOperatorId(), model.getItemId(), null, "删除选品库变更");
		}
		return update;
	}


	/**
	 *	根据区域运营ID和商品Id查询选品库商品信息
	 */
	public ChoiceLotLibItemModel findChoiceItemByChoiceId(Long operatorId, Long itemId, Long choiceLotLibId, Long delBatchNo){
		Map<String, Object> params = new HashMap<>();
		params.put("operatorId", operatorId);
		params.put("itemId", itemId);
		params.put("choiceLotLibId", choiceLotLibId);
		params.put("delBatchNo", delBatchNo);
		return getSqlSession().selectOne(sqlId("findChoiceItemByChoiceId"), params);
	}

	/**
	 *	分页查询选品库Item
	 */
	public Paging<ChoiceLotLibItemModel> page(Map<String, Object> params, Integer offset, Integer limit){
		return this.paging(offset, limit, params);
	}

	/**
	 * 分页条件筛选选品库信息
	 * @param criteriaMap
	 * @return
	 */
	public List<ChoiceLotLibItemModel> pageFindByCriteria(Map<String, Object> criteriaMap) {
		return getSqlSession().selectList(sqlId("pageFindByCriteria"), criteriaMap);
	}

	/**
	 * 条件筛选选品库信息
	 * @param criteriaMap
	 * @return
	 */
	public List<ChoiceLotLibItemModel> listFindByCriteria(Map<String, Object> criteriaMap) {
		return getSqlSession().selectList(sqlId("listFindByCriteria"), criteriaMap);
	}

	/**
	 * 条件筛选选品库信息
	 * @param criteriaMap
	 * @return
	 */
	public List<ChoiceLotLibItemModel> getCreateTimeByChoiceLotLibId(Map<String, Object> criteriaMap) {
		return getSqlSession().selectList(sqlId("getCreateTimeByChoiceLotLibId"), criteriaMap);
	}

	/**
	 * 根据选品库Id统计商品数量
	 * @param choiceLotLibIdList
	 * @return
	 */
	public List<ChoiceLotLibItemTotalBO> findItemNumTotalByChoiceId(Set<Long> choiceLotLibIdList){
		Map<String, Object> param = Maps.newHashMap();
		param.put("choiceLotLibIdList", choiceLotLibIdList);
		return this.getSqlSession().selectList(sqlId("findItemNumTotalByChoiceId"), param);
	}

	/**
	 * 根据选品库Id维护选品库Item 渠道商ids字段
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean updateItemDistributorIds(List<DistributorItemLibModel> modelList, Integer unBind) {
		for (DistributorItemLibModel model : modelList) {
			Map<String, Object> param = Maps.newHashMap();
			param.put("distributorId", model.getDistributorId());
			param.put("choiceLotLibId", model.getChoiceLotLibId());
			param.put("itemId", model.getItemId());
			param.put("unBind", unBind);
			boolean unBindResult = this.getSqlSession().update(sqlId("updateItemDistributorIds"), param) > 0;
			log.info("渠道商绑定解绑商品开始***********************{}",unBindResult);
			ESMQInjection.entryChoiceByItemId(model.getChoiceLotLibId(), model.getOperatorId(), model.getItemId(), null, "渠道商绑定解绑商品");
//			if (unBindResult) {
//				log.info("渠道商绑定解绑商品开始***********************{}",unBindResult);
//				ESMQInjection.entryChoiceByItemIdSync(model.getChoiceLotLibId(), model.getOperatorId(), model.getItemId(), null, "渠道商绑定解绑商品");
//			}
		}
		return Boolean.TRUE;
	}

	// 埋点
	public Boolean update(ChoiceLotLibItemModel model) {
		boolean result = super.update(model);
		if (result) {
			ESMQInjection.entryChoiceByItemId(model.getChoiceLotLibId(),model.getOperatorId(), model.getItemId(), null, "选品库修改");
		}
		return result;
	}


	@Transactional(rollbackFor = Exception.class)
	public Boolean updateBatch(List<ChoiceLotLibItemModel> modelList) {
		for (ChoiceLotLibItemModel model : modelList) {
			this.update(model);
			ESMQInjection.entryChoiceByItemId(model.getChoiceLotLibId(), model.getOperatorId(), model.getItemId(), model.getDelBatchNo(), "渠道商绑定更新商品");
		}
		return Boolean.TRUE;
	}

	public boolean batchUpdateStatus(List<Long> itemIds,Integer status){
		Map<String, Object> params = new HashMap<>();
		params.put("itemIds", itemIds);
		params.put("status", status);
		int i = this.sqlSession.update(this.sqlId("batchUpdateStatus"), params);
		if(i > 0){
			for (Long itemId : itemIds) {
				ESMQInjection.entryChoiceByOperatorIdAndItemId(null,itemId,"admin");
			}
			return true;
		}else{
			return false;
		}
	}

	public List<ChoiceLotLibItemModel> findByOperatorId(Long operatorId, Integer offset, Integer limit){
		return getSqlSession().selectList(sqlId("findByOperatorId"), ImmutableMap.of(
				"operatorId", operatorId,
				"offset", offset,
				"limit", limit
		));
	}


	/**
	 *    根据区域运营ID和商品Id查询选品库商品信息 同步ES用 查询删除的数据
	 */
	public List<ChoiceLotLibItemModel> findCanalChoiceItemByChoiceId(Long operatorId, Long itemId, Long choiceLotLibId, Long delBatchNo){
		Map<String, Object> params = new HashMap<>();
		params.put("operatorId", operatorId);
		params.put("itemId", itemId);
		params.put("choiceLotLibId", choiceLotLibId);
		params.put("delBatchNo", delBatchNo);
		return getSqlSession().selectList(sqlId("findCanalChoiceItemByChoiceId"), params);
	}

	/**
	 *    根据区域运营ID和商品Id查询选品库商品信息 同步ES用 查询删除的数据
	 */
	public List<ChoiceLotLibItemModel> findCanalChoiceItemByChoiceIdsAndItemIds(List<Long> itemIds, List<Long> choiceLotLibIds){
		Map<String, Object> params = new HashMap<>();
		params.put("itemIds", itemIds);
		params.put("choiceLotLibIds", choiceLotLibIds);
		return getSqlSession().selectList(sqlId("findCanalChoiceItemByChoiceId"), params);
	}

	public List<ChoiceLotLibItemModel> findChoiceItemByChoiceIdsAndItemIds(List<Long> itemIds, List<Long> choiceLotLibIds){
		Map<String, Object> params = new HashMap<>();
		params.put("itemIds", itemIds);
		params.put("choiceLotLibIds", choiceLotLibIds);
		return getSqlSession().selectList(sqlId("findChoiceItemByChoiceIdsAndItemIds"), params);
	}

	public List<ChoiceLotLibItemModel> findByOperatorIdsAndItemIds(Set<Long> operatorIds, Set<Long> itemIds) {
		Map<String, Object> params = new HashMap<>();
		params.put("itemIds", itemIds);
		params.put("operatorIds", operatorIds);
		return getSqlSession().selectList(sqlId("findByOperatorIdsAndItemIds"), params);
	}

	public List<ChoiceLotLibItemModel> queryChoiceLotLibItemsBeforeData(Integer choiceLotLibItemEsTime){
		Map<String, Object> params = new HashMap<>();
		params.put("choiceLotLibItemEsTime", choiceLotLibItemEsTime);
		return sqlSession.selectList(sqlId("queryChoiceLotLibItemsBeforeData"), params);
	}

	public Long countForDataReport(Map<String, Object> criteria) {
		if (criteria == null) {
			criteria = Maps.newHashMap();
		}
		Long total = (Long)this.sqlSession.selectOne(this.sqlId("countForDataReport"), criteria);
		return total;
	}
}
