package io.terminus.parana.item.enhance.model.DTO;

import com.google.common.collect.Sets;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.parana.item.third.param.WarehouseBaseParam;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-10-12
 */
@Data
public class InventorySourcingInventoryParamDTO implements Serializable {
    private static final long serialVersionUID = -1869283974192698140L;

    @ApiModelProperty("群组id")
    private String groupId;

    @ApiModelProperty("组合商品id")
    private String bundleId;

    @ApiModelProperty("对象id")
    private String entityId;

    @ApiModelProperty("对象类型")
    private Integer entityType;

    @ApiModelProperty("数量")
    private Long quantity;

    @ApiModelProperty(value = "库存类型", notes = "real, preorder")
    private Integer inventoryType;

    @ApiModelProperty("渠道编码")
    private String channelCode;

    @ApiModelProperty("渠道维度")
    private Integer dimension;

    @ApiModelProperty("仓库")
    private Set<WarehouseBaseParam> warehouses = Sets.newHashSet();

    private String lineId;

    private Integer lineType;

    private String refLineId;

    private Long itemId;

    private Long sdItemId;

    private Long skuId;

    private Long operatorId;

    private Long vendorId;

    private String warehouseCode;

    private Long sdProjectNo;

    private Long sdDeprNo;

    private Long sdLtdNo;

    private BigDecimal stockRatio;

    private String machineNo;
    /**
     * sd仓库编码
     */
    private String sdWarehouseCode;

    /**
     * 状态：1未绑定、2已绑定、3废弃
     */
    private Integer sts;

    private String isrf;

}
