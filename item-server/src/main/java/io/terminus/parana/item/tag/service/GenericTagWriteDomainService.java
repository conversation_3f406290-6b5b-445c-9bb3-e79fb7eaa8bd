package io.terminus.parana.item.tag.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.common.base.JsonSupport;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.common.utils.ObjectUtils;
import io.terminus.parana.item.tag.bo.*;
import io.terminus.parana.item.tag.cache.CacheGenericTagById;
import io.terminus.parana.item.tag.component.TagBatchIdHelper;
import io.terminus.parana.item.tag.enums.TagStatus;
import io.terminus.parana.item.tag.enums.TagTargetType;
import io.terminus.parana.item.tag.enums.TagType;
import io.terminus.parana.item.tag.manager.GenericTagManager;
import io.terminus.parana.item.tag.model.GenericTag;
import io.terminus.parana.item.tag.model.GenericTagBinding;
import io.terminus.parana.item.tag.repository.GenericTagBindingDao;
import io.terminus.parana.item.tag.repository.GenericTagDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-06-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GenericTagWriteDomainService extends AbsServiceBase implements JsonSupport {


    private final CacheGenericTagById cacheGenericTagById;
    private final GenericTagBindingDao genericTagBindingDao;
    private final GenericTagDao genericTagDao;
    private final GenericTagManager genericTagManager;
    private final TagBatchIdHelper tagBatchIdHelper;

    /**
     * 对简单标签的重复性判断
     *
     * @param code         标签编码
     * @param targetType   目标类型
     * @param targetId     目标id
     * @param takeEffectAt 生效开始时间
     * @param loseEffectAt 生效结束时间
     */
    private void assertSimpleMaskTag(String code, Integer targetType, Long targetId, Date takeEffectAt, Date loseEffectAt) {
        boolean duplicate = genericTagBindingDao.checkSimpleMaskDuplicate(code, targetType, targetId, null,
                takeEffectAt, loseEffectAt);

        if (duplicate) {
            log.error("binding conflict by tagCode: {}, targetType: {}, targetId: {}, takeEffectAt: {}, loseEffectAt: {}",
                    code, targetType, targetId, takeEffectAt, loseEffectAt);
            throw new ServiceException("partial.binding.exist");
        }
    }

    public Boolean simpleInject(GenericTagBinding tagBinding) {
        try {
            assertSimpleMaskTag(tagBinding.getTagCode(), tagBinding.getTargetType(), tagBinding.getTargetId(),
                    tagBinding.getEffectAt(), tagBinding.getExpireAt());
            tagBatchIdHelper.fillBatchId(tagBinding);
            Integer createdCount = genericTagBindingDao.creates(Collections.singletonList(tagBinding));
            return createdCount != 0;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to simple inject tag with: {}, cause: {}", tagBinding, printErrorStack(e));
            throw new ServiceException("tag.inject.fail");
        }
    }


    private void assertDuplicate(List<GenericTagBinding> bindingList) {
        Set<String> tagCodeSet = AssembleDataUtils.list2set(bindingList, GenericTagBinding::getTagCode);
        Assert.isFalse(genericTagDao.isCodeExist(tagCodeSet), "partial.tagCode.exist");
    }

    public int batchSimpleInject(List<GenericTagBinding> bindingList) {
        try {
            assertDuplicate(bindingList);
            tagBatchIdHelper.fillBatchId(bindingList);
            return genericTagBindingDao.creates(bindingList);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to create bindings with: {}, cause: {}", bindingList, printErrorStack(e));
            throw new ServiceException("simple.tag.create.fail");
        }
    }

    public int simpleRejectByCode(String code, Date takeEffectAt, Date loseEffectAt, String updatedBy) {
        try {
            return genericTagBindingDao.simpleRejectByCode(code, takeEffectAt, loseEffectAt, updatedBy);
        } catch (Exception e) {
            log.error("fail to reject simple tag by tagCode: {}, takeEffectAt: {}, loseEffectAt: {}, cause: {}",
                    code, takeEffectAt, loseEffectAt, printErrorStack(e));
            throw new ServiceException("simple.tag.reject.fail");
        }
    }

    public int simpleRejectByTarget(String code, Integer targetType, Set<Long> targetIdSet, String updatedBy) {
        try {
            return genericTagBindingDao.simpleRejectByTarget(code, targetType, targetIdSet, updatedBy);
        } catch (Exception e) {
            log.error("fail to reject simple tag by tagCode: {}, targetType: {}, targetIdSet: {}, cause: {}",
                    code, targetType, targetIdSet, printErrorStack(e));
            throw new ServiceException("simple.tag.reject.fail");
        }
    }

    public int simpleReject(SimpleRejectBO bo) {
        try {
            Map<String, Object> map = JSON.objectMapper.convertValue(bo, Map.class);
            return genericTagBindingDao.simpleReject(map);
        } catch (Exception e) {
            log.error("fail to reject simple tag with: {}, cause: {}", bo, printErrorStack(e));
            throw new ServiceException("simple.tag.reject.fail");
        }
    }

    public Long create(GenericTag tag) {
        return createByOwning(tag, GenericTag.SYSTEM_OWNING_TYPE, GenericTag.SYSTEM_OWNING_ID);
    }

    public Long createByOwning(GenericTag tag, Integer owningType, Long owningId) {
        tag.setOwningId(owningId);
        tag.setOwningType(owningType);
        tag.setStatus(TagStatus.VALID.getValue());
        tag.setUpdatedBy(RequestContext.getUpdatedBy());
        genericTagDao.create(tag);
        return tag.getId();
    }

    public boolean bind(TagBindBO bo) {
        return bindByOwning(GenericTag.SYSTEM_OWNING_TYPE, GenericTag.SYSTEM_OWNING_ID, bo);
    }

    public boolean bindByOwning(Integer owningType, Long owningId, TagBindBO bo) {
        List<GenericTag> tagList = validateGenericTag(owningType, owningId, bo.getTagIdSet(), false);
        List<GenericTagBinding> toCreateBindingList = new LinkedList<>();
        for (GenericTag genericTag : tagList) {
            String tagId = genericTag.getId().toString();
            List<Long> existBindingTargetIdList = genericTagBindingDao.queryTargetWhichExistBinding(
                    tagId, TagType.STANDALONE_TAG.getValue(), bo.getTargetType(), bo.getTargetIdSet());

            Set<Long> newTargetIdSet = Sets.newHashSetWithExpectedSize(bo.getTargetIdSet().size());
            newTargetIdSet.addAll(bo.getTargetIdSet());

            if (!CollectionUtils.isEmpty(existBindingTargetIdList)) {
                newTargetIdSet.removeAll(existBindingTargetIdList);
            }

            if (CollectionUtils.isEmpty(newTargetIdSet)) {
                continue;
            }

            for (Long targetId : newTargetIdSet) {
                GenericTagBinding genericTagBinding = new GenericTagBinding();
                genericTagBinding.setTagCode(tagId);
                genericTagBinding.setTagType(TagType.STANDALONE_TAG.getValue());
                genericTagBinding.setTargetType(bo.getTargetType());
                genericTagBinding.setTargetId(targetId);
                genericTagBinding.setEffectAt(bo.getEffectAt());
                genericTagBinding.setExpireAt(bo.getExpireAt());
                genericTagBinding.setAutoRelease(bo.getAutoRelease());
                genericTagBinding.setStatus(TagStatus.VALID.getValue());
                genericTagBinding.setUpdatedBy(RequestContext.getUpdatedBy());

                toCreateBindingList.add(genericTagBinding);
            }
        }

        if (CollectionUtils.isEmpty(toCreateBindingList)) {
            return true;
        }

        tagBatchIdHelper.fillBatchId(toCreateBindingList);
        genericTagBindingDao.creates(toCreateBindingList);
        return true;
    }

    /**
     * 批量绑定用
     * 删除原绑定关系 替换为新绑定关系
     *
     * @param owningType
     * @param owningId
     * @param bo
     * @return
     */
    public boolean flushBindWithLimitSize(Integer owningType, Long owningId, TagBindBO bo, Integer limitSize) {
        Set<Long> tagIdSet = bo.getTagIdSet();
        List<GenericTag> tagList = validateGenericTag(owningType, owningId, tagIdSet, false);
        List<GenericTagBinding> toCreateBindingList = new LinkedList<>();
        List<GenericTagBinding> toDeleteBindingList = new LinkedList<>();
        for (GenericTag genericTag : tagList) {
            String tagId = genericTag.getId().toString();
            List<Long> existBindingTargetIdList = genericTagBindingDao.queryTargetWhichExistBinding(
                    tagId, TagType.STANDALONE_TAG.getValue(), bo.getTargetType(), bo.getTargetIdSet());

            Set<Long> newTargetIdSet = Sets.newHashSetWithExpectedSize(bo.getTargetIdSet().size());
            newTargetIdSet.addAll(bo.getTargetIdSet());

            if (!CollectionUtils.isEmpty(existBindingTargetIdList)) {
                newTargetIdSet.removeAll(existBindingTargetIdList);
            }

            if (CollectionUtils.isEmpty(newTargetIdSet)) {
                continue;
            }

            if (bo.getTargetType().equals(TagTargetType.TAG_TYPE_ITEM.getValue())) {
                for (Long targetId : bo.getTargetIdSet()) {
                    // step1:获取当前targetId的绑定标签信息列表
                    List<GenericTagBinding> bindingList = genericTagBindingDao.findByTarget(targetId, bo.getTargetType());
                    Map<String, GenericTagBinding> bindingMap = bindingList.stream().collect(Collectors.toMap(GenericTagBinding::getTagCode, Function.identity()));
                    // step2:过滤新增的绑定关系
                    tagIdSet.forEach(toBindTagId -> bindingMap.remove(toBindTagId.toString()));
                    // step3:根据limitSize选择保留的绑定关系
                    if (limitSize != null) {
                        int remainBindingSize = bindingMap.size();
                        int toDeleteSize = tagIdSet.size() + remainBindingSize - limitSize;
                        if (toDeleteSize > 0 && remainBindingSize > 0) {
                            if (remainBindingSize <= toDeleteSize) {
                                toDeleteBindingList.addAll(bindingMap.values());
                            } else {
                                List<GenericTagBinding> remainBindingList = Lists.newArrayList(bindingMap.values())
                                        .stream().sorted(Comparator.comparing(GenericTagBinding::getId)).collect(Collectors.toList());
                                toDeleteBindingList.addAll(remainBindingList.subList(0, remainBindingSize - toDeleteSize));
                            }
                        }
                    }
                }
            }

            for (Long targetId : newTargetIdSet) {
                GenericTagBinding genericTagBinding = new GenericTagBinding();
                genericTagBinding.setTagCode(tagId);
                genericTagBinding.setTagType(TagType.STANDALONE_TAG.getValue());
                genericTagBinding.setTargetType(bo.getTargetType());
                genericTagBinding.setTargetId(targetId);
                genericTagBinding.setEffectAt(bo.getEffectAt());
                genericTagBinding.setExpireAt(bo.getExpireAt());
                genericTagBinding.setAutoRelease(bo.getAutoRelease());
                genericTagBinding.setStatus(TagStatus.VALID.getValue());
                genericTagBinding.setUpdatedBy(RequestContext.getUpdatedBy());

                toCreateBindingList.add(genericTagBinding);
            }
        }

        if (!CollectionUtils.isEmpty(toCreateBindingList)) {
            tagBatchIdHelper.fillBatchId(toCreateBindingList);
            genericTagBindingDao.creates(toCreateBindingList);
        }

        if (!CollectionUtils.isEmpty(toDeleteBindingList)) {
            Set<Long> toDeleteIdSet = toDeleteBindingList.stream().map(GenericTagBinding::getId).collect(Collectors.toSet());
            genericTagBindingDao.deletes(toDeleteIdSet, RequestContext.getUpdatedBy());
        }
        return true;
    }

    private GenericTagBinding constructBinding(Integer targetType, Long targetId, GenericTag tag) {
        GenericTagBinding genericTagBinding = new GenericTagBinding();
        genericTagBinding.setTagCode(tag.getId().toString());
        genericTagBinding.setTagType(TagType.STANDALONE_TAG.getValue());
        genericTagBinding.setTargetType(targetType);
        genericTagBinding.setTargetId(targetId);
        genericTagBinding.setEffectAt(GenericTagBinding.MIN_DATE);
        genericTagBinding.setExpireAt(GenericTagBinding.MAX_DATE);
        genericTagBinding.setAutoRelease(Boolean.TRUE);
        genericTagBinding.setStatus(TagStatus.VALID.getValue());
        genericTagBinding.setUpdatedBy(RequestContext.getUpdatedBy());

        return genericTagBinding;
    }

    private List<GenericTagBinding> constructBinding(Integer targetType, Set<Long> targetIdSet, GenericTag tag) {
        if (CollectionUtils.isEmpty(targetIdSet)) {
            return Collections.emptyList();
        }

        return AssembleDataUtils.set2list(targetIdSet, it -> constructBinding(targetType, it, tag));
    }

    public Boolean unbind(TagUnbindBO bo) {
        return unbindByOwning(GenericTag.SYSTEM_OWNING_TYPE, GenericTag.SYSTEM_OWNING_ID, bo);
    }

    public Boolean refreshBinding(TagRefreshBindingBO bo) {
        return refreshBindingByOwning(GenericTag.SYSTEM_OWNING_TYPE, GenericTag.SYSTEM_OWNING_ID, bo);
    }

    /**
     * 刷新标签的绑定信息<br>
     * <p>
     *
     * </p>
     *
     * @param owningType 标签拥有者类型
     * @param owningId   标签拥有者
     * @param bo         绑定调整信息
     * @return 处理成功返回true，否则false
     */
    public Boolean refreshBindingByOwning(Integer owningType, Long owningId, TagRefreshBindingBO bo) {
        if (ObjectUtils.hasIntersection(bo.getToBindTagIdSet(), bo.getToUnbindTagIdSet())) {
            throw new ServiceException("bind.and.unbind.tag.id.has.intersection");
        }

        List<GenericTag> toBindTagList = validateGenericTag(owningType, owningId, bo.getToBindTagIdSet(), true);
        List<GenericTag> toUnbindTagList = validateGenericTag(owningType, owningId, bo.getToUnbindTagIdSet(), true);

        List<GenericTagBinding> toCreateBindingList = new LinkedList<>();
        for (GenericTag genericTag : toBindTagList) {
            List<Long> existTargetIdList = genericTagBindingDao.queryTargetWhichExistBinding(genericTag.getId().toString(),
                    genericTag.getType(), bo.getTargetType(), bo.getTargetIdSet());

            Set<Long> missingTargetIdSet = AssembleDataUtils.notInSet(new HashSet<>(existTargetIdList), bo.getTargetIdSet());
            toCreateBindingList.addAll(constructBinding(bo.getTargetType(), missingTargetIdSet, genericTag));
        }

        tagBatchIdHelper.fillBatchId(toCreateBindingList);
        genericTagManager.refreshBinding(toCreateBindingList, toUnbindTagList, bo.getTargetType(), bo.getTargetIdSet());
        return Boolean.TRUE;
    }

    public Boolean unbindByOwning(Integer owningType, Long owningId, TagUnbindBO bo) {
        validateGenericTag(owningType, owningId, bo.getTagId());
        int deleteCount = genericTagBindingDao.deleteByFilter(TagType.STANDALONE_TAG.getValue(), bo.getTagId().toString(),
                bo.getTargetType(), bo.getTargetIdSet());
        return deleteCount != 0;
    }


    public Boolean unbindForTargetByOwning(Integer owningType, Long owningId, TagUnbindForItemBO bo) {
        return genericTagBindingDao.deleteForTarget(TagType.STANDALONE_TAG.getValue(), bo.getTargetIdSet(), bo.getTargetType());
    }

    public boolean update(GenericTag tag) {
        tag.setUpdatedBy(RequestContext.getUpdatedBy());
        return genericTagDao.update(tag);
    }

    public boolean updateByOwning(Integer owningType, Long owningId, GenericTag tag) {
        validateGenericTag(owningType, owningId, tag.getId());
        return genericTagDao.update(tag);
    }

    public Boolean delete(Set<Long> tagIdSet) {
        return deleteByOwning(GenericTag.SYSTEM_OWNING_TYPE, GenericTag.SYSTEM_OWNING_ID, tagIdSet);
    }

    public Boolean deleteByOwning(Integer owningType, Long owningId, Set<Long> tagIdSet) {
        validateGenericTag(owningType, owningId, tagIdSet, false);

        Boolean isOk = genericTagManager.deleteByOwning(tagIdSet, RequestContext.getUpdatedBy());
        cacheGenericTagById.remove(tagIdSet);
        return isOk;
    }

    private void validateGenericTag(Integer owningType, Long owningId, Long tagId) {
        GenericTag genericTag = cacheGenericTagById.get(tagId);
        Assert.nonNull(genericTag, "tag.not.found");
        Assert.equals(owningType, genericTag.getOwningType(), "tag.owning.type.mismatch");
        Assert.equals(owningId, genericTag.getOwningId(), "tag.owning.id.mismatch");
    }

    private List<GenericTag> validateGenericTag(Integer owningType, Long owningId, Set<Long> tagIdSet, boolean allowNull) {
        List<GenericTag> tagList = cacheGenericTagById.get(tagIdSet).getDirectResult();

        if (!allowNull) {
            Assert.notEmpty(tagList, "tag.not.found");
        }

        Assert.equals(tagIdSet.size(), tagList.size(), "partial.tag.not.found");

        for (GenericTag genericTag : tagList) {
            Assert.equals(owningType, genericTag.getOwningType(), "partial.tag.owning.type.mismatch");
            Assert.equals(owningId, genericTag.getOwningId(), "partial.tag.owning.id.mismatch");
        }

        return tagList;
    }
}
