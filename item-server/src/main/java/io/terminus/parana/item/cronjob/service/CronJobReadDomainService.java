package io.terminus.parana.item.cronjob.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.cronjob.enums.CronJobDelayUnit;
import io.terminus.parana.item.cronjob.model.CronJob;
import io.terminus.parana.item.cronjob.repository.CronJobDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CronJobReadDomainService {

    @Autowired
    private CronJobDao cronJobDao;

    public List<CronJob> scanJobList(Integer tenantId, Integer status,
                                     Integer delay, CronJobDelayUnit unit, List<Integer> typeList) {
        try {
            return cronJobDao.scanJobList(tenantId, status, getExecuteAtWithDelay(delay, unit), typeList);
        } catch (Exception e) {
            log.error("find cronjob failed, status={} delay={} unit={} type={}, cause:{}",
                    status, delay, unit, typeList, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.cronjob.failed");
        }
    }

    public Paging<CronJob> scanJobPaging(Integer tenantId, Integer status,
                                         Integer delay, CronJobDelayUnit unit, List<Integer> typeList,
                                         Integer pageNo, Integer pageSize) {
        try {
            PageInfo pageInfo = PageInfo.of(pageNo, pageSize);
            Map<String, Object> params = Maps.newHashMap();
            params.put("tenantId", tenantId);
            params.put("status", status);
            params.put("executeAt", getExecuteAtWithDelay(delay, unit));
            params.put("typeList", typeList);
            params.put("limit", pageInfo.getLimit());
            params.put("offset", pageInfo.getOffset());
            return cronJobDao.paging(params);
        } catch (Exception e) {
            log.error("find cronjob failed, status={} delay={} unit={} type={}, cause:{}",
                    status, delay, unit, typeList, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.cronjob.failed");
        }
    }


    public CronJob findById(Integer tenantId, Long id) {
        try {
            return cronJobDao.findById(tenantId, id);
        } catch (Exception e) {
            log.error("find cronjob failed, id={}, cause:{}", id, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.cronjob.failed");
        }
    }

    public List<CronJob> findByIds(Integer tenantId, List<Long> ids) {
        try {
            return cronJobDao.findByIds(tenantId, ids);
        } catch (Exception e) {
            log.error("find cronjob failed, ids={}, cause:{}", ids, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.cronjob.failed");
        }
    }

    public List<CronJob> findByTargetCodes(Integer tenantId, List<String> targetCodeList,
                                           List<Integer> typeList, Integer status) {
        try {
            return cronJobDao.findByTargetCodes(tenantId, targetCodeList, typeList, status);
        } catch (Exception e) {
            log.error("find cronjob failed, code={} type={} status={}. cause:{}",
                    targetCodeList, typeList, status, Throwables.getStackTraceAsString(e));
            throw new ServiceException("find.cronjob.failed");
        }
    }

    private Date getExecuteAtWithDelay(Integer delay, CronJobDelayUnit unit) {
        if (delay == null) {
            return null;
        }
        LocalDateTime localDateTime = LocalDateTime.now();
        switch (unit) {
            case HOUR:
                localDateTime = localDateTime.plusHours(delay);
                break;
            case MINUTE:
                localDateTime = localDateTime.plusMinutes(delay);
                break;
            default:
                throw new ServiceException("unsupported.delay.unit");
        }
        return Date.from(localDateTime.atZone(ZoneOffset.systemDefault()).toInstant());
    }
}
