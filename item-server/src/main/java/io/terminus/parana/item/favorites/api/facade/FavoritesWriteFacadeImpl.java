package io.terminus.parana.item.favorites.api.facade;

import com.google.common.collect.Maps;
import io.terminus.common.model.Response;
import io.terminus.parana.item.common.annotation.MicroService;
import io.terminus.parana.item.common.converter.GeneralConverter;
import io.terminus.parana.item.favorites.api.bean.request.*;
import io.terminus.parana.item.favorites.api.bean.request.param.FavoritesParam;
import io.terminus.parana.item.favorites.api.converter.FavoritesApiConverter;
import io.terminus.parana.item.favorites.enums.FavoritesStatus;
import io.terminus.parana.item.favorites.model.Favorites;
import io.terminus.parana.item.favorites.service.FavoritesReadDomainService;
import io.terminus.parana.item.favorites.service.FavoritesWriteDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 收藏写服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FavoritesWriteFacadeImpl implements FavoritesWriteFacade {

    private final FavoritesWriteDomainService favoritesWriteDomainService;
    private final FavoritesApiConverter favoritesApiConverter;

    @Override
    @MicroService
    public Response<Boolean> add(FavoritesAddRequest request) {
        try {
            log.info("Favorites add {}",request);
            Boolean addFlag = favoritesWriteDomainService.cancel(
                    request.getTenantId(), request.getUserId(),
                    request.getTargetId(), request.getTargetType(), request.getOperatorId());
            log.info("Favorites addFlag {}",addFlag);
            Favorites favorites = new Favorites();
            favorites.setTenantId(request.getTenantId());
            favorites.setUserId(request.getUserId());
            favorites.setTargetId(request.getTargetId());
            favorites.setTargetType(request.getTargetType());
            favorites.setOperatorId(request.getOperatorId());
            favorites.setStatus(FavoritesStatus.NORMAL.getValue());
            Map<String, String> extra = Maps.newHashMap();
            extra.put("operatorId", String.valueOf(request.getTargetOperatorId()));
            extra.put("originalPrice", String.valueOf(request.getOriginalPrice()));
            extra.put("skuId", String.valueOf(request.getSkuId()));
            extra.put("choiceLotLibId", String.valueOf(request.getChoiceLotLibId()));
            extra.put("attributes", String.valueOf(request.getAttributes()));
            favorites.setExtra(extra);
            return Response.ok(favoritesWriteDomainService.add(favorites));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<Boolean> cancel(FavoritesCancelRequest request) {
        try {
            return Response.ok(favoritesWriteDomainService.cancel(
                    request.getTenantId(), request.getUserId(),
                    request.getTargetId(), request.getTargetType(), request.getOperatorId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    /**
     * 删除收藏(targetId 和 type)
     *
     * @param request
     * @return
     */
    @Override
    public Response<Boolean> delete(FavoritesDeleteRequest request) {
        try {
            return Response.ok(favoritesWriteDomainService.delete(
                    request.getTenantId(), request.getTargetId(), request.getTargetType()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<Boolean> batchCancelById(FavoritesCancelByIdsRequest request) {
        try {
            return Response.ok(favoritesWriteDomainService.batchCancelById(
                    request.getTenantId(), request.getUserId(), request.getIds()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    @MicroService
    public Response<Boolean> cancelByUserId(FavoritesCancelByUserIdRequest request) {
        try {
            return Response.ok(favoritesWriteDomainService.cancelByUserId(
                    request.getTenantId(), request.getUserId(), request.getTargetType(), request.getOperatorId()));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> recordInCart(FavoritesRecordInCartRequest request) {
        try {
            List<FavoritesParam> favoritesParamList = request.getFavoritesParamList();
            favoritesParamList.forEach(favoritesParam -> favoritesParam.setTenantId(request.getTenantId()));

            List<Favorites> favoritesList = GeneralConverter.batchConvert(favoritesParamList, favoritesApiConverter::param2domain);
            favoritesList.forEach(favorites -> favorites.setStatus(FavoritesStatus.NORMAL.getValue()));
            return Response.ok(favoritesWriteDomainService.batchAdd(favoritesList));
        } catch (Exception e) {
            return Response.fail(e.getMessage());
        }
    }

}
