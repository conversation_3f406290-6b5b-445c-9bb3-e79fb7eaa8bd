package io.terminus.parana.item.comment.app;

import com.google.common.collect.ImmutableSet;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.comment.agreement.CommentAgreement;
import io.terminus.parana.item.comment.api.bean.response.ItemCommentInfo;
import io.terminus.parana.item.comment.api.bean.response.ItemCommentRenderInfo;
import io.terminus.parana.item.comment.api.bean.response.ItemCommentStatisticsInfo;
import io.terminus.parana.item.comment.api.converter.ItemCommentApiInfoConverter;
import io.terminus.parana.item.comment.enums.CommentStatus;
import io.terminus.parana.item.comment.enums.CommentType;
import io.terminus.parana.item.comment.enums.ItemCommentPagingScene;
import io.terminus.parana.item.comment.search.ItemCommentReadDomainService;
import io.terminus.parana.item.comment.search.dataobject.ItemCommentDO;
import io.terminus.parana.item.comment.search.so.ItemCommentSO;
import io.terminus.parana.item.common.base.AbsServiceBase;
import io.terminus.parana.item.common.converter.GeneralConverter;
import io.terminus.parana.item.common.enums.SortType;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 1231 23
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ItemCommentReadApp extends AbsServiceBase {

    private final ItemCommentReadDomainService itemCommentReadDomainService;
    private final ItemCommentApiInfoConverter itemCommentApiInfoConverter;

    private Long countRoot(Long itemId, Integer tenantId) {
        ItemCommentSO so = new ItemCommentSO();
        so.setItemId(itemId);
        so.setParentId(CommentAgreement.ROOT_PARENT_ID);
        so.setStatus(CommentStatus.NORMAL.getValue());
        so.setType(CommentType.ITEM_COMMENT.getValue());
        so.setLimit(0);
        so.setOffset(0);
        so.setTenantId(tenantId);

        Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);
        return paging.getTotal();
    }

    private Long countPassedPursue(Long itemId, Integer tenantId) {
        ItemCommentSO so = new ItemCommentSO();
        so.setItemId(itemId);
        so.setParentId(CommentAgreement.ROOT_PARENT_ID);
        so.setStatus(CommentStatus.NORMAL.getValue());
        so.setType(CommentType.ITEM_COMMENT.getValue());
        so.setLimit(0);
        so.setOffset(0);
        so.setWithValidPursue(true);
        so.setTenantId(tenantId);

        Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);
        return paging.getTotal();
    }

    private Long countWithImage(Long itemId, Integer tenantId) {
        ItemCommentSO so = new ItemCommentSO();
        so.setItemId(itemId);
        so.setParentId(CommentAgreement.ROOT_PARENT_ID);
        so.setStatus(CommentStatus.NORMAL.getValue());
        so.setType(CommentType.ITEM_COMMENT.getValue());
        so.setLimit(0);
        so.setOffset(0);
        so.setWithImage(true);
        so.setTenantId(tenantId);

        Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);
        return paging.getTotal();
    }

    private Map<Long, List<ItemCommentDO>> appendPursue(Set<Long> topIdSet, Integer tenantId) {
        ItemCommentSO so = new ItemCommentSO();
        so.setTopIdSet(topIdSet);
        so.setExcludeRoot(true);
        so.setStatus(CommentStatus.NORMAL.getValue());
        so.setType(CommentType.ITEM_COMMENT.getValue());
        so.setTenantId(tenantId);

        Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);
        return AssembleDataUtils.list2group(paging.getData(), ItemCommentDO::getTopId);
    }

    private Paging<ItemCommentDO> rootPaging(Long itemId, Integer limit, Integer offset, Integer tenantId, boolean withImage,
                                             boolean withPursue) {
        ItemCommentSO so = new ItemCommentSO();
        so.setItemId(itemId);
        so.setLimit(limit);
        so.setOffset(offset);
        so.setStatus(CommentStatus.NORMAL.getValue());
        so.setType(CommentType.ITEM_COMMENT.getValue());
        so.setParentId(CommentAgreement.ROOT_PARENT_ID);
        so.setWithImage(withImage);
        so.setWithValidPursue(withPursue);
        so.setTenantId(tenantId);

        return itemCommentReadDomainService.searchItemCommentPaging(so);
    }

    private List<ItemCommentDO> appendPursue(Long topId, Integer tenantId) {
        ItemCommentSO so = new ItemCommentSO();
        so.setTopId(topId);
        so.setExcludeRoot(true);
        so.setType(CommentType.ITEM_COMMENT.getValue());
        so.setTenantId(tenantId);
        so.setSortType(SortType.ASC.name());

        Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);
        return paging.getData();
    }

    private ItemCommentDO directQuery(Long commentId, Integer tenantId) {
        ItemCommentSO so = new ItemCommentSO();
        so.setId(commentId);
        so.setType(CommentType.ITEM_COMMENT.getValue());
        so.setParentId(CommentAgreement.ROOT_PARENT_ID);
        so.setTenantId(tenantId);
        so.setLimit(1);
        so.setOffset(0);

        Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);
        if (paging.isEmpty()) {
            return null;
        } else {
            return AssembleDataUtils.takeFirst(paging.getData());
        }
    }

    public ItemCommentRenderInfo renderFullInfo(Long commentId, Integer tenantId) {
        ItemCommentRenderInfo result = new ItemCommentRenderInfo();

        try {
            ItemCommentDO root = directQuery(commentId, tenantId);

            if (root == null) {
                return result;
            }

            result.setParent(itemCommentApiInfoConverter.get(root));
            List<ItemCommentDO> children = appendPursue(root.getId(), tenantId);
            result.setChildren(itemCommentApiInfoConverter.get(children));

            return result;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to render full item comment info by id: {}, cause: {}", commentId, printErrorStack(e));
            throw new ServiceException("full.item.comment.render.fail");
        }
    }

    public Paging<ItemCommentRenderInfo> renderItemComment(Long itemId, Integer limit, Integer offset, Integer tenantId,
                                                           boolean withImage, boolean withPursue) {
        try {
            Paging<ItemCommentDO> paging = rootPaging(itemId, limit, offset, tenantId, withImage, withPursue);
            if (paging.isEmpty()) {
                return Paging.empty();
            }

            List<ItemCommentInfo> parentList = itemCommentApiInfoConverter.get(paging.getData());

            Set<Long> parentIdSet = AssembleDataUtils.list2set(parentList, ItemCommentInfo::getId);
            Map<Long, List<ItemCommentDO>> pursueMap = appendPursue(parentIdSet, tenantId);

            List<ItemCommentRenderInfo> resultData = new ArrayList<>(parentList.size());

            for (ItemCommentInfo parent : parentList) {
                List<ItemCommentDO> pursueList = pursueMap.get(parent.getId());

                ItemCommentRenderInfo renderInfo = new ItemCommentRenderInfo(parent, itemCommentApiInfoConverter.get(pursueList));
                resultData.add(renderInfo);
            }

            return new Paging<>(paging.getTotal(), resultData);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to render item comment by itemId: {}, limit: {}, offset: {}, cause: {}",
                    itemId, limit, offset, printErrorStack(e));
            throw new ServiceException("item.comment.render.fail");
        }
    }

    public ItemCommentStatisticsInfo statistics(Long itemId, Integer tenantId) {
        try {
            Long total = countRoot(itemId, tenantId);
            if (total == null || total < 1) {
                return ItemCommentStatisticsInfo.EMPTY;
            }

            Long passedPursue = countPassedPursue(itemId, tenantId);
            Long withImage = countWithImage(itemId, tenantId);

            return new ItemCommentStatisticsInfo(total, withImage, passedPursue);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to statistics item comment by itemId: {}, cause: {}", itemId, printErrorStack(e));
            throw new ServiceException("item.comment.statistics.fail");
        }
    }

    private void processByCode(String sceneCode, ItemCommentSO so) {
        Assert.nonNull(sceneCode, "scene.code.is.null");

        switch (ItemCommentPagingScene.valueOf(sceneCode)) {
            case BUYER:
                Assert.nonNull(so.getUserId());
                so.setStatusSet(ImmutableSet.of(CommentStatus.NORMAL.getValue(),
                        CommentStatus.CHECK_PENDING.getValue(), CommentStatus.SHIELD.getValue()));
                break;
            case SELLER:
                Assert.nonNull(so.getShopId());
                so.setStatusSet(ImmutableSet.of(CommentStatus.NORMAL.getValue(), CommentStatus.SHIELD.getValue()));
                break;
            case ADMIN_AUDIT:
                so.setStatusSet(ImmutableSet.of(CommentStatus.CHECK_PENDING.getValue(),
                        CommentStatus.NOT_PASS.getValue(), CommentStatus.SHIELD.getValue()));
                break;
            case ADMIN_MANAGEMENT:
                so.setStatusSet(ImmutableSet.of(CommentStatus.NORMAL.getValue(), CommentStatus.SHIELD.getValue()));
                break;
            default:
                throw new ServiceException("invalid.scene");
        }
    }

    public Paging<ItemCommentInfo> paging(String sceneCode, ItemCommentSO so) {
        so.setType(CommentType.ITEM_COMMENT.getValue());
        try {
            if (so.getStatus() == null) {
                processByCode(sceneCode, so);
            }
            Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);
            return GeneralConverter.batchConvert(paging, itemCommentApiInfoConverter::get);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to paging item comment with: {}, cause: {}", so, printErrorStack(e));
            throw new ServiceException("item.comment.paging.fail");
        }
    }

    private Map<Long, List<ItemCommentDO>> appendPursueForTree(Set<Long> topIdSet, Integer tenantId, Set<Integer> statusSet) {
        ItemCommentSO so = new ItemCommentSO();
        so.setTopIdSet(topIdSet);
        so.setExcludeRoot(true);
        so.setStatusSet(statusSet);
        so.setType(CommentType.ITEM_COMMENT.getValue());
        so.setTenantId(tenantId);

        Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);
        return AssembleDataUtils.list2group(paging.getData(), ItemCommentDO::getTopId);
    }

    public Paging<ItemCommentRenderInfo> pagingTree(String sceneCode, ItemCommentSO so) {
        so.setType(CommentType.ITEM_COMMENT.getValue());
        so.setParentId(CommentAgreement.ROOT_PARENT_ID);
        try {
            if (so.getStatus() == null) {
                processByCode(sceneCode, so);
            }

            // 获取主评分页
            Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);
            if (paging.isEmpty()) {
                return Paging.empty();
            }
            List<ItemCommentInfo> parentList = itemCommentApiInfoConverter.get(paging.getData());

            // 透出追评
            List<ItemCommentRenderInfo> resultData = new ArrayList<>(parentList.size());

            Set<Long> parentIdSet = AssembleDataUtils.list2set(parentList, ItemCommentInfo::getId);
            Map<Long, List<ItemCommentDO>> pursueMap = appendPursueForTree(parentIdSet, so.getTenantId(), so.getStatusSet());

            for (ItemCommentInfo parent : parentList) {
                List<ItemCommentDO> pursueList = pursueMap.get(parent.getId());

                ItemCommentRenderInfo renderInfo = new ItemCommentRenderInfo(parent, itemCommentApiInfoConverter.get(pursueList));
                resultData.add(renderInfo);
            }

            return new Paging<>(paging.getTotal(), resultData);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to paging item comment with: {}, cause: {}", so, printErrorStack(e));
            throw new ServiceException("item.comment.tree.paging.fail");
        }
    }

    public ItemCommentInfo randomCommentWithStatus(Integer status, Integer tenantId, Long id) {
        ItemCommentSO so = new ItemCommentSO();
        try {
            so.setStatus(status);
            so.setTenantId(tenantId);
            so.setLimit(1);
            so.setId(id);
            so.setType(CommentType.ITEM_COMMENT.getValue());
            Paging<ItemCommentDO> paging = itemCommentReadDomainService.searchItemCommentPaging(so);

            if (paging.isEmpty()) {
                return null;
            } else {
                return itemCommentApiInfoConverter.get(AssembleDataUtils.takeFirst(paging.getData()));
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to get random comment with status: {}, cause: {}", status, printErrorStack(e));
            throw new ServiceException("random.item.comment.query.fail");
        }
    }
}

