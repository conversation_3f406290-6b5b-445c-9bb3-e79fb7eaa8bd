package io.terminus.parana.item.category.api.facade;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.request.OuterCategoryAttributeCreateRequest;
import io.terminus.parana.item.category.api.bean.request.OuterCategoryAttributeDeleteRequest;
import io.terminus.parana.item.category.api.bean.request.OuterCategoryAttributeImportRequest;
import io.terminus.parana.item.category.api.bean.request.OuterCategoryAttributeUpdateRequest;
import io.terminus.parana.item.category.manager.OuterCategoryAttributeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2021-01-19 下午2:39
 */
@Slf4j
@Service
public class OuterCategoryAttributeWriteFacadeImpl implements OuterCategoryAttributeWriteFacade{

    @Autowired
    private OuterCategoryAttributeService categoryAttributeService;

    @Override
    public Response<Long> createOuterAttribute(OuterCategoryAttributeCreateRequest request) {

        try {

            return Response.ok(categoryAttributeService.createOuterAttribute(request));

        } catch (Exception e) {

            log.error(e.getMessage(), e);
            return Response.fail("outerCategoryAttribute.create.fail");

        }
    }

    @Override
    public Response<Boolean> ImportOuterAttribute(List<OuterCategoryAttributeImportRequest> request) {
        try {

            return Response.ok(categoryAttributeService.ImportOuterAttribute(request));

        } catch (Exception e) {

            log.error(e.getMessage(), e);
            return Response.fail("outerCategoryAttribute.Import.create.fail");

        }
    }

    @Override
    public Response<Boolean> ImportOuterAttributeUp(List<OuterCategoryAttributeImportRequest> request) {
        try {

            return Response.ok(categoryAttributeService.ImportOuterAttributeUp(request));

        } catch (Exception e) {

            log.error(e.getMessage(), e);
            return Response.fail("outerCategoryAttribute.Import.update.fail");

        }
    }

    @Override
    public Response<Boolean> updateOuterAttribute(OuterCategoryAttributeUpdateRequest request) {

        try {

            return Response.ok(categoryAttributeService.updateOuterAttribute(request));

        } catch (ServiceException e) {

            log.warn(e.getMessage());
            return Response.fail(e.getMessage());

        } catch (Exception e) {

            log.error(e.getMessage(), e);
            return Response.fail("outerCategoryAttribute.update.fail");

        }
    }

    @Override
    public Response<Boolean> deleteOuterAttribute(OuterCategoryAttributeDeleteRequest request) {

        try {

            return Response.ok(categoryAttributeService.deleteOuterAttribute(request));

        } catch (ServiceException e) {

            log.warn(e.getMessage());
            return Response.fail(e.getMessage());

        } catch (Exception e) {

            log.error(e.getMessage(), e);
            return Response.fail("outerCategoryAttribute.delete.fail");

        }
    }
}
