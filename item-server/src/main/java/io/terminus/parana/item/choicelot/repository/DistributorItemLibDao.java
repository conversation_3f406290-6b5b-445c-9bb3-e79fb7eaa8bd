package io.terminus.parana.item.choicelot.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.choicelot.model.DistributorItemLibModel;
import io.terminus.parana.item.choicelot.model.bo.ChoiceLotLibItemTotalBO;
import io.terminus.parana.item.item.canal.ESMQInjection;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
public class DistributorItemLibDao extends MyBatisDao<DistributorItemLibModel> {

	@Override
	public Boolean create(DistributorItemLibModel model) {
		Boolean isOk = super.create(model);
		if (isOk) {
			ESMQInjection.entryDistributorItemLib(model.getDistributorId(), model.getItemId());
		}
		return isOk;
	}

	@Override
	public Integer creates(List<DistributorItemLibModel> distributorItemLibModels) {
		Integer result = super.creates(distributorItemLibModels);
		if (result > 0) {
			updateES(distributorItemLibModels);
		}
		return result;
	}

	@Override
	public Boolean update(DistributorItemLibModel model) {
		boolean isOk = this.sqlSession.update(sqlId("update"), model) > 0;
		if (isOk) {
			ESMQInjection.entryDistributorItemLib(model.getDistributorId(), model.getItemId());
		}
		return isOk;
	}

	public Boolean justUpdate(DistributorItemLibModel model) {
		return this.sqlSession.update(sqlId("update"), model) > 0;
	}

	/**
	 *	查看渠道商商品库
	 */
	public DistributorItemLibModel queryOne(DistributorItemLibModel model){
		return this.sqlSession.selectOne(sqlId("queryOne"), model);
	}
	/**
	 *	列表查询渠道商商品库
	 */
	public List<DistributorItemLibModel> listByModel(DistributorItemLibModel model){
		return this.list(model);
	}
	/**
	 *	分页查询渠道商商品库
	 */
	public Paging<DistributorItemLibModel> page(Map<String, Object> params, Integer offset, Integer limit){
		return this.paging(offset, limit, params);
	}

	/**
	 *	渠道商商品库移除
	 */
	public Boolean delByBatch(List<DistributorItemLibModel> modelList){
		Map<String,Object> maps = Maps.newHashMap();
		for(DistributorItemLibModel model : modelList){
			maps.put("distributorId", model.getDistributorId());
			maps.put("choiceLotLibId", model.getChoiceLotLibId());
			maps.put("updatedBy", model.getUpdatedBy());
			maps.put("itemId", model.getItemId());
			this.sqlSession.update(sqlId("delByBatch"), maps);
		}
		//更新es
		updateES(modelList);
		return Boolean.TRUE;
	}

	/**
	 *	平台/运营选品移除
	 */
	public Boolean adminOrOperatorDel(List<DistributorItemLibModel> models) {
		Map<String, Object> params = Maps.newHashMap();
		params.put("distributorIds", models.stream().map(DistributorItemLibModel::getDistributorId).collect(Collectors.toList()));
		params.put("itemIds", models.stream().map(DistributorItemLibModel::getItemId).collect(Collectors.toList()));
		params.put("ids", models.stream().map(DistributorItemLibModel::getId).collect(Collectors.toList()));
		boolean isOk = this.sqlSession.update(sqlId("delByBatch"), params) > 0;
		if (isOk) {
			// 分发更新ES
			updateES(models);
		}
		return isOk;
	}

	/**
	 * 统计渠道商商品数量
	 * @param distributorIds
	 * @return
	 */
	public List<ChoiceLotLibItemTotalBO> getItemCount(List<Long> distributorIds){
		return this.sqlSession.selectList(sqlId("getItemCount"),  ImmutableMap.of("distributorIds", distributorIds));
	}

	/**
	 * 统计渠道商选品库数量
	 * @param distributorId
	 * @return
	 */
	public ChoiceLotLibItemTotalBO getItemCountByChoiceId(Long distributorId, Long choiceLotLibId, Integer source){
		return this.sqlSession.selectOne(sqlId("getItemCount"),  ImmutableMap.of("distributorId", distributorId, "choiceLotLibId", choiceLotLibId, "source", source));
	}

	/**
	 *	渠道商商品库
	 */
	public List<DistributorItemLibModel>  getDistributorItemList(Long distributorId, Set<Long> itemIds, Long choiceLotLibId, Integer source){
		Map<String,Object> maps = new HashMap<>(16);
		maps.put("distributorId",distributorId);
		maps.put("itemIds",itemIds);
		maps.put("source",source);
		maps.put("choiceLotLibId",choiceLotLibId);
		return this.sqlSession.selectList(sqlId("getDistributorItemList"), maps);
	}


	/**
	 * 获取每个渠道商的每个选品库已加入我的商品库的数量
	 * @return
	 */
	public List<ChoiceLotLibItemTotalBO> getCommodityNum(List<Long> list , Long distributorId){
		Map<String,Object> maps = new HashMap<>(2);
		maps.put("list",list);
		maps.put("distributorId",distributorId);
		return this.sqlSession.selectList(sqlId("getCommodityNum"),  maps);
	}

	public List<DistributorItemLibModel> listByMap(Map<String, Object> param) {
		return this.sqlSession.selectList(sqlId("listByMap"),  param);
	}

	public List<DistributorItemLibModel> findByOperatorIdsAndItemIds(List<Long> choiceOperatorIds, List<Long> choiceItemIds) {
		return this.getSqlSession().selectList(sqlId("findByOperatorIdsAndItemIds"),
				ImmutableMap.of("operatorIds", choiceOperatorIds, "itemIds", choiceItemIds));
	}


	/**
	 *	渠道商商品库
	 */
	public DistributorItemLibModel  findByDistributorIdAndItemId(Long distributorId, Long itemId){
		Map<String,Object> maps = new HashMap<>(16);
		maps.put("distributorId",distributorId);
		maps.put("itemId",itemId);
		return this.sqlSession.selectOne(sqlId("findByDistributorIdAndItemId"), maps);
	}

	/**
	 *	渠道商商品库
	 */
	public List<DistributorItemLibModel>  findCanalByDistributorIdAndItemId(Long distributorId, Long itemId){
		Map<String,Object> maps = new HashMap<>(16);
		maps.put("distributorId",distributorId);
		maps.put("itemId",itemId);
		return this.sqlSession.selectList(sqlId("findCanalByDistributorIdAndItemId"), maps);
	}

	/**
	 *	渠道商商品库
	 */
	public List<DistributorItemLibModel>  findCanalByDistributorIdsAndItemIds(List<Long> distributorIds, List<Long> itemIds){
		Map<String,Object> maps = new HashMap<>(16);
		maps.put("distributorIds",distributorIds);
		maps.put("itemIds",itemIds);
		return this.sqlSession.selectList(sqlId("findCanalByDistributorIdsAndItemIds"), maps);
	}

	/**
	 *	渠道商商品库
	 */
	public List<DistributorItemLibModel>  findByDistributorIdAndItemIds(Long distributorId, List<Long> itemIds){
		Map<String,Object> maps = new HashMap<>(16);
		maps.put("distributorId",distributorId);
		maps.put("itemIds",itemIds);
		return this.sqlSession.selectList(sqlId("findByDistributorIdAndItemIds"), maps);
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean updates(List<DistributorItemLibModel> models) {
		for (DistributorItemLibModel model : models) {
			this.sqlSession.update(sqlId("update"), model);
		}
		updateES(models);
		return Boolean.TRUE;
	}

	private void updateES(List<DistributorItemLibModel> modelList) {
		if (CollectionUtil.isEmpty(modelList)) {
			return;
		}
		//根据渠道商id分组发送更新ES mq
		Map<Long, List<DistributorItemLibModel>> map = modelList.stream().collect(Collectors.groupingBy(DistributorItemLibModel::getDistributorId));
		for (Map.Entry<Long, List<DistributorItemLibModel>> entry : map.entrySet()) {
			List<Long> itemIds = entry.getValue().stream().map(DistributorItemLibModel::getItemId).collect(Collectors.toList());
			if (!CollectionUtil.isEmpty(itemIds)) {
				ESMQInjection.entryBatchDistributorItemLib(entry.getKey(), itemIds);
			}
		}
	}

	public List<DistributorItemLibModel> findItemByItemIdsAndDistributorIds(List<Long> itemIds, Set<Long> distributorIds) {
		Map<String, Object> params = new HashMap<>();
		params.put("itemIds", itemIds);
		params.put("distributorIds", distributorIds);
		return getSqlSession().selectList(sqlId("findItemByItemIdsAndDistributorIds"), params);
	}
}
