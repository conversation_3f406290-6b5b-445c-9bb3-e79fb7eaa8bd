package io.terminus.parana.item.item.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
public class VendorItemChannlSkuRelationModel implements Serializable{

	/**
	 * 主键Id
	 */
	private Long id;
	/**
	 * vendor_item_channl_relation_id
	 */
	private Long vendorItemChannlRelationId;
	/**
	 * 渠道id(分片字段)
	 */
	private Long channelId;
	/**
	 * 规格id
	 */
	private Long skuId;
	private Long key;
	/**
	 * 渠道价格
	 */
	private Long channelPrice;
	/**
	 * 移除:0 未移除:1
	 */
	private Integer isDelete;
	/**
	 * 最高渠道价格
	 */
	private Long maxChannelPrice;
	/**
	 * 最低渠道价格
	 */
	private Long minChannelPrice;
	/**
	 * 创建人员
	 */
	private String createdBy;
	/**
	 * 创建时间
	 */
	private java.util.Date createdAt;
	/**
	 * 修改人员
	 */
	private String updatedBy;
	/**
	 * 修改时间
	 */
	private java.util.Date updatedAt;

	/**
	 * skuid集合
	 */
	private Set<Long> skuIds;
}
