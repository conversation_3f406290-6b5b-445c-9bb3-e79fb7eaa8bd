package io.terminus.parana.item.channel.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.channel.bo.ChannelPagingCriteria;
import io.terminus.parana.item.channel.dao.ChannelDao;
import io.terminus.parana.item.channel.enums.ChannelStatusType;
import io.terminus.parana.item.channel.extension.ChannelReadExtension;
import io.terminus.parana.item.channel.model.Channel;
import io.terminus.parana.item.common.extension.AbstractNormalExtensionDeal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.URL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChannelReadDomainService extends AbstractNormalExtensionDeal {

    @Autowired(required = false)
    private ChannelReadExtension channelReadExtension;

    private final ChannelDao channelDao;

    public Channel findById(Long id) {
        try {
            return channelDao.findById(id);
        } catch (Exception e) {
            log.error("fail to find channel by id: {}, cause: {}", id, printErrorStack(e));
            throw new ServiceException("channel.find.fail");
        }

    }

    public Boolean isValid(Long id) {
        try {
            Channel channel = channelDao.findById(id);
            Date now = new Date();

            return now.before(channel.getLoseEffectAt()) && now.after(channel.getTakeEffectAt());
        } catch (Exception e) {
            log.error("fail to check if channel is valid by id: {}, cause: {}", id, printErrorStack(e));
            throw new ServiceException("channel.check.valid.fail");
        }
    }

    public List<Channel> listAll() {
        try {
            List<Channel> channelList = channelDao.listAll();

            if (!CollectionUtils.isEmpty(channelList) && channelReadExtension != null) {
                invokeExtension(channelReadExtension::process, channelList);
            }

            return channelList;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to list all channels, cause: {}", printErrorStack(e));
            throw new ServiceException("channel.list.fail");
        }
    }

    public Paging<Channel> paging(ChannelPagingCriteria criteria) {
        try {
            return channelDao.paging(criteria);
        } catch (Exception e) {
            log.error("fail to paging channel with: {}, cause: {}", criteria, printErrorStack(e));
            throw new ServiceException("channel.paging.fail");
        }
    }

    public Boolean verifyToken(String link, String token) {
        try {
            Channel channel = channelDao.findByToken(token);

            // 渠道是否存在
            if (channel == null) {
                return false;
            }

            // 渠道是否关闭
            if (channel.getStatus() == ChannelStatusType.CLOSE.getValue()) {
                return false;
            }

            // 渠道的生效期是否正确
            if (!isChannelTimeValid(channel)) {
                return false;
            }

            URL source = URL.valueOf(channel.getSourceLink());
            URL target = URL.valueOf(link);

            // 判断域名
            if (source.getHost() != null && !source.getHost().equals(target.getHost())) {
                return false;
            }

            // 判断路径
            if (source.getPath() != null && !source.getPath().equals(target.getPath())) {
                return false;
            }

            return true;
        } catch (IllegalStateException e) {
            log.error("fail to parse url: {}, cause: {}", link, printErrorStack(e));
            throw new ServiceException("channel.invalid.url");
        } catch (Exception e) {
            log.error("fail to verify channel token by link: {}, token: {}, cause: {}", link, token, printErrorStack(e));
            throw new ServiceException("channel.verify.fail");
        }
    }

    public Channel findByToken(String token, boolean excludeInvalid) {
        try {
            Channel channel = channelDao.findByToken(token);

            if (channel != null && excludeInvalid && !isChannelTimeValid(channel)) {
                return null;
            }

            return channel;
        } catch (Exception e) {
            log.error("fail to find channel by token: {}, cause: {}", token, printErrorStack(e));
            throw new ServiceException("fail.to.find.item");
        }
    }

    /**
     * 需要使用Date来完成时区转换
     *
     * @param channel 渠道信息
     * @return 渠道是否有效
     */
    public static boolean isChannelTimeValid(Channel channel) {
        Date now = new Date();
        return now.getTime() < channel.getLoseEffectAt().getTime() + DAY_MILLION_SECONDS
                && now.after(channel.getTakeEffectAt());
    }

    private final static Long DAY_MILLION_SECONDS = 24L * 60 * 60 * 1000;

    /**
     * 通过pid查询下级channel
     *
     * @param pid
     * @return
     */
    public List<Channel> findByPid(Long pid) {
        try {
            List<Channel> channelList = channelDao.findByPid(pid);

            if (CollectionUtils.isEmpty(channelList)) {
                return Collections.emptyList();
            }
            return channelList;
        } catch (Exception e) {
            log.error("fail to find List<channel> by pid: {}, cause: {}", pid, printErrorStack(e));
            throw new ServiceException("fail.to.find.channel");
        }
    }

    public List<Channel> findByIds(Set<Long> ids) {
        try {
            List<Channel> channelList = channelDao.findByIds(new ArrayList<Long>(ids));
            if (channelList.isEmpty()) {
                return Collections.emptyList();
            }
            return channelList;
        } catch (Exception e) {
            log.error("fail to find List<channel> by ids: {}, cause: {}", ids, printErrorStack(e));
            throw new ServiceException("fail.to.find.channel");
        }
    }

    public List<Channel> findByPidAndLevel(Long pid, int level) {
        try {
            List<Channel> channelList = channelDao.findByPidAndLevel(pid, level);

            if (CollectionUtils.isEmpty(channelList)) {
                return Collections.emptyList();
            }
            return channelList;
        } catch (Exception e) {
            log.error("fail to find List<channel> by pid: {},lever:{}, cause: {}", pid, level, printErrorStack(e));
            throw new ServiceException("fail.to.find.channel");
        }
    }
}
