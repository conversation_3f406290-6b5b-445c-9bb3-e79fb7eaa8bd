package io.terminus.parana.item.favorites.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.terminus.parana.item.common.base.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * 收藏
 *
 * <AUTHOR>
 * @date 2018-12-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Favorites extends BaseModel implements Serializable {

    private static final long serialVersionUID = 3201683472359928223L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 收藏复合唯一键
     */
    private String uniqueKey;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 收藏目标id
     */
    private Long targetId;

    /**
     * 收藏目标类型
     */
    private Integer targetType;

    /**
     * 收藏目标子类型
     */
    private Integer targetSubType;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 扩展字段
     */
    private Map<String, String> extra;

    /**
     * 扩展字段JSON,存数据库
     */
    @JsonIgnore
    private String extraJson;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    private Long operatorId;

    public void setExtraJson(String extraJson) {
        this.extraJson = extraJson;
        this.extra = json2object(extraJson, MAP_OF_STRING, Collections::emptyMap, "");
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        this.extraJson = object2json(extra, "");
    }
}
