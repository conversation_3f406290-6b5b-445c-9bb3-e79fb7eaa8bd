package io.terminus.parana.item.utils;

import io.netty.util.internal.ThrowableUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Component
public class OKHttpUtil {

    public static Response post(String url, Map<String,Object> params) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        params.forEach((k,v)->{
            builder.addFormDataPart(k,v.toString());
        });
        RequestBody body = builder.build();
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("grantType", "sign")
//                .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .build();
        Response execute = null;
        try {
           execute = client.newCall(request).execute();
        } catch (IOException e) {
            log.error("请求失败: {}", ThrowableUtil.stackTraceToString(e));
            throw new RuntimeException(e.getMessage());
        }
        return execute;
    }

    public static Response get(String url, Map<String, Object> params) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        String param = "";
        StringBuffer sb = new StringBuffer("?");
        if (!params.isEmpty() && params.size() > 0){
            for (Map.Entry<String, Object> item : params.entrySet()){
                Object vaule = item.getValue();
                if (null != vaule){
                    sb.append("&").append(item.getKey()).append("=").append(item.getValue());
                }
            }
            param = sb.toString();
        }else {
            param = "";
        }
        url = url + param;
        log.info("get请求url: {}", url);
        Request request = new Request.Builder()
                .url(url)
                .get()
                .addHeader("grantType", "sign")
//                .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .build();
        Response execute = null;
        try {
            execute = client.newCall(request).execute();
        } catch (IOException e) {
            log.error("请求失败: {}", ThrowableUtil.stackTraceToString(e));
            throw new RuntimeException(e.getMessage());
        }
        return execute;

    }
}
