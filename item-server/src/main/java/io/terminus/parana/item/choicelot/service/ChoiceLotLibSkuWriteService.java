package io.terminus.parana.item.choicelot.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import io.terminus.parana.item.area.component.ItemAuditHelper;
import io.terminus.parana.item.area.model.ItemAudit;
import io.terminus.parana.item.choicelot.api.bean.param.ChoiceLotLibMarkupBean;
import io.terminus.parana.item.choicelot.model.ChoiceLotLib;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibSkuDao;
import io.terminus.parana.item.choicelot.utils.MarkupCalculateUtils;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.item.enums.ItemAuditStatusEnum;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.ItemPriceHistory;
import io.terminus.parana.item.item.model.ParanaItemPriceHistoryMainModel;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.repository.SkuDao;
import io.terminus.parana.item.item.service.ItemPriceHistoryWriteService;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.plugin.third.api.misc.api.ParanaThirdMessageWriteApi;
import io.terminus.parana.item.third.param.ThirdParanaThirdMessageBean;
import io.terminus.parana.item.third.param.ThirdParanaThirdMessageCreateRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChoiceLotLibSkuWriteService {

	private final ChoiceLotLibSkuDao choiceLotLibSkuDao;

	private final ItemPriceHistoryWriteService itemPriceHistoryWriteService;

	private final ParanaThirdMessageWriteApi paranaThirdMessageWriteApi;

	private final ChoiceLotLibReadDomainService choiceLotLibReadDomainService;

	private final ItemReadDomainService itemReadDomainService;
	private final SkuDao skuDao;
	private final IdGenerator idGenerator;
	private final ItemAuditHelper itemAuditHelper;



	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(List<ChoiceLotLibSkuModel> modelList, String updatedBy, List<ItemPriceHistory> itemPriceHistoryList) {
		if (CollectionUtils.isEmpty(modelList)) {
			log.info("ChoiceLotLibSkuWriteService.batchUpdate. modelList is empty");
			return Boolean.TRUE;
		}
		if (ObjectUtil.isNotEmpty(itemPriceHistoryList)) {
			itemPriceHistoryWriteService.creates(itemPriceHistoryList);
		}
		return choiceLotLibSkuDao.batchUpdate(modelList, updatedBy);
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(List<ChoiceLotLibSkuModel> modelList, String updatedBy){
		if(CollectionUtils.isEmpty(modelList)){
			log.info("ChoiceLotLibSkuWriteService.batchUpdate. modelList is empty");
			return Boolean.TRUE;
		}
		return choiceLotLibSkuDao.batchUpdate(modelList, updatedBy) ;
	}

	/**
	 * 价格变更
	 * @param itemPriceHistory
	 * @return
	 */
	@Transactional(isolation= Isolation.DEFAULT, propagation= Propagation.REQUIRED, rollbackFor = Exception.class)
	public Boolean updateBasePrice(ItemPriceHistory itemPriceHistory){
		log.info("ChoiceLotLibSkuWriteService.updateBasePrice param:{}", itemPriceHistory);
		if (null == itemPriceHistory.getSkuId() || null == itemPriceHistory.getOperatorId()) {
			log.error("ChoiceLotLibSkuWriteService.updateBasePrice param error skuId Or operatorId is null");
			return Boolean.FALSE;
		}
		List<ChoiceLotLib> choiceByOperatorId = choiceLotLibReadDomainService.getChoiceByOperatorId(itemPriceHistory.getOperatorId());
		if (CollectionUtils.isEmpty(choiceByOperatorId)) {
			log.error("ChoiceLotLibSkuWriteService.updateBasePrice  ChoiceLotLib is empty");
			return Boolean.TRUE;
		}
		Map<Long, ChoiceLotLib> choiceMap = choiceByOperatorId.stream().collect(Collectors.toMap(ChoiceLotLib::getId, Function.identity()));
		//获取规格信息
		Sku sku = skuDao.findById(itemPriceHistory.getSkuId(), itemPriceHistory.getTenantId());
		// 获取选品库Sku信息
		Map<String, Object> params = Maps.newHashMap();
		params.put("choiceLotLibIds", choiceMap.keySet());
		params.put("itemId", itemPriceHistory.getItemId());
		params.put("skuId", itemPriceHistory.getSkuId());
		List<ChoiceLotLibSkuModel> choiceLotLibSkuModelList = choiceLotLibSkuDao.listByWhere(params);
		if (CollectionUtils.isEmpty(choiceLotLibSkuModelList)) {
			log.info("ChoiceLotLibSkuWriteService.updateBasePrice not exits choice sku info. skuId:{}, choiceId:{}", itemPriceHistory.getSkuId(), itemPriceHistory.getChoiceLotLibId());
			itemPriceHistory.setChoiceLotLibId(0L);
			return itemPriceHistoryWriteService.create(itemPriceHistory);
		}
		choiceLotLibSkuModelList.forEach(v -> {
			ChoiceLotLib choiceLotLib = choiceMap.get(v.getChoiceLotLibId());
			itemPriceHistory.setChoiceLotLibId(v.getChoiceLotLibId());
			itemPriceHistory.setChoiceLotLibName(choiceLotLib.getName());
			itemPriceHistory.setPrate(sku.getName());
			choicePriceUpdate(itemPriceHistory, v);
            log.info("ChoiceLotLibSkuWriteService.updateBasePrice update data:{}", v);
            Boolean isSuccess = choiceLotLibSkuDao.updateModel(v);
            log.info("ChoiceLotLibSkuWriteService.updateBasePrice update result:{}", isSuccess);
            if(isSuccess){
                log.info("ChoiceLotLibSkuWriteService.updateBasePrice  itemPriceHistory update data:{}", itemPriceHistory);
                Boolean aBoolean = itemPriceHistoryWriteService.create(itemPriceHistory);
                log.info("ChoiceLotLibSkuWriteService.updateBasePrice  itemPriceHistory update result:{}", aBoolean);
                //添加消息
                addMessage(itemPriceHistory, v);
            }
		});
		return Boolean.TRUE;
	}

	public void choicePriceUpdate(ItemPriceHistory itemPriceHistory, ChoiceLotLibSkuModel model) {
		itemPriceHistory.setBeforePrice(model.getDistributorPrice());
		model.setBasePrice(itemPriceHistory.getAfterBasePrice());
		model.setOriginalPrice(itemPriceHistory.getAfterOriginalPrice());
		// 分销价=供货价*（1+加价率）
		Long distriPrice = MarkupCalculateUtils.getDistriPrice(itemPriceHistory.getAfterBasePrice(), model.getMarkup());

		// 加价率 = (分销价-供货价）/供货价*100%。
		model.setMarkup(MarkupCalculateUtils.getResellerRate(distriPrice, model.getBasePrice()));

		// 分销毛利=分销价-供货价
		model.setResellerGross(MarkupCalculateUtils.sub(distriPrice, model.getBasePrice()));

		// 分销毛利率=（分销价-供货价）/分销价*100%
		model.setResellerGrossRate(MarkupCalculateUtils.getProfitRate(distriPrice, model.getBasePrice()));
		// 预估毛利率=（建议零售价 - 采购价）/ 建议零售价 × 100%
		model.setPreResellerGrossRate(MarkupCalculateUtils.getProfitRate(model.getOriginalPrice(), distriPrice));
		model.setDistributorPrice(distriPrice);
		itemPriceHistory.setAfterPrice(distriPrice);
		model.setUpdatedBy(model.getUpdatedBy());
		if (model.getOperatorId().equals(1L)) {
			itemPriceHistory.setPlatformBeforeBasePrice(itemPriceHistory.getBeforePrice());
			itemPriceHistory.setPlatformAfterBasePrice(distriPrice);
		} else {
			itemPriceHistory.setUpstreamOperatorChoiceLotLibId(model.getChoiceLotLibId());
			itemPriceHistory.setUpstreamOperatorBeforeBasePrice(itemPriceHistory.getBeforePrice());
			itemPriceHistory.setUpstreamOperatorAfterBasePrice(distriPrice);
		}
	}

	@Async
	public void sendMessage(ThirdParanaThirdMessageCreateRequest thirdParanaThirdMessageCreateRequest) {
		paranaThirdMessageWriteApi.create(thirdParanaThirdMessageCreateRequest);
	}

	private void addMessage(ItemPriceHistory itemPriceHistory, ChoiceLotLibSkuModel choiceLotLibSkuModel) {
		ThirdParanaThirdMessageCreateRequest request = new ThirdParanaThirdMessageCreateRequest();
		request.setMessageType(7);
		request.setOperatorId(choiceLotLibSkuModel.getOperatorId());
		request.setRemark("选品库商品价格变更");
		ThirdParanaThirdMessageBean thirdParanaThirdMessageBean = new ThirdParanaThirdMessageBean();
		thirdParanaThirdMessageBean.setId(itemPriceHistory.getChoiceLotLibId());
		thirdParanaThirdMessageBean.setItemMessage(JSON.toJSONString(itemPriceHistory.getSkuId()));
		request.setBean(thirdParanaThirdMessageBean);
		paranaThirdMessageWriteApi.create(request);
	}


	public Boolean markupUpdateByChoiceId(ChoiceLotLib choiceLotLib, List<ChoiceLotLibMarkupBean> choiceLotMarkupList, String createOrUpdateName) {
//		List<ThirdParanaThirdMessageCreateRequest> list = new ArrayList<>();
		Date now = new Date();
		//审核创建集合
		List<ItemAudit> itemAuditCreateList = new ArrayList<>();
		long startTime = System.currentTimeMillis();
		log.info("ChoiceLotLibSkuWriteService.markupUpdateByChoiceId update data:{}, startTime:{}", choiceLotLib, startTime);

		int offset = 0;
		while (true) {
			List<ChoiceLotLibSkuModel> skuModelList = choiceLotLibSkuDao.page(choiceLotLib.getId(), offset, 300);
			if (CollectionUtil.isEmpty(skuModelList)) {
				break;
			} else {
				Set<Long> skuIdSet = skuModelList.stream().map(ChoiceLotLibSkuModel::getSkuId).collect(Collectors.toSet());
				List<Sku> skuList = skuDao.findByIdSet(skuIdSet, choiceLotLib.getTenantId());
				Set<Long> itemIdSet = skuList.stream().map(Sku::getItemId).collect(Collectors.toSet());
				List<Item> itemList = itemReadDomainService.findByIdSet(itemIdSet, 1, null, null);
				long skuMarkup;
				for (ChoiceLotLibSkuModel sku : skuModelList) {
					ItemAudit itemAudit = new ItemAudit();
					JSONObject exp = new JSONObject();
					skuMarkup = new Long(sku.getMarkup().toString());
					// 匹配最佳加价率
					ChoiceLotLibMarkupBean choiceLotLibMarkupBean = choiceLotMarkupList.stream().filter(g -> sku.getBasePrice().compareTo(g.getBasePrice()) > 0)
							.max(Comparator.comparing(ChoiceLotLibMarkupBean::getBasePrice)).orElse(null);
					long markup = Objects.isNull(choiceLotLibMarkupBean) ? 0L : choiceLotLibMarkupBean.getMarkup();
					//如果前后生效分销价一致则跳过当前循环
					if (markup == skuMarkup) {
						continue;
					}
					sku.setMarkup(markup);
					//分销价
					Long distriPrice = MarkupCalculateUtils.getDistriPrice(sku.getBasePrice(), markup);

                    if (distriPrice > sku.getDistributorPrice()) {
                        itemAudit.setPriceAdjustType(1);
                    } else {
                        itemAudit.setPriceAdjustType(0);
                    }

					exp.put(sku.getChoiceLotLibId() + "_" + sku.getSkuId(), markup);
					exp.put("choiceLotLibId", choiceLotLib.getId());
					itemAudit.setTenantId(choiceLotLib.getTenantId());
					//无论是运营选品库调价还是品台选品库调价 运营ID都为平台ID
					itemAudit.setOperatorId(sku.getOperatorId());
					itemAudit.setItemId(sku.getItemId());
					itemAudit.setSkuId(sku.getSkuId());
					itemAudit.setType("price");
					itemAudit.setCreatedBy(choiceLotLib.getUpdatedBy());
					itemAudit.setCreatedAt(now);
					itemAudit.setDelayTime(now);
					itemAudit.setInitiateOperatorId(sku.getOperatorId());
					itemAudit.setBeforeBasePrice(sku.getBasePrice());
					itemAudit.setBeforeOriginalPrice(sku.getOriginalPrice());
					itemAudit.setBasePrice(sku.getBasePrice());
					itemAudit.setOriginalPrice(sku.getOriginalPrice());
					itemAudit.setAuditBy(createOrUpdateName);
					skuList.stream().filter(e -> sku.getSkuId().equals(e.getId())).findFirst().ifPresent(e -> itemAudit.setVendorId(e.getShopId()));
					itemList.stream().filter(e -> sku.getItemId().equals(e.getId())).findFirst().ifPresent(e -> {
						itemAudit.setBrandId(e.getBrandId());
						itemAudit.setCategoryId(e.getCategoryId());
						itemAudit.setName(e.getName());
					});
					itemAudit.setSourceType(2);
					itemAudit.setExpJson(exp.toJSONString());
					if (choiceLotLib.getOperatorId().equals(1L)) {
						//平台选品库修改加价率 自动通过 待生效状态
						itemAudit.setStatus(ItemAuditStatusEnum.TO_BE_OFFECTIVE.getValue());
						itemAudit.setAuditPriceType(2);
						itemAudit.setPlatformBeforeBasePrice(sku.getDistributorPrice());
						itemAudit.setPlatformAfterBasePrice(distriPrice);
					} else {
						//运营选品库修改加价率
						//判断当前选品库是否供给平台
						itemAudit.setAuditPriceType(1);
						itemAudit.setUpstreamOperatorBeforeBasePrice(sku.getDistributorPrice());
						itemAudit.setUpstreamOperatorAfterBasePrice(distriPrice);
						//手动审核 待平台审核
						itemAudit.setStatus(ItemAuditStatusEnum.PLATFORM_REVIEW.getValue());
					}

					//判断是否为渠道选品库  是的话状态直接改为通过 且operatorId改为当前运营商
					if (choiceLotLib.getType().equals(0)) {
						itemAudit.setStatus(ItemAuditStatusEnum.ADOPT.getValue());
						itemAudit.setOperatorId(sku.getOperatorId());
					}

					itemAuditCreateList.add(itemAudit);

				}
			}
			offset += 300;
		}
		if (ObjectUtil.isEmpty(itemAuditCreateList)) {
			return Boolean.TRUE;
		}
		Boolean flag;
		log.info("修改选品库加价率审核数据:{}", itemAuditCreateList);
		if (choiceLotLib.getOperatorId().equals(1L)) {
			flag = itemAuditHelper.buildData("platform", itemAuditCreateList);
		} else {
			flag = itemAuditHelper.buildData("operator", itemAuditCreateList);
		}

		long endTime = System.currentTimeMillis();
		log.info("ChoiceLotLibSkuWriteService.markupUpdateByChoiceId update  offset:{}, startTime:{}", offset, endTime);
		return flag;
	}

	private void buildHistory(ChoiceLotLibSkuModel sku, Map<Long, List<ChoiceLotLibSkuModel>> platformChoiceLotLibSkuMap, ItemAudit itemAudit, List<Sku> skuList, List<Item> itemList, Date now, List<ItemPriceHistory> itemPriceHistoryCreateList, List<ChoiceLotLibSkuModel> choiceLotLibSkuUpdateList, List<ParanaItemPriceHistoryMainModel> itemPriceHistoryMainModelList) {
		//新增平台变价记录 并修改平台选品库的价格
		if (platformChoiceLotLibSkuMap.containsKey(sku.getSkuId())) {
			List<ChoiceLotLibSkuModel> choiceLotLibSkuModels = platformChoiceLotLibSkuMap.get(sku.getSkuId());
			for (ChoiceLotLibSkuModel choiceLotLibSkuModel : choiceLotLibSkuModels) {
				ItemPriceHistory history1 = buildHistory(null, choiceLotLibSkuModel, itemAudit, skuList, itemList);
				ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel1 = buildHistoryMainData(choiceLotLibSkuModel, itemAudit, now);
				paranaItemPriceHistoryMainModel1.setType(2);
				itemPriceHistoryCreateList.add(history1);
				choiceLotLibSkuUpdateList.add(choiceLotLibSkuModel);
				itemPriceHistoryMainModelList.add(paranaItemPriceHistoryMainModel1);
			}
		}
	}


	@NotNull
	private ParanaItemPriceHistoryMainModel buildHistoryMainData(ChoiceLotLibSkuModel sku, ItemAudit itemAudit, Date now) {
		ParanaItemPriceHistoryMainModel paranaItemPriceHistoryMainModel = new ParanaItemPriceHistoryMainModel();
		paranaItemPriceHistoryMainModel.setId(idGenerator.nextValue(ParanaItemPriceHistoryMainModel.class));
		paranaItemPriceHistoryMainModel.setItemId(sku.getItemId());
		paranaItemPriceHistoryMainModel.setSkuId(sku.getSkuId());
		paranaItemPriceHistoryMainModel.setTenantId(1);
		paranaItemPriceHistoryMainModel.setChoiceLotLibId(sku.getChoiceLotLibId());
		paranaItemPriceHistoryMainModel.setAuditId(itemAudit.getId());
		paranaItemPriceHistoryMainModel.setOperatorId(sku.getOperatorId());
		paranaItemPriceHistoryMainModel.setCreatedAt(now);
		paranaItemPriceHistoryMainModel.setUpdatedAt(now);
		return paranaItemPriceHistoryMainModel;
	}

	@NotNull
	private ItemPriceHistory buildHistory(ChoiceLotLib choiceLotLib, ChoiceLotLibSkuModel sku, ItemAudit itemAudit, List<Sku> skuList, List<Item> itemList) {
		ItemPriceHistory history = new ItemPriceHistory();
		history.setChoiceLotLibId(sku.getChoiceLotLibId());
		if (ObjectUtil.isNotEmpty(choiceLotLib)) {
			history.setChoiceLotLibName(choiceLotLib.getName());
			history.setTenantId(choiceLotLib.getTenantId());
			history.setCreatedBy(choiceLotLib.getUpdatedBy());
		} else {
			history.setTenantId(1);
		}

		history.setItemId(sku.getItemId());
		history.setSkuId(sku.getSkuId());
		history.setCreatedAt(new Date());
		history.setDelayTime(new Date());
		history.setAfterBasePrice(sku.getBasePrice());
		history.setAfterOriginalPrice(sku.getOriginalPrice());
		history.setBeforeOriginalPrice(sku.getOriginalPrice());
		history.setBeforeBasePrice(sku.getBasePrice());
		history.setBeforeOriginalPrice(sku.getOriginalPrice());
		history.setOperatorId(sku.getOperatorId());
		history.setType(1);
		history.setAuditId(itemAudit.getId());
		skuList.stream().filter(e -> sku.getSkuId().equals(e.getId())).findFirst().ifPresent(e -> history.setPrate(e.getName()));
		itemList.stream().filter(item -> history.getItemId().equals(item.getId())).findFirst().ifPresent(item -> history.setBrandName(item.getShopName()));
		choicePriceUpdate(history, sku);
		return history;
	}

	public Boolean batchCreate(List<ChoiceLotLibSkuModel> choiceLotLibSkuCreateList) {
		return choiceLotLibSkuDao.batchCreate(choiceLotLibSkuCreateList);
	}
}
