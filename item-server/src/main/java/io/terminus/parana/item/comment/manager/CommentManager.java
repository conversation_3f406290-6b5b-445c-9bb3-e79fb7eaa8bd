package io.terminus.parana.item.comment.manager;

import io.terminus.parana.item.comment.model.Comment;
import io.terminus.parana.item.comment.repository.CommentDao;
import io.terminus.parana.item.common.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-05-18
 */
@Component
@Slf4j
public class CommentManager {

    private final CommentDao commentDao;

    public CommentManager(CommentDao commentDao) {
        this.commentDao = commentDao;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void createChangeParent(Comment comment, Comment parent) {
        boolean isOk = commentDao.create(comment);
        Assert.isTrue(isOk, "comment.createAndBinding.fail");

        isOk = commentDao.updateParent(parent);
        Assert.isTrue(isOk, "parent.update.fail");
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateStatusChangeParent(Long id, Integer status, String updatedBy, Comment parent) {
        boolean isOk = commentDao.updateStatus(id, status, updatedBy);
        Assert.isTrue(isOk, "comment.update.status.fail");

        isOk = commentDao.updateParent(parent);
        Assert.isTrue(isOk, "parent.update.fail");

        return true;
    }
}