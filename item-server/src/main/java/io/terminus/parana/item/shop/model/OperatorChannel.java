package io.terminus.parana.item.shop.model;

import io.terminus.parana.item.common.base.BaseModel;
import lombok.Data;

import java.util.Date;

@Data
public class OperatorChannel extends BaseModel {

    private static final long serialVersionUID = 7261953261658730181L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 渠道类型 1-B2B商城 2-B2C商城 3-分销商城
     */
    private Integer channelType;

    /**
     * 区域运营id
     */
    private Long operatorId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道介绍
     */
    private String channelIntroduction;

    /**
     * 主图地址
     */
    private String mainImage;

    /**
     * 入驻费
     */
    private Long entryFee;

    /**
     * 已注册门店数量
     */
    private Long registeredShopNum;

    /**
     * 服务详情，富文本
     */
    private String serviceDetail;

    /**
     * 城市名
     */
    private String city;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 最后更新时间
     */
    private Date updatedAt;

}
