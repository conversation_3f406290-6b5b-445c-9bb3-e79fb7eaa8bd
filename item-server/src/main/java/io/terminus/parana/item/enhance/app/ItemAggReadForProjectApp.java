package io.terminus.parana.item.enhance.app;

import com.google.common.collect.Sets;
import io.terminus.parana.item.area.api.bean.response.AreaSkuInfo;
import io.terminus.parana.item.area.api.converter.AreaItemApiInfoConverter;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.category.api.bean.request.feature.tax.TaxQueryByCategoryRequest;
import io.terminus.parana.item.category.api.facade.feature.TaxFacade;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibDao;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibSkuDao;
import io.terminus.parana.item.common.base.JsonSupport;
import io.terminus.parana.item.common.filter.RequestContext;
import io.terminus.parana.item.common.utils.AssembleDataUtils;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.distributorskulib.service.DistributorSkuLibReadService;
import io.terminus.parana.item.enhance.api.bean.response.ItemAggQueryForTradeInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.item.api.converter.output.ItemApiInfoConverter;
import io.terminus.parana.item.item.constant.ItemExtraConstantKey;
import io.terminus.parana.item.item.model.Item;
import io.terminus.parana.item.item.model.Sku;
import io.terminus.parana.item.item.service.ItemReadDomainService;
import io.terminus.parana.item.item.service.SkuReadDomainService;
import io.terminus.parana.item.item.service.VendorItemChannlSkuRelationReadService;
import io.terminus.parana.item.partnership.service.VendorPartnershipReadDomainService;
import io.terminus.parana.item.plugin.third.api.inventory.api.InventoryReadApi;
import io.terminus.parana.item.shop.service.ShopReadDomainService;
import io.terminus.parana.item.tag.api.bean.request.TagQueryByTargetIdRequest;
import io.terminus.parana.item.tag.api.bean.request.base.IdTypePairParam;
import io.terminus.parana.item.tag.api.bean.response.TagBatchResultInfo;
import io.terminus.parana.item.tag.api.bean.response.TagResultInfo;
import io.terminus.parana.item.tag.api.facade.TagReadFacade;
import io.terminus.parana.item.tag.enums.TagTargetType;
import io.terminus.parana.item.tax.api.bean.response.TaxInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">Caily</a>
 * @date 2018-09-17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ItemAggReadForProjectApp implements JsonSupport {

    private final ItemReadDomainService itemReadDomainService;
    private final SkuReadDomainService skuReadDomainService;
    private final AreaItemReadDomainService areaItemReadDomainService;
    private final AreaSkuReadDomainService areaSkuReadDomainService;
    private final ItemApiInfoConverter itemApiInfoConverter;
    private final AreaItemApiInfoConverter areaItemApiInfoConverter;
    private final TagReadFacade tagReadFacade;
    private final TaxFacade taxFacade;
    private final VendorPartnershipReadDomainService vendorPartnershipReadDomainService;
    private final ShopReadDomainService shopReadDomainService;
    private final VendorItemChannlSkuRelationReadService vendorItemChannlSkuRelationReadService;
    private final ChoiceLotLibSkuDao choiceLotLibSkuDao;
    private final ChoiceLotLibDao choiceLotLibDao;
    private final DistributorSkuLibReadService distributorSkuLibReadService;
    private final InventoryReadApi inventoryReadApi;

    private void queryTag(List<ItemAggQueryForTradeInfo> agg, Set<Long> skuIdSet) {
        Set<Long> itemIdSet = AssembleDataUtils.list2set(agg, it -> it.getItemInfo().getId());
        TagBatchResultInfo tagBatchResultInfo = queryItemAndSkuTag(itemIdSet, skuIdSet);

        for (ItemAggQueryForTradeInfo info : agg) {
            Long itemId = info.getItemInfo().getId();
            Long skuId = info.getSkuInfo().getId();

            TagResultInfo tagResultInfo;

            tagResultInfo = tagBatchResultInfo.take(itemId, TagTargetType.TAG_TYPE_ITEM.getValue());


            TagResultInfo skuTag = tagBatchResultInfo.take(skuId, TagTargetType.TAG_TYPE_SKU.getValue());

            if (skuTag != null) {
                if (tagResultInfo == null) {
                    tagResultInfo = skuTag;
                } else {
                    tagResultInfo.append(skuTag);
                }
            }

            info.setTagResultInfo(tagResultInfo);
        }
    }

    public List<ItemAggQueryForTradeInfo> aggQueryForTrade(Long operatorId, Long userId, Set<String> outerSkuIdSet,
                                                           boolean withTag, boolean withItem,
                                                           Integer tenantId, String dimensionType,
                                                           String dimensionCode) {
        List<ItemAggQueryForTradeInfo> agg = new ArrayList<>(outerSkuIdSet.size());

        Set<Long> skuIdSet = Sets.newHashSet();
        // 读取商品基础信息
        Set<Long> categoryIdSet = readRichSku(operatorId, outerSkuIdSet, skuIdSet, tenantId, userId, agg, dimensionType, dimensionCode);

        // 处理打标信息
        if (withTag) {
            queryTag(agg, skuIdSet);
        }

        // 处理类目信息
        if (!CollectionUtils.isEmpty(categoryIdSet)) {
            queryTaxInfo(agg);
        }

        if (!withItem) {
            agg.forEach(it -> it.setItemInfo(null));
        }

        log.info("aggQueryForTrade.agg:{}", agg);
        return agg;
    }

    /**
     * 读取商品基础数据
     *
     * @param skuIdSet      skuId集合
     * @param tenantId      租户id
     * @param agg           商品聚合信息
     * @param dimensionType 维度类型
     * @param dimensionCode 维度编码
     * @return 类目id集合
     */
    private Set<Long> readRichSku(Long operatorId, Set<String> outerSkuIdSet, Set<Long> skuIdSet,
                                  Integer tenantId, Long userId, List<ItemAggQueryForTradeInfo> agg,
                                  String dimensionType, String dimensionCode) {
        //缓存中拿sku
        List<AreaSku> areaSkuList = areaSkuReadDomainService.findByOperatorIdAndOuterSkuIds(operatorId, outerSkuIdSet);
//        skuList = skuList.stream().filter(f -> f.getStatus() == -1).collect(Collectors.toList());
        Set<Long> itemIdSet = areaSkuList.stream().map(AreaSku::getItemId).collect(Collectors.toSet());
        skuIdSet.addAll(areaSkuList.stream().map(AreaSku::getSkuId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(itemIdSet)){
            return new HashSet<>();
        }
        //缓存中拿商品信息
        List<Item> itemList = itemReadDomainService.findByIdSet(itemIdSet, tenantId, dimensionType, dimensionCode);
        log.info("readRichSku itemList {}",itemList);
        Map<Long, Item> idIndexItemMap = itemList.stream().collect(Collectors.toMap(Item::getId, Function.identity()));

        Set<Long> categoryIdSet = new LinkedHashSet<>(itemList.size());

        List<Sku> skuList = skuReadDomainService.findByIdSet(skuIdSet, tenantId, null, null);

        Map<Long, AreaSku> areaSkuMap = areaSkuList.stream().collect(Collectors.toMap(AreaSku::getSkuId, Function.identity()));

        //查询某运营下的所有商品
        List<AreaItem> areaItemList = areaItemReadDomainService.findByOperatorIdAndItemIds(operatorId, itemIdSet);
        Map<Long, AreaItem> areaItemMap = areaItemList.stream().collect(Collectors.toMap(AreaItem::getItemId, Function.identity()));

        for (Sku sku : skuList) {
            Item item = idIndexItemMap.getOrDefault(sku.getItemId(), null);
            if (item == null) {
                log.warn("no item found with id: {}", sku.getItemId());
                continue;
            }

            Long categoryId = item.getCategoryId();
            log.info("readRichSku item {} categoryId {}",item,categoryId);
            categoryIdSet.add(categoryId);

            AreaSku areaSku = areaSkuMap.get(sku.getId());
            AreaItem areaItem = areaItemMap.get(sku.getItemId());

            if (areaItem == null || areaSku == null) {
                continue;
            }

            AreaSkuInfo areaSkuInfo = areaItemApiInfoConverter.domain2info(areaSku);
            SkuInfo skuInfo = itemApiInfoConverter.get(sku);
            skuInfo.setAreaSku(areaSkuInfo);

            ItemAggQueryForTradeInfo info = ItemAggQueryForTradeInfo.builder()
                    .itemInfo(itemApiInfoConverter.get(item))
                    .skuInfo(skuInfo)
                    .areaItemInfo(areaItemApiInfoConverter.domain2info(areaItem))
                    .areaSkuInfo(areaItemApiInfoConverter.domain2info(areaSku))
                    .backCategoryList(getBackCategoryList(item))
                    .build();

            agg.add(info);
        }

        return categoryIdSet;
    }

    private List<Long> getBackCategoryList(Item item) {
        if (item == null || item.getExtra() == null || !item.getExtra().containsKey(ItemExtraConstantKey.ITEM_CATEGORY_LIST_KEY)) {
            return null;
        }

        // 获得反序列化后的原始对象
        List<Long> categoryList = json2object(item.getExtra().get(ItemExtraConstantKey.ITEM_CATEGORY_LIST_KEY), LIST_OF_LONG, Collections::emptyList, "");

        // 转换为List<Long>类型
        return categoryList;
    }


    /**
     * 查询商品打标信息
     *
     * @param itemIdSet 所有id集合
     * @param skuIdSet  skuId集合
     * @return 打标信息
     */
    private TagBatchResultInfo queryItemAndSkuTag(Set<Long> itemIdSet, Set<Long> skuIdSet) {
        List<IdTypePairParam> pairParamList = new ArrayList<>(itemIdSet.size() + skuIdSet.size());

        List<IdTypePairParam> itemBoList = AssembleDataUtils.set2list(itemIdSet, id -> new IdTypePairParam(id, TagTargetType.TAG_TYPE_ITEM.getValue()));
        List<IdTypePairParam> skuBoList = AssembleDataUtils.set2list(skuIdSet, id -> new IdTypePairParam(id, TagTargetType.TAG_TYPE_SKU.getValue()));
        pairParamList.addAll(itemBoList);
        pairParamList.addAll(skuBoList);

        TagQueryByTargetIdRequest request = new TagQueryByTargetIdRequest();
        request.setPairParamList(pairParamList);

        return Assert.take(tagReadFacade.queryAllByTarget(request));
    }

    private void queryTaxInfo(List<ItemAggQueryForTradeInfo> infoList) {
        for (ItemAggQueryForTradeInfo info : infoList) {
            Long categoryId = info.getItemInfo().getCategoryId();

            TaxQueryByCategoryRequest request = new TaxQueryByCategoryRequest();
            request.setCategoryId(categoryId);
            request.setTenantId(RequestContext.getTenantId());
            io.terminus.parana.item.category.api.bean.response.feature.TaxInfo catTaxInfo =
                    Assert.take(taxFacade.queryByCategory(request));

            if (catTaxInfo == null) {
                log.warn("no tax category model found by categoryId: {}", categoryId);
                continue;
            }

            if (catTaxInfo.getTaxCode() == null || catTaxInfo.getTaxRate() == null) {
                log.warn("no tax category taxCode or taxRate found by categoryId: {}", categoryId);
            }

            TaxInfo taxInfo = new TaxInfo();
            taxInfo.setTaxCode(catTaxInfo.getTaxCode());
            taxInfo.setTaxRate(catTaxInfo.getTaxRate());

            info.setTaxInfo(taxInfo);

            info.getItemInfo().setCategoryName(catTaxInfo.getName());
            if (null != catTaxInfo.getExtra() && StringUtils.isNotBlank(catTaxInfo.getExtra().get("categoryNameJson"))) {
                String categoryNameJson = catTaxInfo.getExtra().get("categoryNameJson");
                Map<Long, String> categoryNamesMap = com.alibaba.fastjson.JSON.parseObject(categoryNameJson, Map.class);
                info.setBackCategoryMap(categoryNamesMap);
            }
        }
    }
}
