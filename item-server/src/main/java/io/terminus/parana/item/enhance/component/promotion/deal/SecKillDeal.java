package io.terminus.parana.item.enhance.component.promotion.deal;

import io.terminus.parana.item.third.info.ActivityMatchedLine;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2020-05-12
 */
@Component
public class SecKillDeal extends AbstractPromotionDeal {
    @Override
    public String getCode() {
        return "seckill";
    }

    @Override
    protected Long getPromotionStock(ActivityMatchedLine activityMatchedLine, Long skuId) {
        return super.getPromotionStock(activityMatchedLine, skuId);
    }
}
