package io.terminus.parana.item.favorites.manager;

import io.terminus.parana.item.favorites.extension.base.FavoritesExtension;
import io.terminus.parana.item.favorites.model.Favorites;
import io.terminus.parana.item.footprint.extension.base.FootprintExtension;
import io.terminus.parana.item.footprint.model.Footprint;
import io.terminus.parana.item.interaction.extension.base.AbstractInteractionExtensionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FavoritesExtensionManager extends AbstractInteractionExtensionManager<FavoritesExtension> {

    private FavoritesExtension getFavoritesExtension(List<Favorites> favoritesList) {
        if (CollectionUtils.isEmpty(favoritesList)) {
            return null;
        }

        Integer type = favoritesList.get(0).getTargetType();
        FavoritesExtension favoritesExtension = getExtensionByType(type);

        if (favoritesExtension == null) {
            log.info("favoritesExtension not found, type:{}", type);
        }
        return favoritesExtension;
    }

    public void beforeWriteExtension(List<Favorites> favoritesList) {
        FavoritesExtension favoritesExtension = getFavoritesExtension(favoritesList);
        if (favoritesExtension != null) {
            favoritesExtension.beforeWrite(favoritesList);
        }
    }

    public void afterQueryExtension(List<Favorites> favoritesList, Long referrerId, Long authId) {
        FavoritesExtension favoritesExtension = getFavoritesExtension(favoritesList);
        if (favoritesExtension != null) {
            favoritesExtension.afterQuery(favoritesList, referrerId, authId);
        }
    }

}
