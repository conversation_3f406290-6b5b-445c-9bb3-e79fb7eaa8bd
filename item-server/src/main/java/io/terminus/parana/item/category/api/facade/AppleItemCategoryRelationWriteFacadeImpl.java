package io.terminus.parana.item.category.api.facade;

import cn.hutool.core.util.ObjectUtil;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.parana.item.category.api.bean.request.AppleItemCategoryRelationRequest;
import io.terminus.parana.item.category.api.converter.AppleItemConverter;
import io.terminus.parana.item.category.model.AppleItemCategoryRelationModel;
import io.terminus.parana.item.category.repository.AppleItemCategoryRelationDao;
import io.terminus.parana.item.common.spi.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class AppleItemCategoryRelationWriteFacadeImpl implements AppleItemCategoryRelationWriteFacade{

    @Autowired
    private AppleItemCategoryRelationDao appleItemCategoryRelationDao;
    @Autowired
    private AppleItemConverter converter;
    @Autowired
    private IdGenerator idGenerator;

    @Override
    public Response<Boolean> create(AppleItemCategoryRelationRequest request) {
        try {
            log.info("AppleItemCategoryRelation.Write request:{}",request);
            Boolean isOk = false;
            // 查询绑定关系是否存在
            AppleItemCategoryRelationModel model = appleItemCategoryRelationDao.queryByAppleCategoryId(request.getAppleCategoryId());
            if (ObjectUtil.isEmpty(model)) {
                model = converter.requestToModel(request);
                Long id = idGenerator.nextValue(AppleItemCategoryRelationModel.class);
                model.setId(id);
                model.setTenantId(1L);
                isOk = appleItemCategoryRelationDao.create(model);
            }else {
                model.setBackCategoryId(request.getBackCategoryId());
                model.setBackCategoryName(request.getBackCategoryName());
                model.setUpdateTime(new Date());
                isOk = appleItemCategoryRelationDao.update(model);
            }
            return Response.ok(isOk);
        } catch (ServiceException e) {
            log.warn(e.getMessage());
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("appleItemCategoryRelation.create.fail");
        }
    }
}
