package io.terminus.parana.item.footprint.extension;

import io.terminus.parana.item.common.extension.ExtensionResult;
import io.terminus.parana.item.footprint.extension.base.AbstractShopFootprintExtension;
import io.terminus.parana.item.footprint.model.Footprint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Order
@Slf4j
@Service
public class DefaultShopFootprintExtension extends AbstractShopFootprintExtension {

    @Override
    public ExtensionResult afterQuery(List<Footprint> footprintList, Long referrerId, Long authId) {
        return ExtensionResult.ok();
    }

}
