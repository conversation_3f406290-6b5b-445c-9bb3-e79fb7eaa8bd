package io.terminus.parana.item.footprint.api.converter;

import io.terminus.parana.item.footprint.api.bean.request.param.FootprintParam;
import io.terminus.parana.item.footprint.model.Footprint;
import org.mapstruct.Mapper;

/**
 * dto到domain的转换
 *
 * <AUTHOR> xlt
 * @since : 2018-12-17
 */
@Mapper(componentModel = "spring")
public interface FootprintApiConverter {

    Footprint param2domain(FootprintParam footPrintParam);

}
