package io.terminus.parana.item.favorites.repository;

import com.google.common.collect.Maps;
import io.terminus.common.model.Paging;
import io.terminus.parana.item.common.base.AbstractMybatisDao;
import io.terminus.parana.item.favorites.model.UserCommonItemList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class UserCommonItemListDao extends AbstractMybatisDao<UserCommonItemList> {

    /**
     * 取消清单
     * @param params
     * @return
     */
    public Boolean deleteByItems(Map<String, Object> params) {
        getSqlSession().delete(sqlId("deleteByItems"), params);
        return true;
    }

    /**
     * 条件查询用户常用清单信息
     *
     * @param criteria
     * @return
     */
    public Paging<UserCommonItemList> queryCommonItemListPaging(Map<String, Object> criteria) {
        if (criteria == null) {
            criteria = Maps.newHashMap();
        }
        // get total count
        Long total = sqlSession.selectOne(sqlId("queryCommonItemListCount"), criteria);
        if (total <= 0) {
            return new Paging<>(0L, Collections.emptyList());
        }
        // get data
        List<UserCommonItemList> datas = sqlSession.selectList(sqlId("queryCommonItemListPaging"), criteria);
        return new Paging<>(total, datas);
    }

    /**
     * 条件查询用户常用清单列表
     * @param criteria
     * @return
     */
    public List<UserCommonItemList> queryList(Map<String, Object> criteria) {
        return getSqlSession().selectList(sqlId("queryList"), criteria);
    }

}
