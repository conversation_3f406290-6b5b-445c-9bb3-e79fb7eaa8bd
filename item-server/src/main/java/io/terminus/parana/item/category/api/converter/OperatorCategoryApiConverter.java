package io.terminus.parana.item.category.api.converter;

import io.terminus.parana.item.category.api.bean.request.param.OperatorCategoryParam;
import io.terminus.parana.item.category.model.OperatorCategory;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface OperatorCategoryApiConverter {

    OperatorCategory param2domain(OperatorCategoryParam param);

}
