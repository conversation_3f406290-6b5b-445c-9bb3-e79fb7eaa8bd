package io.terminus.parana.item.choicelot.repository;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.model.Paging;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.item.canal.ESMQInjection;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Repository
public class ChoiceLotLibSkuDao extends MyBatisDao<ChoiceLotLibSkuModel> {

    /**
     * 修改选品库SKU
     */
    public Boolean updateModel(ChoiceLotLibSkuModel model) {
        Boolean update = this.update(model);
        if(update){
            // 分组 区域运营商Id -> 选品库ID
            ESMQInjection.entryChoiceByItemId(model.getChoiceLotLibId(), model.getOperatorId(), model.getItemId(), null, model.getUpdatedBy());
        }
        return update;
    }

    /**
     *	解绑选品库SKU
     */
    public Boolean updateUnBindByItems(Map<String, Object> params) {
        return this.sqlSession.update(sqlId("updateUnBind"), params) > 0;
    }

    /**
     *	解绑选品库SKU
     */
    public Boolean updateUnBind(Map<String, Object> params) {
        return this.sqlSession.update(sqlId("updateUnBind"), params) > 0;
    }

    /**
     *	解绑选品库SKU
     */
    public Boolean deleteBySkuId(Map<String, Object> params) {
        return this.sqlSession.update(sqlId("deleteBySkuId"), params) > 0;
    }

    /**
     *	批量更新选品库SKU
     */
    public Boolean batchUpdate(List<ChoiceLotLibSkuModel> modelList, String updatedBy){
        modelList.forEach(v -> {
            Boolean update = this.update(v);
            if(update){
                ESMQInjection.entryChoiceByItemId(v.getChoiceLotLibId(), v.getOperatorId(), v.getItemId(), null, updatedBy);
            }
        });
        return Boolean.TRUE;
    }

    /**
     * 批量创建选品库SKU
     */
    public Boolean batchCreate(List<ChoiceLotLibSkuModel> modelList) {
        modelList.forEach(v -> {
            Boolean create = this.create(v);
            if (create) {
                ESMQInjection.entryChoiceByItemId(v.getChoiceLotLibId(), v.getOperatorId(), v.getItemId(), null, modelList.get(0).getCreatedBy());
            }
        });
        return Boolean.TRUE;
    }

    /**
     * 查看选品库SKU
     */
    public ChoiceLotLibSkuModel queryOne(ChoiceLotLibSkuModel model) {
        return this.sqlSession.selectOne(sqlId("queryOne"), model);
    }

    /**
     * 列表查询选品库SKU
     */
    public List<ChoiceLotLibSkuModel> listByChoiceLotLibId(Long operatorId, Long itemId, Long choiceLotLibId) {
        return this.getSqlSession().selectList(sqlId("listByChoiceLotLibId"),
                ImmutableMap.of("operatorId", operatorId, "itemId", itemId, "choiceLotLibId", choiceLotLibId));
    }

    /**
     * 分页查询选品库SKU
     */
    public Paging<ChoiceLotLibSkuModel> page(Map<String, Object> params, Integer offset, Integer limit) {
        return this.paging(offset, limit, params);
    }

    public List<ChoiceLotLibSkuModel> page(Long choiceId, Integer offset, Integer limit) {
        Map<String, Object> criteria = new HashMap<>();
        criteria.put("choiceLotLibId", choiceId);
        criteria.put("offset", offset);
        criteria.put("limit", limit);
        return this.sqlSession.selectList(this.sqlId("paging"), criteria);
    }

    public List<ChoiceLotLibSkuModel> listByWhere(Map<String, Object> param) {
        return this.sqlSession.selectList(sqlId("listByWhere"), param);
    }


    public List<ChoiceLotLibSkuModel> findChoiceLotLibSkuBySkuIds(Map<String, Object> param) {
        return this.sqlSession.selectList(sqlId("findChoiceLotLibSkuBySkuIds"), param);
    }

    public List<ChoiceLotLibSkuModel> findListBySkuIdSet(Map<String, Object> param) {
        return this.sqlSession.selectList(sqlId("findListBySkuIdSet"), param);
    }

    public List<ChoiceLotLibSkuModel> findChoiceLotLibSkuByOperatorIdsAndItemIds(List<Long> choiceOperatorIds, List<Long> choiceItemIds) {
        return this.getSqlSession().selectList(sqlId("findChoiceLotLibSkuByOperatorIdsAndItemIds"),
                ImmutableMap.of("operatorIds", choiceOperatorIds, "itemIds", choiceItemIds));
    }

    public List<ChoiceLotLibSkuModel> findByOperatorIdsChoiceLotLibIdsAndItemIds(Set<Long> operatorIds, Set<Long> choiceLotLibIds, Set<Long> itemIds) {
        Map<String, Object> criteria = new HashMap<>();
        criteria.put("operatorIds", operatorIds);
        criteria.put("choiceLotLibIds", choiceLotLibIds);
        criteria.put("itemIds", itemIds);
        return this.sqlSession.selectList(this.sqlId("findByOperatorIdsChoiceLotLibIdsAndItemIds"), criteria);
    }
}
