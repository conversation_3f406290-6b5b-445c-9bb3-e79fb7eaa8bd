package io.terminus.parana.item.distributorchoiceapply.api.facade;

import com.eascs.user.enterprise.api.bean.request.param.EnterpriseAuthenticationQueryParam;
import com.eascs.user.enterprise.api.bean.response.EnterpriseAuthenticationInfo;
import com.eascs.user.enterprise.api.facade.EnterpriseAuthenticationReadFacade;
import io.terminus.parana.common.web.context.RequestContext;
import io.terminus.parana.ipm.api.bean.request.area.AreaScopeTemplateQueryRequest;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.distributorchoiceapply.api.bean.request.DistributorChoiceApplyCloseRequest;
import io.terminus.parana.item.distributorchoiceapply.api.bean.request.DistributorChoiceApplyCreateRequest;
import io.terminus.parana.item.distributorchoiceapply.api.bean.request.DistributorChoiceApplyDeleteRequest;
import io.terminus.parana.item.distributorchoiceapply.api.bean.request.DistributorChoiceApplyUpdateRequest;
import io.terminus.parana.item.distributorchoiceapply.api.converter.DistributorChoiceApplyApiConverter;
import io.terminus.parana.item.distributorchoiceapply.model.DistributorChoiceApplyModel;
import io.terminus.parana.item.distributorchoiceapply.service.DistributorChoiceApplyWriteService;
import io.terminus.parana.item.distributorchoiceapplydetail.model.DistributorChoiceApplyDetailModel;
import io.terminus.parana.item.distributorchoiceapplydetail.service.DistributorChoiceApplyDetailWriteService;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.service.ShopReadDomainService;
import org.springframework.stereotype.Service;


import io.terminus.common.model.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class DistributorChoiceApplyWriteFacadeImpl implements DistributorChoiceApplyWriteFacade {

	private final DistributorChoiceApplyWriteService distributorChoiceApplyWriteService;

	private final DistributorChoiceApplyDetailWriteService distributorChoiceApplyDetailWriteService;
	private final DistributorChoiceApplyApiConverter distributorChoiceApplyApiConverter;
	private final EnterpriseAuthenticationReadFacade enterpriseAuthenticationReadFacade;
	private final ShopReadDomainService shopReadDomainService;

	private final IdGenerator idGenerator;

	@Override
	public Response<Boolean> create(DistributorChoiceApplyCreateRequest request) {
		//运营商id不是1 证明是渠道找运营申请 否则是运营找平台申请
		if(request.getOperatorId() != 1){
			EnterpriseAuthenticationQueryParam param = new EnterpriseAuthenticationQueryParam();
			param.setId(request.getDistributorId());
			EnterpriseAuthenticationInfo enterpriseAuthenticationInfo = Assert.take(enterpriseAuthenticationReadFacade.view(param));
			request.setDistributorName(enterpriseAuthenticationInfo.getCompanyName());
		}else{
			Shop shop = shopReadDomainService.findById(request.getDistributorId(), 1, null);
			if(shop != null){
				request.setDistributorName(shop.getName());
			}
		}
		request.setId(idGenerator.nextValue(DistributorChoiceApplyCreateRequest.class));
		request.setApplyQuantity(request.getIds().size());
		request.setProcessedQuantity(0);
		request.setStatus(2);
		DistributorChoiceApplyModel model = distributorChoiceApplyApiConverter.get(request);
		if(request.getOperatorId() == 1){
			model.setApplySource(1);
		}else{
			model.setApplySource(0);
		}
		Boolean isSuccess = distributorChoiceApplyWriteService.create(model);
		if(isSuccess) {
			List<DistributorChoiceApplyDetailModel> modelList = new ArrayList<>();
			Map<String,Object> params = new HashMap<>();
			params.put("ids",request.getIds());
			params.put("status",2);
			params.put("distributorChoiceApplyId",request.getId());
			params.put("tenantId", request.getTenantId());
			Integer updateBatch = distributorChoiceApplyDetailWriteService.updateBatch(params);
			if(updateBatch > 0) {
				return Response.ok(Boolean.TRUE);
			}else {
				return Response.fail("distributor.choice.apply.detail.update.fail");
			}
		}
		else {
			return Response.fail("创建渠道选品库申请表失败。");
		}
	}

	@Override
	public Response<Boolean> update(DistributorChoiceApplyUpdateRequest request) {
		Boolean isSuccess = distributorChoiceApplyWriteService.update(request.getId(),request.getChoiceLotLibId(),request.getOperatorId(),
				request.getServiceId(),request.getType(),request.getItemIds(),request.getUpdatedBy());
		if(isSuccess) {
			return Response.ok(isSuccess);
		}
		else {
			return Response.fail("修改渠道选品库申请表失败。");
		}
	}

	@Override
	public Response<Boolean> delete(DistributorChoiceApplyDeleteRequest request) {
		DistributorChoiceApplyModel model = distributorChoiceApplyApiConverter.get(request);
		Boolean isSuccess = distributorChoiceApplyWriteService.delete(model);
		if(isSuccess) {
			return Response.ok(isSuccess);
		}
		else {
			return Response.fail("删除渠道选品库申请表失败。");
		}
	}

	@Override
	public Response<Boolean> close(DistributorChoiceApplyCloseRequest request) {
		DistributorChoiceApplyModel model = new DistributorChoiceApplyModel();
		model.setId(request.getId());
		model.setUpdatedBy(request.getUpdatedBy());
		model.setStatus(3);
		Boolean isSuccess = distributorChoiceApplyWriteService.close(model);
		if(isSuccess) {
			return Response.ok(isSuccess);
		}
		else {
			return Response.fail("关闭渠道选品库申请表失败。");
		}
	}

}
