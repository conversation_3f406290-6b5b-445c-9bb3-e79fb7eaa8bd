package io.terminus.parana.item.choicelot.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.area.enums.AreaItemStatus;
import io.terminus.parana.item.area.model.AreaItem;
import io.terminus.parana.item.area.model.AreaSku;
import io.terminus.parana.item.area.repository.AreaItemDao;
import io.terminus.parana.item.area.repository.AreaSkuDao;
import io.terminus.parana.item.area.service.AreaItemReadDomainService;
import io.terminus.parana.item.area.service.AreaSkuReadDomainService;
import io.terminus.parana.item.choicelot.api.bean.param.DistributorItemLibBean;
import io.terminus.parana.item.choicelot.api.bean.param.DistributorItemLibUpdateBean;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceItemUpdateInfoRequest;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceLotLibItemBindRequest;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceLotLibItemBindV2Request;
import io.terminus.parana.item.choicelot.api.bean.request.ChoiceLotLibItemUnBindRequest;
import io.terminus.parana.item.choicelot.model.ChoiceLotLib;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibItemModel;
import io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel;
import io.terminus.parana.item.choicelot.model.DistributorItemLibModel;
import io.terminus.parana.item.choicelot.model.bo.AddChoiceLotItemToItemPoolBo;
import io.terminus.parana.item.choicelot.model.bo.DeleteChoiceLotItemToItemPoolBo;
import io.terminus.parana.item.choicelot.model.bo.UnBindChoiceLotItemBo;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibDao;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibItemDao;
import io.terminus.parana.item.choicelot.repository.ChoiceLotLibSkuDao;
import io.terminus.parana.item.choicelot.repository.DistributorItemLibDao;
import io.terminus.parana.item.choicelot.utils.MarkupCalculateUtils;
import io.terminus.parana.item.common.spi.IdGenerator;
import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.distributorskulib.dao.DistributorSkuLibDao;
import io.terminus.parana.item.plugin.third.api.misc.api.ParanaThirdMessageWriteApi;
import io.terminus.parana.item.shop.model.Shop;
import io.terminus.parana.item.shop.repository.ShopDao;
import io.terminus.parana.trade.buy.api.request.param.FirmMealParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChoiceLotLibItemWriteService {

    private final ChoiceLotLibItemDao choiceLotLibItemDao;

    private final ChoiceLotLibSkuDao choiceLotLibSkuDao;

    private final ChoiceLotLibSkuReadService choiceLotLibSkuReadService;

    private final ChoiceLotLibSkuWriteService choiceLotLibSkuWriteService;

    private final AreaItemReadDomainService areaItemReadDomainService;

    private final AreaSkuReadDomainService areaSkuReadDomainService;

    private final AreaItemDao areaItemDao;

    private final AreaSkuDao areaSkuDao;

    private final ChoiceLotLibReadDomainService choiceLotLibReadDomainService;

    private final ChoiceLotLibItemReadService choiceLotLibItemReadService;

    private final DistributorItemLibReadService distributorItemLibReadService;

    private final IdGenerator idGenerator;

    private final DistributorItemLibDao distributorItemLibDao;

    private final DistributorSkuLibDao distributorSkuLibDao;

    private final ShopDao shopDao;

    private final ChoiceLotLibDao choiceLotLibDao;

    private final ParanaThirdMessageWriteApi paranaThirdMessageWriteApi;


    /**
     * 创建选品库Item
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createBind(List<ChoiceLotLibSkuModel> choiceLotLibSkuList, List<ChoiceLotLibItemModel> choiceLotLibItemList,
                              ChoiceLotLibItemBindRequest request) {
        if(CollectionUtil.isNotEmpty(choiceLotLibSkuList) && CollectionUtil.isNotEmpty(choiceLotLibItemList)){
            request.setItemIds(choiceLotLibSkuList.stream().map(ChoiceLotLibSkuModel::getItemId).distinct().collect(Collectors.toList()));
            //查看选品库sku是否存在
            Map<String, Object> param = Maps.newHashMap();
            param.put("choiceLotLibIds",choiceLotLibSkuList.stream().map(ChoiceLotLibSkuModel::getChoiceLotLibId).collect(Collectors.toSet()));
            param.put("skuIds",choiceLotLibSkuList.stream().map(ChoiceLotLibSkuModel::getSkuId).collect(Collectors.toSet()));
            List<ChoiceLotLibSkuModel> choiceSkuList = choiceLotLibSkuDao.findChoiceLotLibSkuBySkuIds(param);
            Map<String,ChoiceLotLibSkuModel> map = Maps.newHashMap();
            if(CollectionUtil.isNotEmpty(choiceSkuList)){
                //key为选品库id和skuid
                for (ChoiceLotLibSkuModel model : choiceSkuList) {
                    String key = model.getChoiceLotLibId() + "," + model.getSkuId();
                    map.putIfAbsent(key, model);
                }
            }
            List<ChoiceLotLibSkuModel> newList = Lists.newArrayList();
            for (ChoiceLotLibSkuModel model : choiceLotLibSkuList) {
                String key = model.getChoiceLotLibId() + "," + model.getSkuId();
                //如过不存在当前选品库下的sku  则可以新增
                ChoiceLotLibSkuModel sku = map.get(key);
                if(sku == null){
                    newList.add(model);
                }
            }
            if(newList.size() > 0){
                choiceLotLibSkuDao.creates(newList);
            }
            areaItemDao.updateItemChoiceLotIds(request.getOperatorId(), request.getChoiceLotLibId(), request.getItemIds(), BigDecimal.ZERO.intValue());
            return choiceLotLibItemDao.createModel(choiceLotLibItemList, request);
        }
        return true;
    }

    /**
     * 创建选品库ItemV2
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createBindV2(List<ChoiceLotLibSkuModel> choiceLotLibSkuList, List<ChoiceLotLibItemModel> choiceLotLibItemList,
                                ChoiceLotLibItemBindV2Request request) {
        if(CollectionUtil.isNotEmpty(choiceLotLibSkuList) && CollectionUtil.isNotEmpty(choiceLotLibItemList)){
            request.setItemIds(choiceLotLibSkuList.stream().map(ChoiceLotLibSkuModel::getItemId).distinct().collect(Collectors.toList()));
            //查看选品库sku是否存在
            Map<String, Object> param = Maps.newHashMap();
            param.put("choiceLotLibIds",choiceLotLibSkuList.stream().map(ChoiceLotLibSkuModel::getChoiceLotLibId).collect(Collectors.toSet()));
            param.put("skuIds",choiceLotLibSkuList.stream().map(ChoiceLotLibSkuModel::getSkuId).collect(Collectors.toSet()));
            List<ChoiceLotLibSkuModel> choiceSkuList = choiceLotLibSkuDao.findChoiceLotLibSkuBySkuIds(param);
            Map<String,ChoiceLotLibSkuModel> map = Maps.newHashMap();
            if(CollectionUtil.isNotEmpty(choiceSkuList)){
                //key为选品库id和skuid
                for (ChoiceLotLibSkuModel model : choiceSkuList) {
                    String key = model.getChoiceLotLibId() + "," + model.getSkuId();
                    map.putIfAbsent(key, model);
                }
            }
            List<ChoiceLotLibSkuModel> newList = Lists.newArrayList();
            for (ChoiceLotLibSkuModel model : choiceLotLibSkuList) {
                String key = model.getChoiceLotLibId() + "," + model.getSkuId();
                //如过不存在当前选品库下的sku  则可以新增
                ChoiceLotLibSkuModel sku = map.get(key);
                if(sku == null){
                    newList.add(model);
                }
            }
            if(newList.size() > 0){
                choiceLotLibSkuDao.creates(newList);
            }
            areaItemDao.updateItemChoiceLotIds(request.getOperatorId(), request.getChoiceLotLibId(), request.getItemIds(), BigDecimal.ZERO.intValue());
            return choiceLotLibItemDao.createModel(choiceLotLibItemList, null);
        }
        return true;
    }

    /**
     * 解绑选品库Item
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUnBind(ChoiceLotLibItemUnBindRequest request) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("updatedBy", request.getUpdatedBy());
        params.put("itemIds", request.getItemIds());
        long delBatchNo = System.currentTimeMillis();
        params.put("delBatchNo", delBatchNo);
        params.put("choiceLotLibId", request.getChoiceLotLibId());
        params.put("operatorId", request.getOperatorId());
        choiceLotLibSkuDao.updateUnBindByItems(params);
        areaItemDao.updateItemChoiceLotIds(request.getOperatorId(), request.getChoiceLotLibId(), request.getItemIds(), BigDecimal.ONE.intValue());
        return choiceLotLibItemDao.updateUnBindByItems(params, request, delBatchNo);
    }


    /**
     * 平台/运营解绑选品库商品
     * @param choiceLotLibId 选品库id
     * @param itemIds 解绑的商品id集合
     * @param operatorId 区运营id
     * @param userName 操作人
     * @param constraintUnBind 是否强制解绑 0-否 1-是
     */
    public Boolean unBindChoiceLotLibItem(Long choiceLotLibId,List<Long> itemIds,Long operatorId,String userName,Integer constraintUnBind) {
        //数据校验
        UnBindChoiceLotItemBo bo = unBindChoiceLotItemVerify(choiceLotLibId, itemIds, operatorId,constraintUnBind);
        log.info("解绑校验结果： {}", JSON.toJSONString(bo));
        bo.setUserName(userName);
        //数据组装
        unBindChoiceLotItemPackage(bo);
        log.info("解绑数据组装结果： {}",JSON.toJSONString(bo));
        //操作DB保存
        Boolean isOk = unBindChoiceLotItemSave(bo);
        if(isOk){
            paranaThirdMessageWriteApi.removeMessageCreate(bo.getDeleteChoiceLotLibItemList());
        }
        return isOk;
    }


    /**
     * 平台/运营选品库解绑校验
     * 如果是平台解绑 需要校验平台选品库是否被运营商加入到商品池  且运营商是否将商品加入到选品库并被渠道加入到商品库
     * 如果是运营解绑 如果选品库归属是渠道，需要校验是否被渠道加入商品池  如果是归属平台 需要校验是否被平台加入商品池 且是否被平台的下游运营加入到商品池 且是否被平台的下游运营的渠道加入到商品库
     * @param choiceLotLibId
     * @param itemIds
     * @param operatorId
     * @param constraintUnBind
     * @return
     */
    public UnBindChoiceLotItemBo unBindChoiceLotItemVerify(Long choiceLotLibId,List<Long> itemIds,Long operatorId,Integer constraintUnBind){
        UnBindChoiceLotItemBo bo = new UnBindChoiceLotItemBo();
        bo.setOperatorId(operatorId);
        ChoiceLotLib choiceLotLib = choiceLotLibDao.getById(choiceLotLibId,operatorId);
        if(choiceLotLib == null){
            throw new ServiceException("未查询到选品库信息！");
        }
        bo.setChoiceLotLib(choiceLotLib);

        Map<String, Object> criteriaMap = Maps.newHashMap();
        criteriaMap.put("choiceLotLibId", choiceLotLibId);
        criteriaMap.put("itemIds", itemIds);
        List<ChoiceLotLibItemModel> choiceLotLibItems = choiceLotLibItemDao.listFindByCriteria(criteriaMap);
        if(CollectionUtil.isEmpty(choiceLotLibItems)){
            throw new ServiceException("当前商品未加入选品库！" + itemIds);
        }else{
            Set<Long> ids = choiceLotLibItems.stream().map(ChoiceLotLibItemModel::getItemId).collect(Collectors.toSet());
            List<Long> existenceIds = itemIds.stream().filter(f -> !ids.contains(f)).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(existenceIds)) {
                throw new ServiceException("当前商品未加入选品库！" + existenceIds);
            }
        }

        //默认不强制解绑
        if(constraintUnBind == null){
            constraintUnBind = 0;
        }

        Set<Long> distributorIds = Sets.newHashSet();
        //如果选品库归属是渠道  是下游运营解绑选品库 只校验渠道
        if(choiceLotLib.getType() == 0){
            //如果不是强制解绑
            if(constraintUnBind == 0){
                //获得运营选品库已经被渠道加入商品库的
                List<ChoiceLotLibItemModel> list = choiceLotLibItems.stream().filter(f -> f.getOperatorId() != 1 && !StringUtils.isEmpty(f.getDistributorIds())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(list)){
                    throw new ServiceException("当前商品已经被渠道商绑定！" + list.stream().map(ChoiceLotLibItemModel::getItemId).distinct().collect(Collectors.toList()));
                }
            }
            bo.setDownChoiceLotLibItemList(choiceLotLibItems);
            criteriaMap.put("choiceLotLibIds",choiceLotLibItems.stream().map(ChoiceLotLibItemModel::getChoiceLotLibId).collect(Collectors.toList()));
            criteriaMap.put("itemIds",choiceLotLibItems.stream().map(ChoiceLotLibItemModel::getItemId).collect(Collectors.toList()));
            List<ChoiceLotLibSkuModel> choiceLotLibSkuList = choiceLotLibSkuDao.listByWhere(criteriaMap);
            bo.setDownChoiceLotLibSkuList(choiceLotLibSkuList);

            List<AreaItem> areaItemList = areaItemReadDomainService.findByOperatorIdAndItemIds(operatorId, new HashSet<>(itemIds));
            bo.setDownAreaItemList(areaItemList);

            for (ChoiceLotLibItemModel choiceLotLibItemModel : choiceLotLibItems) {
                if(!StringUtils.isEmpty(choiceLotLibItemModel.getDistributorIds())){
                    distributorIds.addAll(Arrays.stream(choiceLotLibItemModel.getDistributorIds().replace("#", "").
                            split(",")).map(Long::parseLong).collect(Collectors.toSet()));
                }
            }
            if(CollectionUtil.isNotEmpty(distributorIds) && CollectionUtil.isNotEmpty(itemIds)){
                //获取渠道绑定选品库商品信息
                Map<String, Object> distributorParam = Maps.newHashMap();
                distributorParam.put("itemIds",itemIds);
                distributorParam.put("distributorIds",distributorIds);
                distributorParam.put("choiceLotLibId",choiceLotLibId);
                List<DistributorItemLibModel> distributorItemLibList = distributorItemLibDao.listByMap(distributorParam);
                bo.setDeleteDistributorItemLibList(distributorItemLibList);
            }
        }else{
            Set<Long> choiceIds = Sets.newHashSet();
            //获取所有运营商
            List<Shop> shopList = shopDao.findByType(2);
            if(CollectionUtil.isNotEmpty(shopList)){
                Set<Long> operatorIds = shopList.stream().map(Shop::getId).collect(Collectors.toSet());
                if(CollectionUtil.isNotEmpty(operatorIds)){
                    //根据商品id集合  运营商id集合查询区域商品信息
                    List<AreaItem> areaItemList = areaItemReadDomainService.findByOperatorIdsAndItemIds(operatorIds, new HashSet<>(itemIds));
                    if(CollectionUtil.isNotEmpty(areaItemList)){
                        //如果不是强制解绑  需要校验商品是否被下游运营商加入商品池
                        if(constraintUnBind == 0){
                            //平台解绑
                            if(operatorId == 1){
                                //过滤在平台的商品 且 不是品牌商供品给运营的
                                List<AreaItem> list = areaItemList.stream().filter(f -> choiceLotLibId.equals(f.getSourceChoiceLotLibId())).collect(Collectors.toList());
                                if(CollectionUtil.isNotEmpty(list)){
                                    throw new ServiceException("当前商品已经被运营商绑定！" + areaItemList.stream().map(AreaItem::getItemId).distinct().collect(Collectors.toList()));
                                }
                            }else{
                                //运营解绑
                                List<AreaItem> list = areaItemList.stream().filter(f -> choiceLotLibId.equals(f.getSourceChoiceLotLibId())).collect(Collectors.toList());
                                if(CollectionUtil.isNotEmpty(list)){
                                    throw new ServiceException("当前商品已经被平台绑定！" + areaItemList.stream().map(AreaItem::getItemId).distinct().collect(Collectors.toList()));
                                }
                            }
                        }
                        List<AreaItem> list = areaItemList.stream().filter(f -> f.getOperatorId().equals(operatorId) || choiceLotLibId.equals(f.getSourceChoiceLotLibId())).collect(Collectors.toList());
                        bo.setDownAreaItemList(list);

                        distributorIds.addAll(list.stream().map(AreaItem::getOperatorId).collect(Collectors.toList()));

                        List<String> key = list.stream().map(m -> m.getOperatorId() + "_" + m.getItemId()).collect(Collectors.toList());
                        //查询区域sku信息
                        List<AreaSku> areaSkuList = areaSkuReadDomainService.findByOperatorIdsAndItemIds(list.stream().map(AreaItem::getOperatorId).collect(Collectors.toList())
                                ,list.stream().map(AreaItem::getItemId).collect(Collectors.toList()));
                        if(CollectionUtil.isNotEmpty(areaSkuList)){
                            areaSkuList = areaSkuList.stream().filter(f -> key.contains(f.getOperatorId() + "_" + f.getItemId())).collect(Collectors.toList());
                            bo.setDownAreaSkuList(areaSkuList);
                        }
                        //获取运营新建的选品库信息
                        Map<String, Object> choiceLotLbtMap = Maps.newHashMap();
                        choiceLotLbtMap.put("operatorIds",list.stream().map(AreaItem::getOperatorId).distinct().collect(Collectors.toList()));
                        List<ChoiceLotLib> choiceLotLibs = choiceLotLibDao.listInfoFilter(choiceLotLbtMap);
                        if(CollectionUtil.isNotEmpty(choiceLotLibs)){
                            List<Long> choiceLotLibIds = choiceLotLibs.stream().map(ChoiceLotLib::getId).collect(Collectors.toList());
                            //根据商品id和选品库 查询商品所在选品库的信息
                            Map<String, Object> choiceLotLibItemMap = Maps.newHashMap();
                            choiceLotLibItemMap.put("itemIds",itemIds);
                            choiceLotLibItemMap.put("choiceIdList",choiceLotLibIds);
                            List<ChoiceLotLibItemModel> choiceLotLibItemList = choiceLotLibItemDao.listFindByCriteria(choiceLotLibItemMap);
                            if(CollectionUtil.isNotEmpty(choiceLotLibItemList)){
                                List<ChoiceLotLibItemModel> choiceLotLibItemNewList;
                                //平台解绑
                                if(operatorId == 1){
                                    //过滤在平台的商品 且 不是品牌商供品给运营的
                                    choiceLotLibItemNewList = choiceLotLibItemList.stream().filter(f ->f.getChoiceLotLibId().equals(choiceLotLibId) || (!f.getOperatorId().equals(operatorId) && f.getSourceType() != 0)).collect(Collectors.toList());
                                }else{
                                    //运营解绑 只查询给平台的 且是运营供品平台的
                                    choiceLotLibItemNewList = choiceLotLibItemList.stream().filter(f ->f.getChoiceLotLibId().equals(choiceLotLibId) || (!f.getOperatorId().equals(operatorId) && f.getSourceType() == 2)).collect(Collectors.toList());
                                }
                                bo.setDownChoiceLotLibItemList(choiceLotLibItemNewList);
                                choiceIds = choiceLotLibItemNewList.stream().map(ChoiceLotLibItemModel::getChoiceLotLibId).collect(Collectors.toSet());
                                choiceLotLibItemMap.put("choiceLotLibIds", choiceIds);
                                choiceLotLibItemMap.put("itemIds",choiceLotLibItemNewList.stream().map(ChoiceLotLibItemModel::getItemId).collect(Collectors.toList()));
                                List<ChoiceLotLibSkuModel> choiceLotLibSkuList = choiceLotLibSkuDao.listByWhere(choiceLotLibItemMap);
                                if(CollectionUtil.isNotEmpty(choiceLotLibSkuList)){
                                    choiceLotLibSkuList = choiceLotLibSkuList.stream().filter(f -> key.contains(f.getOperatorId() + "_" + f.getItemId())).collect(Collectors.toList());
                                    bo.setDownChoiceLotLibSkuList(choiceLotLibSkuList);
                                }
                                for (ChoiceLotLibItemModel choiceLotLibItemModel : choiceLotLibItemNewList) {
                                    if(!StringUtils.isEmpty(choiceLotLibItemModel.getDistributorIds())){
                                        distributorIds.addAll(Arrays.stream(choiceLotLibItemModel.getDistributorIds().replace("#", "").
                                                split(",")).map(Long::parseLong).collect(Collectors.toSet()));
                                    }
                                }
                            }
                        }
                        if(CollectionUtil.isNotEmpty(distributorIds) && CollectionUtil.isNotEmpty(itemIds)){
                            //获取渠道绑定选品库商品信息
                            Map<String, Object> distributorParam = Maps.newHashMap();
                            distributorParam.put("itemIds",itemIds);
                            distributorParam.put("distributorIds",distributorIds);
                            distributorParam.put("choiceLotLibIds", choiceIds);
                            List<DistributorItemLibModel> distributorItemLibList = distributorItemLibDao.listByMap(distributorParam);
                            if(CollectionUtil.isNotEmpty(distributorItemLibList)){
                                distributorItemLibList = distributorItemLibList.stream().filter(f -> key.contains(f.getOperatorId() + "_" + f.getItemId())).collect(Collectors.toList());
                                bo.setDeleteDistributorItemLibList(distributorItemLibList);
                            }
                        }
                    }
                }
            }
        }

        return bo;
    }

    /**
     * 组装数据
     * 如果是平台解绑 需要把平台的areaItem里面绑定的运营商选品库id移除  且删除运营商的areaItem areaSku信息  且删除运营商的选品库商品 选品库sku信息 且删除distributorItemLib表的数据
     * 如果是上游运营解绑 需要把上游运营商的areaItem里面绑定的平台选品库id移除 且删除平台的areaItem areaSku 选品库商品、选品库sku信息  且删除下游运营商的areaItem areaSku
     *      选品库商品 选品库sku信息 且删除distributorItemLib表的数据
     * 如果是下游运营商解绑 需要把下游运营商的areaItem里面绑定的选品库id移除  且删除选品库商品、选品库sku信息  且删除distributorItemLib表的数据
     * @param bo
     * @return
     */
    public void unBindChoiceLotItemPackage(UnBindChoiceLotItemBo bo){
        String userName = bo.getUserName();
        Long operatorId = bo.getOperatorId();
        ChoiceLotLib choiceLotLib = bo.getChoiceLotLib();
        List<AreaItem> downAreaItemList = bo.getDownAreaItemList();
        List<AreaSku> downAreaSkuList = bo.getDownAreaSkuList();
        List<ChoiceLotLibItemModel> downChoiceLotLibItemList = bo.getDownChoiceLotLibItemList();
        List<ChoiceLotLibSkuModel> downChoiceLotLibSkuList = bo.getDownChoiceLotLibSkuList();

        if(CollectionUtil.isNotEmpty(downAreaItemList)){
            for (AreaItem areaItem : downAreaItemList) {
                areaItem.setUpdatedBy(userName);
                //当前操作的运营商商品移除选品库 其他的改为删除状态
                if(areaItem.getOperatorId().equals(operatorId)){
                    if(!StringUtils.isEmpty(areaItem.getChoiceLotLibIds())){
                        areaItem.setChoiceLotLibIds(areaItem.getChoiceLotLibIds().replace("#"+choiceLotLib.getId()+"#,",""));
                    }
                }else{
                    areaItem.setStatus(-3);
                }
            }
            bo.setDeleteAreaItemList(downAreaItemList);
        }

        if(CollectionUtil.isNotEmpty(downAreaSkuList)){
            for (AreaSku areaSku : downAreaSkuList) {
                areaSku.setUpdatedBy(userName);
                //当前操作的运营商商品移除选品库 其他的改为删除状态
                if(!areaSku.getOperatorId().equals(operatorId)){
                    areaSku.setStatus(-3);
                }
            }
            bo.setDeleteAreaSKuList(downAreaSkuList);
        }

        if(CollectionUtil.isNotEmpty(downChoiceLotLibItemList)){
            for (ChoiceLotLibItemModel choiceLotLibItemModel : downChoiceLotLibItemList) {
                choiceLotLibItemModel.setUpdatedBy(userName);
                choiceLotLibItemModel.setStateDeleted(true);
            }
            bo.setDeleteChoiceLotLibItemList(downChoiceLotLibItemList);
        }

        if(CollectionUtil.isNotEmpty(downChoiceLotLibSkuList)){
            for (ChoiceLotLibSkuModel choiceLotLibSkuModel : downChoiceLotLibSkuList) {
                choiceLotLibSkuModel.setUpdatedBy(userName);
                choiceLotLibSkuModel.setStateDeleted(true);
            }
            bo.setDeleteChoiceLotLibSkuList(downChoiceLotLibSkuList);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean unBindChoiceLotItemSave(UnBindChoiceLotItemBo bo){
        boolean isOK = false;
        List<AreaItem> deleteAreaItemList = bo.getDeleteAreaItemList();
        List<AreaSku> deleteAreaSKuList = bo.getDeleteAreaSKuList();
        List<ChoiceLotLibItemModel> deleteChoiceLotLibItemList = bo.getDeleteChoiceLotLibItemList();
        List<ChoiceLotLibSkuModel> deleteChoiceLotLibSkuList = bo.getDeleteChoiceLotLibSkuList();
        List<DistributorItemLibModel> deleteDistributorItemLibList = bo.getDeleteDistributorItemLibList();

        log.info("解绑保存DB参数  areaItem：{}",JSON.toJSONString(deleteAreaItemList));
        log.info("解绑保存DB参数  areaSku：{}",JSON.toJSONString(deleteAreaSKuList));
        log.info("解绑保存DB参数  choiceLotLibItem：{}",JSON.toJSONString(deleteChoiceLotLibItemList));
        log.info("解绑保存DB参数  choiceLotLibSku：{}",JSON.toJSONString(deleteChoiceLotLibSkuList));
        log.info("解绑保存DB参数  distributorItem：{}",JSON.toJSONString(deleteDistributorItemLibList));

        if(CollectionUtil.isNotEmpty(deleteAreaItemList)){
            for (AreaItem areaItem : deleteAreaItemList) {
                isOK = areaItemDao.update(areaItem);
                Assert.isTrue(isOK,"区运营商品修改失败！");
            }
        }
        if(CollectionUtil.isNotEmpty(deleteAreaSKuList)){
            for (AreaSku areaSku : deleteAreaSKuList) {
                isOK = areaSkuDao.update(areaSku);
                Assert.isTrue(isOK,"区运营商品规格修改失败！");
            }
        }
        if(CollectionUtil.isNotEmpty(deleteChoiceLotLibItemList)){
            isOK = choiceLotLibItemDao.updateBatch(deleteChoiceLotLibItemList);
            Assert.isTrue(isOK,"区运营选品库商品删除失败！");
        }

        if(CollectionUtil.isNotEmpty(deleteChoiceLotLibSkuList)){
            isOK = choiceLotLibSkuDao.batchUpdate(deleteChoiceLotLibSkuList,bo.getUserName());
            Assert.isTrue(isOK,"区运营选品库商品规格删除失败！");
        }

        if(CollectionUtil.isNotEmpty(deleteDistributorItemLibList)){
            isOK = distributorItemLibDao.adminOrOperatorDel(deleteDistributorItemLibList);
            Assert.isTrue(isOK,"移除商品池失败！");
            //同时移除sku
            isOK = distributorSkuLibDao.delByBatch(deleteDistributorItemLibList);
            Assert.isTrue(isOK,"移除商品池失败！");
        }
        return isOK;
    }



    /**
     * 更新选品库可见渠道商字段
     * @param model
     */
    public Boolean updateShowDistributorIds(ChoiceLotLibItemModel model, String showDistributorIds){
       return choiceLotLibItemDao.updateShowDistributorIds(model, showDistributorIds);
    }

    /**
     * 选品库商品信息更新
     *
     * @return
     */
    public Boolean updateChoiceByItemId(ChoiceItemUpdateInfoRequest request) {
        // 获取areaItem
        List<AreaItem> areaItemList = areaItemReadDomainService.findChoiceByOperatorIdAndItemIds(request.getOperatorId(), request.getItems(), 1);
        if (CollectionUtils.isEmpty(areaItemList)) {
            log.error("ChoiceLotLibItemWriteService.updateChoiceByItemInfo error. query areaItem is empty");
            return false;
        }
        Set<Long> choiceIdList = areaItemList.stream().filter(f -> !StringUtils.isEmpty(f.getChoiceLotLibIds()))
                .map(v -> v.getChoiceLotLibIds().replace("#", "")
                ).filter(string -> !string.isEmpty()).map(Long::valueOf).collect(Collectors.toSet());
        log.info("ChoiceLotLibItemWriteService.updateChoiceByItemInfo get choiceIds str:{}", choiceIdList);
        if (CollectionUtils.isEmpty(choiceIdList)) {
            log.error("ChoiceLotLibItemWriteService.updateChoiceByItemInfo get choiceIds str:{}", choiceIdList);
            return false;
        }
	   /*  获取选品库Item
		Map<String, Object> criteriaMap = Maps.newHashMap();
		criteriaMap.put("choiceIdList", choiceIdList);
		criteriaMap.put("itemIds", request.getItems());
		List<ChoiceLotLibItemModel> libItemModelList = choiceLotLibItemDao.listFindByCriteria(criteriaMap);
		if(CollectionUtils.isEmpty(libItemModelList)){
			log.error("ChoiceLotLibItemWriteService.updateChoiceByItemInfo get libItemModelList is empty");
			return false;
		}*/
        // 获取选品库sku
        List<ChoiceLotLibSkuModel> choiceLotLibSkuModelList = choiceLotLibSkuReadService.listByWhere(request.getOperatorId(), null, choiceIdList, null, request.getItems());
        if (CollectionUtils.isEmpty(choiceLotLibSkuModelList)) {
            log.error("ChoiceLotLibItemWriteService.updateChoiceByItemInfo get choiceLotLibSkuModelList is empty");
            return false;
        }

        // 获取item sku 信息
        List<AreaSku> areaSkuList = areaSkuReadDomainService.queryByItem(request.getOperatorId(), request.getItems());
        Map<Long, AreaSku> areaSkuMap = areaSkuList.stream().collect(Collectors.toMap(AreaSku::getSkuId, Function.identity()));
        List<ChoiceLotLibSkuModel> updateChoiceSkuList = choiceLotLibSkuModelList.stream().filter(v -> areaSkuMap.containsKey(v.getSkuId())).peek(sku -> {
            AreaSku areaSku = areaSkuMap.get(sku.getSkuId());
            sku.setBasePrice(areaSku.getBasePrice());
            sku.setOriginalPrice(areaSku.getOriginalPrice());
            sku.setUpdatedBy(request.getUpdatedBy());
            sku.setProfitRate(MarkupCalculateUtils.getProfitRate(areaSku.getOriginalPrice(), areaSku.getBasePrice()));
            sku.setDistributorPrice(MarkupCalculateUtils.getDistriPrice(areaSku.getBasePrice(), sku.getMarkup()));
            sku.setResellerGross(MarkupCalculateUtils.sub(sku.getDistributorPrice(), areaSku.getBasePrice()));
            sku.setResellerGrossRate(MarkupCalculateUtils.getProfitRate(sku.getDistributorPrice(), sku.getBasePrice()));
        }).collect(Collectors.toList());
        return choiceLotLibSkuWriteService.batchUpdate(updateChoiceSkuList,  request.getUpdatedBy());
    }

    public void updateBatch(List<ChoiceLotLibItemModel> choiceLotLibItemModels) {
        choiceLotLibItemDao.updateBatch(choiceLotLibItemModels);
    }


    @Transactional
    public Boolean batchNotJoin(List<DistributorItemLibBean> itemLibBeanList,String userName){
        List<Long> choiceLotIds = itemLibBeanList.stream().map(DistributorItemLibBean::getChoiceLotLibId).distinct().collect(Collectors.toList());
        List<Long> itemIds = itemLibBeanList.stream().map(DistributorItemLibBean::getItemId).distinct().collect(Collectors.toList());
        //根据选品库和商品 查询商品所在选品库的信息
        log.info("根据选品库和商品查询商品所在选品库的信息 param:{} {}",choiceLotIds,itemIds);
        List<ChoiceLotLibItemModel> choiceLotLibItemList = choiceLotLibItemReadService.findChoiceItemByChoiceIdsAndItemIds(itemIds, choiceLotIds);
        if(CollectionUtil.isEmpty(choiceLotLibItemList)){
            throw new ServiceException("选择的商品选品库中不存在！");
        }
        List<ChoiceLotLibItemModel> list = choiceLotLibItemList.stream().filter(f -> f.getIfNoJoin() != null && f.getIfNoJoin() == 1).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(list)){
            throw new ServiceException("选择的商品已经操作拒绝！" + list.stream().map(ChoiceLotLibItemModel::getItemId).collect(Collectors.toList()));
        }

        for (ChoiceLotLibItemModel model : choiceLotLibItemList) {
            model.setUpdatedBy(userName);
            //标记为不加入
            model.setIfNoJoin(1);
            choiceLotLibItemDao.update(model);
        }
        return Boolean.TRUE;
    }

    public Boolean addChoiceLotItemToItemPool(Long distributorId,Long operatorId,List<DistributorItemLibBean> itemLibBeanList,Long tenantId,String userName){
        //数据校验
        AddChoiceLotItemToItemPoolBo bo = addChoiceLotItemToItemPoolVerify(distributorId, operatorId, itemLibBeanList);
        bo.setItemLibBeanList(itemLibBeanList);
        bo.setTenantId(tenantId);
        bo.setOperatorId(operatorId);
        bo.setSource(0);
        bo.setDistributorId(distributorId);
        bo.setUserName(userName);
        //数据组装
        addChoiceLotItemToItemPoolPackage(bo);
        //操作DB保存
        return addChoiceLotItemToItemPoolSave(bo);
    }

    public AddChoiceLotItemToItemPoolBo addChoiceLotItemToItemPoolVerify(Long distributorId, Long operatorId, List<DistributorItemLibBean> itemLibBeanList){
        AddChoiceLotItemToItemPoolBo bo = new AddChoiceLotItemToItemPoolBo();
        Map<Long, FirmMealParam> projectMap = Maps.newHashMap();
        if(distributorId != 1){
            List<FirmMealParam> firmMealParams = choiceLotLibReadDomainService.queryFirmMealList(distributorId, null, operatorId);
            if(org.apache.commons.collections4.CollectionUtils.isEmpty(firmMealParams)){
                throw new ServiceException("您还未购买服务, 请先购买...");
            }
            projectMap = firmMealParams.stream().collect(Collectors.toMap(FirmMealParam::getProjectId, Function.identity(),
                    BinaryOperator.maxBy(Comparator.comparing(FirmMealParam::getExpiringDate))));
        }


        List<Long> choiceLotIds = itemLibBeanList.stream().map(DistributorItemLibBean::getChoiceLotLibId).distinct().collect(Collectors.toList());
        List<Long> itemIds = itemLibBeanList.stream().map(DistributorItemLibBean::getItemId).distinct().collect(Collectors.toList());

        //查询当前平台/项目是否已经存在商品
        List<AreaItem> ifExist = areaItemReadDomainService.findByOperatorIdAndItemIds(distributorId, new HashSet<>(itemIds));
        if(CollectionUtil.isNotEmpty(ifExist)){
            throw new ServiceException("选择的商品已经存在！" +  ifExist.stream().map(AreaItem::getItemId).collect(Collectors.toList()));
        }
        //查询项目商品信息
        List<AreaItem> areaItemList = areaItemReadDomainService.findByOperatorIdAndItemIds(operatorId, new HashSet<>(itemIds));
        if(CollectionUtil.isEmpty(areaItemList)){
            throw new ServiceException("选择的商品不存在！");
        }
        Map<Long, AreaItem> areaItemMap = areaItemList.stream().collect(Collectors.toMap(AreaItem::getItemId, Function.identity(), (k1, k2) -> k1));
        bo.setAreaItemMap(areaItemMap);

        //根据选品库和商品 查询商品所在选品库的信息
        log.info("根据选品库和商品查询商品所在选品库的信息 param:{} {}",choiceLotIds,itemIds);
        List<ChoiceLotLibItemModel> choiceLotLibItemList = choiceLotLibItemReadService.findChoiceItemByChoiceIdsAndItemIds(itemIds, choiceLotIds);
        if(CollectionUtil.isEmpty(choiceLotLibItemList)){
            throw new ServiceException("选择的商品选品库中不存在！");
        }
        Map<Long, List<ChoiceLotLibItemModel>> choiceItemMap = choiceLotLibItemList.stream().collect(Collectors.groupingBy(ChoiceLotLibItemModel::getItemId));

        Map<Long, List<DistributorItemLibBean>> distributorItemLibMap = itemLibBeanList.stream().collect(Collectors.groupingBy(DistributorItemLibBean::getItemId));
        List<Long> repetitionItemIds = Lists.newArrayList();
        for (Long itemId : distributorItemLibMap.keySet()) {
            if(distributorItemLibMap.get(itemId).size() > 1){
                repetitionItemIds.add(itemId);
            }
        }
        if(CollectionUtil.isNotEmpty(repetitionItemIds)){
            throw new ServiceException("加入商品池商品有重复！商品id：" + repetitionItemIds);
        }
        bo.setChoiceLotLibItemList(choiceLotLibItemList);

        Map<String, Object> paramDistributorItemLibMap = Maps.newHashMap();
        paramDistributorItemLibMap.put("itemIds",itemIds);
        paramDistributorItemLibMap.put("distributorId",distributorId);
        List<DistributorItemLibModel> list = distributorItemLibReadService.listByMap(paramDistributorItemLibMap);
        if(CollectionUtil.isNotEmpty(list)){
            throw new ServiceException(String.format("当前商品 [%s]已加入我的商品库, 请勿重复加入", list.stream().map(DistributorItemLibModel::getItemId).
                    distinct().collect(Collectors.toList())));
        }

        for(DistributorItemLibBean bean : itemLibBeanList){
            log.info("加入商品库的商品信息:{}",bean);
            if(distributorId != 1 && !projectMap.containsKey(bean.getChoiceLotLibId())){
                throw new ServiceException(String.format("您还未购买商品[%s]服务, 请先购买...", bean.getItemId()));
            }
            int selectedQuantity = 0;
            FirmMealParam firmMealParam = null;
            if(distributorId != 1){
                firmMealParam = projectMap.get(bean.getChoiceLotLibId());
                log.info("DistributorItemLibWriteFacadeImpl.after,foreach.firmMealParam:{}",firmMealParam);
                int quantity = null != firmMealParam.getItemQuantity() ? firmMealParam.getItemQuantity() : 0;
                selectedQuantity = null != firmMealParam.getSelectedQuantity() ? firmMealParam.getSelectedQuantity() : 0;
                if(selectedQuantity >= quantity){
                    log.info("DistributorItemLibWriteFacadeImpl.addMyItemLib. item number out. serviceId:{},  serviceItemNumber:{}", firmMealParam.getServiceId(), quantity);
                    throw new ServiceException(String.format("当前商品[%s]已超出服务限购数量, 请升级服务...", bean.getItemId()));
                }
            }
            List<ChoiceLotLibItemModel> choiceList = choiceItemMap.get(bean.getItemId());
            if(CollectionUtil.isEmpty(choiceList)){
                throw new ServiceException(String.format("该商品信息未加入选品库, itemId:[%s]", bean.getItemId()));
            }
            AreaItem areaItem = areaItemMap.get(bean.getItemId());
            if(Objects.isNull(areaItem)){
                throw new ServiceException(String.format("商品不存在 itemId:[%s]", bean.getItemId()));
            }
            if(distributorId != 1){
                // 增加已选数量fix
                selectedQuantity++;
                firmMealParam.setSelectedQuantity(selectedQuantity);
                projectMap.put(bean.getChoiceLotLibId(),firmMealParam);
                bean.setServiceId(firmMealParam.getServiceId());
            }
            log.info("quantity++.quantity++:{}", projectMap);
        }
        return bo;
    }

    /**
     * 组装数据
     * @param bo
     * @return
     */
    public void addChoiceLotItemToItemPoolPackage(AddChoiceLotItemToItemPoolBo bo){
        List<DistributorItemLibBean> itemLibBeanList = bo.getItemLibBeanList();
        List<ChoiceLotLibItemModel> choiceLotLibItemList = bo.getChoiceLotLibItemList();
        Map<Long, AreaItem> areaItemMap = bo.getAreaItemMap();
        Long tenantId = bo.getTenantId();
        Long operatorId = bo.getOperatorId();
        Integer source = bo.getSource();
        Long distributorId = bo.getDistributorId();
        String userName = bo.getUserName();

        //查询区域sku信息
        List<AreaSku> areaSkuList = areaSkuReadDomainService.findByOperatorIdsAndItemIds(Collections.singletonList(operatorId), choiceLotLibItemList.stream().
                map(ChoiceLotLibItemModel::getItemId).collect(Collectors.toList()));
        Map<Long, List<AreaSku>> areaSkuMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(areaSkuList)){
            areaSkuMap = areaSkuList.stream().collect(Collectors.groupingBy(AreaSku::getItemId));
        }

        //查询选品库sku信息
        Map<String, Object> params = Maps.newHashMap();
        params.put("skuIds", areaSkuList.stream().map(AreaSku::getSkuId).collect(Collectors.toSet()));
        params.put("choiceLotLibIds", choiceLotLibItemList.stream().map(ChoiceLotLibItemModel::getChoiceLotLibId).collect(Collectors.toSet()));
        List<ChoiceLotLibSkuModel> choiceLotLibSKuList = choiceLotLibSkuReadService.findChoiceLotLibSkuBySkuIds(params);
        Map<String, List<ChoiceLotLibSkuModel>> choiceLotLibSkuMap = Maps.newHashMap();
        if(CollectionUtil.isNotEmpty(choiceLotLibSKuList)){
            choiceLotLibSkuMap = choiceLotLibSKuList.stream().collect(Collectors.groupingBy(g -> g.getChoiceLotLibId() + "_" + g.getSkuId()));
        }

        List<DistributorItemLibModel> distributorModelList =  Lists.newArrayList();
        List<AreaItem> insertAreaItemList = Lists.newArrayList();
        List<AreaSku> insertAreaSkuList = Lists.newArrayList();

        for (DistributorItemLibBean bean : itemLibBeanList) {
            DistributorItemLibModel model = new DistributorItemLibModel();
            model.setTenantId(tenantId);
            model.setId(idGenerator.nextValue(DistributorItemLibModel.class));
            model.setOperatorId(operatorId);
            model.setSource(Objects.isNull(source) ? 0 : source);
            model.setChoiceLotLibId(bean.getChoiceLotLibId());
            model.setServiceId(bean.getServiceId());
            model.setDistributorId(distributorId);
            model.setItemId(bean.getItemId());
            model.setCreatedBy(userName);
            model.setUpdatedBy(userName);
            distributorModelList.add(model);

            AreaItem areaItem = areaItemMap.get(bean.getItemId());
            areaItem.setId(idGenerator.nextValue(AreaItem.class));
            areaItem.setUpdatedBy(userName);
            areaItem.setOperatorId(distributorId);
            areaItem.setChoiceLotLibIds(null);
            areaItem.setSourceChoiceLotLibId(bean.getChoiceLotLibId());
            //如果为1  是平台服务选品 商品来源为2运营供品平台
            if(distributorId == 1){
                areaItem.setSourceType(2);
                areaItem.setSourceOperatorId(operatorId);
            }
            insertAreaItemList.add(areaItem);
            for (AreaSku areaSku : areaSkuMap.get(bean.getItemId())) {
                areaSku.setId(idGenerator.nextValue(AreaSku.class));
                areaSku.setUpdatedBy(userName);
                areaSku.setOperatorId(distributorId);
                //商品的成本 取选品库的分销价
                List<ChoiceLotLibSkuModel> list = choiceLotLibSkuMap.get(bean.getChoiceLotLibId() + "_" + areaSku.getSkuId());
                if(CollectionUtil.isNotEmpty(list)){
                    areaSku.setBasePrice(list.get(0).getDistributorPrice());
                }
                insertAreaSkuList.add(areaSku);
            }
        }
        bo.setInsertAreaItemList(insertAreaItemList);
        bo.setInsertAreaSKuList(insertAreaSkuList);
        bo.setDistributorModelList(distributorModelList);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean addChoiceLotItemToItemPoolSave(AddChoiceLotItemToItemPoolBo bo){
        List<AreaItem> areaItemList = bo.getInsertAreaItemList();
        List<AreaSku> areaSKuList = bo.getInsertAreaSKuList();
        List<DistributorItemLibModel> distributorModelList = bo.getDistributorModelList();
        boolean isOK = false;

        if(CollectionUtil.isNotEmpty(areaItemList)){
            isOK = areaItemDao.creates(areaItemList) > 0;
            Assert.isTrue(isOK,"保存商品信息失败！");
        }

        if(CollectionUtil.isNotEmpty(areaSKuList)){
            isOK = areaSkuDao.creates(areaSKuList) > 0 ;
            Assert.isTrue(isOK,"保存商品规格信息失败！");
        }
        if(CollectionUtil.isNotEmpty(distributorModelList)){
            isOK = distributorItemLibDao.creates(distributorModelList) > 0 ;
            Assert.isTrue(isOK,"保存商品池失败！");
        }
        isOK =  choiceLotLibItemDao.updateItemDistributorIds(distributorModelList, BigDecimal.ZERO.intValue());
        Assert.isTrue(isOK,"选品库操作失败！");
        return isOK;
    }


    public Boolean deleteChoiceLotItemToItemPool(Long distributorId, List<DistributorItemLibUpdateBean> distributorModel,
                                                 Integer constraintDelete,Integer tenantId, String userName){
        //数据校验
        DeleteChoiceLotItemToItemPoolBo bo = deleteChoiceLotItemToItemPoolVerify(distributorId, distributorModel, constraintDelete);
        bo.setUserName(userName);
        //数据组装
        deleteChoiceLotItemToItemPoolPackage(bo);
        //操作DB保存
        Boolean isOk = deleteChoiceLotItemToItemPoolSave(bo);
        if(isOk){
            paranaThirdMessageWriteApi.removeMessageCreate(bo.getDeleteChoiceLotLibItemList());
        }
        return isOk;

    }


    /**
     *平台移除商品：
     *平台上游运营的choiceLotLibItem  渠道id清除掉平台的id
     *需要删除平台的areaItem areaSku  平台的choiceLotLibItem choiceLotLibSku 被下游运营加入使用的 下游运营商的areaItem areaSku  choiceLotLibItem choiceLotLibSku
     *
     *运营移除商品：
     *平台的choiceLotLibItem 的渠道id移除掉当前运营id
     *需要删除运营的areaItem areaSku choiceLotLibItem choiceLotLibSku
     * @param distributorId
     * @param distributorModel
     * @param constraintDelete
     * @return
     */
    public DeleteChoiceLotItemToItemPoolBo deleteChoiceLotItemToItemPoolVerify(Long distributorId, List<DistributorItemLibUpdateBean> distributorModel, Integer constraintDelete){
        DeleteChoiceLotItemToItemPoolBo bo = new DeleteChoiceLotItemToItemPoolBo();
        if(distributorId == null){
            throw new ServiceException("渠道id不能为空！");
        }
        bo.setDistributorId(distributorId);

        List<Long> itemIds = distributorModel.stream().map(DistributorItemLibUpdateBean::getItemId).collect(Collectors.toList());

        //默认不强制移除
        if(constraintDelete == null){
            constraintDelete = 0;
        }

        Set<Long> operatorIds = Sets.newHashSet();
        operatorIds.add(distributorId);

        Set<Long> distributorIds = Sets.newHashSet();
        distributorIds.add(distributorId);

        //如果是平台移除
        if(distributorId == 1){
            //获取所有运营商
            List<Shop> shopList = shopDao.findByType(2);
            if(CollectionUtil.isNotEmpty(shopList)){
                Set<Long> downOperatorIds = shopList.stream().map(Shop::getId).collect(Collectors.toSet());
                if(CollectionUtil.isNotEmpty(downOperatorIds)){
                    operatorIds.addAll(downOperatorIds);
                }
            }
        }

        List<AreaItem> areaItemList = areaItemReadDomainService.findByOperatorIdsAndItemIds(operatorIds, new HashSet<>(itemIds));
        List<AreaSku> areaSkuList = areaSkuReadDomainService.findByOperatorIdsAndItemIds(new ArrayList<>(operatorIds),itemIds);

        //平台移除
        List<Long> upOperatorIds = Lists.newArrayList();
        if(distributorId == 1){
            //如果不是强制移除  需要校验商品是否被下游运营商加入商品池
            if(constraintDelete == 0){
                //判断商品是否被平台的下游运营加入商品池  这里需要排除掉平台自己
                List<AreaItem> list = areaItemList.stream().filter(f -> !Objects.equals(f.getOperatorId(), 1L) &&
                        f.getSourceType() == 2).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(list)){
                    throw new ServiceException("当前商品已经被运营商绑定！" + list.stream().map(AreaItem::getItemId).distinct().collect(Collectors.toList()));
                }
            }
            //中台移除 中台本身的商品来源 只能是2 运营供品平台   中台下游的运营商商品来源类型也是2
            List<AreaItem> adminAreaItemList = areaItemList.stream().filter(f -> f.getSourceType() == 2).collect(Collectors.toList());
            bo.setDeleteAreaItemList(adminAreaItemList);

            List<String> keyList = adminAreaItemList.stream().map(m -> m.getOperatorId() + "_" + m.getItemId()).collect(Collectors.toList());
            upOperatorIds = adminAreaItemList.stream().map(AreaItem::getSourceOperatorId).collect(Collectors.toList());
            List<AreaSku> adminAreaSkuListList = areaSkuList.stream().filter(f -> keyList.contains(f.getOperatorId() + "_" + f.getItemId())).collect(Collectors.toList());
            bo.setDeleteAreaSkuList(adminAreaSkuListList);
        }else{
            //运营移除
            bo.setDeleteAreaItemList(areaItemList);
            bo.setDeleteAreaSkuList(areaSkuList);
        }


        //获取选品库信息
        Map<String, Object> choiceLotLbtMap = Maps.newHashMap();
        //运营移除 需要获取平台的选品库信息
        if(distributorId != 1){
            operatorIds.add(1L);
        }
        choiceLotLbtMap.put("operatorIds",operatorIds);
        List<ChoiceLotLib> choiceLotLibs = choiceLotLibDao.listInfoFilter(choiceLotLbtMap);
        if(CollectionUtil.isNotEmpty(choiceLotLibs)){
            List<Long> choiceLotLibIds = choiceLotLibs.stream().map(ChoiceLotLib::getId).collect(Collectors.toList());

            //根据商品id和选品库 查询商品所在选品库的信息
            Map<String, Object> choiceLotLibItemMap = Maps.newHashMap();
            choiceLotLibItemMap.put("itemIds",itemIds);
            choiceLotLibItemMap.put("choiceIdList",choiceLotLibIds);
            List<ChoiceLotLibItemModel> choiceLotLibItemList = choiceLotLibItemDao.listFindByCriteria(choiceLotLibItemMap);
            choiceLotLibItemMap.put("choiceLotLibIds",choiceLotLibIds);
            List<ChoiceLotLibSkuModel> choiceLotLibSkuList = choiceLotLibSkuDao.listByWhere(choiceLotLibItemMap);

            if(CollectionUtil.isNotEmpty(choiceLotLibItemList)){
                //平台移除
                if(distributorId == 1){
                    //删除中台、以及下游的选品库商品信息
                    List<ChoiceLotLibItemModel> adminChoiceLotLibItemList = choiceLotLibItemList.stream().filter(f -> f.getSourceType() == 2).collect(Collectors.toList());
                    if(CollectionUtil.isNotEmpty(adminChoiceLotLibItemList)){
                        bo.setDeleteChoiceLotLibItemList(adminChoiceLotLibItemList);

                        List<String> key = adminChoiceLotLibItemList.stream().map(m -> m.getOperatorId() + "_" + m.getChoiceLotLibId() + "_" + m.getItemId()).collect(Collectors.toList());
                        List<ChoiceLotLibSkuModel> adminChoiceLotLibSkuList = choiceLotLibSkuList.stream().filter(f -> key.contains(f.getOperatorId() + "_" + f.getChoiceLotLibId() + "_" + f.getItemId())).collect(Collectors.toList());
                        bo.setDeleteChoiceLotLibSkuList(adminChoiceLotLibSkuList);
                    }
                    List<Long> ids = upOperatorIds;
                    //找到上游选品库商品信息 渠道id需要去掉平台
                    List<ChoiceLotLibItemModel> upChoiceLotLibItemList = choiceLotLibItemList.stream().filter(f -> ids.contains(f.getOperatorId())).collect(Collectors.toList());
                    bo.setUpdateChoiceLotLibItemList(upChoiceLotLibItemList);

                }else{
                    //如果不是强制移除 需要校验商品是否被渠道加入商品池
                    if(constraintDelete == 0){
                        //获得运营选品库已经被渠道加入商品库的
                        List<ChoiceLotLibItemModel> list = choiceLotLibItemList.stream().filter(f -> Objects.equals(f.getOperatorId(), distributorId) && !StringUtils.isEmpty(f.getDistributorIds())).collect(Collectors.toList());
                        if(CollectionUtil.isNotEmpty(list)){
                            throw new ServiceException("当前商品已经被渠道商绑定！" + list.stream().map(ChoiceLotLibItemModel::getItemId).distinct().collect(Collectors.toList()));
                        }
                    }
                    bo.setDeleteChoiceLotLibItemList(choiceLotLibItemList.stream().filter(f ->f.getOperatorId().equals(distributorId)).collect(Collectors.toList()));
                    bo.setDeleteChoiceLotLibSkuList(choiceLotLibSkuList.stream().filter(f ->f.getOperatorId().equals(distributorId)).collect(Collectors.toList()));

                    bo.setUpdateChoiceLotLibItemList(choiceLotLibItemList.stream().filter(f -> f.getOperatorId() == 1).collect(Collectors.toList()));
                }

                if(CollectionUtil.isNotEmpty(bo.getDeleteChoiceLotLibItemList())){
                    for (ChoiceLotLibItemModel choiceLotLibItemModel : bo.getDeleteChoiceLotLibItemList()) {
                        if(!StringUtils.isEmpty(choiceLotLibItemModel.getDistributorIds())){
                            distributorIds.addAll(Arrays.stream(choiceLotLibItemModel.getDistributorIds().replace("#", "").
                                    split(",")).map(Long::parseLong).collect(Collectors.toSet()));
                        }
                    }
                }

            }
        }
        if(CollectionUtil.isNotEmpty(distributorIds) && CollectionUtil.isNotEmpty(itemIds)){
            //获取渠道绑定选品库商品信息
            Map<String, Object> distributorParam = Maps.newHashMap();
            distributorParam.put("itemIds",itemIds);
            distributorParam.put("distributorIds",distributorIds);
//            distributorParam.put("choiceLotLibIds",bo.getDeleteChoiceLotLibItemList().stream().map(ChoiceLotLibItemModel::getChoiceLotLibId).collect(Collectors.toList()));
            List<DistributorItemLibModel> distributorItemLibList = distributorItemLibDao.listByMap(distributorParam);
            bo.setDeleteDistributorItemLibList(distributorItemLibList);
        }
        return bo;
    }

    /**
     * 组装数据
     *平台移除商品：
     *平台上游运营的choiceLotLibItem  渠道id清除掉平台的id
     *需要删除平台的areaItem areaSku  平台的choiceLotLibItem choiceLotLibSku 被下游运营加入使用的 下游运营商的areaItem areaSku  choiceLotLibItem choiceLotLibSku
     *
     *运营移除商品：
     *平台的choiceLotLibItem 的渠道id移除掉当前运营id
     *需要删除运营的areaItem areaSku choiceLotLibItem choiceLotLibSku
     * @param bo
     * @return
     */
    public void deleteChoiceLotItemToItemPoolPackage(DeleteChoiceLotItemToItemPoolBo bo){
        String userName = bo.getUserName();
        List<AreaItem> deleteAreaItemList = bo.getDeleteAreaItemList();
        List<AreaSku> deleteAreaSkuList = bo.getDeleteAreaSkuList();
        List<ChoiceLotLibItemModel> deleteChoiceLotLibItemList = bo.getDeleteChoiceLotLibItemList();
        List<ChoiceLotLibSkuModel> deleteChoiceLotLibSkuList = bo.getDeleteChoiceLotLibSkuList();
        List<ChoiceLotLibItemModel> updateChoiceLotLibItemList = bo.getUpdateChoiceLotLibItemList();

        if(CollectionUtil.isNotEmpty(deleteAreaItemList)){
            for (AreaItem areaItem : deleteAreaItemList) {
                areaItem.setUpdatedBy(userName);
                areaItem.setStatus(-3);
            }
        }

        if(CollectionUtil.isNotEmpty(deleteAreaSkuList)){
            for (AreaSku areaSku : deleteAreaSkuList) {
                areaSku.setUpdatedBy(userName);
                areaSku.setStatus(-3);
            }
        }

        if(CollectionUtil.isNotEmpty(deleteChoiceLotLibItemList)){
            for (ChoiceLotLibItemModel choiceLotLibItemModel : deleteChoiceLotLibItemList) {
                choiceLotLibItemModel.setUpdatedBy(userName);
                choiceLotLibItemModel.setStateDeleted(true);
            }
        }

        if(CollectionUtil.isNotEmpty(deleteChoiceLotLibSkuList)){
            for (ChoiceLotLibSkuModel choiceLotLibSkuModel : deleteChoiceLotLibSkuList) {
                choiceLotLibSkuModel.setUpdatedBy(userName);
                choiceLotLibSkuModel.setStateDeleted(true);
            }
        }

        if(CollectionUtil.isNotEmpty(updateChoiceLotLibItemList)){
            for (ChoiceLotLibItemModel choiceLotLibItemModel : updateChoiceLotLibItemList) {
                choiceLotLibItemModel.setUpdatedBy(userName);
                if(!StringUtils.isEmpty(choiceLotLibItemModel.getDistributorIds())){
                    //平台移除
                    if(bo.getDistributorId() == 1){
                        choiceLotLibItemModel.setDistributorIds(choiceLotLibItemModel.getDistributorIds().replace("#1#,",""));
                    }else{
                        choiceLotLibItemModel.setDistributorIds(choiceLotLibItemModel.getDistributorIds().replace("#"+bo.getDistributorId()+"#,",""));
                    }
                }
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteChoiceLotItemToItemPoolSave(DeleteChoiceLotItemToItemPoolBo bo){
        boolean isOK = false;
        List<AreaItem> deleteAreaItemList = bo.getDeleteAreaItemList();
        List<AreaSku> deleteAreaSKuList = bo.getDeleteAreaSkuList();
        List<ChoiceLotLibItemModel> deleteChoiceLotLibItemList = bo.getDeleteChoiceLotLibItemList();
        List<ChoiceLotLibSkuModel> deleteChoiceLotLibSkuList = bo.getDeleteChoiceLotLibSkuList();
        List<ChoiceLotLibItemModel> updateChoiceLotLibItemList = bo.getUpdateChoiceLotLibItemList();
        List<DistributorItemLibModel> deleteDistributorItemLibList = bo.getDeleteDistributorItemLibList();

        if(CollectionUtil.isNotEmpty(deleteAreaItemList)){
            Map<Long, List<AreaItem>> areaItemMap = deleteAreaItemList.stream().collect(Collectors.groupingBy(AreaItem::getOperatorId));
            for (Long operatorId : areaItemMap.keySet()) {
                isOK = areaItemDao.batchUpdateAreaItemStatus(operatorId, "-3",areaItemMap.get(operatorId).stream().map(AreaItem::getItemId).collect(Collectors.toList()),bo.getUserName());
                Assert.isTrue(isOK,"区运营商品修改失败！");
            }
        }
        if(CollectionUtil.isNotEmpty(deleteAreaSKuList)){
            Map<Long, List<AreaSku>> areaSkuMap = deleteAreaSKuList.stream().collect(Collectors.groupingBy(AreaSku::getOperatorId));
            for (Long operatorId : areaSkuMap.keySet()) {
                isOK = areaSkuDao.batchUpdateAreaItemStatus(operatorId, "-3",areaSkuMap.get(operatorId).stream().map(AreaSku::getItemId).collect(Collectors.toList()),bo.getUserName());
                Assert.isTrue(isOK,"区运营商品规格修改失败！");
            }
        }
        if(CollectionUtil.isNotEmpty(deleteChoiceLotLibItemList)){
            Map<Long, List<ChoiceLotLibItemModel>> choiceLotLibItemMap = deleteChoiceLotLibItemList.stream().collect(Collectors.groupingBy(ChoiceLotLibItemModel::getChoiceLotLibId));
            for (Long choiceLotLibId : choiceLotLibItemMap.keySet()) {
                Long delBatchNo = System.currentTimeMillis();
                Map<String, Object> params = Maps.newHashMap();
                params.put("updatedBy",bo.getUserName());
                params.put("delBatchNo",delBatchNo);
                Long operatorId = choiceLotLibItemMap.get(choiceLotLibId).get(0).getOperatorId();
                params.put("operatorId", operatorId);
                params.put("choiceLotLibId",choiceLotLibId);
                List<Long> itemIds = choiceLotLibItemMap.get(choiceLotLibId).stream().map(ChoiceLotLibItemModel::getItemId).collect(Collectors.toList());
                params.put("itemIds", itemIds);
                ChoiceLotLibItemUnBindRequest request = new ChoiceLotLibItemUnBindRequest();
                request.setOperatorId(operatorId);
                request.setItemIds(itemIds);
                request.setChoiceLotLibId(choiceLotLibId);
                isOK = choiceLotLibItemDao.updateUnBindByItems(params,request,delBatchNo);
                Assert.isTrue(isOK,"选品库商品删除失败！");
            }
        }

        if(CollectionUtil.isNotEmpty(updateChoiceLotLibItemList)){
            for (ChoiceLotLibItemModel itemModel : updateChoiceLotLibItemList) {
                isOK = choiceLotLibItemDao.update(itemModel);
                Assert.isTrue(isOK,"选品库商品更新失败！");
            }
        }

        if(CollectionUtil.isNotEmpty(deleteChoiceLotLibSkuList)){
            Map<Long, List<ChoiceLotLibSkuModel>> choiceLotLibSkuMap = deleteChoiceLotLibSkuList.stream().collect(Collectors.groupingBy(ChoiceLotLibSkuModel::getChoiceLotLibId));
            for (Long choiceLotLibId : choiceLotLibSkuMap.keySet()) {
                Map<String, Object> params = Maps.newHashMap();
                params.put("updatedBy",bo.getUserName());
                params.put("operatorId", choiceLotLibSkuMap.get(choiceLotLibId).get(0).getOperatorId());
                params.put("choiceLotLibId",choiceLotLibId);
                params.put("itemIds", choiceLotLibSkuMap.get(choiceLotLibId).stream().map(ChoiceLotLibSkuModel::getItemId).collect(Collectors.toList()));
                isOK = choiceLotLibSkuDao.updateUnBindByItems(params);
                Assert.isTrue(isOK,"区运营选品库商品规格删除失败！");
            }
        }

        if(CollectionUtil.isNotEmpty(deleteDistributorItemLibList)){
            isOK = distributorItemLibDao.adminOrOperatorDel(deleteDistributorItemLibList);
            Assert.isTrue(isOK,"移除商品池失败！");
            //同时移除sku
            isOK = distributorSkuLibDao.delByBatch(deleteDistributorItemLibList);
            Assert.isTrue(isOK,"移除商品池失败！");
        }
        return isOK;
    }

}
