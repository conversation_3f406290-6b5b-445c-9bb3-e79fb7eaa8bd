package io.terminus.parana.item.category.model;

import io.terminus.parana.item.common.base.BaseModel;
import lombok.Data;

@Data
public class StCategory extends BaseModel {

	/**
	 * 
	 */
	private Long id;
	/**
	 * 类目编码
	 */
	private String cateNum;
	/**
	 * 类目名称
	 */
	private String cateName;
	/**
	 * 类目等级
	 */
	private String level;
	/**
	 * 父类编码
	 */
	private String pnum;
	/**
	 * 是否删除 0: 否 1：是
	 */
	private Integer delFlag;
	/**
	 * 租户Id
	 */
	private Integer tenantId;
	/**
	 * 
	 */
	private java.util.Date createdAt;
	/**
	 * 
	 */
	private java.util.Date updatedAt;
	/**
	 * 操作用户id
	 */
	private String updatedBy;

	/**
	 * 标识是否有孩子类目
	 */
	private Boolean hasChildren;
}
