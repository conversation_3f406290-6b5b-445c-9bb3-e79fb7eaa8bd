package io.terminus.parana.item.comment.api.converter;

import io.terminus.parana.item.comment.api.bean.response.ItemCommentInfo;
import io.terminus.parana.item.comment.model.Comment;
import io.terminus.parana.item.comment.search.dataobject.ItemCommentDO;
import io.terminus.parana.item.common.converter.BaseConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-07-19
 */
@Component
public class LogicItemCommentApiInfoConverter extends ItemCommentApiInfoConverterImpl implements ItemCommentApiInfoConverter, BaseConverter {

    @Override
    public ItemCommentInfo get(ItemCommentDO itemCommentDO) {
        ItemCommentInfo info = super.get(itemCommentDO);

        if (itemCommentDO == null) {
            return null;
        }

        info.setItemId(itemCommentDO.getTargetId());

        Map<String, String> extra = itemCommentDO.getExtra();
        if (!CollectionUtils.isEmpty(extra)) {
            info.setUserName(extra.get("user_name"));
            info.setUserAvatar(extra.get("user_avatar"));
            info.setSkuAttributes(json2mapOfString(extra.get("sku_attributes")));
            info.setItemName(extra.get("item_name"));
            info.setSkuImage(extra.get("sku_image"));
            info.setPrice(extra.containsKey("price") ? Long.valueOf(extra.get("price")) : null);
        }

        return info;
    }

    @Override
    public ItemCommentInfo get(Comment comment) {
        ItemCommentInfo info = super.get(comment);

        if (comment == null) {
            return null;
        }

        info.setItemId(comment.getTargetId());
        Map<String, String> extra = comment.getExtra();
        if (!CollectionUtils.isEmpty(extra)) {
            info.setUserName(extra.get("user_name"));
            info.setUserAvatar(extra.get("user_avatar"));
            info.setSkuAttributes(json2mapOfString(extra.get("sku_attributes")));
            info.setItemName(extra.get("item_name"));
            info.setSkuImage(extra.get("sku_image"));
            info.setPrice(extra.containsKey("price") ? Long.valueOf(extra.get("price")) : null);
        }

        return info;
    }
}
