package io.terminus.parana.item.zhengqi.inventory;

import io.terminus.parana.item.common.utils.Assert;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.third.enums.ThirdEntityType;
import io.terminus.parana.item.third.enums.ThirdWarehouseScopeDimensionType;
import io.terminus.parana.item.plugin.third.api.inventory.api.InventoryReadApi;
import io.terminus.parana.item.third.info.InventorySourcing;
import io.terminus.parana.item.third.info.InventorySourcingByDimensionInfo;
import io.terminus.parana.item.third.info.InventorySourcingInventoryInfo;
import io.terminus.parana.item.third.param.InventorySourcingByDimensionParam;
import io.terminus.parana.item.third.param.InventorySourcingInventoryParam;
import io.terminus.parana.item.third.param.ThirdInventorySourcingRequest;
import io.terminus.parana.item.zhengqi.bo.ZQLocationBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Component
public class ZhengQiInventoryInvoker {

    @Autowired
    private InventoryReadApi inventoryReadApi;


    private Map<String,Long> sourcingStockQty(Long operatorId,Map<Long,List<SkuInfo>> skuInfoVendorMap,ZQLocationBO location,Integer tenantId){

        Map<String,Long> sourcingResultMap = new HashMap<>();

        if(null == skuInfoVendorMap || skuInfoVendorMap.isEmpty()){
            return sourcingResultMap;
        }

        //定义库存寻源参数
        ThirdInventorySourcingRequest request = new ThirdInventorySourcingRequest();
        List<InventorySourcingByDimensionParam> dimensionParams = new ArrayList<>();

        for(Long vendorId : skuInfoVendorMap.keySet()){

            List<SkuInfo> skuInfoList = skuInfoVendorMap.get(vendorId);

            List<InventorySourcingInventoryParam> inventoryParams = new ArrayList<>();

            for(SkuInfo skuInfo : skuInfoList){
                InventorySourcingInventoryParam inventoryParam = new InventorySourcingInventoryParam();
                inventoryParam.setEntityId(String.valueOf(skuInfo.getId()));
                inventoryParam.setEntityType(ThirdEntityType.SKU_ID.getCode());

                inventoryParams.add(inventoryParam);
            }

            InventorySourcingByDimensionParam dimensionParam = new InventorySourcingByDimensionParam();
            dimensionParam.setDivisionIds(buildDivisionIds(location));
            dimensionParam.setDimensionId(String.valueOf(vendorId));
            dimensionParam.setDimensionType(ThirdWarehouseScopeDimensionType.MERCHANT.getCode());
            dimensionParam.setSourcingInventoryParams(inventoryParams);
            dimensionParam.setVendorId(vendorId);
            dimensionParam.setOperatorId(operatorId);

            dimensionParams.add(dimensionParam);
        }

        request.setSourcingByDimensionParams(dimensionParams);
        request.setTenantId(tenantId);

        InventorySourcing sourcingInfo = inventoryReadApi.sourcing(request);

        if (sourcingInfo == null || CollectionUtils.isEmpty(sourcingInfo.getSourcingByDimensionInfos())) {
            log.warn("inventory simple sourcing result is null");
            return sourcingResultMap;
        }

        List<InventorySourcingByDimensionInfo> dimensionInfos = sourcingInfo.getSourcingByDimensionInfos();
        // 寻源结果读取库存：1、普通商品寻源 2、商品组寻源-取库存最小值
        for (InventorySourcingByDimensionInfo inventory : dimensionInfos) {

            if (CollectionUtils.isEmpty(inventory.getInventories())) {
                continue;
            }
            for (InventorySourcingInventoryInfo info : inventory.getInventories()) {

                String entityId = info.getEntityId();
                Long skuStock = sourcingResultMap.get(entityId);

                if(null == skuStock){
                    skuStock = Long.MAX_VALUE;
                }
                Long maxSellableQuantity = info.getMaxSellableQuantity();

                if (info.getSatisfied() && maxSellableQuantity != null) {
                    skuStock = maxSellableQuantity < skuStock ? maxSellableQuantity : skuStock;
                }
                sourcingResultMap.put(entityId,skuStock);
            }
        }

        Map<String,Long> resultMap = new HashMap<>();
        for(String entityId : sourcingResultMap.keySet()){

           Long skuStock = sourcingResultMap.get(entityId);
           skuStock = skuStock.equals(Long.MAX_VALUE) ? 0 : skuStock;
            resultMap.put(entityId,skuStock);
        }

        return resultMap;
    }


    private Map<String,Long> sourcingGroupStockQty(Long operatorId,Map<Long,List<SkuInfo>> groupSkuInfoVendorMap,ZQLocationBO location,Integer tenantId){

        Map<String,Long> sourcingResultMap = new HashMap<>();

        if(null == groupSkuInfoVendorMap || groupSkuInfoVendorMap.isEmpty()){
            return sourcingResultMap;
        }

        List<InventorySourcingByDimensionParam> sourcingByDimensionParams = new ArrayList<>();

        for(Long vendorId : groupSkuInfoVendorMap.keySet()){

            // STEP 1： 设置寻源的基础信息，包括：省、市、区、店铺id
            InventorySourcingByDimensionParam inventorySourcingByDimensionParam = new InventorySourcingByDimensionParam();
            inventorySourcingByDimensionParam.setDivisionIds(buildDivisionIds(location));

            // 设置寻源目标维度为商家
            inventorySourcingByDimensionParam.setDimensionId(String.valueOf(vendorId));
            inventorySourcingByDimensionParam.setDimensionType(ThirdWarehouseScopeDimensionType.MERCHANT.getCode());
            inventorySourcingByDimensionParam.setVendorId(vendorId);
            inventorySourcingByDimensionParam.setOperatorId(operatorId);

            List<SkuInfo> groupSkuInfosList = groupSkuInfoVendorMap.get(vendorId);
            List<InventorySourcingInventoryParam> bundleParams = new ArrayList<>();

            for(SkuInfo groupSkuInfo : groupSkuInfosList){

                Assert.isTrue(groupSkuInfo.isGroup(),"必须是组合商品才能进去组合品库存寻源！");

                List<InventorySourcingInventoryParam> bundleParam =
                        buildSouringInventoryParam(groupSkuInfo.getSkuIds(), groupSkuInfo.getId());

                bundleParams.addAll(bundleParam);
            }

            inventorySourcingByDimensionParam.setSourcingInventoryParams(bundleParams);
            sourcingByDimensionParams.add(inventorySourcingByDimensionParam);
        }

        ThirdInventorySourcingRequest request = new ThirdInventorySourcingRequest();
        request.setSourcingByDimensionParams(sourcingByDimensionParams);
        request.setTenantId(tenantId);

        InventorySourcing inventorySourcingInfo = inventoryReadApi.sourcing(request);

        if(null == inventorySourcingInfo || null == inventorySourcingInfo.getSourcingByDimensionInfos()){
            log.warn("inventory group sourcing result is null");
            return sourcingResultMap;
        }

        List<InventorySourcingByDimensionInfo> sourcingByDimensionInfos = inventorySourcingInfo.getSourcingByDimensionInfos();

        for(InventorySourcingByDimensionInfo sourcingInfo : sourcingByDimensionInfos){
            if(null == sourcingInfo){
                log.warn("inventory group sourcing sourcingInfo is null");
                continue;
            }else{

                Set<InventorySourcingInventoryInfo> inventories = sourcingInfo.getInventories();
                for(InventorySourcingInventoryInfo info : inventories){

                    String skuIdEntity = info.getBundleId();
                    Long skuStock = sourcingResultMap.get(skuIdEntity);
                    if(null == skuStock){
                        skuStock = Long.MAX_VALUE;
                    }
                    if(null != info.getMaxSellableQuantity()){
                        skuStock = info.getMaxSellableQuantity() < skuStock ? info.getMaxSellableQuantity() : skuStock;
                    }

                    skuStock = skuStock.equals(Long.MAX_VALUE) ? 0 : skuStock;
                    sourcingResultMap.put(skuIdEntity,skuStock);
                }
            }
        }

        return sourcingResultMap;
    }



    public Map<Long,Long> queryStockQtyInfo(Long operatorId,List<SkuInfo> skuInfos,ZQLocationBO location,Integer tenantId){

        Map<Long,Long> result = new HashMap<>();

        if(null == operatorId){
            log.warn("operatorId is null");
            return result;
        }
        
        if(null == skuInfos || skuInfos.isEmpty()){
            log.warn("skuInfos is null or empty");
            return result;
        }

        Map<Long,List<SkuInfo>> skuInfoVendorMap = new HashMap<>();
        Map<Long,List<SkuInfo>> groupSkuInfoVendorMap = new HashMap<>();

        for(SkuInfo skuInfo : skuInfos){

            result.put(skuInfo.getId(),Long.valueOf("0"));

            Long vendorId = skuInfo.getShopId();
            if(null == vendorId){
                log.warn("vendorId is null or empty,sku_id is {}",skuInfo.getId());
                continue;
            }

            if(skuInfo.isGroup()){
                List<SkuInfo> groupSkuInfos = groupSkuInfoVendorMap.get(vendorId);
                if(null == groupSkuInfos){
                    groupSkuInfos = new ArrayList<>();
                }
                groupSkuInfos.add(skuInfo);
                groupSkuInfoVendorMap.put(vendorId,groupSkuInfos);
            }else{
                List<SkuInfo> vendorSkuInfos = skuInfoVendorMap.get(vendorId);
                if(null == vendorSkuInfos){
                    vendorSkuInfos = new ArrayList<>();
                }
                vendorSkuInfos.add(skuInfo);
                skuInfoVendorMap.put(vendorId,vendorSkuInfos);
            }
        }

        Map<String,Long> sourcingMap = new HashMap<>();
        //组合商品批量寻源
        if(!groupSkuInfoVendorMap.isEmpty()){

            Map<String,Long> sourcingGroupResultMap = sourcingGroupStockQty(operatorId,groupSkuInfoVendorMap,location,tenantId);
            if(null != sourcingGroupResultMap && !sourcingGroupResultMap.isEmpty() ){
                sourcingMap.putAll(sourcingGroupResultMap);
            }
        }

        //普通商品批量寻源
        if(!skuInfoVendorMap.isEmpty()){

            Map<String,Long> sourcingResultMap = sourcingStockQty(operatorId,skuInfoVendorMap,location,tenantId);
            if(null != sourcingResultMap && !sourcingResultMap.isEmpty() ){
                sourcingMap.putAll(sourcingResultMap);
            }
        }

        for(String skuEntyId : sourcingMap.keySet()){
            Long stockQty =  sourcingMap.get(skuEntyId);
            if(null != stockQty){
                result.put(Long.valueOf(skuEntyId),stockQty);
            }
        }

        return result;
    }


    private String[] buildDivisionIds(ZQLocationBO bo) {
        return new String[]{String.valueOf(bo.getProvinceId()), String.valueOf(bo.getCityId()), String.valueOf(bo.getDistrictId())};
    }

    /**
     * 构建寻源的bundle信息
     *
     * @param skuIds   后端sku映射
     * @param bundleId 组id
     * @return bundle信息
     */
    private List<InventorySourcingInventoryParam> buildSouringInventoryParam(Map<Long, Integer> skuIds, Long bundleId) {
        List<InventorySourcingInventoryParam> result = new LinkedList<>();

        for (Map.Entry<Long, Integer> backSku : skuIds.entrySet()) {
            InventorySourcingInventoryParam inventorySourcingInventoryParam = new InventorySourcingInventoryParam();
            inventorySourcingInventoryParam.setBundleId(String.valueOf(bundleId));
            inventorySourcingInventoryParam.setEntityId(String.valueOf(backSku.getKey()));
            inventorySourcingInventoryParam.setEntityType(ThirdEntityType.SKU_ID.getCode());
            inventorySourcingInventoryParam.setQuantity(backSku.getValue().longValue());

            result.add(inventorySourcingInventoryParam);
        }

        return result;
    }

}
