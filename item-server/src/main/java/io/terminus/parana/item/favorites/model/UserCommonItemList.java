package io.terminus.parana.item.favorites.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserCommonItemList implements Serializable {

    private static final long serialVersionUID = -4756947083772460446L;

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("商品id")
    private Long itemId;

    @ApiModelProperty("区域运营id")
    private Long operatorId;

    @ApiModelProperty("来源 1-商详页收藏 2-购买")
    private Integer source;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("修改时间")
    private Date updatedAt;

    @ApiModelProperty("更新者id")
    private String updatedBy;

}
