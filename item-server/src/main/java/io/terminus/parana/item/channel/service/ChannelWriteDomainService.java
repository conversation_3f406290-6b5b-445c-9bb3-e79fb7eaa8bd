package io.terminus.parana.item.channel.service;

import io.terminus.common.exception.ServiceException;
import io.terminus.parana.item.channel.dao.ChannelDao;
import io.terminus.parana.item.channel.enums.ChannelStatusType;
import io.terminus.parana.item.channel.extension.ChannelWriteExtension;
import io.terminus.parana.item.channel.model.Channel;
import io.terminus.parana.item.common.business.tag.ExecuteType;
import io.terminus.parana.item.common.extension.AbstractNormalExtensionDeal;
import io.terminus.parana.item.common.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2019-05-27
 */
@Slf4j
@Service
public class ChannelWriteDomainService extends AbstractNormalExtensionDeal {

    private final ChannelDao channelDao;

    @Autowired(required = false)
    private ChannelWriteExtension channelWriteExtension;

    public ChannelWriteDomainService(ChannelDao channelDao) {
        this.channelDao = channelDao;
    }

    public Long create(Channel channel) {
        try {
            // 同名重复检测
            Channel exist = channelDao.findByName(channel.getName());
            if (exist != null) {
                throw new ServiceException("channel.duplicate.with.name");
            }

            // 处理父类目信息   普通渠道：pid=1  特殊渠道： pid =0
            Long pid = channel.getPid();
            if (pid == null || pid == 1L) {
                channel.setLevel(1);
                channel.setPid(1L);
            } else if (pid == 0L) {
                // 特殊渠道不允许创建下级渠道
                throw new ServiceException("special.channel.cant.have.children.channel");
            } else {
                Channel parent = channelDao.findById(channel.getPid());
                if (parent == null) {
                    throw new ServiceException("parent.channel.not.found");
                }
                channel.setLevel(parent.getLevel() + 1);
            }

            String token = UUID.randomUUID().toString().replace("-", "").toLowerCase();
            channel.setToken(token);

            if (channel.getStatus() == null) {
                channel.setStatus(ChannelStatusType.CLOSE.getValue());
            }

            channelDao.create(channel);
            invokeExtension(channel, ExecuteType.CREATE);

            return channel.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (IllegalStateException e) {
            log.warn("fail to parse item url: {}, cause: {}", channel.getSourceLink(), printErrorStack(e));
            throw new ServiceException("item.source.link.invalid");
        } catch (Exception e) {
            log.error("fail to create item with channelDTO: {}, cause: {}", channel, printErrorStack(e));
            throw new ServiceException("item.create.fail");
        }
    }

    public Boolean update(Channel channel) {
        try {
            Channel exist = channelDao.findById(channel.getId());
            if (exist == null) {
                throw new ServiceException("channel.not.exist");
            }
            Channel nameExist = channelDao.findByName(channel.getName());
            if (nameExist != null && !nameExist.getId().equals(channel.getId())) {
                throw new ServiceException("channel.duplicate.with.name");
            }

            invokeExtension(channel, ExecuteType.UPDATE);
            return channelDao.update(channel);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to update item with channelDTO: {}, cause: {}", channel, printErrorStack(e));
            throw new ServiceException("item.update.fail");
        }
    }

    public Boolean updateStatus(Long id, Integer status) {
        if (!ChannelStatusType.isValid(status)) {
            throw new ServiceException("invalid status");
        }

        try {
            Channel exist = channelDao.findById(id);
            if (exist == null) {
                throw new ServiceException("channel.not.exist");
            }

            return channelDao.updateStatus(id, status);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("fail to update item status with id: {}, status: {}, cause: {}", id, status, printErrorStack(e));
            throw new ServiceException("item.status.update.fail");
        }

    }

    public Boolean delete(Long id) {
        try {
            Long childrenCount = channelDao.countChildren(id);
            Assert.equals(childrenCount, 0L, "channel.has.child");

            return channelDao.delete(id);
        } catch (Exception e) {
            log.error("fail to delete item by id: {}, cause: {}", id, printErrorStack(e));
            throw new ServiceException("item.delete.fail");
        }
    }

    private void invokeExtension(Channel channel, ExecuteType executeType) {
        if (channelWriteExtension != null) {
            invokeExtension(channelWriteExtension::process, channel, executeType);
        }
    }
}
