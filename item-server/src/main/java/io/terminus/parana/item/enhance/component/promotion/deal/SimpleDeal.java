package io.terminus.parana.item.enhance.component.promotion.deal;

import io.terminus.parana.item.enhance.api.bean.response.DynamicRenderInfo;
import io.terminus.parana.item.item.api.bean.response.sku.SkuInfo;
import io.terminus.parana.item.third.info.ActivityMatchedLine;
import io.terminus.parana.item.third.info.ActivityResult;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 直降活动优惠处理
 *
 * <AUTHOR> herf="mailto:<EMAIL>">xunyard</a>
 * @date 2018-12-18
 */
@Component
public class SimpleDeal extends AbstractPromotionDeal {

    private static final String ACTIVITY_SIMPLE = "simple";

    @Override
    public String getCode() {
        return ACTIVITY_SIMPLE;
    }

    @Override
    public boolean handle(DynamicRenderInfo vo,
                          SkuInfo selectedSku,
                          List<ActivityResult> resultInfoList,
                          List<ActivityMatchedLine> lineInfoList,
                          List<ActivityMatchedLine> shopInfoList) {
        return super.handle(vo, selected<PERSON>ku, resultInfoList, lineInfoList, shopInfoList);
    }
}
