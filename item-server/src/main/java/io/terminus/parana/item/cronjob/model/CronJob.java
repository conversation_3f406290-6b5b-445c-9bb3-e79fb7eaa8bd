package io.terminus.parana.item.cronjob.model;

import lombok.Data;

import java.util.Date;

/**
 * 定时任务
 */
@Data
public class CronJob {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Integer tenantId;

    /**
     * 目标code
     */
    private String targetCode;

    /**
     * 执行参数
     */
    private String executeJson;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 日志
     */
    private String log;

    /**
     * 执行时间
     */
    private Date executeAt;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
