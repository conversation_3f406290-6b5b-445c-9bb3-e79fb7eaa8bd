package io.terminus.parana.item.category.api.converter;

import io.terminus.parana.item.category.api.bean.request.AppleItemCategoryRelationRequest;
import io.terminus.parana.item.category.model.AppleItemCategoryRelationModel;
import io.terminus.parana.item.open.api.bean.response.AppleItemCategoryInfo;
import io.terminus.parana.item.open.api.bean.response.AppleItemCategoryRelationInfo;
import io.terminus.parana.item.open.model.AppleItemCategoryModel;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AppleItemConverter {

    AppleItemCategoryRelationModel requestToModel(AppleItemCategoryRelationRequest request);

    AppleItemCategoryModel infoToModel(AppleItemCategoryInfo info);

    List<AppleItemCategoryModel> infosToModels(List<AppleItemCategoryInfo> infos);

    List<AppleItemCategoryInfo> modelsToInfos(List<AppleItemCategoryModel> infos);

    List<AppleItemCategoryRelationInfo> modelsToInfos2(List<AppleItemCategoryRelationModel> infos);
}
