<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="StCategoryBinding">
    <resultMap id="StCategoryBindingMap" type="io.terminus.parana.item.category.model.StCategoryBinding">
    	<id property="id" column="id"/>
    	<result property="cateId" column="cate_id"/>
        <result property="cateName" column="cate_name"/>
    	<result property="stCateNum" column="st_cate_num"/>
    	<result property="delFlag" column="del_flag"/>
    	<result property="tenantId" column="tenant_id"/>
    	<result property="createdAt" column="created_at"/>
    	<result property="updatedAt" column="updated_at"/>
    	<result property="updatedBy" column="updated_by"/>
    </resultMap>

    <sql id="tb">
        parana_st_category_binding
    </sql>

    <sql id="cols_all">
            `id`,
            `cate_id`,
            `cate_name`,
            `st_cate_num`,
            `del_flag`,
            `tenant_id`,
            `created_at`,
            `updated_at`,
            `updated_by`
    </sql>

    <sql id="vals">
    #{id},
    #{cateId},
    #{cateName},
    #{stCateNum},
    #{delFlag},
    #{tenantId},
    #{createdAt},
    #{updatedAt},
    #{updatedBy}
    </sql>

    <sql id="criteria">
        <where>
            del_flag = 0
            <if test="id != null">AND id = #{id}</if>
            <if test="cateId != null">AND cate_id = #{cateId}</if>
            <if test="cateName != null">AND cate_name = #{cateName}</if>
            <if test="stCateNum != null">AND st_cate_num = #{stCateNum}</if>
            <if test="stCateNums != null and stCateNums.size() > 0">
                AND st_cate_num IN
                <foreach collection="stCateNums" item="stCateNum" open="(" separator="," close=")">
                    #{stCateNum}
                </foreach>
            </if>
            <if test="tenantId != null">AND tenant_id = #{tenantId}</if>
            <if test="createdAt != null">AND created_at = #{createdAt}</if>
            <if test="updatedAt != null">AND updated_at = #{updatedAt}</if>
            <if test="updatedBy != null">AND updated_by = #{updatedBy}</if>
        </where>
    </sql>

    <insert id="create" parameterType="io.terminus.parana.item.category.model.StCategoryBinding" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_all"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="io.terminus.parana.item.category.model.StCategoryBinding" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_all"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
        (
        #{i.id},
        #{i.cateId},
        #{i.cateName},
        #{i.stCateNum},
        #{i.delFlag},
        #{i.tenantId},
        #{i.createdAt},
        #{i.updatedAt},
        #{i.updatedBy}
        )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="StCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="StCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="update" parameterType="io.terminus.parana.item.category.model.StCategoryBinding">
        UPDATE
        <include refid="tb"/>
        <set>
        	<if test="cateId != null">cate_id = #{cateId},</if>
            <if test="cateName != null">cate_name = #{cateName},</if>
        	<if test="stCateNum != null">st_cate_num = #{stCateNum},</if>
        	<if test="delFlag != null">del_flag = #{delFlag},</if>
        	<if test="createdAt != null">created_at = #{createdAt},</if>
        	<if test="updatedAt != null">updated_at = #{updatedAt},</if>
        	<if test="updatedBy != null">updated_by = #{updatedBy},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <sql id="paging_criteria">
        <where>
            del_flag = 0
            <if test="id != null">AND id = #{id}</if>
            <if test="cateId != null">AND cate_id = #{cateId}</if>
            <if test="cateName != null">AND cate_name = #{cateName}</if>
            <if test="stCateNum != null">AND st_cate_num = #{stCateNum}</if>
            <if test="tenantId != null">AND tenant_id = #{tenantId}</if>
            <if test="createdAt != null">AND created_at = #{createdAt}</if>
            <if test="updatedAt != null">AND updated_at = #{updatedAt}</if>
            <if test="updatedBy != null">AND updated_by = #{updatedBy}</if>
        </where>
    </sql>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <include refid="paging_criteria"/>
    </select>

    <select id="paging" parameterType="map" resultMap="StCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <include refid="paging_criteria"/>
        ORDER BY `id` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="list" parameterType="io.terminus.parana.item.category.model.StCategoryBinding" resultMap="StCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <include refid="criteria"/>
        ORDER BY `id` DESC
    </select>

    <select id="queryOne" parameterType="io.terminus.parana.item.category.model.StCategoryBinding" resultMap="StCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <include refid="criteria"/>
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <select id="deleteById" parameterType="long">
        DELETE
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="deleteByIds" parameterType="list">
        DELETE
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateBindingStNum" parameterType="java.util.Map">
        update <include refid="tb"/> set st_cate_num = #{newCateNum} where st_cate_num = #{oldCateNum}
    </update>

</mapper>
