<?xml version="1.0" encoding="UTF-8" ?>

<!--
  ~ Copyright (c) 2018. 杭州端点网络科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="io.terminus.parana.item.category.repository.OperatorCategoryBindingDao">
    <resultMap id="OperatorCategoryBindingMap" type="io.terminus.parana.item.category.model.OperatorCategoryBinding">
        <id column="id" property="id"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_category_id" property="operatorCategoryId"/>
        <result column="back_category_id" property="backCategoryId"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedBy"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <sql id="tb">
        `parana_operator_category_binding`
    </sql>

    <sql id="cols_exclude_id">
        `operator_id`, `operator_category_id`, `back_category_id`, `created_at`, `updated_at`, `updated_by`
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="vals">
        #{operatorId}, #{operatorCategoryId}, #{backCategoryId}, now(), now(), #{updatedBy}
    </sql>

    <sql id="paging_criteria">
        <if test="idSet != null">AND `id` IN
            <foreach collection="idSet" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="operatorCategoryId != null">AND `operator_category_id` = #{operatorCategoryId}</if>
        <if test="backCategoryId != null">AND `back_category_id` = #{backCategoryId}</if>
        <if test="operatorId != null">AND `operator_id` = #{operatorId}</if>
    </sql>

    <update id="update" parameterType="io.terminus.parana.item.category.model.OperatorCategory">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="backCategoryId != null">back_category_id = #{backCategoryId},</if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>

    <insert id="create" parameterType="io.terminus.parana.item.category.model.OperatorCategoryBinding" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="io.terminus.parana.item.category.model.OperatorCategoryBinding" keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" separator=",">
            (#{i.operatorId}, #{i.operatorCategoryId}, #{i.backCategoryId}, now(), now(), #{i.updatedBy})
        </foreach>
    </insert>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="paging_criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="OperatorCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="paging_criteria"/>
        </where>
        ORDER BY `updated_at` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByOperatorCategoryId" parameterType="long" resultMap="OperatorCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `operator_category_id` = #{operatorCategoryId}
    </select>

    <select id="findByOperatorCategoryIdSet" parameterType="map" resultMap="OperatorCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `operator_category_id` IN
        <foreach collection="operatorCategoryIdSet" item="operatorCategoryId" open="(" separator="," close=")">
            #{operatorCategoryId}
        </foreach>
        <if test="operatorId != null">AND `operator_id` = #{operatorId}</if>
    </select>

    <select id="findByBackCategoryIds" parameterType="collection" resultMap="OperatorCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `back_category_id` IN
        <foreach collection="backCategoryIds" item="backCategoryId" open="(" separator="," close=")">
            #{backCategoryId}
        </foreach>
    </select>

    <select id="findByBackCategoryIdsAndOperatorIds" parameterType="collection" resultMap="OperatorCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `operator_id` IN
        <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
        AND `back_category_id` IN
        <foreach collection="backCategoryIds" item="backCategoryId" open="(" separator="," close=")">
            #{backCategoryId}
        </foreach>
    </select>

    <select id="checkIfOperatorCategoryIdHasBinding" parameterType="long" resultType="long">
        SELECT `id` FROM
        <include refid="tb"/>
        WHERE `operator_category_id` = #{operatorCategoryId}
        LIMIT 1
    </select>

    <select id="findByOperatorCategoryIdAndBackCategoryId" parameterType="map" resultMap="OperatorCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `operator_category_id` = #{operatorCategoryId}
        AND `back_category_id` = #{backCategoryId}
    </select>

    <select id="countByOperatorCategoryId" parameterType="long" resultType="int">
        SELECT COUNT(`id`)
        FROM
        <include refid="tb"/>
        WHERE `operator_category_id` = #{operatorCategoryId}
    </select>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE `id` = #{id};
    </delete>

    <delete id="deletes" parameterType="list">
        DELETE FROM
        <include refid="tb"/>
        WHERE `id` IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByOperatorCategoryId" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE `operator_category_id` = #{operatorCategoryId}
    </delete>

    <select id="listByOperatorCategoryId" parameterType="long" resultType="long">
        SELECT back_category_id
        FROM
        <include refid="tb"/>
        WHERE operator_category_id = #{operatorCategoryId}
    </select>

    <select id="listByOperatorCategoryIdSet" parameterType="list" resultType="long">
        SELECT back_category_id
        FROM
        <include refid="tb"/>
        WHERE
        operator_category_id IN
        <foreach collection="list" separator="," open="("
                 close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listAll" resultMap="OperatorCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>

    </select>

    <select id="findByCategoryIdAndTargetId" parameterType="long" resultMap="OperatorCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `operator_category_id` = #{operatorCategoryId}
        <if test="backCategoryId != null">AND `back_category_id` = #{backCategoryId}</if>
        <if test="operatorId != null">AND `operator_id` = #{operatorId}</if>
        limit 1;

    </select>

    <select id="findByOperatorIdAndIds" parameterType="map" resultMap="OperatorCategoryBindingMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `operator_id` = #{operatorId}
        <if test="ids != null">
            AND `operator_category_id` IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>