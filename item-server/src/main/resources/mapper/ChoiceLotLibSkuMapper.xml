<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ChoiceLotLibSkuModel">
    <resultMap id="ChoiceLotLibSkuMap" type="io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel">
    	<id property="id" column="id"/>
    	<result property="choiceLotLibId" column="choice_lot_lib_id"/>
    	<result property="itemId" column="item_id"/>
    	<result property="operatorId" column="operator_id"/>
    	<result property="tenantId" column="tenant_id"/>
    	<result property="skuId" column="sku_id"/>
    	<result property="basePrice" column="base_price"/>
    	<result property="originalPrice" column="original_price"/>
    	<result property="profitRate" column="profit_rate"/>
    	<result property="markup" column="markup"/>
    	<result property="resellerGross" column="reseller_gross"/>
    	<result property="resellerGrossRate" column="reseller_gross_rate"/>
    	<result property="distributorPrice" column="distributor_price"/>
    	<result property="preResellerGrossRate" column="pre_reseller_gross_rate"/>
    	<result property="source" column="source"/>
    	<result property="stateDeleted" column="state_deleted"/>
    	<result property="createdBy" column="created_by"/>
    	<result property="createdAt" column="created_at"/>
    	<result property="updatedBy" column="updated_by"/>
    	<result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="table_name">
        choice_lot_lib_sku
    </sql>

    <sql id="columns_all">
        id,
        choice_lot_lib_id,
        item_id,
        operator_id,
        tenant_id,
        sku_id,
        base_price,
        original_price,
        profit_rate,
        markup,
        reseller_gross,
        reseller_gross_rate,
        distributor_price,
        pre_reseller_gross_rate,
        source,
        state_deleted,
        created_by,
        created_at,
        updated_by,
        updated_at
    </sql>

    <sql id="values_all">
        #{id},
        #{choiceLotLibId},
        #{itemId},
        #{operatorId},
        #{tenantId},
        #{skuId},
        #{basePrice},
        #{originalPrice},
        #{profitRate},
        #{markup},
        #{resellerGross},
        #{resellerGrossRate},
        #{distributorPrice},
        #{preResellerGrossRate},
        #{source},
        0,
        #{createdBy},
        now
        (
        ),
        #{updatedBy},
        now
        (
        )
    </sql>

    <sql id="criteria">
        <where>
        	<trim prefixOverrides="AND">
                state_deleted = 0
                AND choice_lot_lib_id = #{choiceLotLibId}
        		<if test="itemId != null">AND item_id = #{itemId}</if>
        		<if test="operatorId != null">AND operator_id = #{operatorId}</if>
        		<if test="skuId != null">AND sku_id = #{skuId}</if>
        		<if test="basePrice != null">AND base_price = #{basePrice}</if>
        		<if test="originalPrice != null">AND original_price = #{originalPrice}</if>
        		<if test="preResellerGrossRate != null">AND pre_reseller_gross_rate =  #{preResellerGrossRate}</if>
        		<if test="profitRate != null">AND profit_rate = #{profitRate}</if>
        		<if test="markup != null">AND markup = #{markup}</if>
        		<if test="resellerGross != null">AND reseller_gross = #{resellerGross}</if>
        		<if test="resellerGrossRate != null">AND reseller_gross_rate = #{resellerGrossRate}</if>
        		<if test="distributorPrice != null">AND distributor_price = #{distributorPrice}</if>
        		<if test="source != null">AND source = #{source}</if>
        	</trim>
        </where>
    </sql>

    <insert id="create" parameterType="io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_all"/>)
        VALUES
        (<include refid="values_all"/>)
    </insert>

    <insert id="creates" parameterType="io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_all"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
        (
            #{i.id},
        	#{i.choiceLotLibId},
        	#{i.itemId},
        	#{i.operatorId},
        	#{i.tenantId},
        	#{i.skuId},
        	#{i.basePrice},
        	#{i.originalPrice},
        	#{i.profitRate},
        	#{i.markup},
        	#{i.resellerGross},
        	#{i.resellerGrossRate},
        	#{i.distributorPrice},
            #{i.preResellerGrossRate},
        	#{i.source},
        	0,
        	#{i.createdBy},
        	now(),
        	#{i.updatedBy},
            now()
        )
        </foreach>
    </insert>

    <update id="updateUnBind" parameterType="map">
        update
        <include refid="table_name"/>
        set
        state_deleted = 1,updated_at = now(), updated_by=#{updatedBy}
        <where>
            state_deleted = 0
            and operator_id = #{operatorId}
            and choice_lot_lib_id = #{choiceLotLibId}
            <if test="null != itemIds and itemIds.size > 0">
                 and item_id in
                 <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                     #{item}
                 </foreach>
            </if>
        </where>
    </update>

    <update id="update" parameterType="io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel">
            update
            <include refid="table_name"/>
            SET
            <if test="basePrice != null">base_price = #{basePrice},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="markup != null">markup = #{markup},</if>
            <if test="resellerGross != null">reseller_gross = #{resellerGross},</if>
            <if test="preResellerGrossRate != null">pre_reseller_gross_rate = #{preResellerGrossRate},</if>
            <if test="resellerGrossRate != null">reseller_gross_rate = #{resellerGrossRate},</if>
            <if test="distributorPrice != null">distributor_price = #{distributorPrice},</if>
            <if test="stateDeleted != null">state_deleted = #{stateDeleted},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            updated_at = now()
            <where>
                operator_id = #{operatorId}
                AND sku_id = #{skuId}
                AND choice_lot_lib_id = #{choiceLotLibId}
                AND id = #{id}
            </where>
    </update>



    <sql id="paging_criteria">
        <where>
        	<trim prefixOverrides="AND">
                state_deleted = 0
        		<if test="choiceLotLibId != null">AND choice_lot_lib_id = #{choiceLotLibId}</if>
        		<if test="itemId != null">AND item_id = #{itemId}</if>
        		<if test="operatorId != null">AND operator_id = #{operatorId}</if>
        		<if test="skuId != null">AND sku_id = #{skuId}</if>
        		<if test="basePrice != null">AND base_price = #{basePrice}</if>
        		<if test="originalPrice != null">AND original_price = #{originalPrice}</if>
        		<if test="profitRate != null">AND profit_rate = #{profitRate}</if>
        		<if test="markup != null">AND markup = #{markup}</if>
        		<if test="resellerGross != null">AND reseller_gross = #{resellerGross}</if>
        		<if test="resellerGrossRate != null">AND reseller_gross_rate = #{resellerGrossRate}</if>
        		<if test="commission != null">AND commission = #{commission}</if>
        		<if test="distributorPrice != null">AND distributor_price = #{distributorPrice}</if>
        		<if test="source != null">AND source = #{source}</if>
                <!--        choiceLotLibIds        -->
                <if test="choiceLotLibIds!= null and choiceLotLibIds.size > 0">
                    AND choice_lot_lib_id in
                    <foreach collection="choiceLotLibIds" item="choiceLotLibId" open="(" separator="," close=")">
                        #{choiceLotLibId}
                    </foreach>
                </if>
                <!--itemIds -->
                <if test="itemIds!= null and itemIds.size > 0">
                    AND item_id in
                    <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
                        #{itemId}
                    </foreach>
                </if>
                <!--skuIds -->
                <if test="skuIds!= null and skuIds.size > 0">
                    AND sku_id in
                    <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                        #{skuId}
                    </foreach>
                </if>
            </trim>
        </where>
    </sql>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="table_name"/>
        <include refid="paging_criteria"/>
    </select>

    <select id="paging" parameterType="map" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="paging_criteria"/>
        ORDER BY `id` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="listByChoiceLotLibId" parameterType="map" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        where state_deleted = 0
        and operator_id = #{operatorId}
        and item_id = #{itemId}
        and choice_lot_lib_id = #{choiceLotLibId}
    </select>

    <select id="findById" parameterType="long" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryOne" parameterType="io.terminus.parana.item.choicelot.model.ChoiceLotLibSkuModel" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="criteria"/>
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <select id="listByWhere" parameterType="map" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <where>
            state_deleted = 0
            <if test="operatorId != null">
               and operator_id = #{operatorId}
            </if>
            <if test="itemId != null">
                and item_id = #{itemId}
            </if>
            <if test="choiceLotLibId != null">
               and choice_lot_lib_id = #{choiceLotLibId}
            </if>
            <if test="skuId != null">
                and sku_id = #{skuId}
            </if>
            <if test="choiceLotLibIds != null and choiceLotLibIds.size > 0">
                and choice_lot_lib_id in
                <foreach collection="choiceLotLibIds" item="choiceId" open="(" separator="," close=")">
                    #{choiceId}
                </foreach>
            </if>
            <if test="itemIds != null and itemIds.size > 0">
                and item_id in
                <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="skuIds != null and skuIds.size > 0">
                and sku_id in
                <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findChoiceLotLibSkuBySkuIds" parameterType="map" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <where>
            state_deleted = 0
            <if test="choiceLotLibId != null">AND choice_lot_lib_id = #{choiceLotLibId}</if>
            <if test="operatorId != null">and operator_id = #{operatorId}</if>
            <if test="itemId != null">
                and item_id = #{itemId}
            </if>
            <if test="skuIds != null and skuIds.size > 0">
                and sku_id in
                <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="choiceLotLibIds != null and choiceLotLibIds.size() > 0">
                AND choice_lot_lib_id
                in
                <foreach collection="choiceLotLibIds" item="choiceLotLibId" open="(" separator="," close=")">
                    #{choiceLotLibId}
                </foreach>
            </if>
        </where>
    </select>

    <update id="deleteBySkuId" parameterType="map">
        update
        <include refid="table_name"/>
        set
        state_deleted = 1,updated_at = now()
        <where>
            state_deleted = 0
            and sku_id in
            <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                #{skuId}
            </foreach>
        </where>
    </update>

    <select id="findChoiceLotLibSkuByOperatorIdsAndItemIds" parameterType="map" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <where>
            state_deleted = 0
            <if test="itemIds != null and itemIds.size() > 0">
                and item_id in
                <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="operatorIds != null and operatorIds.size > 0">
                AND operator_id in
                <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
                    #{operatorId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findByOperatorIdsChoiceLotLibIdsAndItemIds" parameterType="map" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <where>
            state_deleted = 0
            <if test="itemIds != null and itemIds.size() > 0">
                and item_id in
                <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="operatorIds != null and operatorIds.size > 0">
                AND operator_id in
                <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
                    #{operatorId}
                </foreach>
            </if>
            <if test="choiceLotLibIds != null and choiceLotLibIds.size > 0">
                AND choice_lot_lib_id in
                <foreach collection="choiceLotLibIds" item="choiceLotLibId" open="(" separator="," close=")">
                    #{choiceLotLibId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="list" parameterType="map" resultMap="ChoiceLotLibSkuMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="paging_criteria"/>
        ORDER BY `id` DESC
    </select>

</mapper>
