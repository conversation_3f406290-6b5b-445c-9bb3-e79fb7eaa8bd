<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="io.terminus.parana.item.area.repository.AreaSkuDao">
    <resultMap id="AreaSkuMap" type="io.terminus.parana.item.area.model.AreaSku">
        <id column="id" property="id"/>
        <result column="area_item_id" property="areaItemId"/>
        <result column="item_id" property="itemId"/>
        <result column="sku_id" property="skuId"/>
        <result column="sku_code" property="skuCode"/>
        <result column="vendor_id" property="vendorId"/>
        <result column="operator_id" property="operatorId"/>
        <result column="name" property="name"/>
        <result column="image" property="image"/>
        <result column="channel_price_json" property="channelPriceJson"/>
        <result column="original_price" property="originalPrice"/>
        <result column="default_price" property="defaultPrice"/>
        <result column="white_list_price" property="whiteListPrice"/>
        <result column="v1_price" property="v1Price"/>
        <result column="v2_price" property="v2Price"/>
        <result column="base_price" property="basePrice"/>
        <result column="register_buy_price" property="registerBuyPrice"/>
        <result column="register_spread_price" property="registerSpreadPrice"/>
        <result column="spread_price" property="spreadPrice"/>
        <result column="p_commission" property="pCommission"/>
        <result column="commission" property="commission"/>
        <result column="hq_operator_status" property="hqOperatorStatus"/>
        <result column="vendor_status" property="vendorStatus"/>
        <result column="area_operator_status" property="areaOperatorStatus"/>
        <result column="status" property="status"/>
        <result column="cooperation_mode" property="cooperationMode"/>
        <result column="logistics_mode" property="logisticsMode"/>
        <result column="min_quantity" property="minQuantity"/>
        <result column="attrs_json" property="attrsJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="in_stock" property="inStock"/>
        <result column="outerItemId" property="outer_item_id"/>
        <result column="outerSkuId" property="outer_sku_id"/>
    </resultMap>

    <sql id="tb">
        area_sku
    </sql>

    <sql id="cols_all">
        `id`, `area_item_id`, `item_id`, `sku_id`, `sku_code`, `vendor_id`, `operator_id`, `name`, `image`,
        `channel_price_json`, `original_price`, `default_price`,`white_list_price`,`v1_price`,`v2_price`, `base_price`, `register_buy_price` , `register_spread_price`,`spread_price`, `p_commission`, `commission`, `hq_operator_status`, `vendor_status`,
        `area_operator_status`, `status`, `cooperation_mode`, `logistics_mode`, `min_quantity`, `attrs_json`,
         `created_at`, `updated_at`, `updated_by`
         , `in_stock`, `outer_item_id`, `outer_sku_id`
    </sql>

    <sql id="vals_all">
        #{id}, #{areaItemId}, #{itemId}, #{skuId}, #{skuCode}, #{vendorId}, #{operatorId}, #{name}, #{image},
        #{channelPriceJson}, #{originalPrice}, #{defaultPrice}, #{whiteListPrice}, #{v1Price}, #{v2Price}, #{basePrice},#{registerBuyPrice},#{registerSpreadPrice}, #{spreadPrice}, #{pCommission}, #{commission}, #{hqOperatorStatus}, #{vendorStatus},
        #{areaOperatorStatus}, #{status}, #{cooperationMode}, #{logisticsMode}, #{minQuantity}, #{attrsJson},
        now(), now(), #{updatedBy}
        , #{inStock}, #{outerItemId}, #{outerSkuId}
    </sql>

    <sql id="criteria">
        <where>
            <include refid="visible"/>
            <if test="id != null">AND `id` = #{id}</if>
            <if test="areaItemId != null">AND `area_item_id` = #{areaItemId}</if>
            <if test="itemId != null">AND `item_id` = #{itemId}</if>
            <if test="skuId != null">AND `sku_id` = #{skuId}</if>
            <if test="skuCode != null">AND `sku_code` = #{skuCode}</if>
            <if test="vendorId != null">AND `vendor_id` = #{vendorId}</if>
            <if test="operatorId != null">AND `operator_id` = #{operatorId}</if>
        </where>
    </sql>

    <sql id="visible">
        `status` != -3
    </sql>

    <insert id="create" parameterType="io.terminus.parana.item.area.model.AreaSku"
            keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_all"/>)
        VALUES
        (<include refid="vals_all"/>)
    </insert>

    <insert id="creates" parameterType="io.terminus.parana.item.area.model.AreaSku">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_all"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (#{i.id}, #{i.areaItemId}, #{i.itemId}, #{i.skuId}, #{i.skuCode}, #{i.vendorId}, #{i.operatorId}, #{i.name},
            #{i.image}, #{i.channelPriceJson}, #{i.originalPrice},#{i.defaultPrice},#{i.whiteListPrice},#{i.v1Price},#{i.v2Price},
            #{i.basePrice},#{i.registerBuyPrice},#{i.registerSpreadPrice}, #{i.spreadPrice}, #{i.pCommission}, #{i.commission},
            #{i.hqOperatorStatus}, #{i.vendorStatus},
            #{i.areaOperatorStatus}, #{i.status}, #{i.cooperationMode}, #{i.logisticsMode}, #{i.minQuantity},
            #{i.attrsJson},
            now(), now(), #{i.updatedBy}
            , #{i.inStock}, #{i.outerItemId}, #{i.outerSkuId})
        </foreach>
    </insert>

    <update id="update" parameterType="io.terminus.parana.item.area.model.AreaSku">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="name != null">`name` = #{name},</if>
            <if test="image != null">`image` = #{image},</if>
            <if test="channelPriceJson != null">`channel_price_json` = #{channelPriceJson},</if>
            <if test="originalPrice != null">`original_price` = #{originalPrice},</if>
            <if test="defaultPrice != null">`default_price` = #{defaultPrice},</if>
            <if test="basePrice != null">`base_price` = #{basePrice},</if>
            <if test="registerBuyPrice != null">`register_buy_price` = #{registerBuyPrice},</if>
            <if test="registerSpreadPrice != null">`register_spread_price` = #{registerSpreadPrice},</if>
            <if test="whiteListPrice != null">`white_list_price` = #{whiteListPrice},</if>
            <if test="v1Price != null">`v1_price` = #{v1Price},</if>
            <if test="v2Price != null">`v2_price` = #{v2Price},</if>
            <if test="spreadPrice != null">`spread_price` = #{spreadPrice},</if>
            <if test="pCommission != null">`p_commission` = #{pCommission},</if>
            <if test="attrsJson != null">`attrs_json` = #{attrsJson},</if>
            <if test="commission != null">`commission` = #{commission},</if>
            <if test="minQuantity != null">`min_quantity` = #{minQuantity},</if>
            <if test="areaOperatorStatus != null">`area_operator_status` = #{areaOperatorStatus},</if>
            <if test="status != null">`status` = #{status},</if>
            `updated_by` = #{updatedBy},
            `updated_at` = now()
        </set>
        <where>
            id = #{id}
        </where>
    </update>

    <update id="batchUpdateByVendorPartnership" parameterType="io.terminus.parana.item.area.model.AreaSku">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="operatorId != null">`operator_id` = #{operatorId},</if>
            <if test="vendorId != null">`vendor_id` = #{vendorId},</if>
            <if test="cooperationMode != null">`cooperation_mode` = #{cooperationMode},</if>
            <if test="logisticsMode != null">`logistics_mode` = #{logisticsMode},</if>
            `updated_at` = now()
        </set>
        WHERE `vendor_id` = #{vendorId}
        AND `operator_id` = #{operatorId}
    </update>

    <update id="updateByItem" parameterType="map">
        UPDATE <include refid="tb"/>
        SET `name` = #{name},
        `image` = #{image}
        WHERE
        <include refid="visible"/>
        AND `item_id` = #{itemId}
        AND `operator_id` = #{operatorId}
    </update>

    <select id="findByOperatorIdAndItemId" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` = #{operatorId}
        AND `item_id` = #{itemId}
    </select>

    <select id="findCanalByOperatorIdAndItemId" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `operator_id` = #{operatorId}
        AND `item_id` = #{itemId}
    </select>

    <select id="findByItem" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        <if test="operatorId != null">AND `operator_id` = #{operatorId}</if>
        AND `item_id` IN
        <foreach collection="itemIdSet" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>

    <select id="findByOperatorIdsAndItemIds" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` IN
        <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
        AND `item_id` IN
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>

    <select id="findByOperatorIdAndSkuId" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` = #{operatorId}
        AND `sku_id` = #{skuId}
        LIMIT 1
    </select>

    <select id="findCanalByOperatorIdAndSkuId" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `operator_id` = #{operatorId}
        AND `sku_id` = #{skuId}
    </select>

    <select id="findByOperatorIdAndSkuIds" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <trim prefixOverrides="AND">
                <include refid="visible"/>
                <if test="operatorId != null">AND `operator_id` = #{operatorId}</if>
                <if test="skuIds != null and skuIds.size() > 0">
                    AND `sku_id` IN
                    <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
                        #{skuId}
                    </foreach>
                </if>
            </trim>
        </where>
        order by sku_id
    </select>

    <select id="findByOperatorIdAndOuterSkuIds" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <trim prefixOverrides="AND">
                <include refid="visible"/>
                <if test="operatorId != null">AND `operator_id` = #{operatorId}</if>
                <if test="outerSkuIds != null and outerSkuIds.size() > 0">
                    AND `outer_sku_id` IN
                    <foreach collection="outerSkuIds" item="outerSkuId" open="(" separator="," close=")">
                        #{outerSkuId}
                    </foreach>
                </if>
            </trim>
        </where>
    </select>

    <select id="findByOperatorIdsAndSkuIds" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` IN
        <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
        AND `sku_id` IN
        <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
    </select>

    <select id="findByOperatorIdsAreaItemIds" parameterType="map">
        SELECT FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `item_id` IN
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        AND `operator_id` IN
        <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
    </select>

    <update id="updateDefaultPrice" parameterType="io.terminus.parana.item.area.model.AreaSku">
        UPDATE
        <include refid="tb"/>
        SET `base_price` = #{basePrice},
        `default_price` = #{defaultPrice},
        `p_commission` = #{pCommission},
        `commission` = #{commission},
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `operator_id` = #{operatorId}
        AND `sku_id` = #{skuId}
    </update>

    <update id="justUpdate" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `updated_at` = now()
        WHERE
        <include refid="visible"/>
        AND `operator_id` = #{operatorId}
        AND `sku_id` = #{skuId}
    </update>


    <update id="operatorOnShelf" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `area_operator_status` = 1,
        `status` = 1,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE `status` = -1
        AND `operator_id` = #{operatorId}
        AND `item_id` = #{itemId}
    </update>

    <update id="operatorOffShelf" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `area_operator_status` = -1,
        `status` = -1,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE `status` = 1
        AND `operator_id` = #{operatorId}
        AND `item_id` = #{itemId}
    </update>

    <update id="operatorDeleteAll" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `status` = -3,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `operator_id` = #{operatorId}
        AND `vendor_id` = #{vendorId}
    </update>

    <update id="updateItemStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `status` = #{status}
        WHERE
        `item_id` in
        <foreach collection="itemIdSet" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </update>

    <update id="operatorDeleteByItemId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `status` = -3,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `item_id` = #{itemId}
        AND `vendor_id` = #{vendorId}
        AND `operator_id` IN
        <foreach collection="operatorIdSet" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
    </update>

    <select id="findBySingleItemLimitOperator" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` IN
        <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
        AND `item_id` = #{itemId}
    </select>

    <select id="findBySingleSkuLimitOperator" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` IN
        <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
        AND `sku_id` = #{skuId}
    </select>

    <select id="findBySkuLimitOperator" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` IN
        <foreach collection="operatorIdSet" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
        AND `sku_id` IN
        <foreach collection="skuIdSet" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>

    </select>

    <update id="hqFreeze" parameterType="map">
        update
        <include refid="tb"/>
        SET `hq_operator_status` = -2,
        `status` = -2,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `hq_operator_status` = 1
        AND `item_id` = #{itemId}
        AND `operator_id` = #{operatorId}
    </update>

    <update id="hqFreezeOperator" parameterType="map">
        update
        <include refid="tb"/>
        SET `hq_operator_status` = -2,
        `status` = -2,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `hq_operator_status` = 1
        AND `operator_id` = #{operatorId}
    </update>

    <update id="hqFreezeVendor" parameterType="map">
        update
        <include refid="tb"/>
        SET `vendor_status` = -2,
        `status` = -2,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `vendor_id` = #{vendorId}
    </update>

    <update id="hqUnfreeze" parameterType="map">
        update
        <include refid="tb"/>
        SET `hq_operator_status` = 1,
        `area_operator_status` = -1,
        `status` = LEAST(-1, `vendor_status`),
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `hq_operator_status` = -2
        AND `item_id` = #{itemId}
        AND `operator_id` = #{operatorId}
    </update>

    <update id="hqUnfreezeOperator" parameterType="map">
        update
        <include refid="tb"/>
        SET `hq_operator_status` = 1,
        `area_operator_status` = -1,
        `status` = LEAST(-1, `vendor_status`),
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `hq_operator_status` = -2
        AND `operator_id` = #{operatorId}
    </update>

    <update id="hqUnfreezeVendor" parameterType="map">
        update
        <include refid="tb"/>
        SET `vendor_status` = 1,
        `area_operator_status` = -1,
        `status` = LEAST(-1, `hq_operator_status`),
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `vendor_id` = #{vendorId}
    </update>

    <update id="vendorFreeze" parameterType="map">
        update
        <include refid="tb"/>
        SET `vendor_status` = -2,
        `status` = -2,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `vendor_status` = 1
        AND `item_id` = #{itemId}
        AND `operator_id` IN
        <foreach collection="operatorIdSet" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
    </update>

    <update id="vendorUnfreeze" parameterType="map">
        update
        <include refid="tb"/>
        SET `vendor_status` = 1,
        `area_operator_status` = -1,
        `status` = LEAST(-1, `vendor_status`, `area_operator_status`),
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `vendor_status` = -2
        AND `item_id` = #{itemId}
        AND `operator_id` IN
        <foreach collection="operatorIdSet" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
    </update>

    <select id="queryBySkuCode" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `operator_id` = #{operatorId}
        AND `sku_code` IN
        <foreach collection="skuCodeSet" item="skuCode" open="(" separator="," close=")">
            #{skuCode}
        </foreach>
        <if test="vendorId != null">AND `vendor_id` = #{vendorId}</if>
    </select>

    <update id="operatorAudi" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `area_operator_status` = -5,
        `status` = -5,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE `status` = -1
        AND `operator_id` = #{operatorId}
        AND `item_id` = #{itemId}
    </update>

    <update id="operatorOnAudi" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `area_operator_status` = 1,
        `status` = 1,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE `status` = -5
        AND `operator_id` = #{operatorId}
        AND `item_id` = #{itemId}
    </update>

    <update id="operatorOffAudit" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `area_operator_status` = 1,
        `status` = 1,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND`operator_id` = #{operatorId}
        AND `item_id` = #{itemId}
    </update>

    <update id="updateTriggerTime" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE
        <include refid="visible"/>
        AND `operator_id` = #{operatorId}
        AND `sku_id` IN
        <foreach collection="skuIdSet" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
    </update>

    <delete id="deleteBySkuIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        `updated_at` = now(),
        `status` = -3
        WHERE sku_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        `updated_at` = now(),
        `status` = -3
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findByOperatorId" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` IN
        <foreach collection="operatorIdSet" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
    </select>

    <select id="pageFindByOperatorId" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` = #{operatorId}
        order by id
        LIMIT #{offset}, #{limit}
    </select>

    <select id="pageFindByVendorId" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `vendor_id` = #{vendorId}
        LIMIT #{offset}, #{limit}
    </select>

    <select id="pageFindByOperatorIdAndVendorId" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <include refid="visible"/>
        AND `operator_id` = #{operatorId}
        AND `vendor_id` = #{vendorId}
        LIMIT #{offset}, #{limit}
    </select>

    <update id="updateInStock" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `in_stock` = #{inStock},
        `updated_at` = now()
        WHERE `operator_id` = #{operatorId}
        AND `sku_id` = #{skuId}
    </update>
    <update id="vendorOffShelf" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `area_operator_status` = -1,
        `status` = -1,
        `updated_at` = now(),
        `updated_by` = #{updatedBy}
        WHERE `status` = 1
        AND `operator_id`  in
        <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
        AND `item_id` = #{itemId}
    </update>

    <select id="querySkuNumberTotal" parameterType="map" resultType="java.lang.Long">
        SELECT
           COUNT(1)
        FROM
            <include refid="tb"/>
        WHERE status != -3
        <if test="operatorId != null">
            AND operator_id = #{operatorId}
        </if>
        <if test="vendorId != null">
            AND vendor_id = #{vendorId}
            <choose>
                <!--type 1 查询总sku数量 2查询在售sku数量 -->
                <when test="type != null and type == 1">
                    AND status in (1,-1,-2)
                </when>
                <otherwise>
                    AND status = 1
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="queryCurrentMonthSkuNumberTotal" parameterType="map" resultType="java.lang.Long">
        SELECT
        COUNT( 1 )
        FROM
        <include refid="tb"/>
        WHERE
        STATUS != -3
        <if test="operatorId!=null">
            AND operator_id = #{operatorId}
        </if>
        AND DATE_FORMAT( created_at, '%Y%m' ) = DATE_FORMAT( CURDATE( ), '%Y%m' )
    </select>

    <update id="batchUpdateAreaItemStatus" parameterType="map">
        update
        <include refid="tb"/>
        SET
        status = #{status},updated_by = #{userName},updated_at = now()
        where
        <include refid="visible"/>
        AND `operator_id` = #{operatorId}
        <if test="itemIdSet != null and itemIdSet.size() > 0">
            and `item_id` in
            <foreach collection="itemIdSet" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
    </update>

    <select id="findByShopId" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `vendor_id` = #{shopId}
        LIMIT #{offset}, #{limit}
    </select>


    <select id="findBySkuIds" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        sku_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findDeleteByOperatorIdsAndItemIds" parameterType="map" resultMap="AreaSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `status` = -3
        AND `operator_id` IN
        <foreach collection="operatorIds" item="operatorId" open="(" separator="," close=")">
            #{operatorId}
        </foreach>
        AND `item_id` IN
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>
</mapper>
