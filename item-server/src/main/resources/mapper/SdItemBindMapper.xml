<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="SdItemBindBO">
    <resultMap id="SdItemBindMap" type="io.terminus.parana.item.open.model.SdItemBindBO">
        <id property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="sdId" column="sd_id"/>
        <result property="itemId" column="item_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="vendorId" column="vendor_id"/>
        <result property="operatorId" column="operator_id"/>
        <result property="sdItemId" column="sd_item_id"/>
        <result property="sdDeptNo" column="sd_dept_no"/>
        <result property="sdLtdNo" column="sd_ltd_no"/>
        <result property="sdProjectNo" column="sd_project_no"/>
        <result property="prate" column="prate"/>
        <result property="pprate" column="pprate"/>
        <result property="erpStock" column="erp_stock"/>
        <result property="erpUnit" column="erp_unit"/>
        <result property="stockRatio" column="stock_ratio"/>
        <result property="sdSts" column="sd_sts"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatedBy" column="updated_by"/>
    </resultMap>

    <sql id="table_name">
        sd_item_bind
    </sql>

    <sql id="columns_all">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="columns_exclude_id">
        id,
        tenant_id,
        sd_id,
        item_id,
        sku_id,
        vendor_id,
        operator_id,
        sd_item_id,
        sd_dept_no,
        sd_ltd_no,
        sd_project_no,
        prate,
        pprate,
        erp_stock,
        erp_unit,
        stock_ratio,
        sd_sts,
        status,
        created_at,
        updated_at,
        updated_by
    </sql>

    <sql id="values_exclude_id">
        #{id},
        #{tenantId},
        #{sdId},
        #{itemId},
        #{skuId},
        #{vendorId},
        #{operatorId},
        #{sdItemId},
        #{sdDeptNo},
        #{sdLtdNo},
        #{sdProjectNo},
        #{prate},
        #{pprate},
        #{erpStock},
        #{erpUnit},
        #{stockRatio},
        #{sdSts},
        #{status},
        #{createdAt},
        #{updatedAt},
        #{updatedBy}
    </sql>

    <sql id="criteria">
        <where>
            <trim prefixOverrides="AND">
                <if test="id != null">AND id = #{id}</if>
                <if test="tenantId != null">AND tenant_id = #{tenantId}</if>
                <if test="sdId != null">AND sd_id = #{sdId}</if>
                <if test="itemId != null">AND item_id = #{itemId}</if>
                <if test="skuId != null">AND sku_id = #{skuId}</if>
                <if test="vendorId != null">AND vendor_id = #{vendorId}</if>
                <if test="operatorId != null">AND operator_id = #{operatorId}</if>
                <if test="sdItemId != null">AND sd_item_id = #{sdItemId}</if>
                <if test="sdDeptNo != null">AND sd_dept_no = #{sdDeptNo}</if>
                <if test="sdLtdNo != null">AND sd_ltd_no = #{sdLtdNo}</if>
                <if test="sdProjectNo != null">AND sd_project_no = #{sdProjectNo}</if>
                <if test="prate != null">AND prate = #{prate}</if>
                <if test="pprate != null">AND pprate = #{pprate}</if>
                <if test="erpStock != null">AND erp_stock = #{erpStock}</if>
                <if test="erpUnit != null">AND erp_unit = #{erpUnit}</if>
                <if test="stockRatio != null">AND stock_ratio = #{stockRatio}</if>
                <if test="sdSts != null">AND sd_sts = #{sdSts}</if>
                <if test="status != null">AND status = #{status}</if>
                <if test="createdAt != null">AND created_at = #{createdAt}</if>
                <if test="updatedAt != null">AND updated_at = #{updatedAt}</if>
                <if test="updatedBy != null">AND updated_by = #{updatedBy}</if>
            </trim>
        </where>
    </sql>

    <insert id="create" parameterType="io.terminus.parana.item.open.model.SdItemBindBO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        (<include refid="values_exclude_id"/>)
    </insert>

    <insert id="creates" parameterType="io.terminus.parana.item.open.model.SdItemBindBO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.id},
            #{i.tenantId},
            #{i.sdId},
            #{i.itemId},
            #{i.skuId},
            #{i.vendorId},
            #{i.operatorId},
            #{i.sdItemId},
            #{i.sdDeptNo},
            #{i.sdLtdNo},
            #{i.sdProjectNo},
            #{i.prate},
            #{i.pprate},
            #{i.erpStock},
            #{i.erpUnit},
            #{i.stockRatio},
            #{i.sdSts},
            #{i.status},
            #{i.createdAt},
            #{i.updatedAt},
            #{i.updatedBy}
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="SdItemBindMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="SdItemBindMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="update" parameterType="io.terminus.parana.item.open.model.SdItemBindBO">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="sdId != null">sd_id = #{sdId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="skuId != null">sku_id = #{skuId},</if>
            <if test="vendorId != null">vendor_id = #{vendorId},</if>
            <if test="sdItemId != null">sd_item_id = #{sdItemId},</if>
            <if test="sdDeptNo != null">sd_dept_no = #{sdDeptNo},</if>
            <if test="sdLtdNo != null">sd_ltd_no = #{sdLtdNo},</if>
            <if test="sdProjectNo != null">sd_project_no = #{sdProjectNo},</if>
            <if test="prate != null">prate = #{prate},</if>
            <if test="pprate != null">pprate = #{pprate},</if>
            <if test="erpStock != null">erp_stock = #{erpStock},</if>
            <if test="erpUnit != null">erp_unit = #{erpUnit},</if>
            <if test="stockRatio != null">stock_ratio = #{stockRatio},</if>
            <if test="sdSts != null">sd_sts = #{sdSts},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
        AND tenant_id = #{tenantId}
        AND operator_id = #{operatorId}
    </update>

    <sql id="paging_criteria">
        <where>
            <trim prefixOverrides="AND">
                <if test="id != null">AND id = #{id}</if>
                <if test="tenantId != null">AND tenant_id = #{tenantId}</if>
                <if test="sdId != null">AND sd_id = #{sdId}</if>
                <if test="itemId != null">AND item_id = #{itemId}</if>
                <if test="skuId != null">AND sku_id = #{skuId}</if>
                <if test="vendorId != null">AND vendor_id = #{vendorId}</if>
                <if test="operatorId != null">AND operator_id = #{operatorId}</if>
                <if test="sdItemId != null">AND sd_item_id = #{sdItemId}</if>
                <if test="sdDeptNo != null">AND sd_dept_no = #{sdDeptNo}</if>
                <if test="sdLtdNo != null">AND sd_ltd_no = #{sdLtdNo}</if>
                <if test="sdProjectNo != null">AND sd_project_no = #{sdProjectNo}</if>
                <if test="prate != null">AND prate = #{prate}</if>
                <if test="pprate != null">AND pprate = #{pprate}</if>
                <if test="erpStock != null">AND erp_stock = #{erpStock}</if>
                <if test="erpUnit != null">AND erp_unit = #{erpUnit}</if>
                <if test="stockRatio != null">AND stock_ratio = #{stockRatio}</if>
                <if test="sdSts != null">AND sd_sts = #{sdSts}</if>
                <if test="status != null">AND status = #{status}</if>
                <if test="createdAt != null">AND created_at = #{createdAt}</if>
                <if test="updatedAt != null">AND updated_at = #{updatedAt}</if>
                <if test="updatedBy != null">AND updated_by = #{updatedBy}</if>
            </trim>
        </where>
    </sql>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="table_name"/>
        <include refid="paging_criteria"/>
    </select>

    <select id="paging" parameterType="map" resultMap="SdItemBindMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="paging_criteria"/>
        ORDER BY `id` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="list" parameterType="io.terminus.parana.item.open.model.SdItemBindBO" resultMap="SdItemBindMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="criteria"/>
        ORDER BY `id` DESC
    </select>

    <select id="queryOne" parameterType="io.terminus.parana.item.open.model.SdItemBindBO" resultMap="SdItemBindMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        <include refid="criteria"/>
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <select id="deleteById" parameterType="long">
        DELETE
        FROM
        <include refid="table_name"/>
        WHERE id = #{id}
    </select>

    <select id="deleteByIds" parameterType="list">
        DELETE
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="batchQueryBindInfo" parameterType="map" resultMap="SdItemBindMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE 1 = 1
        AND `status` = #{status}
        <if test="skuIds.size() > 0 and skuIds != null" >
            AND sku_id IN
            <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                #{skuId}
            </foreach>
        </if>
        <if test="operatorIds.size() > 0 and operatorIds != null">
            AND operator_id IN
            <foreach collection="operatorIds" item="operatorId" open="(" close=")" separator=",">
                #{operatorId}
            </foreach>
        </if>
    </select>

    <select id="bySdCnoAndPnoBatchQueryBindInfo" parameterType="map" resultMap="SdItemBindMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE 1 = 1
        AND `status` = #{status}
        AND operator_id = #{operatorId}
        <if test="cnoList.size() > 0 and cnoList != null" >
            AND sd_project_no IN
            <foreach collection="cnoList" item="cno" open="(" close=")" separator=",">
                #{cno}
            </foreach>
        </if>
        <if test="pnoList.size() > 0 and pnoList != null" >
            AND sd_item_id IN
            <foreach collection="pnoList" item="pno" open="(" close=")" separator=",">
                #{pno}
            </foreach>
        </if>
    </select>

    <select id="bySkuIdsBatchQueryBindInfo" parameterType="map" resultMap="SdItemBindMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE 1 = 1
        AND `status` = #{status}
        AND operator_id = #{operatorId}
        <if test="skuIds.size() > 0 and skuIds != null" >
            AND sku_id IN
            <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                #{skuId}
            </foreach>
        </if>
    </select>

    <select id="byItemIdIdsBatchQueryBindInfo" parameterType="map" resultMap="SdItemBindMap">
        SELECT
        <include refid="columns_all"/>
        FROM
        <include refid="table_name"/>
        WHERE 1 = 1
        AND `status` = #{status}
        AND operator_id = #{operatorId}
        <if test="itemIds.size() > 0 and itemIds != null" >
            AND item_id IN
            <foreach collection="itemIds" item="itemId" open="(" close=")" separator=",">
                #{itemId}
            </foreach>
        </if>
    </select>

</mapper>
