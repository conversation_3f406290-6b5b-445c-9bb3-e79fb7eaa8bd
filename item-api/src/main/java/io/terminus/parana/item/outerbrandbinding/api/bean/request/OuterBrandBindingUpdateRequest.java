package io.terminus.parana.item.outerbrandbinding.api.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.outerbrandbinding.constant.errorcode.OuterBrandBindingErrorCode;
import io.terminus.parana.item.outerbrandbinding.enums.OuterBrandBindingOperationType;
import lombok.Data;

@Data
@ApiModel(value = "OuterBrandBindingUpdateRequest", description = "品牌绑定关系表修改请求", parent = AbstractRequest.class)
public class OuterBrandBindingUpdateRequest extends AbstractRequest {

    @ApiModelProperty(value = "")
    private Long id;
    @ApiModelProperty(value = "供应商id")
    private Long vendorId;
    @ApiModelProperty(value = "品牌id")
    private Long categoryId;
    @ApiModelProperty(value = "外部品牌id")
    private Long outerCategoryId;
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createdAt;
    @ApiModelProperty(value = "修改人")
    private String updatedBy;
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updatedAt;

    @Override
    public void checkParam() {
        super.checkParam();
        ParamUtil.nonNull(id, OuterBrandBindingErrorCode.ID_IS_BLANK);
        ParamUtil.nonNull(vendorId, OuterBrandBindingErrorCode.VENDOR_ID_IS_BLANK);
        ParamUtil.nonNull(categoryId, OuterBrandBindingErrorCode.CATEGORY_ID_IS_BLANK);
        ParamUtil.nonNull(outerCategoryId, OuterBrandBindingErrorCode.OUTER_CATEGORY_ID_IS_BLANK);
        ParamUtil.nonNull(createdBy, OuterBrandBindingErrorCode.CREATED_BY_IS_BLANK);
        ParamUtil.nonNull(createdAt, OuterBrandBindingErrorCode.CREATED_AT_IS_BLANK);
        ParamUtil.nonNull(updatedBy, OuterBrandBindingErrorCode.UPDATED_BY_IS_BLANK);
        ParamUtil.nonNull(updatedAt, OuterBrandBindingErrorCode.UPDATED_AT_IS_BLANK);
    }

    @Override
    public OperationType getOperationType() {
        return OuterBrandBindingOperationType.OUTER_BRAND_BINDING_UPDATE;
    }
}
