package io.terminus.parana.item.choicelot.api.bean.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.api.consts.OperationType;
import io.terminus.api.request.AbstractRequest;
import io.terminus.api.utils.ParamUtil;
import io.terminus.parana.item.choicelot.api.bean.param.ChoiceLotLibItemExcelImportParam;
import io.terminus.parana.item.choicelot.enums.ChoiceLotLibItemOperationType;
import io.terminus.parana.item.choicelot.errorcode.ChoiceLotLibItemErrorCode;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ChoiceLotLibItemBindRequest", description = "选品库Item创建(绑定)请求", parent = AbstractRequest.class)
public class ChoiceLotLibItemBindRequest extends AbstractRequest {

	private static final long serialVersionUID = 3929060570977426097L;

	@ApiModelProperty(value = "选品库ID", required = true)
	private Long choiceLotLibId;

	@ApiModelProperty(value = "item列表", required = true)
	private List<Long> itemIds;

	@ApiModelProperty(value = "区域运营ID", hidden = true)
	private Long operatorId;

	@ApiModelProperty(value = "添加来源 0-渠道商添加 1-区域运营同步")
	private Integer source;

	@ApiModelProperty(value = "可见渠道商(分割符号[,])", hidden = true)
	private String showDistributorIds;

	private String createBy;

	@ApiModelProperty("待导入的商品信息")
	private List<ChoiceLotLibItemExcelImportParam> choiceLotLibItemExcelImportParamList;

	@Override
	public void checkParam() {
		super.checkParam();
		ParamUtil.nonNull(choiceLotLibId, ChoiceLotLibItemErrorCode.CHOICE_LOT_LIB_ID_IS_BLANK);
	}

    @Override
    public OperationType getOperationType() {
        return ChoiceLotLibItemOperationType.CHOICE_LOT_LIB_ITEM_CREATE;
    }
}
