package io.terminus.parana.item.mq.consumer;


import io.terminus.parana.item.sap.api.facade.ItemCommonReadFacade;
import io.terminus.parana.item.sap.api.facade.ItemCommonWriteFacade;
import io.terminus.parana.item.sap.event.SapItemCreateEvent;

//@Slf4j
//@MQConsumer
public class SapItemCreateConsumer {

    private final ItemCommonReadFacade itemCommonReadFacade;
    private final ItemCommonWriteFacade itemCommonWriteFacade;


    public SapItemCreateConsumer(ItemCommonReadFacade itemCommonReadFacade,
                                 ItemCommonWriteFacade itemCommonWriteFacade) {
        this.itemCommonReadFacade = itemCommonReadFacade;
        this.itemCommonWriteFacade = itemCommonWriteFacade;
    }

//    @MQSubscribe(tag = SapItemCreateEvent.TAG)
    public void subscribe(SapItemCreateEvent event) {

//        log.info("[SAPITEM]消费MQ_SAP_ITEM_CREATE消息开始 START,param:{}", event);
//        Assert.nonNull(event, "event.is.null");
//        Assert.nonNull(event.getItemId(), "event.itemId.is.null");
//        Assert.nonNull(event.getTenantId(), "event.tenantId.is.null");
//
//        FullCommonItemQueryRequest request = new FullCommonItemQueryRequest();
//        request.setItemId(event.getItemId());
//        request.setTenantId(event.getTenantId());
//
//        FullCommonItemInfo fullItems = Assert.take(itemCommonReadFacade.queryFullItem(request));
//        //TODO 调用SAP保存商品返回ID接口
//
//        String sapId = "";
//
//        ItemSapIdUpdateRequest updateRequest= new ItemSapIdUpdateRequest();
//        updateRequest.setItemId(event.getItemId());
//        updateRequest.setTantentId(event.getTenantId());
//        updateRequest.setSapId(sapId);
//
//        Boolean updSapIdRes = Assert.take(itemCommonWriteFacade.updateItemSapId(updateRequest));
//
//        if(!updSapIdRes){
//            log.error("[SAPITEM]消费MQ_SAP_ITEM_CREATE,更新商品sapId失败，itemId:{},sapId{}",event.getItemId(),sapId);
//        }
//
//        log.info("[SAPITEM]消费MQ_SAP_ITEM_CREATE消息开始 END,param:{}", event);
    }


}
